package com.prx.commons.utils;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2024/6/3 15:07
 */
public class ThreadPoolDefine {

  // cpu number * 4
  static int CORE_SIZE = 10;

  // cpu number * 8
  static int MAX_SIZE = 100;

  private static final Integer QUEUE_SIZE = 10;

  private static final Long KEEP_ALIVE_TIME = 10L;

  public static ExecutorService CALCULATE_VARIANT_THREAD_POOL =
      new ThreadPoolExecutor(
          CORE_SIZE,
          MAX_SIZE,
          KEEP_ALIVE_TIME,
          TimeUnit.MINUTES,
          new ArrayBlockingQueue<>(QUEUE_SIZE),
          new BasicThreadFactory.Builder()
              .uncaughtExceptionHandler(new MyHandler())
              .daemon(false)
              .build(),
          new ThreadPoolExecutor.CallerRunsPolicy());
}

class MyHandler implements Thread.UncaughtExceptionHandler {
  private static final Logger log = LoggerFactory.getLogger(MyHandler.class);

  @Override
  public void uncaughtException(Thread t, Throwable e) {
    log.error("threadId = {}, threadName = {}, ex = {}", t.getId(), t.getName(), e.getMessage());
    log.error("error reason is : ", e);
  }
}
