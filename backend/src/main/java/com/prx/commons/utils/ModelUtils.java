package com.prx.commons.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.prx.dto.model.DataModelInfo;
import com.prx.dto.model.ThreeEventAttributes;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.dict.FileTypeEnum;
import com.sp.proxverse.common.model.dto.result.StructFieldInfo;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelForeignPO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.FileFieldPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.util.ChineseUtils;
import com.sp.proxverse.common.util.DataSourceUtils;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.DataModelForeignService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.dao.service.DataModelSortService;
import com.sp.proxverse.interfaces.dao.service.FileFieldService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.common.model.DataTypeEnum;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.datasource.index.TableDesc;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import scala.collection.Iterator;

@Slf4j
@Component
public class ModelUtils {

  @Autowired DataModelFileService dataModelFileService;

  @Autowired FileFieldService fileFieldService;

  @Autowired DataModelService dataModelService;

  @Autowired DataModelForeignService dataModelForeignService;

  @Autowired DataModelSortService dataModelSortService;

  @Autowired FileService fileService;

  public TableDesc activityTableDesc(DataModelInfo dataModelNewInfo) {
    List<Integer> fieldIdList =
        Lists.newArrayList(
            dataModelNewInfo.getModel().getCaseid(),
            dataModelNewInfo.getModel().getEvent(),
            dataModelNewInfo.getModel().getTime(),
            dataModelNewInfo.getModel().getStartTime());

    List<FileFieldPO> fieldPOS = fileFieldService.listByIds(fieldIdList);
    Map<Integer, FileFieldPO> fieldPOMap =
        fieldPOS.stream()
            .collect(Collectors.toMap(FileFieldPO::getId, Function.identity(), (k1, k2) -> k1));

    FileFieldPO caseId = fieldPOMap.get(dataModelNewInfo.getModel().getCaseid());
    FileFieldPO event = fieldPOMap.get(dataModelNewInfo.getModel().getEvent());
    FileFieldPO time = fieldPOMap.get(dataModelNewInfo.getModel().getTime());
    FileFieldPO startTimePO = fieldPOMap.get(dataModelNewInfo.getModel().getStartTime());
    FileFieldPO startTime = Objects.isNull(startTimePO) ? time : startTimePO;

    if (caseId == null || event == null || time == null) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.CASE_ATTR_CHECK));
    }
    ThreeEventAttributes treeEventAttributes =
        ThreeEventAttributes.builder()
            .caseId(caseId.getFieldOrigin())
            .activity(event.getFieldOrigin())
            .timestamp(time.getFieldOrigin())
            .startTimestamp(startTime.getFieldOrigin())
            .build();

    String[] sortArray = buildSortColumns(dataModelNewInfo.modelId, treeEventAttributes);

    return new TableDesc(
        dataModelNewInfo.getActive().getSparkTableName(dataModelNewInfo.newVersionNumber),
        new String[] {treeEventAttributes.getCaseId()},
        sortArray,
        new String[] {
          treeEventAttributes.getCaseId(),
          treeEventAttributes.getTimestamp(),
          treeEventAttributes.getActivity(),
          treeEventAttributes.getStartTimestamp()
        });
  }

  public TableDesc caseTableDesc(DataModelInfo dataModelNewInfo) {
    DataModelFilePO caseTable = dataModelNewInfo.getCaseTable();
    if (caseTable == null) {
      return null;
    }
    DataModelFilePO activeTable = dataModelNewInfo.getActive();
    List<DataModelForeignPO> fileForeign =
        dataModelForeignService.getFileForeignt(
            dataModelNewInfo.modelId, caseTable.getFileId(), activeTable.getFileId());
    if (fileForeign.size() != 1) {
      throw new IllegalArgumentException(
          "Error for build case table, could not found case bucket table join key");
    }
    FileFieldPO field = fileFieldService.getById(fileForeign.get(0).getPointFieldId());
    String[] sortColumn = new String[] {field.getFieldOrigin()};
    return new TableDesc(
        caseTable.getSparkTableName(dataModelNewInfo.newVersionNumber),
        new String[] {field.getFieldOrigin()},
        sortColumn,
        sortColumn);
  }

  public DataModelFilePO getDataModelFileByFileType(int modelId, FileActiveTypeEnum typeEnum) {
    return dataModelFileService.getOne(
        new LambdaQueryWrapper<DataModelFilePO>()
            .eq(DataModelFilePO::getActive, typeEnum.getValue())
            .eq(DataModelFilePO::getDataModelId, modelId)
            .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  public int getBucketNumber(int modelId) {
    DataModelPO model = getDataModelPO(modelId);
    return model.getNumBuckets();
  }

  @NotNull
  private DataModelPO getDataModelPO(int modelId) {
    DataModelPO model = dataModelService.getById(modelId);
    if (model == null) {
      throw new BizException(5000, "Error for found model with model id " + modelId);
    }
    return model;
  }

  public String genTempTableNameWithCurrentDB(int poolId) {
    return DataSourceUtils.makeFullTableName(
        UUID.randomUUID().toString().replace("-", "_"), DataSourceUtils.makeDatabaseName(poolId));
  }

  public String[] buildSortColumns(Integer dataModelId, ThreeEventAttributes threeEventAttributes) {
    String[] sortArray;
    List<Integer> sortList = dataModelSortService.getSortingList(dataModelId);
    if (CollectionUtils.isNotEmpty(sortList)) {
      List<FileFieldPO> sortFieldList = fileFieldService.listByIds(sortList);
      sortArray = new String[sortFieldList.size() + 3];
      sortArray[0] = threeEventAttributes.getCaseId();
      sortArray[1] = threeEventAttributes.getTimestamp();
      for (int i = 0; i < sortFieldList.size(); i++) {
        sortArray[i + 2] = sortFieldList.get(i).getFieldOrigin();
      }
      sortArray[sortArray.length - 1] = threeEventAttributes.getActivity();
    } else {
      sortArray =
          new String[] {
            threeEventAttributes.getCaseId(),
            threeEventAttributes.getTimestamp(),
            threeEventAttributes.getActivity()
          };
    }
    return sortArray;
  }

  public String getSourceTableName(int poolId, DataModelFilePO activityFile) {
    if (activityFile == null) {
      return null;
    }
    FilePO file = fileService.getById(activityFile.getFileId());
    return getActiveParquetTable(file, poolId);
  }

  public String getSourceTableName(int modelId, FileActiveTypeEnum active) {
    DataModelFilePO activityFile = dataModelFileService.getFileByType(modelId, active);
    if (activityFile == null) {
      return null;
    }
    DataModelPO model = dataModelService.getById(modelId);
    FilePO file = fileService.getById(activityFile.getFileId());
    return getActiveParquetTable(file, model.getPoolId());
  }

  public String getSourceTableName(FilePO file, Integer poolId) {
    return getActiveParquetTable(file, poolId);
  }

  public List<FilePO> getFileList(int modelId) {
    List<DataModelFilePO> fileList = dataModelFileService.getFileList(modelId);
    if (CollectionUtils.isEmpty(fileList)) {
      return new ArrayList<>();
    }
    List<Integer> fileIds =
        fileList.stream().map(DataModelFilePO::getFileId).collect(Collectors.toList());
    return fileService.listByIds(fileIds);
  }

  public void refreshTableStructureByFile(FilePO filePo, Integer dataPoolId) {
    List<FileFieldPO> fileFieldPOS =
        fileFieldService.list(
            new LambdaQueryWrapper<FileFieldPO>()
                .eq(FileFieldPO::getFileId, filePo.getId())
                .eq(FileFieldPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .eq(filePo.getTenantId() != null, FileFieldPO::getTenantId, filePo.getTenantId()));
    Map<String, FileFieldPO> oldFileFieldMap =
        fileFieldPOS.stream()
            .collect(
                Collectors.toMap(FileFieldPO::getFieldOrigin, Function.identity(), (k1, k2) -> k1));
    String activeParquetTable = getActiveParquetTable(filePo, dataPoolId);
    if (SparkSessionEnv.getSparkSession().catalog().tableExists(activeParquetTable)) {
      StructType schema =
          SparkSessionEnv.getSparkSession().read().table(activeParquetTable).schema();
      updateFileFieldBySchema(schema, filePo.getId(), filePo.getTenantId(), oldFileFieldMap);
    }
  }

  public void refreshTableStructure(DataModelPO model) {
    List<FilePO> fileList = this.getFileList(model.getId());
    if (CollectionUtils.isEmpty(fileList)) {
      return;
    }
    fileList = fileList.stream().filter(po -> po.getType() != null).collect(Collectors.toList());

    for (FilePO filePO : fileList) {
      refreshTableStructureByFile(filePO, model.getPoolId());
    }
  }

  private String getActiveParquetTable(FilePO file, Integer poolId) {
    String databaseName = org.apache.spark.sql.datasource.DataSourceUtils.makeDatabaseName(poolId);
    String activeParquetTable;
    // todo check status
    if (FileTypeEnum.TRANSFORMATION.getValue().equals(file.getType())) {
      activeParquetTable =
          org.apache.spark.sql.datasource.DataSourceUtils.makeFullTableName(
              file.getFilename(), databaseName);
    } else {
      activeParquetTable =
          org.apache.spark.sql.datasource.DataSourceUtils.makeFullTableName(
              org.apache.spark.sql.datasource.DataSourceUtils.makeFileTableName(
                  ChineseUtils.transformChineseToPinyinIfNeed(file.getFilename()),
                  file.getId().toString()),
              databaseName);
    }
    return activeParquetTable;
  }

  private void updateFileFieldBySchema(
      StructType schema,
      Integer fileId,
      Integer tenantId,
      Map<String, FileFieldPO> oldFileFieldMap) {
    Iterator<StructField> iterator = schema.iterator();
    List<StructFieldInfo> tableInfoAttrListByPoolIds = new ArrayList<>();
    Set<FileFieldPO> update = new HashSet<>();

    while (iterator.hasNext()) {
      org.apache.spark.sql.types.StructField structFile = iterator.next();
      Integer columnType =
          DataTypeEnum.transformationDataTypeEnumByName(structFile.dataType().typeName())
              .getValue();
      if (oldFileFieldMap.containsKey(structFile.name())) {
        FileFieldPO fileFieldPO = oldFileFieldMap.remove(structFile.name());
        if (!Objects.equals(columnType, fileFieldPO.getFieldType())) {
          fileFieldPO.setFieldType(columnType);
          update.add(fileFieldPO);
        }
      } else {
        StructFieldInfo structField = new StructFieldInfo();
        tableInfoAttrListByPoolIds.add(structField);
        structField.setColumnType(columnType);
        structField.setColumnName(structFile.name());
        structField.setExpressionLhs(structFile.sql());
      }
    }

    if (CollectionUtils.isNotEmpty(update)) {
      fileFieldService.updateBatchById(update);
    }

    if (CollectionUtils.isNotEmpty(tableInfoAttrListByPoolIds)) {
      List<FileFieldPO> fileFieldPOs = new ArrayList<>();
      for (StructFieldInfo structField : tableInfoAttrListByPoolIds) {
        FileFieldPO fileFieldPO = new FileFieldPO();
        fileFieldPOs.add(fileFieldPO);
        fileFieldPO.setFileId(fileId);
        fileFieldPO.setField(structField.getColumnName());
        fileFieldPO.setFieldOrigin(structField.getColumnName());
        fileFieldPO.setFieldType(structField.getColumnType());
        fileFieldPO.setDateFormat("");
        fileFieldPO.setHasPrikey(0);
        fileFieldPO.setTenantId(tenantId);
        fileFieldPO.setHasUpdateField(0);
      }
      fileFieldService.saveBatch(fileFieldPOs);
    }
    if (CollectionUtils.isNotEmpty(oldFileFieldMap.values())) {
      fileFieldService.removeByIds(
          oldFileFieldMap.values().stream().map(FileFieldPO::getId).collect(Collectors.toList()));
    }
  }
}
