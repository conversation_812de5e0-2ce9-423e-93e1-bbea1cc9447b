package com.prx.commons.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dto.conformance.BpmnParam;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.FileUtil;
import com.sp.proxverse.common.util.FileUtils;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BpmnUtilsPy {

  // TODO feng.pan fix path
  private static final String PYTHON_SOURCE_PATH = "/tmp/sp/file/python/source/";
  private static final String BPMN_TEMP_PATH = "/tmp/sp/file/bpmn/";
  private static String PYTHON_CMD = "python3";

  public static String convertVariantsToBpmn(List<String> variantList) {
    try {
      File file = FileUtil.createFile(PYTHON_SOURCE_PATH, "variants.csv");
      try (BufferedWriter out = new BufferedWriter(new FileWriter(file))) {
        out.write("case:concept:name,concept:name,time:timestamp\n");
        int index = 1;
        Date now = DateTimeUtil.getPreDayDate(new Date(), 10);
        for (String variant : variantList) {
          String[] eventArray = variant.split(",");
          int next = 1;
          for (String event : eventArray) {
            out.write(index + ",");
            out.write(event + ",");
            out.write(DateTimeUtil.date2String(DateTimeUtil.getNextHourDate(now, next)));
            out.write("\n");
            next++;
          }
          index++;
        }
      }

      String filePath = PYTHON_SOURCE_PATH + "variants.csv";
      String pythonPath = FileUtils.getTempPath() + "pm4py/bpmn_build.py";
      String uuid = UUID.randomUUID().toString();
      uuid = PYTHON_SOURCE_PATH + uuid + ".bpmn";

      ProcessBuilder pb = new ProcessBuilder(PYTHON_CMD, pythonPath, filePath, uuid);
      log.info("py command => {}", pb.command().toString());
      Process process = pb.start();
      process.waitFor();

      BufferedReader errorReader =
          new BufferedReader(new InputStreamReader(process.getErrorStream()));
      String errorLine;
      while ((errorLine = errorReader.readLine()) != null) {
        log.error(" run bpmn_build.py error. error message:{}", errorLine);
      }

      return uuid;
    } catch (Exception e) {
      log.error("convertVariantsToBpmn error => ", e);
    }
    return null;
  }

  public static BpmnParam getBpmnToPetriNetParams(String bpmnCode) throws Exception {
    String fileName = UUID.randomUUID().toString() + ".bpmn";
    String filePath = BPMN_TEMP_PATH + fileName;
    try {
      File file = FileUtil.createFile(BPMN_TEMP_PATH, fileName);
      FileWriter fw = new FileWriter(file);
      BufferedWriter bw = new BufferedWriter(fw);
      bw.write(bpmnCode);
      bw.close();
      fw.close();

      // TODO feng.pan fix path
      String pythonPath = FileUtils.getTempPath() + "pm4py/bpmn_calc.py";

      ProcessBuilder pb = new ProcessBuilder(PYTHON_CMD, pythonPath, filePath);
      Process process = pb.start();

      // Concurrently read the error stream
      Thread errorThread =
          new Thread(
              () -> {
                try (BufferedReader errorReader =
                    new BufferedReader(
                        new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                  String errorLine;
                  while ((errorLine = errorReader.readLine()) != null) {
                    log.error("Python error stream: {}", errorLine);
                  }
                } catch (IOException e) {
                  log.error("Error reading the error stream", e);
                }
              });
      errorThread.start();

      // Read the input stream
      StringBuilder bpmnParam = new StringBuilder();

      processBpmn(process, bpmnParam);

      // Wait for the error stream thread to finish
      errorThread.join();
      // Check the exit value
      int exitValue = process.waitFor();
      if (exitValue != 0) {
        log.error("Python script exited with error code: {}", exitValue);
      }
      log.info("bpmn_param =>{}", bpmnParam);
      BpmnParam param = JSON.toJavaObject(JSON.parseObject(bpmnParam.toString()), BpmnParam.class);
      file.delete();
      if (param.getTransitions().stream().allMatch(f -> Objects.equals(f.get(1), "null"))) {
        throw new BizException(5000, "not build bpmn node");
      }

      List<List<String>> edges = param.getEdges();

      boolean source = edges.stream().anyMatch(f -> Objects.equals(f.get(0), "source"));
      if (!source) {
        // 说明没有开始节点，需要手动插入source节点
        for (List<String> edge : edges) {
          boolean findSource = edges.stream().anyMatch(f -> Objects.equals(edge.get(0), f.get(1)));
          if (!findSource) {
            edges.add(Lists.newArrayList("source", edge.get(0)));
            break;
          }
        }
      }

      boolean sink = edges.stream().anyMatch(f -> Objects.equals(f.get(1), "sink"));
      if (!sink) {
        // 说明没有结束节点，需要手动插入sink节点
        for (List<String> edge : edges) {
          boolean findSink = edges.stream().anyMatch(f -> Objects.equals(edge.get(1), f.get(0)));
          if (!findSink) {
            edges.add(Lists.newArrayList(edge.get(1), "sink"));
            break;
          }
        }
      }

      return param;
    } catch (Exception e) {
      log.error("getBpmnToPetriNetParams error =>", e);
    }
    throw new BizException(5000, "execute bpmn error");
  }

  private static void processBpmn(Process process, StringBuilder bpmnParam) {
    try (BufferedReader inputReader =
        new BufferedReader(
            new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
      String inputLine;
      while ((inputLine = inputReader.readLine()) != null) {
        bpmnParam.append(inputLine);
      }
    } catch (IOException e) {
      log.error("Error reading the input stream", e);
    }
  }
}
