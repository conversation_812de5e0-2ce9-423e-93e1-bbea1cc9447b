package com.prx.dto.model;

import com.prx.service.model.DictEncodings;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.util.DataSourceUtils;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.sql.pql.dict.EncodedColumnDictIndexBuilder;

/**
 * <AUTHOR>
 * @date 2024/1/31 13:56
 */
@Data
@Builder
public class DataModelInfo {

  public int newVersionNumber;

  public int poolId;

  public int modelId;

  /** Activity */
  private Integer bucketNum;

  String bucketGroupDesc;

  // VARIANT_TABLE
  DataModelFilePO variantTable;

  // VIRTUAL_CASE
  DataModelFilePO virtualCase;

  // ACTIVE
  DataModelFilePO active;

  // CASE
  DataModelFilePO caseTable;

  // SECOND
  List<DataModelFilePO> secondTables;

  DataModelPO model;

  public List<DataModelFilePO> getSourceTables() {
    List<DataModelFilePO> sourceTables = new ArrayList<>();

    if (CollectionUtils.isNotEmpty(secondTables)) {
      sourceTables.addAll(secondTables);
    }

    if (active != null) {
      sourceTables.add(active);
    }

    if (caseTable != null) {
      sourceTables.add(caseTable);
    }
    return sourceTables;
  }

  public String getDictTableName() {
    return EncodedColumnDictIndexBuilder.sparkTableName(
        modelId, getDataBaseName(), newVersionNumber);
  }

  public String getDictTableName(int versionNumber) {
    return EncodedColumnDictIndexBuilder.sparkTableName(modelId, getDataBaseName(), versionNumber);
  }

  public String getDataBaseName() {
    return DataSourceUtils.makeDatabaseName(poolId);
  }

  public Set<String> getCleanupSparkTableNameByVersion(int versionNumber) {
    Set<String> tableNames = new HashSet<>();
    tableNames.add(getDictTableName(versionNumber));
    if (variantTable != null) {
      tableNames.add(variantTable.getSparkTableName(versionNumber));
    }

    if (virtualCase != null) {
      String sparkTableName = virtualCase.getSparkTableName(versionNumber);
      tableNames.add(sparkTableName);
      tableNames.add(DictEncodings.dictTableNameByFullTableName(sparkTableName));
    }

    if (active != null) {
      String sparkTableName = active.getSparkTableName(versionNumber);
      tableNames.add(sparkTableName);
      tableNames.add(DictEncodings.dictTableNameByFullTableName(sparkTableName));
    }

    if (caseTable != null) {
      String sparkTableName = caseTable.getSparkTableName(versionNumber);
      tableNames.add(sparkTableName);
      tableNames.add(DictEncodings.dictTableNameByFullTableName(sparkTableName));
    }

    if (secondTables != null) {
      for (DataModelFilePO dataModelFile : secondTables) {
        String sparkTableName = dataModelFile.getSparkTableName(versionNumber);
        tableNames.add(sparkTableName);
        tableNames.add(DictEncodings.dictTableNameByFullTableName(sparkTableName));
      }
    }
    return tableNames;
  }
}
