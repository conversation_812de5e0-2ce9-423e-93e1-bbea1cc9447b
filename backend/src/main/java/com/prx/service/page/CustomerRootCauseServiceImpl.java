package com.prx.service.page;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.prx.service.page.entity.CustomerRootCauseDto;
import com.prx.service.page.entity.CustomerRootCauseRequest;
import com.sp.proxverse.common.mapper.CustomerRootCauseMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.CustomerRootCausePo;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/8 15:43
 */
@Service
public class CustomerRootCauseServiceImpl
    extends ServiceImpl<CustomerRootCauseMapper, CustomerRootCausePo>
    implements ICustomerRootCauseService {

  @Autowired CustomerRootCauseMapper customerRootCauseMapper;

  @Override
  public Integer saveCustomerRootCause(CustomerRootCauseRequest request) {
    CustomerRootCausePo customerRootCausePo = new CustomerRootCausePo();
    customerRootCausePo.setTopicSheetId(request.getTopicSheetId());
    customerRootCausePo.setDeleted(DeletedEnum.NO_DELETED.getValue());
    customerRootCausePo.setCreateTime(LocalDateTime.now());
    customerRootCausePo.setExpressionName(request.getExpressionName());
    customerRootCausePo.setExpression(request.getExpression());
    customerRootCauseMapper.insert(customerRootCausePo);
    return customerRootCausePo.getId();
  }

  @Override
  public Boolean updateCustomerRootCause(CustomerRootCauseRequest request) {
    return this.update(
        new LambdaUpdateWrapper<CustomerRootCausePo>()
            .eq(CustomerRootCausePo::getId, request.getId())
            .set(
                request.getExpressionName() != null,
                CustomerRootCausePo::getExpressionName,
                request.getExpressionName())
            .set(
                request.getExpression() != null,
                CustomerRootCausePo::getExpression,
                request.getExpression()));
  }

  @Override
  public Boolean deleteCustomerRootCause(Integer Id) {
    return this.update(
        new LambdaUpdateWrapper<CustomerRootCausePo>()
            .eq(CustomerRootCausePo::getId, Id)
            .set(CustomerRootCausePo::getDeleted, DeletedEnum.HAS_DELETED.getValue()));
  }

  @Override
  public List<CustomerRootCauseDto> listCustomerRootCause(Integer topicSheetId) {

    LambdaQueryWrapper<CustomerRootCausePo> queryWrapper =
        new LambdaQueryWrapper<CustomerRootCausePo>()
            .eq(topicSheetId != null, CustomerRootCausePo::getTopicSheetId, topicSheetId)
            .eq(CustomerRootCausePo::getDeleted, DeletedEnum.NO_DELETED.getValue());

    List<CustomerRootCausePo> list = this.list(queryWrapper);
    return list.stream().map(this::convert).collect(Collectors.toList());
  }

  @Override
  public CustomerRootCauseDto getCustomerRootCause(Integer Id) {
    CustomerRootCausePo customerRootCausePo = this.getById(Id);
    CustomerRootCauseDto rootCauseDto = convert(customerRootCausePo);
    return rootCauseDto;
  }

  private CustomerRootCauseDto convert(CustomerRootCausePo rootCausePo) {
    if (Objects.isNull(rootCausePo)) {
      return new CustomerRootCauseDto();
    }

    CustomerRootCauseDto dto = new CustomerRootCauseDto();
    dto.setId(rootCausePo.getId());
    dto.setTopicSheetId(rootCausePo.getTopicSheetId());
    dto.setCreateTime(rootCausePo.getCreateTime());
    dto.setUpdateTime(rootCausePo.getUpdateTime());
    dto.setExpressionName(rootCausePo.getExpressionName());
    dto.setExpression(rootCausePo.getExpression());
    return dto;
  }
}
