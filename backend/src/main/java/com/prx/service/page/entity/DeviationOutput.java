package com.prx.service.page.entity;

import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.vo.TopicDeviationOutputVO;
import java.util.Collections;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:56
 */
@Getter
@Setter
@Builder
public class DeviationOutput extends PageResp {
  @Tolerate
  public DeviationOutput() {
    // comment empty
  }

  private List<TopicDeviationOutputVO> list;

  public static DeviationOutput success(List<TopicDeviationOutputVO> list) {
    DeviationOutput rtn = new DeviationOutput();
    rtn.setCode(2000);
    rtn.setList(list);
    return rtn;
  }

  public static DeviationOutput none() {
    DeviationOutput rtn = new DeviationOutput();
    rtn.setCode(2000);
    rtn.setList(Collections.emptyList());
    return rtn;
  }
}
