package com.prx.service.page.entity;

import com.sp.proxverse.common.model.dict.RootCauseAnalysisEnum;
import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/** <AUTHOR> */
@Getter
@Setter
@Builder
public class RootCauseOfDeviation extends PageRequest {

  @Tolerate
  public RootCauseOfDeviation() {
    // comment empty
  }

  private Integer topicId;

  private Integer topicSheetId;

  private Integer sortKpiId;

  private Integer kpiId;

  /** @see RootCauseAnalysisEnum */
  private Integer rootCauseAnalysisType;

  private Integer rootCauseAnalysisId;

  @ApiModelProperty(value = "排序类型：1：asc，2：desc ")
  private Integer sortType;

  private String startTime;

  private String endTime;

  /** 是否开启忽略小案例事件 */
  private Double filterThreshold;

  private Boolean filterThresholdEnable;
}
