package com.prx.service.page.entity;

import com.sp.proxverse.common.model.base.PageResp;
import java.util.Collections;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:56
 */
@Getter
@Setter
@Builder
public class JdbcTableOutput extends PageResp {
  @Tolerate
  public JdbcTableOutput() {
    // comment empty
  }

  private List<String> list;

  public static JdbcTableOutput success(List<String> list) {
    JdbcTableOutput rtn = new JdbcTableOutput();
    rtn.setCode(2000);
    rtn.setList(list);
    return rtn;
  }

  public static JdbcTableOutput none() {
    JdbcTableOutput rtn = new JdbcTableOutput();
    rtn.setCode(2000);
    rtn.setList(Collections.emptyList());
    return rtn;
  }
}
