package com.prx.service.page.entity;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:56
 */
@Getter
@Setter
@Builder
public class JdbcTable extends PageRequest {

  @Tolerate
  public JdbcTable() {
    // comment empty
  }

  @ApiModelProperty(value = "数据链接ID", required = true)
  @NotNull(message = "{400006}")
  private Integer dataConnectorId;

  private String search;
}
