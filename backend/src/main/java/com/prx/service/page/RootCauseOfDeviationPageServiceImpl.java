package com.prx.service.page;

import com.google.common.collect.Lists;
import com.prx.commons.utils.ThreadPoolDefine;
import com.prx.service.DimensionQuery;
import com.prx.service.model.ModelDescFactory;
import com.prx.service.page.entity.CustomerRootCauseDto;
import com.prx.service.page.entity.DeviationOutput;
import com.prx.service.page.entity.RootCauseOfDeviation;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.dict.*;
import com.sp.proxverse.common.model.dict.kpi.VariableKpiRuleEnum;
import com.sp.proxverse.common.model.dto.*;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.oauth2.AsyncLoginInfo;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.RelatedOutputVO;
import com.sp.proxverse.common.model.vo.TopicDeviationOutputVO;
import com.sp.proxverse.common.model.vo.request.CalculateRootCauseRequest;
import com.sp.proxverse.common.model.vo.request.FieldSearchRequest;
import com.sp.proxverse.common.model.vo.request.KpiVariableSortDTO;
import com.sp.proxverse.common.model.vo.request.ParseKpiDTO;
import com.sp.proxverse.common.model.vo.request.VariableSaveSubRequest;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.KpiService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetKpiService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetService;
import com.sp.proxverse.interfaces.service.biz.CommonBizService;
import com.sp.proxverse.interfaces.service.data.DataAPIService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.spark.sql.AnalysisException;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/06/24 11:06 desc: 计算事件在各个kpi表达式里的结果值，以及结果值的up and down value，以及案例数占比、排序
 */
@Slf4j
@Service
public class RootCauseOfDeviationPageServiceImpl
    implements PageService<RootCauseOfDeviation, DeviationOutput> {

  @Autowired private TopicSheetKpiService topicSheetKpiService;
  @Autowired private TopicSheetService topicSheetService;
  @Autowired private PQLService pqlService;
  @Autowired private ModelDescFactory modelDescFactory;
  @Autowired private KpiService kpiService;
  @Autowired private DataAPIService dataApiService;

  @Autowired private CommonBizService commonBizService;

  private final ThreadLocal<List<String>> currentResultList = new ThreadLocal<>();

  private Integer topicSheetId;

  private Integer topicId;

  private Integer sortKpiId;

  private RootCauseAnalysisEnum analysisEnum;

  private final ThreadLocal<List<String>> pqlFilterList = new ThreadLocal<>();

  private final ThreadLocal<List<KpiPO>> KpiPoList = new ThreadLocal<>();

  private Integer totalCountNum;

  /**
   * example: source(pool_5.`t_4_prod_temp_31_bucket_v_3`.`eventName`) || "," ||
   * target(pool_5.`t_4_prod_temp_31_bucket_v_3`.`eventName`) in ("技术审核,PMC审核")
   */
  private final String LINE_ROOT_CAUSE_TEMPLATE =
      "SOURCE_EXPRESSION || ',' || TARGET_EXPRESSION in  (SOURCE_TARGET_VALUE)";

  private static final Integer CASE_COUNT_NUM_KPI_ID = Integer.MAX_VALUE;

  private static final String CASE_COUNT_NUM = "caseCountNum";

  private static final String CASE_COUNT_NUM_EXPRESSION =
      "count(distinct ENCODED_CASE_CASE_ID_COLUMN())";
  @Autowired private ICustomerRootCauseService customerRootCauseService;
  private static final String VALUE_STR = "value";
  /**
   * @param rootCauseOfDeviation
   * @return
   */
  @Override
  @SneakyThrows
  public DeviationOutput page(RootCauseOfDeviation rootCauseOfDeviation) {

    topicSheetId = rootCauseOfDeviation.getTopicSheetId();
    topicId = rootCauseOfDeviation.getTopicId();
    sortKpiId = rootCauseOfDeviation.getSortKpiId();
    pqlFilterList.set(new ArrayList<>());
    KpiPoList.set(new ArrayList<>());
    analysisEnum = null;
    currentResultList.set(new ArrayList<>());
    totalCountNum = null;

    List<SheetKpiDTO> topicSheetKpiList = getTopicSheetKpiList(rootCauseOfDeviation);
    if (CollectionUtils.isEmpty(topicSheetKpiList)) {
      return DeviationOutput.none();
    }

    DataModelFileDTO dataModelFileDto = topicSheetService.getTopicSheetFileBySheetId(topicSheetId);
    if (Objects.isNull(dataModelFileDto)) {
      return DeviationOutput.none();
    }
    ModelDesc modelDesc = getModelDesc(topicSheetId);
    if (Objects.isNull(modelDesc)) {
      return DeviationOutput.none();
    }

    checkSortKpiIsExistThenNewKpi(topicSheetKpiList);
    /** kpi 指标可以有若干个，但是 sortKpi 有且只有一个,先处理sortKpi ,然后根据sortKpi 的 resultSet 再求别的 kpi */
    SheetKpiDTO sortKpi =
        topicSheetKpiList.stream()
            .filter(c -> Objects.equals(sortKpiId, c.getId()))
            .findFirst()
            .orElse(null);
    if (Objects.isNull(sortKpi)) {
      log.info("not config sortKpi");
      return DeviationOutput.none();
    }
    Map<Integer, List<VariableKpiDTO>> kpiResultMap =
        calculateKpi(modelDesc, rootCauseOfDeviation, topicSheetKpiList, sortKpi);
    if (CollectionUtils.isEmpty(currentResultList.get())) {
      DeviationOutput.success(this.sortResult(new ArrayList<>()));
    }

    Map<String, Integer> eventCountMap = calculateEventCountMap(rootCauseOfDeviation);

    List<TopicDeviationOutputVO> list =
        buildDeviationOutputList(topicSheetKpiList, kpiResultMap, eventCountMap);
    return DeviationOutput.success(this.sortResult(list));
  }

  private Map<Integer, List<VariableKpiDTO>> calculateKpi(
      ModelDesc modelDesc,
      RootCauseOfDeviation rootCauseOfDeviation,
      List<SheetKpiDTO> topicSheetKpiList,
      SheetKpiDTO sortKpi) {

    Integer sortKpiIdLocal = sortKpi.getId();
    Integer topicIdLocal = rootCauseOfDeviation.getTopicId();
    Integer topicSheetIdLocal = rootCauseOfDeviation.getTopicSheetId();

    Map<Integer, List<VariableKpiDTO>> kpiResultMap =
        processSortKpi(modelDesc, rootCauseOfDeviation, sortKpi);
    if (MapUtils.isEmpty(kpiResultMap)) {
      return new HashMap<>();
    }

    List<VariableKpiDTO> sortKpiVariableKpiDTOS = kpiResultMap.get(sortKpiIdLocal);
    currentResultList.set(
        sortKpiVariableKpiDTOS.stream()
            .map(VariableKpiDTO::getVariable)
            .collect(Collectors.toList()));

    if (Objects.nonNull(rootCauseOfDeviation.getRootCauseAnalysisId())) {
      return kpiResultMap;
    }
    processNonSortKpi(
        rootCauseOfDeviation,
        topicSheetKpiList,
        topicIdLocal,
        topicSheetIdLocal,
        sortKpiIdLocal,
        modelDesc.activeTableEventColumn(),
        kpiResultMap);
    return kpiResultMap;
  }

  private List<TopicDeviationOutputVO> sortResult(List<TopicDeviationOutputVO> list) {
    // 保证排序不被map打乱
    List<String> eventNameList =
        list.stream()
            .filter(f -> Objects.equals(sortKpiId, f.getDeviationKipDTOList().get(0).getKpiId()))
            .map(TopicDeviationOutputVO::getEventName)
            .distinct()
            .collect(Collectors.toList());
    Map<String, List<TopicDeviationOutputVO>> eventGroupMap =
        list.stream().collect(Collectors.groupingBy(TopicDeviationOutputVO::getEventName));
    List<TopicDeviationOutputVO> resultList = new ArrayList<>();
    for (String eventName : eventNameList) {
      List<TopicDeviationOutputVO> deviationOutputVOList = eventGroupMap.get(eventName);
      List<DeviationKipDTO> collect =
          deviationOutputVOList.stream()
              .flatMap(m -> m.getDeviationKipDTOList().stream())
              .filter(c -> !CASE_COUNT_NUM_KPI_ID.equals(c.getKpiId()))
              .distinct()
              .collect(Collectors.toList());
      TopicDeviationOutputVO outputVO = deviationOutputVOList.get(0);
      TopicDeviationOutputVO deviationOutputVO =
          TopicDeviationOutputVO.builder()
              .eventName(outputVO.getEventName())
              .rate(outputVO.getRate())
              .topicId(outputVO.getTopicId())
              .value(outputVO.getValue())
              .filterExpression(outputVO.getFilterExpression())
              .deviationKipDTOList(collect)
              .build();
      resultList.add(deviationOutputVO);
    }
    return resultList;
  }

  @Trace(operationName = "buildDeviationOutputList")
  private List<TopicDeviationOutputVO> buildDeviationOutputList(
      List<SheetKpiDTO> topicSheetKpiList,
      Map<Integer, List<VariableKpiDTO>> kpiResultMap,
      Map<String, Integer> eventCountMap) {
    List<TopicDeviationOutputVO> list = new ArrayList<>();
    BigDecimal per = BigDecimal.valueOf(100);
    int total =
        totalCountNum == null ? this.queryTotalCaseNum(topicId, topicSheetId) : totalCountNum;

    for (SheetKpiDTO kpi : topicSheetKpiList) {
      String name = dataApiService.formatRefer(topicId, kpi.getName());
      List<VariableKpiDTO> kpiResultDTOList = kpiResultMap.get(kpi.getId());
      if (Objects.isNull(kpiResultDTOList)) {
        kpiResultDTOList = new ArrayList<>();
      }
      for (VariableKpiDTO variableKpiDTO : kpiResultDTOList) {
        String variable = variableKpiDTO.getVariable();
        if (StringUtils.isBlank(variable)) {
          // eventName 有可能为 null
          continue;
        }

        Integer count = eventCountMap.get(variable);
        if (Objects.isNull(count)) {
          continue;
        }
        BigDecimal rate =
            BigDecimal.valueOf(count).divide(BigDecimal.valueOf(total), 3, RoundingMode.HALF_UP);
        TopicDeviationOutputVO outputVO = new TopicDeviationOutputVO();
        outputVO.setTopicId(topicId);
        outputVO.setEventName(variable);
        outputVO.setValue(count);
        outputVO.setRate(rate.multiply(per));

        String filterExpression = "";
        if (analysisEnum != null) {
          filterExpression = analysisEnum.generateFilterExpression(variable);
        }

        outputVO.setFilterExpression(filterExpression);
        List<DeviationKipDTO> deviationKipDTOList = new ArrayList<>();
        DeviationKipDTO deviationKipDTO = new DeviationKipDTO();
        deviationKipDTO.setKpiId(kpi.getId());
        deviationKipDTO.setKpiName(name);
        deviationKipDTO.setEventName(variable);
        deviationKipDTO.setFormat(kpi.getFormat());
        deviationKipDTO.setFormatting(kpi.getFormatting());
        deviationKipDTO.setColumnType(kpi.getColumnType());
        deviationKipDTO.setUnit(kpi.getUnit());
        deviationKipDTOList.add(deviationKipDTO);
        outputVO.setDeviationKipDTOList(deviationKipDTOList);
        list.add(outputVO);
      }
    }
    return list;
  }

  @Trace(operationName = "calculateEventCountMap")
  private Map<String, Integer> calculateEventCountMap(RootCauseOfDeviation rootCauseOfDeviation) {

    List<String> filterList = pqlFilterList.get();
    List<KpiPO> dimensionList = KpiPoList.get();
    KpiPO count = KpiPO.builder().expression(CASE_COUNT_NUM_EXPRESSION).name("count").build();
    dimensionList.add(count);

    ParseKpiDTO parseKpiBuild =
        ParseKpiDTO.builder()
            .topicId(topicId)
            .sheetId(topicSheetId)
            .dimensionColumnList(dimensionList)
            .addFilterList(filterList)
            .type(PqlResultTypeEnum.BY_ROW.getValue())
            .sortList(
                Lists.newArrayList(
                    KpiVariableSortDTO.builder().sortName("count").sortType(2).build()))
            .rule(VariableKpiRuleEnum.LIMIT.getValue())
            .build();

    List<EventCountDTO> eventCountList = null;
    try {
      Result result = pqlService.calcExpression(parseKpiBuild);
      eventCountList = result.getList(EventCountDTO.class);
    } catch (Exception e) {
      log.error("execute pql error. parseKpiBuild:{}", parseKpiBuild, e);
      throw new BizException(5000, e.getMessage());
    }
    Map<String, Integer> eventCountMap = new HashMap<>();
    if (Objects.equals(
        RootCauseAnalysisEnum.LINE_ROOT_CAUSE.getCode(),
        rootCauseOfDeviation.getRootCauseAnalysisType())) {
      for (EventCountDTO dto : eventCountList) {
        String combinedVariable = dto.getSource() + "," + dto.getTarget();
        eventCountMap.put(combinedVariable, dto.getCount());
      }
    } else {
      eventCountMap =
          eventCountList.stream()
              .collect(
                  Collectors.toMap(
                      EventCountDTO::getVariable, EventCountDTO::getCount, (k1, k2) -> k1));
    }
    return eventCountMap;
  }

  @SneakyThrows
  private Map<Integer, List<VariableKpiDTO>> processSortKpi(
      ModelDesc modelDesc, RootCauseOfDeviation rootCauseOfDeviation, SheetKpiDTO sortKpi) {
    Map<Integer, List<VariableKpiDTO>> kpiResultMap = new HashMap<>();

    ParseKpiDTO parseKpiDTO =
        generateSortKpiPQL(sortKpi.getExpression(), modelDesc, rootCauseOfDeviation);
    Result result = pqlService.calcExpression(parseKpiDTO);

    List<VariableKpiDTO> variableKpiDTOList = result.getList(VariableKpiDTO.class);
    if (CollectionUtils.isEmpty(variableKpiDTOList)) {
      return Collections.emptyMap();
    }
    // filter source or variable null value
    variableKpiDTOList =
        variableKpiDTOList.stream()
            .filter(
                c -> {
                  if (c.getVariable() != null) {
                    return true;
                  }
                  return c.getSource() != null;
                })
            .collect(Collectors.toList());

    Map<String, KpiPO> nameDimensionColumnMap =
        parseKpiDTO.getDimensionColumnList().stream()
            .collect(Collectors.toMap(KpiPO::getName, Function.identity()));

    // rework 不可以增加 filter ，否则会影响结果集正确性
    if (RootCauseAnalysisEnum.REWORK_ROOT_CAUSE
        .getCode()
        .equals(rootCauseOfDeviation.getRootCauseAnalysisType())) {
      kpiResultMap.put(sortKpi.getId(), variableKpiDTOList);
      return kpiResultMap;
    }

    if (RootCauseAnalysisEnum.LINE_ROOT_CAUSE
        .getCode()
        .equals(rootCauseOfDeviation.getRootCauseAnalysisType())) {
      processLineRootCause(variableKpiDTOList, nameDimensionColumnMap);
    } else {
      // 自定义根因分析不需要增加此 filter
      if (Objects.isNull(rootCauseOfDeviation.getRootCauseAnalysisId())) {
        processOtherRootCause(variableKpiDTOList, nameDimensionColumnMap);
      }
    }
    kpiResultMap.put(sortKpi.getId(), variableKpiDTOList);
    return kpiResultMap;
  }

  private void processLineRootCause(
      List<VariableKpiDTO> variableKpiDTOList, Map<String, KpiPO> nameDimensionColumnMap) {
    String sourceKpiExpression = nameDimensionColumnMap.get("source").getExpression();
    String targetKpiExpression = nameDimensionColumnMap.get("target").getExpression();

    String sourceTargetValue =
        variableKpiDTOList.stream()
            .map(
                dto -> {
                  String sourceTarget = dto.getSource() + "," + dto.getTarget();
                  dto.setVariable(sourceTarget);
                  return "'" + sourceTarget + "'";
                })
            .collect(Collectors.joining(","));

    String formatTemplate =
        LINE_ROOT_CAUSE_TEMPLATE
            .replaceAll("SOURCE_EXPRESSION", sourceKpiExpression)
            .replaceAll("TARGET_EXPRESSION", targetKpiExpression)
            .replaceAll("SOURCE_TARGET_VALUE", sourceTargetValue);

    this.pqlFilterList.set(Collections.singletonList(formatTemplate));
  }

  private void processOtherRootCause(
      List<VariableKpiDTO> variableKpiDTOList, Map<String, KpiPO> nameDimensionColumnMap) {
    String variableExpression = nameDimensionColumnMap.get("variable").getExpression();
    List<String> eventList =
        variableKpiDTOList.stream().map(VariableKpiDTO::getVariable).collect(Collectors.toList());
    String eventFilter = buildEventFilter(eventList, variableExpression);
    this.pqlFilterList.set(Collections.singletonList(eventFilter));
  }

  private ParseKpiDTO generateSortKpiPQL(
      String expression, ModelDesc modelDesc, RootCauseOfDeviation rootCauseOfDeviation) {
    Integer topicIdLocal = rootCauseOfDeviation.getTopicId();
    Integer sheetId = rootCauseOfDeviation.getTopicSheetId();
    Integer pageNum = rootCauseOfDeviation.getPageNum();
    Integer pageSize = rootCauseOfDeviation.getPageSize();
    Integer rootCauseAnalysisType = rootCauseOfDeviation.getRootCauseAnalysisType();
    Integer rootCauseAnalysisId = rootCauseOfDeviation.getRootCauseAnalysisId();

    List<KpiPO> dimension =
        createDimensionList(
            rootCauseAnalysisType,
            rootCauseAnalysisId,
            modelDesc.activeTableEventColumn(),
            expression);
    List<FieldSearchRequest> fieldSearchRequests =
        getFieldSearchRequests(dimension, rootCauseOfDeviation);

    ParseKpiDTO parseKpiBuild =
        ParseKpiDTO.builder()
            .topicId(topicIdLocal)
            .sheetId(sheetId)
            .dimensionColumnList(dimension)
            .searchList(fieldSearchRequests)
            .type(PqlResultTypeEnum.BY_ROW.getValue())
            .rule(VariableKpiRuleEnum.SCROLL.getValue())
            .pageNum(pageNum)
            .pageSize(pageSize)
            .build();

    parseKpiBuild.setSortList(createSortList(rootCauseOfDeviation));
    return parseKpiBuild;
  }

  /**
   * @param dimension
   * @param rootCauseOfDeviation
   * @return
   */
  private List<FieldSearchRequest> getFieldSearchRequests(
      List<KpiPO> dimension, RootCauseOfDeviation rootCauseOfDeviation) {

    List<FieldSearchRequest> fieldSearchRequests = new ArrayList<>();
    // 开启 忽略小案例事件
    if (Boolean.TRUE.equals(rootCauseOfDeviation.getFilterThresholdEnable())) {
      totalCountNum = this.queryTotalCaseNum(topicId, topicSheetId);
      Double filterThreshold = rootCauseOfDeviation.getFilterThreshold();
      FieldSearchRequest searchRequest =
          FieldSearchRequest.builder()
              .searchKey(CASE_COUNT_NUM)
              .searchValue(String.valueOf(totalCountNum * filterThreshold))
              .operationType(OperationTypeEnum.GT.getValue())
              .build();
      fieldSearchRequests.add(searchRequest);

      KpiPO kpiPO =
          KpiPO.builder().name(CASE_COUNT_NUM).expression(CASE_COUNT_NUM_EXPRESSION).build();
      dimension.add(kpiPO);
      return fieldSearchRequests;
    }
    return fieldSearchRequests;
  }

  private List<KpiPO> createDimensionList(
      Integer rootCauseAnalysisType,
      Integer rootCauseAnalysisId,
      String eventColumn,
      String expression) {

    List<KpiPO> dimension = new ArrayList<>();
    if (Objects.nonNull(rootCauseAnalysisId)) {
      CustomerRootCauseDto customerRootCause =
          customerRootCauseService.getCustomerRootCause(rootCauseAnalysisId);
      String customerExpression = customerRootCause.getExpression();
      dimension.add(createKpiPO(customerExpression, "variable"));
      KpiPoList.set(new ArrayList<>(dimension));
      dimension.add(createKpiPO(CASE_COUNT_NUM_EXPRESSION, VALUE_STR));
      return dimension;
    }

    analysisEnum = RootCauseAnalysisEnum.getByCode(rootCauseAnalysisType);
    if (analysisEnum == null) {
      log.error("not support type, rootCauseAnalysisType:{}", rootCauseAnalysisType);
      throw new BizException(5000, "not support type");
    }

    String template = analysisEnum.getSortPqlTemplate();
    if (StringUtils.isNotBlank(template)) {
      String[] formattedExpressions = formatExpressions(template, eventColumn);
      String[] columns = formatColumns(analysisEnum.getColumnName());
      for (int i = 0; i < formattedExpressions.length; i++) {
        dimension.add(createKpiPO(formattedExpressions[i].trim(), columns[i]));
      }
    }

    KpiPoList.set(new ArrayList<>(dimension));
    dimension.add(createKpiPO(expression, VALUE_STR));
    return dimension;
  }

  private String[] formatExpressions(String template, String eventColumn) {
    if (template.contains(",")) {
      String[] splits = template.split(",");
      String[] results = new String[splits.length];
      for (int i = 0; i < splits.length; i++) {
        String format = String.format(splits[i], eventColumn);
        results[i] = format;
      }
      return results;
    } else {
      return new String[] {String.format(template, eventColumn)};
    }
  }

  private String[] formatColumns(String column) {
    if (column.contains(",")) {
      return column.split(",");
    } else {
      return new String[] {String.format(column)};
    }
  }

  private KpiPO createKpiPO(String expression, String name) {
    return KpiPO.builder().expression(expression).name(name).build();
  }

  private List<KpiVariableSortDTO> createSortList(RootCauseOfDeviation rootCauseOfDeviation) {
    Integer sortType = null;
    if (rootCauseOfDeviation.getSortType() == null) {
      sortType = PqlSortTypeEnum.ASC.getValue();
    } else {
      sortType = rootCauseOfDeviation.getSortType();
    }

    return Lists.newArrayList(
        KpiVariableSortDTO.builder().sortType(sortType).sortName(VALUE_STR).build());
  }

  private void processNonSortKpi(
      RootCauseOfDeviation rootCauseOfDeviation,
      List<SheetKpiDTO> topicSheetKpiList,
      Integer topicId,
      Integer sheetId,
      Integer sortKpiId,
      String eventColumn,
      Map<Integer, List<VariableKpiDTO>> kpiResultMap) {

    // 用户可以设置若干个指标，先求sortKpi 指标，然后根据sortKpi 指标的结果集里求其他指标
    topicSheetKpiList =
        topicSheetKpiList.stream()
            .filter(f -> !Objects.equals(f.getId(), sortKpiId))
            .collect(Collectors.toList());

    for (SheetKpiDTO sheetKpiDTO : topicSheetKpiList) {
      KpiPO kpipo = kpiService.getById(sheetKpiDTO.getId());

      List<KpiPO> dimensionList =
          createDimensionList(
              rootCauseOfDeviation.getRootCauseAnalysisType(),
              rootCauseOfDeviation.getRootCauseAnalysisId(),
              eventColumn,
              kpipo.getExpression());

      List<VariableKpiDTO> variableKpiDTOList =
          executePQL(topicId, sheetId, dimensionList, pqlFilterList.get());
      kpiResultMap.put(sheetKpiDTO.getId(), variableKpiDTOList);
    }
  }

  private List<VariableKpiDTO> executePQL(
      Integer topicId, Integer sheetId, List<KpiPO> dimensionList, List<String> filter) {

    ParseKpiDTO parseKpiBuild =
        ParseKpiDTO.builder()
            .topicId(topicId)
            .sheetId(sheetId)
            .dimensionColumnList(dimensionList)
            .addFilterList(filter)
            .type(PqlResultTypeEnum.BY_ROW.getValue())
            .rule(VariableKpiRuleEnum.LIMIT.getValue())
            .build();

    Result result = pqlService.calcExpression(parseKpiBuild);
    List<VariableKpiDTO> variableKpiDTOList = null;
    try {
      variableKpiDTOList = result.getList(VariableKpiDTO.class);

      // deal with source,target combine variable
      for (VariableKpiDTO dto : variableKpiDTOList) {
        if (dto.getSource() != null) {
          String combinedVariable = dto.getSource() + "," + dto.getTarget();
          dto.setVariable(combinedVariable);
        }
      }

    } catch (Exception e) {
      log.error("execute pql error. parseKpiBuild:{}", parseKpiBuild, e);
      throw new BizException(5000, e.getMessage());
    }
    return variableKpiDTOList;
  }

  private String queryKpi(Integer topicId, Integer sheetId, String expression) {
    String kpiResultMost;
    try {
      String kpiExpression = buildKpiExpressionWithFirst(topicId, sheetId, expression);
      kpiResultMost = pqlService.calcExpression(topicId, sheetId, kpiExpression);
    } catch (Exception e) {
      log.error("getTopicDeviation kpi error ", e);
      kpiResultMost = "--";
    }
    return kpiResultMost;
  }

  private List<SheetKpiDTO> getTopicSheetKpiList(RootCauseOfDeviation rootCauseOfDeviation) {
    Integer kpiId = rootCauseOfDeviation.getKpiId();
    if (Objects.isNull(kpiId)) {
      return topicSheetKpiService.getTopicSheetKpiList(rootCauseOfDeviation.getTopicSheetId());
    } else {
      KpiPO kpi = kpiService.getById(kpiId);
      SheetKpiDTO sheetKpiDTO =
          SheetKpiDTO.builder()
              .id(kpi.getId())
              .name(kpi.getName())
              .unit(kpi.getUnit())
              .expression(kpi.getExpression())
              .type(kpi.getType())
              .build();
      return Lists.newArrayList(sheetKpiDTO);
    }
  }

  private ModelDesc getModelDesc(Integer topicSheetId) {
    DataModelFileDTO dataModelFileDto = topicSheetService.getTopicSheetFileBySheetId(topicSheetId);
    if (Objects.isNull(dataModelFileDto)) {
      return null;
    }
    return modelDescFactory.getOrCreate(dataModelFileDto.getDataModelId());
  }

  private String buildEventFilter(List<String> eventList, String eventColumn) {
    List<String> eventFilter =
        eventList.stream().map(m -> "'" + m + "'").collect(Collectors.toList());
    return eventColumn + " in (" + StringUtils.join(eventFilter, ",") + ")";
  }

  private int queryTotalCaseNum(Integer topicId, Integer sheetId) {
    // 查询出总案件case数量
    String expression = "count(encoded_case_case_id_column())";
    String totalNum = pqlService.calcExpression(topicId, sheetId, expression);
    int total = Integer.parseInt(totalNum);
    total = Objects.equals(total, 0) ? 1 : total;
    return total;
  }

  private String buildKpiExpressionWithFirst(Integer topicId, Integer sheetId, String expression) {
    if (DimensionQuery.checkHasAggregationFunction(topicId, sheetId, expression, pqlService)) {
      return expression;
    } else {
      return "first(" + expression + ")";
    }
  }

  public List<RelatedOutputVO> queryRootCauseCalResult(CalculateRootCauseRequest request) {
    DataModelFileDTO dataModel =
        topicSheetService.getTopicSheetFileBySheetId(request.getTopicSheetId());
    if (Objects.isNull(dataModel)) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_MODEL_NOT_EXIST));
    }
    if (CollectionUtils.isEmpty(request.getVariableList())) {
      return new ArrayList<>();
    }
    String totalCaseNumStr =
        pqlService.calcExpression(
            request.getTopicId(), request.getTopicSheetId(), CASE_COUNT_NUM_EXPRESSION); // 总数
    ModelDesc modelDesc = modelDescFactory.getOrCreate(dataModel.getDataModelId());
    BigDecimal totalCaseNum = new BigDecimal(totalCaseNumStr);
    BigDecimal multi = BigDecimal.valueOf(100);

    List<RelatedOutputVO> list = new ArrayList<>();

    for (VariableSaveSubRequest variableSaveSubRequest : request.getVariableList()) {
      // 此变量决定相关性算法中横轴的数量
      String expression;
      if (variableSaveSubRequest.getRefParam() != null) {
        // 自定义 kpi expression
        expression = variableSaveSubRequest.getRefParam();
      } else {
        expression =
            "`"
                + variableSaveSubRequest.getFileName()
                + "`.`"
                + variableSaveSubRequest.getVariable()
                + "`";
      }

      List<KpiPO> dimensionKpiList = new ArrayList<>();
      dimensionKpiList.add(KpiPO.builder().name("variantName").expression(expression).build());
      dimensionKpiList.add(
          KpiPO.builder().name("num").expression(CASE_COUNT_NUM_EXPRESSION).build());
      ParseKpiDTO parseBuild =
          ParseKpiDTO.builder()
              .topicId(dataModel.getTopicId())
              .sheetId(request.getTopicSheetId())
              .dimensionColumnList(dimensionKpiList)
              .sortList(
                  Lists.newArrayList(
                      KpiVariableSortDTO.builder().sortName("num").sortType(2).build()))
              .type(0)
              .rule(VariableKpiRuleEnum.LIMIT.getValue())
              .limit(50000)
              .build();
      Result result = pqlService.calcExpression(parseBuild);
      List<RelatedDTO> paramList = new ArrayList<>();
      try {
        paramList = result.getList(RelatedDTO.class);
      } catch (Exception e) {
        log.error("getHasOrNoneCase error", e);
      }
      List<String> paramValueList =
          paramList.stream().map(RelatedDTO::getVariantName).collect(Collectors.toList());
      Map<String, Long> paramNumMap =
          paramList.stream()
              .collect(
                  Collectors.toMap(RelatedDTO::getVariantName, RelatedDTO::getNum, (k1, k2) -> k1));
      String expressionVariableNum = "count(distinct " + expression + ")";
      // 查询字段种类值数量，相关性作归一化，相关性值/种类个数，如果num=0，则此变量不作归一化处理
      String num = null;
      try {
        num =
            pqlService.calcExpression(
                dataModel.getTopicId(), request.getTopicSheetId(), expressionVariableNum);
      } catch (Exception e) {
        //  not allowed to use an aggregate function in the argument of another aggregate function
        if (e instanceof AnalysisException) {
          RelatedOutputVO outputVO = new RelatedOutputVO();
          outputVO.setContainRatioDecimal(BigDecimal.valueOf(100));
          outputVO.setNotContainRatioDecimal(BigDecimal.ZERO);
          outputVO.setVariant(variableSaveSubRequest.getVariable());
          outputVO.setRelatedValue(I18nUtil.getMessage(I18nConst.NO_CORRELATION));
          outputVO.setRelatedValueDecimal(BigDecimal.ZERO);
          return Lists.newArrayList(outputVO);
        }
      }
      List<RelatedDTO> hasCase = new ArrayList<>();
      List<RelatedDTO> noneCase = new ArrayList<>();

      AsyncLoginInfo auth = commonBizService.getAuthContext();
      String event = request.getEvent();
      String variant = request.getVariant();
      String line = request.getLine();
      String rework = request.getRework();
      String customerExpression = request.getCustomerExpression();
      // 有的case里，该变量下各数值所占数量
      CompletableFuture<List<RelatedDTO>> hasCaseFeature =
          CompletableFuture.supplyAsync(
              () ->
                  getHasOrNoneCase(
                      modelDesc,
                      dataModel,
                      event,
                      variant,
                      line,
                      rework,
                      customerExpression,
                      topicSheetId,
                      expression,
                      true,
                      auth),
              ThreadPoolDefine.CALCULATE_VARIANT_THREAD_POOL);
      // 再计算没有的case，在paramValue里的count
      CompletableFuture<List<RelatedDTO>> noneCaseFeature =
          CompletableFuture.supplyAsync(
              () ->
                  getHasOrNoneCase(
                      modelDesc,
                      dataModel,
                      event,
                      variant,
                      line,
                      rework,
                      customerExpression,
                      topicSheetId,
                      expression,
                      false,
                      auth),
              ThreadPoolDefine.CALCULATE_VARIANT_THREAD_POOL);
      try {
        hasCase = hasCaseFeature.get(30, TimeUnit.SECONDS);
        noneCase = noneCaseFeature.get(30, TimeUnit.SECONDS);
      } catch (Exception e) {
        log.error("get feature error", e);
      }
      paramValueList =
          paramValueList.stream().filter(Objects::nonNull).collect(Collectors.toList());
      Map<String, RelatedDTO> hasCaseMap =
          hasCase.stream()
              .collect(
                  Collectors.toMap(
                      RelatedDTO::getVariantName, Function.identity(), (k1, k2) -> k1));
      Map<String, RelatedDTO> noneCaseMap =
          noneCase.stream()
              .collect(
                  Collectors.toMap(
                      RelatedDTO::getVariantName, Function.identity(), (k1, k2) -> k1));
      List<RelatedDTO> hasCaseBySort = new ArrayList<>();
      List<RelatedDTO> noneCaseBySort = new ArrayList<>();
      // 为两个横轴集合填充数据，没有的填充0
      for (int i = 0; i < paramValueList.size(); i++) {
        this.buildCaseByParamSort(hasCaseBySort, paramValueList, hasCaseMap, i);
        this.buildCaseByParamSort(noneCaseBySort, paramValueList, noneCaseMap, i);
      }
      Map<Integer, BigDecimal> relatedRate = new HashMap<>();
      Long hasCaseParamTotal =
          hasCaseBySort.stream().map(RelatedDTO::getNum).reduce(Long::sum).orElse(0L);
      Long noneCaseParamTotal =
          noneCaseBySort.stream().map(RelatedDTO::getNum).reduce(Long::sum).orElse(0L);
      Long hasAndNoneParamTotal = hasCaseParamTotal + noneCaseParamTotal;
      BigDecimal hasAndNoneParamTotalDecimal = BigDecimal.valueOf(hasAndNoneParamTotal);
      for (int i = 0; i < hasCaseBySort.size(); i++) {
        for (int j = 0; j < noneCaseBySort.size(); j++) {
          if (i == j) {
            long paramValueSum = hasCaseBySort.get(i).getNum() + noneCaseBySort.get(j).getNum();
            if (hasAndNoneParamTotalDecimal.compareTo(BigDecimal.ZERO) == 0) {
              relatedRate.put(i, BigDecimal.ZERO);
            } else {
              relatedRate.put(
                  i,
                  BigDecimal.valueOf(paramValueSum)
                      .divide(hasAndNoneParamTotalDecimal, 8, RoundingMode.HALF_UP));
            }
          }
        }
      }
      BigDecimal hasCaseParamTotalDecimal = BigDecimal.valueOf(hasCaseParamTotal);
      BigDecimal noneCaseParamTotalDecimal = BigDecimal.valueOf(noneCaseParamTotal);
      // 定义相关系数
      BigDecimal related = BigDecimal.ZERO;
      related = this.calRelated(hasCaseBySort, hasCaseParamTotalDecimal, relatedRate, related);
      related = this.calRelated(noneCaseBySort, noneCaseParamTotalDecimal, relatedRate, related);
      if (!Objects.equals(num, "0")) {
        related = related.divide(new BigDecimal(num), 3, RoundingMode.HALF_UP);
      }
      BigDecimal containRatio =
          this.calculateContainRatio(
              request.getTopicId(),
              request.getTopicSheetId(),
              totalCaseNum,
              request.getEvent(),
              request.getVariant(),
              request.getLine(),
              request.getRework(),
              customerExpression,
              modelDesc);

      List<RelatedDTO> limitRelateList =
          hasCaseBySort.stream()
              .filter(
                  f ->
                      StringUtils.isNotBlank(f.getVariantName())
                          && paramNumMap.get(f.getVariantName()) != 0)
              .peek(
                  m -> {
                    Long withEventCaseNum = m.getNum();
                    m.setNum(paramNumMap.getOrDefault(m.getVariantName(), m.getNum()));
                    if (totalCaseNum.compareTo(BigDecimal.ZERO) == 0) {
                      m.setContainRate(BigDecimal.ZERO);
                    } else {
                      BigDecimal multiply =
                          BigDecimal.valueOf(withEventCaseNum)
                              .divide(BigDecimal.valueOf(m.getNum()), 3, RoundingMode.HALF_UP)
                              .multiply(multi);
                      m.setContainRate(multiply);
                    }
                    m.setSortRatio(m.getContainRate().subtract(containRatio).abs());
                  })
              .sorted(Comparator.comparing(RelatedDTO::getSortRatio).reversed())
              .limit(10)
              .peek(
                  m -> {
                    m.setVariantName(
                        StringUtils.isBlank(m.getVariantName())
                            ? Consts.EMPYT
                            : m.getVariantName());
                    m.setNotContainRate(multi.subtract(m.getContainRate()));
                  })
              .collect(Collectors.toList());
      RelatedOutputVO outputVO = new RelatedOutputVO();
      outputVO.setContainRatioDecimal(containRatio);
      outputVO.setNotContainRatioDecimal(multi.subtract(containRatio));
      outputVO.setVariant(variableSaveSubRequest.getVariable());
      if (related.compareTo(BigDecimal.ZERO) == 0) {
        outputVO.setRelatedValue(I18nUtil.getMessage(I18nConst.NO_CORRELATION));
        outputVO.setRelatedValueDecimal(BigDecimal.ZERO);
      } else {
        outputVO.setRelatedValue(related.toString());
        outputVO.setRelatedValueDecimal(related);
      }
      outputVO.setSubRelatedDTOList(limitRelateList);

      list.add(outputVO);
    }

    return list.stream()
        .sorted(Comparator.comparing(RelatedOutputVO::getRelatedValueDecimal).reversed())
        .collect(Collectors.toList());
  }

  /** Calculate values with and without variants */
  public List<RelatedDTO> getHasOrNoneCase(
      ModelDesc modelDesc,
      DataModelFileDTO dataModelFileDTO,
      String event,
      String variant,
      String line,
      String rework,
      String customerExpression,
      Integer sheetId,
      String expression,
      boolean hasOrNone,
      AsyncLoginInfo auth) {

    commonBizService.invokeAuthContext(auth);
    List<TopicFilterPO> addFilter = new ArrayList<>();
    Optional.ofNullable(
            createTopicFilter(
                event, variant, line, rework, customerExpression, hasOrNone, modelDesc))
        .ifPresent(addFilter::add);
    List<KpiPO> kpiList =
        Arrays.asList(
            KpiPO.builder().name("variantName").expression(expression).build(),
            KpiPO.builder().name("num").expression(CASE_COUNT_NUM_EXPRESSION).build());
    ParseKpiDTO parseKpiDTO = buildParseKpiDTO(dataModelFileDTO, sheetId, kpiList, addFilter);
    Result ret = pqlService.calcExpression(parseKpiDTO);
    return getRelatedDTOList(ret);
  }

  private TopicFilterPO createTopicFilter(
      String event,
      String variant,
      String line,
      String rework,
      String customerExpression,
      boolean hasOrNone,
      ModelDesc modelDesc) {

    Map<String, Function<Boolean, TopicFilterPO>> filterStrategies = new LinkedHashMap<>();

    filterStrategies.put(event, hasOr -> FilterStrategies.eventStrategy(event, hasOr));
    filterStrategies.put(
        variant, hasOr -> FilterStrategies.variantStrategy(variant, hasOr, modelDesc));
    filterStrategies.put(line, hasOr -> FilterStrategies.lineStrategy(line, hasOr));
    filterStrategies.put(rework, hasOr -> FilterStrategies.reworkStrategy(rework, hasOr));
    filterStrategies.put(
        customerExpression, hasOr -> FilterStrategies.customerStrategy(customerExpression, hasOr));

    return filterStrategies.entrySet().stream()
        .filter(entry -> StringUtils.isNotBlank(entry.getKey()))
        .findFirst()
        .map(entry -> entry.getValue().apply(hasOrNone))
        .orElse(null);
  }

  private ParseKpiDTO buildParseKpiDTO(
      DataModelFileDTO dataModelFileDTO,
      Integer sheetId,
      List<KpiPO> kpiList,
      List<TopicFilterPO> addFilter) {

    return ParseKpiDTO.builder()
        .topicId(dataModelFileDTO.getTopicId())
        .sheetId(sheetId)
        .dimensionColumnList(kpiList)
        .addTopicFilterList(addFilter)
        .type(0)
        .rule(VariableKpiRuleEnum.LIMIT.getValue())
        .limit(500)
        .build();
  }

  private List<RelatedDTO> getRelatedDTOList(Result ret) {
    try {
      return ret.getList(RelatedDTO.class);
    } catch (Exception e) {
      log.error("getHasOrNoneCase error", e);
      return new ArrayList<>();
    }
  }

  private void buildCaseByParamSort(
      List<RelatedDTO> hasCaseBySort,
      List<String> paramValueList,
      Map<String, RelatedDTO> hasCaseMap,
      int i) {
    RelatedDTO relatedDTO = hasCaseMap.get(paramValueList.get(i));
    if (Objects.isNull(relatedDTO)) {
      hasCaseBySort.add(RelatedDTO.builder().num(0L).variantName(paramValueList.get(i)).build());
    } else {
      hasCaseBySort.add(relatedDTO);
    }
  }

  private BigDecimal calRelated(
      List<RelatedDTO> caseList,
      BigDecimal caseParamTotal,
      Map<Integer, BigDecimal> relatedRate,
      BigDecimal related) {

    for (int i = 0; i < caseList.size(); i++) {
      BigDecimal multiplyRate = caseParamTotal.multiply(relatedRate.get(i));
      if (multiplyRate.compareTo(BigDecimal.ZERO) == 0) {
        continue;
      }
      BigDecimal subtract = BigDecimal.valueOf(caseList.get(i).getNum()).subtract(multiplyRate);
      BigDecimal divide = subtract.pow(2).divide(multiplyRate, 3, RoundingMode.HALF_UP);
      related = related.add(divide);
    }
    return related;
  }

  private BigDecimal calculateContainRatio(
      Integer topicId,
      Integer sheetId,
      BigDecimal currentCaseNum,
      String event,
      String variant,
      String line,
      String rework,
      String customerExpression,
      ModelDesc modelDesc) {
    if (currentCaseNum.compareTo(BigDecimal.ZERO) == 0) {
      return BigDecimal.ZERO;
    }
    BigDecimal per = BigDecimal.valueOf(100);

    // 创建参数到枚举类型的映射
    Map<String, RootCauseAnalysisEnum> paramToEnum = new HashMap<>();
    paramToEnum.put(event, RootCauseAnalysisEnum.EVENT_ROOT_CAUSE);
    paramToEnum.put(variant, RootCauseAnalysisEnum.PATH_CAUSE);
    paramToEnum.put(line, RootCauseAnalysisEnum.LINE_ROOT_CAUSE);
    paramToEnum.put(rework, RootCauseAnalysisEnum.REWORK_ROOT_CAUSE);
    paramToEnum.put(customerExpression, RootCauseAnalysisEnum.CUSTOMER_EXPRESSION);

    // 找到第一个非空的参数及其对应的枚举类型
    Optional<Map.Entry<String, RootCauseAnalysisEnum>> entryOptional =
        paramToEnum.entrySet().stream().filter(e -> StringUtils.isNotBlank(e.getKey())).findFirst();

    if (!entryOptional.isPresent()) {
      throw new UnsupportedOperationException("Incomplete parameters");
    }

    Map.Entry<String, RootCauseAnalysisEnum> entry = entryOptional.get();
    String variable = entry.getKey();
    RootCauseAnalysisEnum rootCause = entry.getValue();

    // rework ,customerExpression 无需通过 generateFilterExpression,可直接通过
    String filterExpression = getFilterExpression(rework, customerExpression, variable, rootCause);

    TopicFilterPO filter =
        TopicFilterPO.builder()
            .type(
                rootCause == RootCauseAnalysisEnum.EVENT_ROOT_CAUSE
                    ? TopicFilterTypeEnum.WITH.getValue()
                    : TopicFilterTypeEnum.EXPRESSION.getValue())
            .expression(filterExpression)
            .value(RootCauseAnalysisEnum.EVENT_ROOT_CAUSE == rootCause ? variable : null)
            .build();

    List<String> result =
        pqlService.calcExpression(
            topicId, sheetId, CASE_COUNT_NUM_EXPRESSION, Collections.singletonList(filter), 1);
    return new BigDecimal(result.get(0))
        .divide(currentCaseNum, 3, RoundingMode.HALF_UP)
        .multiply(per);
  }

  private static String getFilterExpression(
      String rework, String customerExpression, String variable, RootCauseAnalysisEnum rootCause) {
    if (StringUtils.isNotBlank(customerExpression)) {
      return customerExpression;
    }
    if (StringUtils.isNotBlank(rework)) {
      return rework;
    }
    return rootCause.generateFilterExpression(variable);
  }

  private void checkSortKpiIsExistThenNewKpi(List<SheetKpiDTO> topicSheetKpiList) {
    if (Objects.nonNull(sortKpiId)) {
      return;
    }

    SheetKpiDTO sheetKpiDTO =
        SheetKpiDTO.builder()
            .id(CASE_COUNT_NUM_KPI_ID)
            .name(CASE_COUNT_NUM)
            .expression(CASE_COUNT_NUM_EXPRESSION)
            .build();
    sortKpiId = sheetKpiDTO.getId();
    topicSheetKpiList.add(sheetKpiDTO);
  }
}

class FilterStrategies {
  public static TopicFilterPO eventStrategy(String event, boolean hasOr) {
    return TopicFilterPO.builder()
        .type(hasOr ? TopicFilterTypeEnum.WITH.getValue() : TopicFilterTypeEnum.WITHOUT.getValue())
        .value(event)
        .build();
  }

  public static TopicFilterPO variantStrategy(String variant, boolean hasOr, ModelDesc modelDesc) {
    String operator = hasOr ? "=" : "!=";
    String expression =
        String.format(
            "process_variant(%s) %s '%s'", modelDesc.activeTableEventColumn(), operator, variant);
    return TopicFilterPO.builder()
        .type(TopicFilterTypeEnum.EXPRESSION.getValue())
        .expression(expression)
        .build();
  }

  public static TopicFilterPO lineStrategy(String line, boolean hasOr) {
    String lineTemplate =
        hasOr ? "`case_table`.`variant` like '%%%s%%'" : "`case_table`.`variant` not like '%%%s%%'";
    String formatExpression = String.format(lineTemplate, line);
    return TopicFilterPO.builder()
        .type(TopicFilterTypeEnum.EXPRESSION.getValue())
        .expression(formatExpression)
        .build();
  }

  public static TopicFilterPO reworkStrategy(String rework, boolean hasOr) {
    String expression = hasOr ? rework : "not " + rework;
    return TopicFilterPO.builder()
        .type(TopicFilterTypeEnum.EXPRESSION.getValue())
        .expression(expression)
        .build();
  }

  public static TopicFilterPO customerStrategy(String customerExpression, boolean hasOr) {
    String expression = hasOr ? customerExpression : "not " + customerExpression;
    return TopicFilterPO.builder()
        .type(TopicFilterTypeEnum.EXPRESSION.getValue())
        .expression(expression)
        .build();
  }
}
