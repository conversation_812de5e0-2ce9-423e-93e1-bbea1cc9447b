package com.prx.service.page.entity;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:35
 */
@Getter
@Setter
@Builder
public class CustomerRootCauseDto {

  @Tolerate
  public CustomerRootCauseDto() {
    // comment empty
  }

  private Integer id;

  private Integer topicSheetId;

  @ApiModelProperty(value = "表达式名称", required = true)
  private String expressionName;

  @ApiModelProperty(value = "表达式", required = true)
  private String expression;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;
}
