package com.prx.service.data;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.annotations.VisibleForTesting;
import com.prx.service.data.convert.XlsxToCsvConverter;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.mapper.FileMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileStatusEnum;
import com.sp.proxverse.common.model.dict.FileTypeEnum;
import com.sp.proxverse.common.model.dict.FileUploadStatusEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.CloudFileInfoPO;
import com.sp.proxverse.common.model.po.DataPoolFilePO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.model.vo.fileUpload.FileMergeReqVo;
import com.sp.proxverse.common.model.vo.fileUpload.FileMergeResVo;
import com.sp.proxverse.common.model.vo.fileUpload.FileResourceCreationReqVo;
import com.sp.proxverse.common.model.vo.fileUpload.FileResourceCreationResVo;
import com.sp.proxverse.common.model.vo.fileUpload.FileUploadReqVo;
import com.sp.proxverse.common.util.DataUtil;
import com.sp.proxverse.common.util.FileUtils;
import com.sp.proxverse.common.util.HadoopUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.CloudFileInfoService;
import com.sp.proxverse.interfaces.dao.service.DataPoolFileService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import com.sp.proxverse.interfaces.service.data.FileStatistics;
import com.sp.proxverse.utils.CheckInvalid;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IOUtils;
import org.apache.spark.internal.config.ConfigHelpers;
import org.apache.spark.network.util.ByteUnit;
import org.apache.spark.util.Utils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@Primary
public class FileServiceImpl extends ServiceImpl<FileMapper, FilePO>
    implements FileService, InitializingBean {
  @Value("${prx.admin.license.enabled:false}")
  private Boolean licenseEnabled;

  @Value("${csv.file.save.path:}")
  private String csvFileSavePath;

  @Value("${csv.child.file.save.path:}")
  private String csvChildFileSavePath;

  @Value("${prx.dataModel.file.maxUploadSize:1g}")
  private String maxUploadSize;

  private Long maxFileSize;

  @Autowired DataPoolFileService dataPoolFileService;

  @Autowired private UserInfoUtil userInfoUtil;

  @Value("${business.file.path:}")
  private String businessFilePath;

  @Value("${prx.file.xlsx.delimit:,}")
  private String xlsxDelimit;

  @Autowired private CloudFileInfoService cloudFileInfoService;

  @Autowired private FileStatistics fileStatistics;

  @Override
  public void afterPropertiesSet() {
    maxFileSize = ConfigHelpers.byteFromString(maxUploadSize, ByteUnit.BYTE);
  }

  @Override
  public List<FilePO> getList() {

    if (licenseEnabled) {
      return baseMapper.getList();
    } else {
      return baseMapper.getList4Tenant(userInfoUtil.getGroupId());
    }
  }

  @Override
  public FileMergeResVo fileMerge(FileMergeReqVo fileMergeReqVo) {
    long startTime = System.currentTimeMillis();
    String dockerPath = csvChildFileSavePath + fileMergeReqVo.getIdentifier();

    String mergeFilePath;
    // hack! need a convert factory to impl this.
    String csvFilePath =
        csvFileSavePath + fileMergeReqVo.getIdentifier() + "." + fileMergeReqVo.getSuffix();
    if (Objects.equals(fileMergeReqVo.getSuffix(), "xlsx")) {
      mergeFilePath = csvFilePath + ".tmp";
    } else {
      mergeFilePath = csvFilePath;
    }

    File createFile = new File(mergeFilePath);
    OutputStream outputStream = null;
    try {
      Path path = new Path(dockerPath);
      FileSystem fileSystem = HadoopUtil.getFileSystemByPath(path);
      FileStatus[] fileStatuses = fileSystem.listStatus(path);
      List<FileStatus> fileStatusesSorted =
          Arrays.stream(fileStatuses)
              .sorted(Comparator.comparing(FileStatus::getModificationTime))
              .collect(Collectors.toList());
      outputStream = fileSystem.create(new Path(mergeFilePath));
      fileMerge(fileStatusesSorted, fileSystem, outputStream);
    } catch (RuntimeException | IOException e) {
      log.error("文件合并时数据异常!", e);
      throw new BizException(5000, "文件合并时数据异常！");
    } finally {
      IOUtils.closeStream(outputStream);
    }
    // 删除小文件
    HadoopUtil.deletePath(dockerPath);
    log.info("合并文件生成路径：" + mergeFilePath);
    log.info("合并花费时间:" + (System.currentTimeMillis() - startTime));
    String charset = FileUtils.charset(mergeFilePath);

    FileMergeResVo fileMergeResVo = new FileMergeResVo();
    fileMergeResVo.setState(true);
    fileMergeResVo.setFileSize(createFile.length());
    fileMergeResVo.setFileName(fileMergeReqVo.getIdentifier());
    fileMergeResVo.setEncoding(charset);

    // hack! need a convert factory to impl this.
    if (Objects.equals(fileMergeReqVo.getSuffix(), "xlsx")) {
      try {
        new XlsxToCsvConverter(xlsxDelimit).convertToCsv(mergeFilePath, csvFilePath);
        HadoopUtil.deletePath(mergeFilePath);
      } catch (IOException e) {
        log.info("convert xlsx to csv error:", e);
        throw new BizException(5000, "Error for convert xlsx to csv :");
      }
      fileMergeResVo.setFilePath(csvFilePath);
    }
    return fileMergeResVo;
  }

  private static void fileMerge(
      List<FileStatus> fileStatusesSorted, FileSystem fileSystem, OutputStream outputStream) {
    for (FileStatus fileStatus : fileStatusesSorted) {
      try (FSDataInputStream open = fileSystem.open(fileStatus.getPath())) {
        IOUtils.copyBytes(open, outputStream, 4096);
      } catch (Exception e) {
        log.error("文件合并时数据异常!", e);
        throw new BizException(5000, "文件合并时数据异常！");
      }
    }
  }

  @Override
  public String fileUpload(FileUploadReqVo chunk) throws IOException {
    // 数据校验
    if (chunk.getDataPoolId() == null) {
      throw new BizException(5000, "dataPoolId不能为空");
    }
    String identifier = chunk.getIdentifier();

    String dockerFilePath = csvChildFileSavePath + identifier;
    MultipartFile file = chunk.getFile();
    Integer chunkNumber = chunk.getChunkNumber();
    FileUtils.creatDirectory(dockerFilePath);
    String filePath = dockerFilePath + "/" + chunkNumber;
    log.info("断点续传文件路径" + filePath);
    HadoopUtil.copyToPath(file.getInputStream(), filePath);

    // 如果文件是最后一个将进行合并处理
    if (Objects.equals(chunk.getTotalChunks(), chunk.getChunkNumber())) {
      int dotIndex = chunk.getFilename().lastIndexOf('.');
      String suffix = (dotIndex != -1) ? chunk.getFilename().substring(dotIndex + 1) : "";
      FileResourceCreationReqVo fileResourceCreationReqVo = new FileResourceCreationReqVo();
      fileResourceCreationReqVo.setDataPoolId(Integer.valueOf(chunk.getDataPoolId()));
      fileResourceCreationReqVo.setIdentifier(chunk.getIdentifier());
      fileResourceCreationReqVo.setChunkNumber(chunk.getTotalChunks());
      fileResourceCreationReqVo.setSuffix(suffix);
      fileResourceCreationReqVo.setFileName(chunk.getFilename());

      FileResourceCreationResVo fileResourceCreationResVo =
          this.fileResourceCreation(fileResourceCreationReqVo);
      return fileResourceCreationResVo.getFileId().toString();
    }

    return "";
  }

  /**
   * 递增文件名
   *
   * @param lists
   * @param fileName
   * @param suffix
   * @return
   */
  public String fileNameIncremental(List<String> lists, String fileName, Integer suffix) {
    StringBuilder fileNameBuilder = new StringBuilder();
    fileNameBuilder.append(fileName);
    if (suffix == null) {
      suffix = 0;
    } else {
      fileNameBuilder.append("_");
      fileNameBuilder.append(suffix);
    }
    suffix++;
    if (lists.stream().anyMatch(s -> s.equalsIgnoreCase(fileNameBuilder.toString()))) {
      return fileNameIncremental(lists, fileName, suffix);
    }
    return fileNameBuilder.toString();
  }

  @Override
  public FileResourceCreationResVo fileResourceCreation(
      FileResourceCreationReqVo fileResourceCreationReqVo) {
    // 这里处理文件重命名的逻辑
    List<DataPoolFilePO> poolFilePOList =
        dataPoolFileService.list(
            new LambdaQueryWrapper<DataPoolFilePO>()
                .eq(DataPoolFilePO::getDataPoolId, fileResourceCreationReqVo.getDataPoolId()));

    if (CollectionUtils.isNotEmpty(poolFilePOList)) {
      List<Integer> fileIdList =
          poolFilePOList.stream().map(DataPoolFilePO::getFileId).collect(Collectors.toList());
      List<FilePO> filePOList =
          this.list(
              new LambdaQueryWrapper<FilePO>()
                  .in(FilePO::getId, fileIdList)
                  .eq(FilePO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      List<String> fileNameList =
          filePOList.stream().map(FilePO::getFilename).collect(Collectors.toList());
      String[] split = fileResourceCreationReqVo.getFileName().split("\\.");

      String s = this.fileNameIncremental(fileNameList, split[0], null);
      fileResourceCreationReqVo.setFileName(s);
    }

    FileMergeResVo fileMergeResVo = this.fileMerge(fileResourceCreationReqVo);

    // 先把文件信息存入mysql
    String fullFilename = fileResourceCreationReqVo.getFileName();
    String[] split = fullFilename.split("[.]");
    String fileName = split[0];
    FilePO filePO =
        FilePO.builder()
            .filename(fileName)
            .uniqFilename(
                fileResourceCreationReqVo.getIdentifier()
                    + "."
                    + fileResourceCreationReqVo.getSuffix())
            .loadStartTime(new Date())
            .type(FileTypeEnum.filterFileType(fileResourceCreationReqVo.getSuffix()))
            .createTime(new Date())
            .status(FileStatusEnum.NEW.getValue())
            .uploadStatus(FileUploadStatusEnum.LOADED.getValue())
            .originalLength(fileMergeResVo.getFileSize() / 1024)
            .charEncode(fileMergeResVo.getEncoding())
            .updateTime(new Date())
            .build();
    this.save(filePO);

    // 数据池与文件ID关联
    dataPoolFileService.save(
        DataPoolFilePO.builder()
            .dataPoolId(fileResourceCreationReqVo.getDataPoolId())
            .fileId(filePO.getId())
            .fileType(FileTypeEnum.CSV.getValue())
            .deleted(DeletedEnum.HAS_DELETED.getValue())
            .build());

    FileResourceCreationResVo fileResourceCreationResVo = new FileResourceCreationResVo();
    fileResourceCreationResVo.setFileId(filePO.getId());
    return fileResourceCreationResVo;
  }

  @Override
  public void cancelFileUpload(String identifier) {
    String dockerFilePath = csvChildFileSavePath + identifier;
    // 删除小文件
    if (StringUtils.isNotBlank(dockerFilePath)) {
      HadoopUtil.deletePath(dockerFilePath);
    }
  }

  @Override
  public void checkFileName(Integer poolId, String fileName) {
    List<DataPoolFilePO> list =
        dataPoolFileService.list(
            new LambdaQueryWrapper<DataPoolFilePO>()
                .eq(DataPoolFilePO::getDataPoolId, poolId)
                .eq(DataPoolFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    List<Integer> fileIdList =
        list.stream().map(DataPoolFilePO::getFileId).collect(Collectors.toList());
    List<FilePO> fileList = baseMapper.selectBatchIds(fileIdList);

    boolean anyMatch = fileList.stream().anyMatch(m -> Objects.equals(m.getFilename(), fileName));
    if (!anyMatch) {
      throw new BizException(5000, "该提取名称和已有提取或者文件名重复，请重新输入");
    }
  }

  @Override
  public void changeToUTF8(File tempFile, File createFile) {
    try (BufferedReader reader =
            new BufferedReader(
                new InputStreamReader(
                    new FileInputStream(tempFile),
                    FileUtils.charset(tempFile.getCanonicalPath())));
        OutputStreamWriter writer =
            new OutputStreamWriter(new FileOutputStream(createFile), StandardCharsets.UTF_8)) {
      String s;
      int n = 0;
      while ((s = reader.readLine()) != null) {
        if (n == 0 && !s.trim().isEmpty()) {
          s = s.replaceAll("[ \\+\\-\\*\\/\\.\\{\\}\\(\\)\\n\\t=]", "");
          n++;
        }
        writer.write(s);
        writer.write("\n");
      }

    } catch (IOException e) {
      log.error("文件编码转换失败: {}", tempFile.getAbsolutePath(), e);
      throw new BizException(5000, "文件处理异常");
    } finally {
      // 确保删除临时文件（即使处理失败）
      if (!tempFile.delete()) {
        log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
      }
    }
  }

  @Override
  public CloudFileInfoPO importFileComplete(MultipartFile fileMultipart, FileTypeEnum fileType) {
    if (fileType == null) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
    }
    String uuid = UUID.randomUUID().toString();
    String fileDirectory = businessFilePath + uuid;
    String filePath = fileDirectory + "/" + fileMultipart.getOriginalFilename();

    try (InputStream inputStream = fileMultipart.getInputStream()) {
      HadoopUtil.copyToPath(inputStream, filePath);
    } catch (IOException e) {
      log.error(I18nUtil.getMessage(I18nConst.FILE_UPLOAD_FAILED), e);
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_UPLOAD_FAILED));
    }

    CloudFileInfoPO cloudFileInfoPO = new CloudFileInfoPO();
    cloudFileInfoPO.setFileName(fileMultipart.getOriginalFilename());
    cloudFileInfoPO.setFileUrl(filePath);
    cloudFileInfoPO.setStorageType(CloudFileInfoPO.LOCAL_STORAGE);
    cloudFileInfoPO.setFileType(fileType.name());
    cloudFileInfoService.save(cloudFileInfoPO);
    return cloudFileInfoPO;
  }

  @Override
  public Boolean removeFileComplete(Integer fileId) {
    if (fileId == null) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
    }
    CloudFileInfoPO cloudFileInfoPO = cloudFileInfoService.getById(fileId);
    if (cloudFileInfoPO == null) {
      return false;
    }
    cloudFileInfoService.removeById(fileId);
    if (Objects.equals(cloudFileInfoPO.getFileType(), FileTypeEnum.ATTACHMENT.name())
        || Objects.equals(cloudFileInfoPO.getFileType(), FileTypeEnum.PROCESS_DESCRIPTION.name())) {
      int lastIndex = cloudFileInfoPO.getFileUrl().lastIndexOf('/');
      if (lastIndex != -1) {
        String removeUrl = cloudFileInfoPO.getFileUrl().substring(0, lastIndex);
        return FileUtils.deleteFile(new File(removeUrl));
      } else {
        return false;
      }
    }
    return true;
  }

  @VisibleForTesting
  public void setMaxFileSize(Long maxFileSize) {
    this.maxFileSize = maxFileSize;
  }

  public String checkFile(String fullFileName, long fileSize) {
    return checkFile(fullFileName, fileSize, true, 0);
  }

  @Override
  public String checkFile(String fullFileName, long fileSize, boolean hasSuffix, Integer poolId) {
    if (fileSize > maxFileSize) {
      if (Utils.byteStringAsGb(maxFileSize + "b") > 0) {
        return String.format(
            I18nUtil.getMessage(I18nConst.ERROR_FILE_SIZE),
            Utils.byteStringAsGb(maxFileSize.toString() + "b") + "Gb");
      } else {
        return String.format(
            I18nUtil.getMessage(I18nConst.ERROR_FILE_SIZE),
            Utils.byteStringAsMb(maxFileSize.toString() + "b") + "Mb");
      }
    }
    if (!hasSuffix) {
      fullFileName = fullFileName + ".csv";
    }

    String[] split = fullFileName.split("[.]");
    if (split.length < 2) {
      return I18nUtil.getMessage(I18nConst.ERROR_FILE_TYPE);
    }
    String fileName = split[0];
    if (poolId != null && poolId > 0) {
      List<DataPoolFilePO> poolFilePOList =
          dataPoolFileService.list(
              new LambdaQueryWrapper<DataPoolFilePO>()
                  .eq(DataPoolFilePO::getDataPoolId, poolId)
                  .eq(DataPoolFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      if (!poolFilePOList.isEmpty()) {

        List<Integer> fileIdList =
            poolFilePOList.stream().map(DataPoolFilePO::getFileId).collect(Collectors.toList());
        if (this.count(
                new LambdaQueryWrapper<FilePO>()
                    .in(FilePO::getId, fileIdList)
                    .eq(FilePO::getFilename, fileName))
            > 0) {
          return I18nUtil.getMessage(I18nConst.DATA_POOL_FILE_NAME_EXIST);
        }
      }
    }

    if (!FileTypeEnum.getDataPoolImportFileType().contains(split[split.length - 1].toLowerCase())) {
      return I18nUtil.getMessage(I18nConst.ERROR_FILE_TYPE);
    }

    if (!CheckInvalid.isValiadte(fileName)) {
      return I18nUtil.getMessage(I18nConst.ERROR_FILE_NAME);
    }

    if (!fileStatistics.checkDataSpace(fileSize / 1024)) {
      return I18nUtil.getMessage(I18nConst.MAXIMUM_DATA_SPACE_REACHED);
    }
    if (DataUtil.isNumeric(fileName)) {
      return I18nUtil.getMessage(I18nConst.TABLE_NAME_MUST_CONTAIN_CHARS);
    }
    return "";
  }
}
