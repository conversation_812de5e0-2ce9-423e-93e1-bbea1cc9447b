package com.prx.service.bookmark

import java.util.Date

import com.sp.proxverse.common.UserInfoUtil
import com.sp.proxverse.common.exception.BizException
import com.sp.proxverse.common.model.dict._
import com.sp.proxverse.common.model.i18n.I18nConst
import com.sp.proxverse.common.model.po.{TopicFilterBookMarkPO, TopicFilterMarkPO}
import com.sp.proxverse.common.model.vo.request.topic.{BookMarkIdRequest, ListBookMarkResp, QueryBookMarkRequest, SaveBookMarkRequest}
import com.sp.proxverse.common.util.{DateTimeUtil, I18nUtil}
import com.sp.proxverse.interfaces.dao.service.{TopicFilterBookMarkService, TopicFilterMarkService, TopicFilterService}
import com.sp.proxverse.interfaces.service.data.pql.TopicFilterManager
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import scala.collection.JavaConverters._

/**
  * <AUTHOR>
  * @date 2023/12/12 13:59
  */
@Service
class BookMarkService {
  @Autowired
  var topicFilterService: TopicFilterService = _
  @Autowired
  var userInfoUtil: UserInfoUtil = _
  @Autowired
  var topicFilterMarkService: TopicFilterMarkService = _
  @Autowired
  var topicFilterBookMarkService: TopicFilterBookMarkService = _
  @Autowired
  var topicFilterManager: TopicFilterManager = _
  @Autowired
  var bookmarkIndexIdService: BookmarkIndexIdService = _

  def deleteBookMark(request: BookMarkIdRequest): Boolean = {
    if (request.getId == null) {
      false
    } else {
      topicFilterMarkService.removeByIndexId(request.getTopicId, request.getId)
    }
  }

  def loadBookMark(request: BookMarkIdRequest): Boolean = {
    val mark = topicFilterMarkService.getByIndexId(request.getTopicId, request.getIdList.get(0))
    if (mark.getDeleted == 1) {
      false
    } else {
      topicFilterService.removeTopicFilter(request.getTopicId)
      val bookmarkList = topicFilterBookMarkService.listByMarkId(mark.getId)
      val filterIdList = bookmarkList.asScala.map(_.getFilterId).asJavaCollection
      val topicFilterList = topicFilterService.listByIds(filterIdList)
      topicFilterList.forEach(f => {
        f.setId(null)
        f.setMarkType(FilterMarkTypeEnum.TOPIC_FILTER.getValue)
      })
      topicFilterService.saveList(topicFilterList, userInfoUtil.getUserInfo.getUserId)
      topicFilterManager.invalidate(request.getTopicId)
      true
    }
  }

  def getBookMarkList(request: QueryBookMarkRequest): Array[ListBookMarkResp] = {
    val list = topicFilterMarkService.getList(request.getTopicId)
    list.asScala.map(m => ListBookMarkResp.builder
      .topicId(m.getTopicId)
      .id(m.getIndexId)
      .name(m.getName)
      .num(m.getNum)
      .time(DateTimeUtil.date2String(m.getTime))
      .build)
      .toArray
  }

  def saveBookMark(request: SaveBookMarkRequest): Int = {
    if (request.getMarkId == null) {
      save(request)
    } else {
      update(request)
    }
  }

  def update(request: SaveBookMarkRequest): Int = {
    val byId = topicFilterMarkService.getByIndexId(request.getTopicId, request.getMarkId)
    if (byId == null) throw new BizException(5000, I18nUtil.getMessage(I18nConst.BOOK_MARK_NOT_EXISTS))
    topicFilterMarkService.updateById(TopicFilterMarkPO.builder
      .id(byId.getId)
      .`type`(request.getType)
      .name(request.getName)
      .build)
    request.getMarkId
  }

  def save(request: SaveBookMarkRequest): Int = {
    val topicFilterList = topicFilterService.getTopicFilterListWithBizType(request.getTopicId, userInfoUtil.getUserInfo.getUserId, TopicFilterBizTypeEnum.BIZ_CREATE.getValue)
    if (CollectionUtils.isEmpty(topicFilterList)) throw new BizException(5000, I18nUtil.getMessage(I18nConst.ADD_FILTER_FIRST))
    val count = topicFilterList.asScala
      .map(_.getPrikeyId)
      .distinct
      .size
    val one = topicFilterMarkService.getOneByName(request.getTopicId, request.getName)
    // 新增，覆盖已有name
    if (one != null) {
      one.setNum(count)
      one.setTime(new Date)
      one.setType(request.getType)
      topicFilterMarkService.updateById(one)
      topicFilterBookMarkService.deleteByMarkId(one.getId)
      topicFilterList.forEach(f => {
        f.setId(null)
        f.setMarkType(FilterMarkTypeEnum.BOOK_MARK.getValue)
      })
      topicFilterService.saveBatch(topicFilterList)
      val bookMarkArray = topicFilterList.asScala
        .map(m => TopicFilterBookMarkPO.builder.markId(one.getId).filterId(m.getId).build)
        .asJavaCollection
      topicFilterBookMarkService.saveBatch(bookMarkArray)
      one.getId
    } else {
      val mark = TopicFilterMarkPO.builder
        .topicId(request.getTopicId)
        .name(request.getName)
        .num(count.toInt)
        .time(new Date)
        .`type`(request.getType)
        .indexId(bookmarkIndexIdService.getMaxIndexId(request.getTopicId))
        .build
      topicFilterMarkService.save(mark)
      topicFilterList.forEach(f => {
        f.setId(null)
        f.setMarkType(FilterMarkTypeEnum.BOOK_MARK.getValue)
      })
      topicFilterService.saveBatch(topicFilterList)
      val bookMarkArray = topicFilterList.asScala
        .map(m => TopicFilterBookMarkPO.builder.markId(mark.getId).filterId(m.getId).build)
        .asJavaCollection
      topicFilterBookMarkService.saveBatch(bookMarkArray)
      mark.getId
    }
  }
}

