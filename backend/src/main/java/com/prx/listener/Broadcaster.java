package com.prx.listener;

import com.sp.proxverse.common.mapper.TNodeInfoMapper;
import com.sp.proxverse.common.model.TNodeInfo;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.java.Log;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.spark.JsonProto;
import org.apache.spark.util.event.ModelRefreshEvent;
import org.apache.spark.util.event.ServerEnv;
import org.apache.spark.util.event.ServerListenerEvent;
import org.apache.spark.util.event.ServerListenerInterface;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Log
public class Broadcaster implements ApplicationListener<ApplicationReadyEvent> {

  @Value("${server.port}")
  private String port;

  @Value("${prx.node.role:Writer}")
  private String role;

  public TNodeInfo tNodeInfo;

  @Resource private TNodeInfoMapper tNodeInfoMapper;

  public static ThreadLocal<Boolean> isBroadcastEventScope =
      new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
          return false;
        }
      };

  public void deleteIpAndPortOnExit() {
    // 注册ShutdownHook
    Runtime.getRuntime()
        .addShutdownHook(
            new Thread(
                () -> {
                  // 在这里执行你自定义的清理操作
                  log.info("程序关闭------------------：" + tNodeInfo);
                  tNodeInfoMapper.deleteById(tNodeInfo.getId());
                }));
  }

  @Override
  public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
    // 记录启动程序的ip和启动端口，并进行数据插入
    // 获取本机的 InetAddress 对象
    InetAddress inetAddress = null;
    try {
      inetAddress = InetAddress.getLocalHost();
    } catch (UnknownHostException e) {
      throw new RuntimeException(e);
    }
    // 获取本机的 IP 地址
    String ipAddress = inetAddress.getHostAddress();
    // 打印 IP 地址
    log.info("当前机器的 IP 地址是: " + ipAddress + "端口：" + this.port + "角色：" + role);
    tNodeInfo = new TNodeInfo();
    tNodeInfo.setId(UUID.randomUUID().toString());
    tNodeInfo.setIp(ipAddress);
    tNodeInfo.setUser_role(role);
    tNodeInfo.setPort(port);
    tNodeInfoMapper.insert(tNodeInfo);
    this.deleteIpAndPortOnExit();
    ServerEnv.registerSyncListener(new BroadCastListener(tNodeInfoMapper, tNodeInfo.currentNode()));
  }

  static class BroadCastListener implements ServerListenerInterface {
    TNodeInfoMapper tNodeInfoMapper;
    String currentNode;

    public BroadCastListener(TNodeInfoMapper tNodeInfoMapper, String currentNode) {
      this.tNodeInfoMapper = tNodeInfoMapper;
      this.currentNode = currentNode;
    }

    @Override
    public void onOtherEvent(ServerListenerEvent event) {
      if (Broadcaster.isBroadcastEventScope.get()) {
        return;
      }
      List<TNodeInfo> tNodeInfos = tNodeInfoMapper.selectList(null);
      for (TNodeInfo nodeInfo : tNodeInfos) {
        if (!nodeInfo.currentNode().equals(currentNode)) {
          sendEventToNode(nodeInfo, JsonProto.sparkEventToJson(event));
        }
      }
    }

    private final OkHttpClient client =
        new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    private void sendEventToNode(TNodeInfo nodeInfo, String eventJsonString) {
      // 构建完整的 URL
      String url =
          "http://"
              + nodeInfo.getIp()
              + ":"
              + nodeInfo.getPort()
              + "/broadcast/receive?from="
              + currentNode; // 替换为实际的端点路径
      okhttp3.MediaType JSON = okhttp3.MediaType.get("application/json; charset=utf-8");

      // 创建请求体
      RequestBody body = RequestBody.create(JSON, eventJsonString);

      // 创建请求
      Request request = new Request.Builder().url(url).post(body).build();

      boolean sent = false;
      int retries = 3; // 设置重试次数
      for (int i = 0; i < retries && !sent; i++) {
        try (Response response = client.newCall(request).execute()) {
          if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
          }
          sent = true; // 发送成功，跳出循环
        } catch (IOException e) {
          log.warning("Attempt " + (i + 1) + " failed: " + e.getMessage());
          if (i < retries - 1) {
            try {
              TimeUnit.SECONDS.sleep(2); // 等待2秒后重试
            } catch (InterruptedException ex) {
              Thread.currentThread().interrupt(); // 保持中断状态
              break;
            }
          }
        }
      }
      if (!sent) {
        log.warning("Failed to send event to node after " + retries + " attempts.");
        tNodeInfoMapper.deleteById(nodeInfo.getId());
      }
    }

    @Override
    public void onModelRefresh(ModelRefreshEvent onModelRefresh) {
      onOtherEvent(
          new ModelRefreshEvent(onModelRefresh.modeId(), onModelRefresh.versionNumber(), null));
    }
  }
}
