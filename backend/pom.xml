<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sp</groupId>
        <artifactId>proxverse-project</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>backend</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sp</groupId>
            <artifactId>proxverse-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sp</groupId>
            <artifactId>proxverse-service-interface</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sp</groupId>
            <artifactId>proxverse-server</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
      <dependency>
        <groupId>com.sp</groupId>
        <artifactId>proxverse-data-core</artifactId>
        <version>${project.version}</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp3.version}</version>
      </dependency>
      <dependency>
        <groupId>com.prx</groupId>
        <artifactId>prx-common</artifactId>
      </dependency>
      <dependency>
        <groupId>org.camunda.bpm.model</groupId>
        <artifactId>camunda-bpmn-model</artifactId>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>5.2.3</version>
      </dependency>
    </dependencies>

    <build>
        <finalName>backend</finalName>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
