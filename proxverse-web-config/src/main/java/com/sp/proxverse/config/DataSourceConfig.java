package com.sp.proxverse.config;

import com.baomidou.dynamic.datasource.creator.DruidDataSourceCreator;
import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import javax.sql.DataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
@DependsOn({"akmApplicationResource"})
public class DataSourceConfig {

  @Resource private DynamicDataSourceProperties properties;

  @Resource private DruidDataSourceCreator druidDataSourceCreator;

  @Bean
  public DynamicDataSourceProvider dynamicDataSourceProvider() {
    return new AbstractDataSourceProvider() {
      @Override
      public Map<String, DataSource> loadDataSources() {
        Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();
        Map<String, DataSource> dataSourceMap = new HashMap<>(datasourceMap.size());

        // 遍历配置的数据源
        datasourceMap.forEach(
            (ds, property) -> {
              property.setType(CustomDruidDataSource.class);
              DataSource dataSource = druidDataSourceCreator.createDataSource(property);
              dataSourceMap.put(ds, dataSource);
            });

        return dataSourceMap;
      }
    };
  }
}
