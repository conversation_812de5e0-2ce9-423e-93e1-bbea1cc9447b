package com.sp.proxverse.config;

import java.text.SimpleDateFormat;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TransactionLogger {
  private static final Logger log = LoggerFactory.getLogger(TransactionLogger.class);
  private static final Logger TRANSACTION_LOGGER = LoggerFactory.getLogger("TRANSACTION_LOGGER");
  private static final String LOG_PATTERN = "{}||{}||{}||{}||{}||{}||{}||{}||{}||{}||{}||{}";

  public static void logTransaction(TransactionLog txLog) {
    log.info(
        LOG_PATTERN,
        formatDateTime(System.currentTimeMillis()),
        nullToNA(txLog.getAppCode()),
        nullToNA(txLog.getTraceNo()),
        nullToNA(txLog.getJumpTraceNo()),
        nullToNA(txLog.getRequestNo()),
        nullToNA(txLog.getTransCode()),
        nullToNA(txLog.getStatus()),
        nullToNA(txLog.getReturnCode()),
        txLog.getDuration(),
        nullToNA(txLog.getErrorMsg()),
        nullToNA(txLog.getPodIp()),
        nullToNA(txLog.getHostname()));
    // 构造日志参数
    Object[] logParams =
        new Object[] {
          formatDateTime(System.currentTimeMillis()),
          nullToNA(txLog.getAppCode()),
          nullToNA(txLog.getTraceNo()),
          nullToNA(txLog.getJumpTraceNo()),
          nullToNA(txLog.getRequestNo()),
          nullToNA(txLog.getTransCode()),
          nullToNA(txLog.getStatus()),
          nullToNA(txLog.getReturnCode()),
          txLog.getDuration(),
          nullToNA(txLog.getErrorMsg()),
          nullToNA(txLog.getPodIp()),
          nullToNA(txLog.getHostname())
        };

    // 使用Logback记录日志
    TRANSACTION_LOGGER.info(LOG_PATTERN, logParams);
  }

  private static String formatDateTime(long timestamp) {
    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(timestamp));
  }

  private static String nullToNA(String value) {
    return value == null || value.trim().isEmpty() ? "N/A" : value;
  }
}
