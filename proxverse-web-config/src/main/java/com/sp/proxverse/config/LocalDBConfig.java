package com.sp.proxverse.config;

import static com.sp.proxverse.common.util.FileUtils.readFileAsString;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.sp.proxverse.common.exception.BizException;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;

@Configuration
@Profile(value = {"test-inmem", "test-persistent"})
@Slf4j
@Order(1)
public class LocalDBConfig {

  @Autowired
  public long init(DynamicRoutingDataSource dynamicRoutingDataSource) {
    try {
      initDB(
          dynamicRoutingDataSource.getDataSource("engine").getConnection(),
          "sp_process",
          "db_init_sql/sp_process.sql");
      initDB(
          dynamicRoutingDataSource.getDataSource("oauth2").getConnection(),
          "sp_oauth2",
          "db_init_sql/sp_oauth2.sql");
      initDB(
          dynamicRoutingDataSource.getDataSource("execution").getConnection(),
          "sp_execution",
          "db_init_sql/sp_execution.sql");

      return 1;
    } catch (SQLException | IOException e) {
      log.info("init db config error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  private static void initDB(Connection connection, String DB, String path)
      throws SQLException, IOException {

    try (Statement statement = connection.createStatement()) {
      statement.executeUpdate("create schema IF NOT EXISTS " + DB);
      statement.executeUpdate("set schema " + DB);
      try (ResultSet resultSet = statement.executeQuery("show tables from " + DB)) {
        if (resultSet.next()) {
          log.info("Exists db, Skip init db ");
          return;
        }
      }
      try {
        statement.executeUpdate(
            readFileAsString(Thread.currentThread().getContextClassLoader(), path));
      } catch (SQLException e) {
        log.error("error for init table ", e);
      }
    }
  }
}
