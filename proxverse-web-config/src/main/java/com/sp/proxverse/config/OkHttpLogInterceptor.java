package com.sp.proxverse.config;
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                            O\ = /O
//                        ____/`---'\____
//                      .   ' \\| |// `.
//                       / \\||| 1 |||// \
//                     / _||||| -9- |||||- \
//                       | | \\\ 9 /// | |
//                     | \_| ''\-1-/'' | |
//                      \ .-\__ `0` ___/-. /
//                   ___`. .' /--9--\ `. . __
//                ."" '< `.___\_<3>_/___.' >'"".
//               | | : `- \`.;`\ 0 /`;.`/ - ` : | |
//                 \ \ `-. \_ __\ /__ _/ .-` / /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//
//         .............................................
//                  佛祖保佑                  永无BUG

import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

public class OkHttpLogInterceptor implements Interceptor {

  private static final Logger log = LoggerFactory.getLogger(OkHttpLogInterceptor.class);

  @Value("${okHttpLogEnabled:true}")
  private boolean okHttpLogEnabled;

  static boolean isPlaintext(Buffer buffer) {
    try {
      Buffer prefix = new Buffer();
      long byteCount = buffer.size() < 64 ? buffer.size() : 64;
      buffer.copyTo(prefix, 0, byteCount);
      for (int i = 0; i < 16; i++) {
        if (prefix.exhausted()) {
          break;
        }
        int codePoint = prefix.readUtf8CodePoint();
        if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
          return false;
        }
      }
      return true;
    } catch (EOFException e) {
      // Truncated UTF-8 sequence.
      log.warn("字节流转字符串异常", e);
      return false;
    }
  }

  @Override
  public Response intercept(Chain chain) throws IOException {
    if (!okHttpLogEnabled) {
      return chain.proceed(chain.request());
    }
    Request request = chain.request();
    RequestBody requestBody = request.body();
    boolean hasRequestBody = requestBody != null;
    String requestString = null;
    if (hasRequestBody && requestBody.contentLength() != -1) {
      Buffer buffer = new Buffer();
      requestBody.writeTo(buffer);
      if (isPlaintext(buffer)) {
        requestString = buffer.readString(StandardCharsets.UTF_8);
      }
    }
    log.debug("request={}, requestBody={}", request, requestString);
    long startMillis = System.currentTimeMillis();
    Response response = chain.proceed(request);
    log.debug(
        "request={}, response={}, usedTime={}",
        request,
        response.peekBody(1024 * 1024).string(),
        System.currentTimeMillis() - startMillis);
    return response;
  }
}
