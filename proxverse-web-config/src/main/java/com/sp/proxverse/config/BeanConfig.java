package com.sp.proxverse.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;

/**
 * <AUTHOR>
 * @create 2022-04-28 4:39 下午
 */
@Configuration
@EnableAuthorizationServer
public class BeanConfig {

  @Bean
  public BCryptPasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }
  //
  //    /**
  //     * 重新rest过滤器
  //     *
  //     * @return
  //     */
  //    @Bean
  //    public RequestInterceptor requestTokenBearerInterceptor() {
  //
  //        return new RequestInterceptor() {
  //
  //            @Override
  //            public void apply(RequestTemplate requestTemplate) {
  //                if (SecurityContextHolder.getContext() != null &&
  // SecurityContextHolder.getContext().getAuthentication() != null) {
  //                    String token = null;
  //                    if (SecurityContextHolder.getContext().getAuthentication().getDetails()
  // instanceof String) {
  //                        token = (String)
  // SecurityContextHolder.getContext().getAuthentication().getDetails();
  //                    }
  //                    if (SecurityContextHolder.getContext().getAuthentication().getDetails()
  // instanceof Map) {
  //                        Map<String, String> details = (Map)
  // SecurityContextHolder.getContext().getAuthentication().getDetails();
  //                        token = details.get("accessToken");
  //                    } else {
  //                        OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails)
  //
  // SecurityContextHolder.getContext().getAuthentication().getDetails();
  //                        token = details.getTokenValue();
  //                    }
  //
  //                    requestTemplate.header("Authorization", "bearer " + token);
  //                }
  //
  //            }
  //        };
  //    }
}
