package com.sp.proxverse.config;

import com.sp.proxverse.config.license.LicenseCheckInterceptor;
import com.sp.proxverse.config.license.LicenseConfig;
import com.sp.proxverse.handler.ParameterInterceptor;
import com.sp.proxverse.handler.RequirePermissionInterceptor;
import javax.annotation.Resource;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.multipart.support.MultipartFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
  @Resource private TraceInterceptor traceInterceptor;

  @Resource private ParameterInterceptor parameterInterceptor;

  @Resource private RequirePermissionInterceptor requirePermissionInterceptor;

  @Resource @Lazy private LicenseCheckInterceptor licenseCheckInterceptor;

  @Resource private LicenseConfig licenseConfig;
  private static final String WBEJARS = "/webjars/**";
  private static final String CLASSPATH_STATIC = "classpath:/static/";
  private static final String ACTUATOR = "/actuator/**";
  private static final String SWAGGER_RESOURCES = "/swagger-resources/**";
  private static final String FAVICON_ICO = "/favicon.ico";
  private static final String V3 = "/v3/**";
  private static final String HTML = "/*.html";

  @Bean
  public SpGlobalControllerAdvice globalControllerAdvice() {
    return new SpGlobalControllerAdvice();
  }

  @Bean
  public FilterRegistrationBean<MultipartFilter> multipartFilter() {

    FilterRegistrationBean<MultipartFilter> filterRegistrationBean = new FilterRegistrationBean<>();
    filterRegistrationBean.setFilter(new MultipartFilter());
    filterRegistrationBean.addUrlPatterns("/file/importFile", "/file/fileUpload");
    // 设置 filter 的执行顺序在第一位
    filterRegistrationBean.setOrder(1);
    return filterRegistrationBean;
  }

  @InitBinder
  public void initBinder(WebDataBinder binder) {
    binder.registerCustomEditor(String.class, new StringTrimmerEditor(true));
  }

  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
    registry
        .addResourceHandler("swagger-ui.html")
        .addResourceLocations("classpath:/META-INF/resources/");
    registry
        .addResourceHandler(WBEJARS)
        .addResourceLocations("classpath:/META-INF/resources/webjars/");
    registry
        .addResourceHandler("chk.html")
        .addResourceLocations(CLASSPATH_STATIC, "classpath:/templates/");
    registry
        .addResourceHandler("*.html")
        .addResourceLocations(CLASSPATH_STATIC, "classpath:/templates/");
    registry.addResourceHandler("/*.txt").addResourceLocations(CLASSPATH_STATIC);
    registry
        .addResourceHandler("/*.png", "/*.jpg", "/*.jpeg")
        .addResourceLocations(CLASSPATH_STATIC);
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {

    registry
        .addInterceptor(traceInterceptor)
        .addPathPatterns("/**")
        .excludePathPatterns(ACTUATOR, SWAGGER_RESOURCES, FAVICON_ICO, WBEJARS, V3, HTML);
    registry
        .addInterceptor(parameterInterceptor)
        .addPathPatterns("/**")
        .excludePathPatterns(
            ACTUATOR, SWAGGER_RESOURCES, FAVICON_ICO, WBEJARS, V3, HTML, "/sp-engine/login");
    if (licenseConfig.isLicenseEnabled()) {
      registry
          .addInterceptor(licenseCheckInterceptor)
          .addPathPatterns("/**")
          .excludePathPatterns(
              "/license/**",
              "/admin/signOut",
              "/admin/selectById",
              ACTUATOR,
              SWAGGER_RESOURCES,
              FAVICON_ICO,
              WBEJARS,
              V3,
              HTML,
              "/admin/checkUserSource",
              "/sp-engine/getCommonConfig",
              "/api/oauth2/*",
              "/admin/getCompanyConfigInfo");
    }
    registry
        .addInterceptor(requirePermissionInterceptor)
        .addPathPatterns(
            "/sp-engine/**", "/data-merge/**", "/sp-process/**", "/sp-signal/**", "/admin/**")
        .excludePathPatterns(
            "/license/**",
            "/admin/login",
            "/admin/otherLoginInfo",
            "/admin/sapLogin",
            "/sp-engine/getCommonConfig",
            "/admin/kickOutAllUsers",
            "/admin/signOut",
            "/admin/checkUserSource",
            "/admin/loginByAuthorizationCode",
            "/admin/loginBySecureToken",
            "/admin/getUserPermissionList",
            "/sp-engine/getVersify",
            "/admin/getCompanyConfigInfo",
            "/sp-engine/jdbc/pql/evaluation");
  }
}
