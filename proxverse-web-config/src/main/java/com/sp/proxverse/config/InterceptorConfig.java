package com.sp.proxverse.config;

import com.sp.proxverse.config.license.LicenseCheckInterceptor;
import com.sp.proxverse.handler.ParameterInterceptor;
import com.sp.proxverse.handler.RequirePermissionInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class InterceptorConfig {

  @Bean
  public ParameterInterceptor parameterInterceptor() {
    return new ParameterInterceptor();
  }

  @Bean
  public RequirePermissionInterceptor requirePermissionInterceptor() {
    return new RequirePermissionInterceptor();
  }

  @Bean
  public LicenseCheckInterceptor licenseCheckInterceptor() {
    return new LicenseCheckInterceptor();
  }
}
