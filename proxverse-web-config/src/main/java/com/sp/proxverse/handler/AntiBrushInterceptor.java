package com.sp.proxverse.handler;
//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                            O\ = /O
//                        ____/`---'\____
//                      .   ' \\| |// `.
//                       / \\||| 1 |||// \
//                     / _||||| -9- |||||- \
//                       | | \\\ 9 /// | |
//                     | \_| ''\-1-/'' | |
//                      \ .-\__ `0` ___/-. /
//                   ___`. .' /--9--\ `. . __
//                ."" '< `.___\_<3>_/___.' >'"".
//               | | : `- \`.;`\ 0 /`;.`/ - ` : | |
//                 \ \ `-. \_ __\ /__ _/ .-` / /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//
//         .............................................
//                  佛祖保佑                  永无BUG

import com.sp.proxverse.service.CacheUtil;
import com.sp.proxverse.spdatamerge.annotation.AntiBrush;
import com.sp.proxverse.utils.ResponseUtils;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

public class AntiBrushInterceptor implements HandlerInterceptor {

  private static final Logger log = LoggerFactory.getLogger(AntiBrushInterceptor.class);

  @Autowired private CacheUtil cacheUtil;

  @Override
  public boolean preHandle(
      HttpServletRequest request, HttpServletResponse response, Object handler) {
    if (handler.getClass().isAssignableFrom(HandlerMethod.class)) {
      HandlerMethod handlerMethod = (HandlerMethod) handler;
      // 如果没有@AntiBrush，直接return true
      AntiBrush antiBrush = handlerMethod.getMethod().getDeclaredAnnotation(AntiBrush.class);
      if (Objects.isNull(antiBrush)) {
        return true;
      }

      String sessionId = request.getSession().getId();
      if (StringUtils.isBlank(sessionId)) {
        return true;
      }
      Object o = cacheUtil.get(sessionId);
      if (Objects.isNull(o)) {
        cacheUtil.set(sessionId, true, 1500, TimeUnit.MILLISECONDS);
        return true;
      } else {
        log.warn("操作过于频繁,key={}", sessionId);
        ResponseUtils.writeJson(response, "{\"code\":-1,\"msg\":\"您操作的太频繁了\"}");
        return false;
      }
    }
    return true;
  }
}
