package com.sp.proxverse.config.oauth2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;

/**
 * <AUTHOR>
 * @create 2022-04-25 4:28 下午
 */

@Configuration
@EnableResourceServer
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {
    @Autowired
    OauthExceptionEntryPointConfig oauthAuthenticationEntryPoint;

    @Autowired
    CustomDefaultAccessTokenConverter customDefaultAccessTokenConverter;

    @Autowired
    RemoteTokenServices remoteTokenServices;

    @Override
    public void configure(HttpSecurity http) throws Exception {
      http
          .csrf().disable()
          .exceptionHandling()
          .and()
          .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
          .and()
          .authorizeRequests()
          //下边的路径放行
          .antMatchers(
              //swagger界面接口放行
              "/v2/api-docs",
              "/doc.html",
              "/swagger-ui.html",
              "/course/coursebase/**",
              "/webjars/**",
              "/hello/**",
              "/swagger-resources/**",
              "/v3/**",
              "/sp-data/insert",
              "/sp-data/queryDataByIncre",
              "/sp-data/buildQueryDbSqlNoFilter",
              "/sp-data/delete",
              "/sp-data/selectField",
              "/sp-data/queryByCaseIdNoFilter",
              "/sp-data/saveJdbcConnector",
              "/sp-data/getLastByUpdateTimeField",
              "/sp-data/deleteConnector",
              "/sp-data/queryLimitBySource",
              "/sp-data/queryKpiValueById",
              "/sp-data/pullJdbcData",
              "/sp-data/preProcess",
              "/sp-spark/search",
              "/sp-spark/insert",
              "/sp-spark/pullJdbcData",
              "/sp-spark/preProcess",
              "/sp-engine/saveWebsite",
              "/sp-engine/getVersify",
              "/sp-engine/testMq",
              "/sp-job/test",
              "/data-model/queryTenantId",
              "/data-model/getColumnInfoById",
              "/admin/selectTenantIdByToken",
              "/admin/sgFPFcossxYsQhAN",
              "/admin/getUsernameByTenantId",
              "/admin/getCompanyInfo",
              "/license/list",
              "/license/activate",
              "/license/install",
              "/api/spark/**",
              "/api/v1/applications/**",
              "/oauth2/authorization/*",
              "/**",
              "/static/**"
          ).permitAll()
          //拦截所有请求  可以按照角色，权限等进行拦截 GrantedAuthority
          .antMatchers("/**").hasAuthority("USER");
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        // 配置资源 ID
        remoteTokenServices.setAccessTokenConverter(customDefaultAccessTokenConverter);
        // 配置资源 ID
        resources.resourceId("core");
        resources.authenticationEntryPoint(oauthAuthenticationEntryPoint);
        resources.tokenServices(remoteTokenServices);
    }

    @Configuration
    protected static class TokenInfoServicesConfiguration {
        private final ResourceServerProperties resource;

        protected TokenInfoServicesConfiguration(ResourceServerProperties resource) {
            this.resource = resource;
        }

        @Bean
        public RemoteTokenServices remoteTokenServices() {
            RemoteTokenServices services = new RemoteTokenServices();
            services.setCheckTokenEndpointUrl(this.resource.getTokenInfoUri());
            services.setClientId(this.resource.getClientId());
            services.setClientSecret(this.resource.getClientSecret());
            return services;
        }
    }


}
