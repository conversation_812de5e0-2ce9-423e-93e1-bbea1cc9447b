/*
 * This file was generated by the Gradle 'init' task.
 */

/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
  id 'scala'
}
repositories {
  mavenLocal()
  maven { url 'http://121.37.155.119:8081/repository/maven-public/' }
  maven { url 'https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/' }
}
sourceSets {
  main {
    scala {
      srcDirs = ['src/main/scala', 'src/main/java', 'src/saToken']
    }
    java {
      srcDirs = []
    }
  }

  test {
    scala {
      srcDirs = ['src/test/scala', 'src/test/java']
    }
    java {
      srcDirs = []
    }
  }
}

dependencies {
  implementation 'com.larksuite.oapi:oapi-sdk:2.3.4'
  implementation 'com.github.ben-manes.caffeine:caffeine:2.9.3'
  implementation 'org.springframework.boot:spring-boot-starter-web:2.3.2.RELEASE'
  implementation project(':proxverse-service-interface')
  implementation 'com.alibaba:druid:1.1.21'
  implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.7'
  implementation 'org.apache.httpcomponents:httpclient:4.5.13'
  implementation 'org.apache.commons:commons-lang3:3.12.0'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'com.squareup.okhttp3:okhttp:3.14.9'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis:2.3.2.RELEASE'
  implementation project(':proxverse-common')
  implementation 'org.springframework:spring-webflux:5.2.8.RELEASE'
  implementation 'com.fasterxml.jackson.core:jackson-core:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4'
  implementation 'com.fasterxml.jackson.module:jackson-module-scala_2.12:2.13.0'
  implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.13.3'
  implementation 'cn.dev33:sa-token-spring-boot-starter:1.37.0'
  implementation 'org.flywaydb:flyway-mysql:8.5.7'
  implementation 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'org.apache.spark:spark-core_2.12:3.3.1-prx-0.0.2'
  implementation 'org.apache.spark:spark-sql_2.12:3.3.1-prx-0.0.2'
  implementation 'org.apache.spark:spark-hive_2.12:3.3.1-prx-0.0.2'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'com.baomidou:mybatis-plus-core:3.5.7'
  implementation 'com.baomidou:mybatis-plus-generator:3.5.7'
  implementation 'com.sp:proxverse-pql:1.0.0'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:2.3.2.RELEASE'
  implementation(files("${projectDir}/src/main/resources/lib/licmgr-keymgr-1.0-standalone.jar"))
  implementation(files("${projectDir}/src/main/resources/lib/licmgr-keygen-1.0-standalone.jar"))
}

description = 'proxverse-web-config'


dependencies {
  implementation 'com.larksuite.oapi:oapi-sdk:2.3.4'
  implementation 'com.github.ben-manes.caffeine:caffeine:2.9.3'
  implementation 'org.springframework.boot:spring-boot-starter-web:2.3.2.RELEASE'
  implementation project(':proxverse-service-interface')
  implementation 'com.alibaba:druid:1.1.21'
  implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.7'
  implementation 'org.apache.httpcomponents:httpclient:4.5.13'
  implementation 'org.apache.commons:commons-lang3:3.12.0'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'com.squareup.okhttp3:okhttp:3.14.9'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis:2.3.2.RELEASE'
  implementation project(':proxverse-common')
  implementation 'org.springframework:spring-webflux:5.2.8.RELEASE'
  implementation 'com.fasterxml.jackson.core:jackson-core:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4'
  implementation 'com.fasterxml.jackson.module:jackson-module-scala_2.12:2.13.0'
  implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.13.3'
  implementation 'cn.dev33:sa-token-spring-boot-starter:1.37.0'
  implementation 'org.flywaydb:flyway-mysql:8.5.7'
  implementation 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'org.apache.spark:spark-core_2.12:3.3.1-prx-0.0.2'
  implementation 'org.apache.spark:spark-sql_2.12:3.3.1-prx-0.0.2'
  implementation 'org.apache.spark:spark-hive_2.12:3.3.1-prx-0.0.2'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'com.baomidou:mybatis-plus-core:3.5.7'
  implementation 'com.baomidou:mybatis-plus-generator:3.5.7'
  implementation 'com.sp:proxverse-pql:1.0.0'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:2.3.2.RELEASE'
  implementation 'com.sp:keymgr:1.0.0'
  implementation 'com.sp:keygen:1.0.0'
}

description = 'proxverse-web-config'
