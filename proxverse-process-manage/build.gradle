/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
  id 'scala'
}
repositories {
  mavenLocal()
  maven { url 'http://121.37.155.119:8081/repository/maven-public/' }
  maven { url 'https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/' }
}
sourceSets {
  main {
    scala {
      srcDirs = ['src/main/scala', 'src/main/java']
    }
    java {
      srcDirs = []
    }
  }

  test {
    scala {
      srcDirs = ['src/test/scala', 'src/test/java']
    }
    java {
      srcDirs = []
    }
  }
}

dependencies {
    implementation project(':proxverse-web-config')
    implementation project(':proxverse-service-interface')
    implementation project(':proxverse-data-core')
    implementation project(':proxverse-oauth2')
    implementation project(':proxverse-job')
    implementation 'org.mapstruct:mapstruct:1.4.1.Final'
    implementation 'org.mapstruct:mapstruct-processor:1.4.1.Final'
    implementation 'org.slf4j:log4j-over-slf4j:1.7.30'
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.7'
    implementation 'io.springfox:springfox-swagger2:2.9.2'
    implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
    implementation 'io.springfox:springfox-swagger-ui:2.9.2'
    implementation 'mysql:mysql-connector-java:8.0.28'
    implementation 'com.alibaba:druid:1.1.21'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc:2.3.2.RELEASE'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'org.hibernate:hibernate-validator:6.2.0.Final'
    implementation 'net.logstash.logback:logstash-logback-encoder:5.2'
    implementation 'com.squareup.okhttp3:okhttp:3.14.9'
    implementation 'com.huaweicloud:esdk-obs-java-bundle:3.21.8'
    implementation project(':proxverse-common')
    implementation project(':proxverse-data-merge')
    implementation 'io.netty:netty-all:4.1.86.Final'
    implementation 'io.netty:netty-buffer:4.1.86.Final'
    implementation 'io.netty:netty-common:4.1.86.Final'
    implementation 'dom4j:dom4j:1.6.1'
    implementation 'org.camunda.bpm.model:camunda-bpmn-model:7.17.0'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.13.4'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4'
    implementation 'com.fasterxml.jackson.module:jackson-module-scala_2.12:2.13.0'
    implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.13.3'
    implementation 'io.process.analytics.tools.bpmn:bpmn-layout-generator:0.1.5'
    implementation project(':proxverse-data')
    implementation 'org.scala-lang:scala-library:2.12.15'
    implementation 'cn.hutool:hutool-all:5.3.2'
    testImplementation 'org.springframework.boot:spring-boot-starter-test:2.3.2.RELEASE'
    compileOnly 'org.apache.commons:commons-lang3:3.12.0'
    compileOnly 'org.projectlombok:lombok:1.18.20'
  implementation 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'com.baomidou:mybatis-plus-core:3.5.7'
  implementation 'com.baomidou:mybatis-plus-generator:3.5.7'
  implementation 'com.sp:proxverse-pql:1.0.0'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:2.3.2.RELEASE'
}

description = 'proxverse-process-manage'
