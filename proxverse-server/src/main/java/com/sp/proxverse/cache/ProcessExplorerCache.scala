package com.sp.proxverse.cache

import com.google.common.cache._
import com.sp.proxverse.common.model.vo.processExplorer.ProcessExplorerRes
import java.util.concurrent.atomic.AtomicLong
import java.util.stream.Collectors
import org.apache.spark.cache.AbstractCustomCache
import org.apache.spark.internal.Logging
import org.apache.spark.sql.PQLConf.PQL_PLAN_CACHE_ENABLED
import org.apache.spark.sql.internal.SQLConf
import org.apache.spark.util.SizeEstimator

case class ProcessExplorerCacheKey (modelId: Int, paramHash: Long)

class ProcessExplorerCache (cacheBytes: Long,cacheName: String)
    extends AbstractCustomCache[ProcessExplorerCacheKey, ProcessExplorerRes](cacheName)
    with Logging {

  private val localFunction = new ThreadLocal[() => ProcessExplorerRes]

  case class CachedRows(result: ProcessExplorerRes) {
    lazy val weighted: Long =
      if (result.getEventNodes == null
          || result.getEventNodes.isEmpty
          || result.getLineNodes == null
          || result.getLineNodes.isEmpty) 0
      else {
        val eventNodeSize = result.getEventNodes.size()
        val lineNodeSize = result.getLineNodes.size()
        SizeEstimator
          .estimate(result.getEventNodes.get(eventNodeSize - 1))
          .toInt * eventNodeSize + SizeEstimator
          .estimate(result.getLineNodes.get(lineNodeSize - 1))
          .toInt * lineNodeSize
      }
  }

  val resultCache: LoadingCache[ProcessExplorerCacheKey, CachedRows] = CacheBuilder
    .newBuilder()
    .maximumWeight(cacheBytes)
    .weigher(new Weigher[ProcessExplorerCacheKey, CachedRows]() {
      override def weigh(key: ProcessExplorerCacheKey, value: CachedRows): Int = {
        currentWeight.getAndAdd(value.weighted)
        value.weighted.toInt
      }
    })
    .removalListener((notification: RemovalNotification[ProcessExplorerCacheKey, CachedRows]) => {
      currentWeight.addAndGet(-notification.getValue.weighted)
    })
    .softValues()
    .build(new CacheLoader[ProcessExplorerCacheKey, CachedRows]() {
      def load(df: ProcessExplorerCacheKey): CachedRows = try {
        SQLConf.get.setConf(PQL_PLAN_CACHE_ENABLED, false)
        CachedRows(localFunction.get().apply())
      }
    })

  override def getCacheExecution(
      processExplorerCacheKey: ProcessExplorerCacheKey,
      func: () => ProcessExplorerRes): ProcessExplorerRes =
    try {
      localFunction.set(func)
      val result = resultCache(processExplorerCacheKey)
      result.result
    } finally {
      localFunction.set(null)
    }

  override def invalidate(modelId: Int): Unit = {
    resultCache.invalidateAll(
      resultCache
        .asMap()
        .keySet()
        .stream()
        .filter(key => key.modelId == modelId)
        .collect(Collectors.toList[ProcessExplorerCacheKey]))
  }
}
