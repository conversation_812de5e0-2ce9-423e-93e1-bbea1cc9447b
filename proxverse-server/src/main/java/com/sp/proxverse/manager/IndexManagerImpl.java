package com.sp.proxverse.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sp.proxverse.common.util.DataSourceUtils;
import com.sp.proxverse.common.util.IndexKeyBuilderUtil;
import com.sp.proxverse.mapper.model.index.ModelIndexService;
import com.sp.proxverse.model.index.ModelIndexEntity;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.PQLConf;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.internal.SQLConf;
import org.apache.spark.sql.pql.ModelCatalog;
import org.apache.spark.sql.pql.dict.EncodedColumnDictIndexReader;
import org.apache.spark.sql.pql.manager.CatalogManager;
import org.apache.spark.sql.pql.manager.IndexManager;
import org.apache.spark.sql.pql.model.IndexBuilder;
import org.apache.spark.sql.pql.model.ModelIndex;
import org.apache.spark.util.event.ModelRefreshEvent;
import org.apache.spark.util.event.ServerEnv;
import org.apache.spark.util.event.ServerListenerInterface;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class IndexManagerImpl implements IndexManager, InitializingBean {

  @Autowired ModelIndexService modelIndexService;

  @Autowired CatalogManager catalogManager;

  List<IndexBuilder> builders = new ArrayList<>();

  @Override
  public void afterPropertiesSet() {
    ServerEnv.registerSyncListener(new IndexListener());
  }

  public void registerIndexBuilder(IndexBuilder builder) {
    builders.add(builder);
  }

  @Override
  public void register(ModelIndex modelIndex) {
    List<ModelIndexEntity> modelIndexEntities =
        modelIndexService.list(
            new LambdaQueryWrapper<ModelIndexEntity>()
                .eq(ModelIndexEntity::getModelId, modelIndex.modelId()));

    String tableName = DataSourceUtils.ignoreVersionModelTableName(modelIndex.sparkTableName());
    if (modelIndexEntities.isEmpty()) {
      ModelIndexEntity build =
          ModelIndexEntity.builder()
              .indexName(modelIndex.indexName())
              .sparkTableName(tableName)
              .modelId(modelIndex.modelId())
              .build();
      modelIndexService.save(build);
    } else {
      ModelIndexEntity modelIndexEntity = modelIndexEntities.get(0);
      modelIndexService.update(
          new LambdaUpdateWrapper<ModelIndexEntity>()
              .eq(ModelIndexEntity::getModelId, modelIndexEntity.getModelId())
              .set(ModelIndexEntity::getIndexName, modelIndex.indexName())
              .set(ModelIndexEntity::getSparkTableName, tableName)
              .set(ModelIndexEntity::getCreateTime, LocalDateTime.now()));
    }
  }

  @Override
  public void cleanUp(ModelIndex modelId) {
    modelIndexService.removeById(modelId.modelId());
  }

  @Override
  public List<ModelIndex> listIndex(int modelId, int versionNumber) {
    if (SQLConf.get().getConfString("spark.sql.pql.index.disabled", "false").equals("true")) {
      return new ArrayList<>();
    }
    return listIndexInternal(IndexKeyBuilderUtil.makeIndexCacheKey(modelId, versionNumber));
  }

  private List<ModelIndex> listIndexInternal(String indexCache) {
    Integer modelId = IndexKeyBuilderUtil.extractModeIdByIndexCacheKey(indexCache);
    Integer versionNumber = IndexKeyBuilderUtil.extractVersionByIndexCacheKey(indexCache);
    List<ModelIndexEntity> list =
        modelIndexService
            .list(
                new LambdaQueryWrapper<ModelIndexEntity>()
                    .eq(ModelIndexEntity::getModelId, modelId))
            .stream()
            .filter(
                index ->
                    EncodedColumnDictIndexReader.isDictIndex(
                        index.getSparkTableName().toLowerCase()))
            .collect(Collectors.toList());

    ArrayList<ModelIndex> modelIndices = new ArrayList<>(list.size());
    for (ModelIndexEntity entity : list) {
      try {
        String tableName =
            DataSourceUtils.makeModelTableNameByFullTableName(
                entity.getSparkTableName().toLowerCase(), versionNumber);
        ModelIndex modelIndex =
            new ModelIndex(
                entity.getModelId(),
                entity.getIndexName(),
                tableName,
                () -> SparkSessionEnv.getSparkSession());
        modelIndices.add(modelIndex);
      } catch (Exception e) {
        log.error("Failed loading model index " + entity.toString(), e);
      }
    }
    return modelIndices;
  }

  class IndexListener implements ServerListenerInterface {

    @Override
    public void onModelRefresh(ModelRefreshEvent onModelRefresh) {
      if (PQLConf.encodedColDictEncodeWithModelBuild() && onModelRefresh.newModel() != null) {
        ModelCatalog modelCatalog =
            catalogManager.createCatalogByModelId(
                onModelRefresh.modeId(), onModelRefresh.versionNumber());
        for (IndexBuilder builder : builders) {
          builder.cleanUp(modelCatalog);
        }
        for (IndexBuilder builder : builders) {
          for (ModelIndex modelIndex : builder.buildIndexes(modelCatalog)) {
            if (modelIndex != null) {
              register(modelIndex);
            }
          }
        }
      }
    }
  }
}
