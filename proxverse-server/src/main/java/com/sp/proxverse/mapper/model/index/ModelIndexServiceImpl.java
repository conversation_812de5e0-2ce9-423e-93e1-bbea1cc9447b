package com.sp.proxverse.mapper.model.index;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.model.index.ModelIndexEntity;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@DS("engine")
@Primary
public class ModelIndexServiceImpl extends ServiceImpl<ModelIndexMapper, ModelIndexEntity>
    implements ModelIndexService {}
