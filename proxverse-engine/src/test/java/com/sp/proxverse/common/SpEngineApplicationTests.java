package com.sp.proxverse.common;

import com.sp.proxverse.common.model.oauth2.UserDetailsToken;
import com.sp.proxverse.common.model.vo.RelatedOutputVO;
import com.sp.proxverse.common.model.vo.request.RelatedRequest;
import com.sp.proxverse.common.util.FileUtil;
import com.sp.proxverse.common.util.FileUtils;
import com.sp.proxverse.engine.service.biz.ProcessTreeReadBizService;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
class SpEngineApplicationTests {

  @Autowired private ProcessTreeReadBizService processTreeReadBizService;

  @Autowired private UserInfoUtil userInfoUtil;

  @Test
  void contextLoads() {
    // comment empty
  }

  @Test
  void test06() {
    RelatedRequest request = new RelatedRequest();
    request.setTopicSheetId(1);
    request.setEvent("试驾");
    //        fileIdList.setVariantList(Lists.newArrayList("车型"));
    List<RelatedOutputVO> ret = processTreeReadBizService.queryRootCauseCalResult(request);
    log.info("ret:{}", ret);
  }

  void testRemoveStringAttr() {

    String fileCode =
        " <semantic:task id=\"id8b602f06-4aa6-4a3e-8f8f-aa7cd02a6f2e\" name=\"C\" x=\"640\">";
    StringBuilder fileCodeBuilder = new StringBuilder();
    String[] splits = fileCode.split("x=\"");
    for (int i = 0; i < splits.length; i++) {
      if (i == 0) {
        fileCodeBuilder.append(splits[i]);
      }
      int i1 = splits[i].indexOf("\"");
      fileCodeBuilder.append(splits[i].substring(i1 + 1));
    }

    log.info("fileCodeBuilder:{}", fileCodeBuilder);
  }

  @Test
  void loadClass() throws IOException {
    InputStream resourceAsStream =
        Thread.currentThread()
            .getContextClassLoader()
            .getResourceAsStream("./python/convertVariantsToBpmn.py");
    byte[] bytes = new byte[0];
    bytes = new byte[resourceAsStream.available()];
    resourceAsStream.read(bytes);
    String resourceAsString = new String(bytes);

    File file = FileUtil.createFile(FileUtils.getTempPath(), "convertVariantsToBpmn.py");
    FileUtil.writeToFile(file, resourceAsString);
    UserDetailsToken userInfo = userInfoUtil.getUserInfo();
    log.info(file.getPath());
  }
}
