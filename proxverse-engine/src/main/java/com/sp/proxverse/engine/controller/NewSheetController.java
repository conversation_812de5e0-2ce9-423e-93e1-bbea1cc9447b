package com.sp.proxverse.engine.controller;

import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.po.ComponentConfigPO;
import com.sp.proxverse.common.model.vo.request.UpdateNewSheetReq;
import com.sp.proxverse.common.model.vo.sheet.UpdateComponentConfigReq;
import com.sp.proxverse.engine.service.biz.NewsheetBizService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2022-03-02 9:59 上午
 */
@Api(value = "newSheet相关接口", tags = "newSheet相关接口")
@RequestMapping(value = "/sp-engine")
@RestController
public class NewSheetController {
  @Autowired TopicSheetNewService topicSheetNewService;

  @Autowired private NewsheetBizService newsheetBizService;

  // 前端用来更新组建位置信息
  @PostMapping(value = "updateNewSheetInfo")
  @ApiOperation(notes = "更新newSheet信息", value = "更新newSheetInfo")
  public Response<Object> updateNewSheetInfo(@RequestBody UpdateNewSheetReq updateNewSheetReq) {

    String result = topicSheetNewService.updateNewSheetInfo(updateNewSheetReq);
    return Response.success(result);
  }

  @GetMapping(value = "selectNewSheetInfo")
  @ApiOperation(notes = "查找selectNewSheetInfo", value = "查找selectNewSheetInfo")
  public Response<Object> selectNewSheetInfo(
      @RequestParam(value = "topicSheetId") Integer topicSheetId) {

    String result = topicSheetNewService.selectNewSheetInfo(topicSheetId);
    return Response.success(result);
  }

  // 前端用来更新组建位置信息
  @PostMapping(value = "saveComponentConfig")
  @ApiOperation(notes = "保存组件信息", value = "保存组件信息")
  public Response<Boolean> saveComponentConfig(@RequestBody UpdateComponentConfigReq request) {

    return Response.success(newsheetBizService.saveComponentConfig(request));
  }

  @PostMapping(value = "getComponentConfig")
  @ApiOperation(notes = "获取组件信息", value = "获取组件信息")
  public Response<ComponentConfigPO> getComponentConfig(
      @RequestBody UpdateComponentConfigReq request) {

    return Response.success(newsheetBizService.getComponentConfig(request));
  }
}
