package com.sp.proxverse.engine.async;

import com.sp.proxverse.common.model.dto.VariantDTO;
import com.sp.proxverse.data.util.bpmn.BaseElement;
import com.sp.proxverse.data.util.bpmn.Event;
import com.sp.proxverse.data.util.bpmn.model.EndEventImpl;
import com.sp.proxverse.data.util.bpmn.model.NullEventImpl;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.Stack;
import java.util.concurrent.Future;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2022-08-30 2:11 下午
 */
@Slf4j
@Component
@Scope(value = "singleton")
public class ConformanceCheckAsync {

  /**
   * 解析行数据
   *
   * @param rootBaseElements 跟节点
   * @param variantDTOS 变体
   * @return
   */
  @Async
  public Future<Map<String, Boolean>> bpmnCheck(
      List<BaseElement> rootBaseElements,
      List<VariantDTO> variantDTOS,
      Map<String, Set<String>> incomingEventIdMap) {

    Map<String, Boolean> hashMap = new HashMap<>();
    for (VariantDTO variantDTO : variantDTOS) {
      Map<String, Set<String>> incomingEventIdMapCopy = new HashMap<>();
      for (Map.Entry<String, Set<String>> stringSetEntry : incomingEventIdMap.entrySet()) {
        Set<String> objects = new HashSet<>();
        objects.addAll(stringSetEntry.getValue());
        incomingEventIdMapCopy.put(stringSetEntry.getKey(), objects);
      }

      String variant = variantDTO.getVariant();
      if (variant == null) {
        continue;
      }
      String[] split = variant.split(",");

      if (split.length == 0) {
        continue;
      }
      Queue<String> queue = new LinkedList<String>();
      for (String s : split) {
        queue.offer(s);
      }
      boolean checkResult = false;
      for (BaseElement rootBaseElement : rootBaseElements) {
        checkResult = doCheckBpmn(rootBaseElement, queue, incomingEventIdMapCopy);
        if (checkResult) {
          break;
        }
      }
      hashMap.put(variantDTO.getVariant(), checkResult);
    }

    Future<Map<String, Boolean>> future = new AsyncResult<>(hashMap);
    return future;
  }

  public boolean doCheckBpmn(
      BaseElement baseElement, Queue<String> queue, Map<String, Set<String>> incomingEventIdMap) {
    if (baseElement == null) {
      return false;
    }
    Event event = (Event) baseElement;

    Stack<BaseElement> baseElements = new Stack<>();
    while (true) {
      event = (Event) event.getCheckBpmnByName(queue, incomingEventIdMap, baseElements);
      if (event == null) {
        if (queue.isEmpty()) {
          return true;
        } else {
          return false;
        }
      }
      if (event instanceof EndEventImpl && queue.isEmpty()) {
        return true;
      }
      if (event instanceof NullEventImpl) {
        return false;
      }
    }

    //
    //        if (baseElement instanceof StartEventImpl || baseElement instanceof
    // ParallelGatewayImpl) {
    //            BaseElement oneOutGoing = ((EventAbstract)
    // baseElement).getCheckBpmnByName(queue.element());
    //            return doCheckBpmn(oneOutGoing, queue, incomingEventIdMap);
    //        }
    //        String remove = queue.poll();
    //        if (baseElement != null && Objects.equals(baseElement.getName(), remove)) {
    //
    //            // 事件为空时，校验该节点是否时结束节点
    //            if (queue.isEmpty()) {
    //                if (((Event) baseElement).checkLastOutGoingIsEnd()) {
    //                    return true;
    //                }
    //                return false;
    //            }
    //            BaseElement oneOutGoing = ((EventAbstract)
    // baseElement).getCheckBpmnByName(queue.element());
    //            return doCheckBpmn(oneOutGoing, queue, incomingEventIdMap);
    //        } else {
    //            return false;
    //        }
  }
}
