package com.sp.proxverse.engine.util;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 校验分析相关
 *
 * <AUTHOR>
 * @create 2022-06-02 12:20 下午
 */
@Slf4j
public class CheckUtil {

  public static List<Object> checkFormat(List<String> lists) {
    List<Object> result = new ArrayList<>();

    for (String list : lists) {
      if (StringUtils.isBlank(list)) {
        result.add(list);
        continue;
      }
      try {
        // 分析时间
        DateTime parse = DateUtil.parse(list);
        result.add(parse);
      } catch (DateException e) {
        try {
          // 分析数字
          result.add(Double.parseDouble(list));
        } catch (Exception ee) {
          result.add(list);
        }
      }
    }
    return result;
  }
}
