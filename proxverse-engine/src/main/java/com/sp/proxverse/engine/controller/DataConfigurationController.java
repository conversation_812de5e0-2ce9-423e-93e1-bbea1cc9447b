package com.sp.proxverse.engine.controller;

import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.po.WaterMarkConfigPO;
import com.sp.proxverse.common.model.vo.SupportedFunctionVO;
import com.sp.proxverse.engine.service.biz.DataConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/11/22 11:01
 */
@Slf4j
@Api(value = "数据配置控制器", tags = "系统数据配置")
@RequestMapping(value = "/sp-engine")
@RestController
public class DataConfigurationController {

  @Autowired private DataConfigurationService dataConfigurationService;

  @ApiOperation(value = "获取系统数据配置", notes = "获取系统数据配置")
  @GetMapping(value = "/getDataConfigurationMap")
  public Response<String> getDataConfigurationMap() {
    return Response.success(dataConfigurationService.getDataConfigurationMap());
  }

  @ApiOperation(value = "获取系统数据配置", notes = "获取系统数据配置")
  @GetMapping(value = "/getCommonConfig")
  public Response<String> getCommonConfig() {
    return Response.success(dataConfigurationService.getPublicConfigurationMap());
  }

  @ApiOperation(value = "get supported funcs list", notes = "get supported funcs list")
  @GetMapping(value = "/supportedFunctions")
  public Response<List<SupportedFunctionVO>> getSupportedFunctions() {
    return Response.success(dataConfigurationService.supportedFunctionsFromConfFile());
  }

  @ApiOperation(value = "获取水印配置", notes = "获取水印配置")
  @GetMapping(value = "/getWaterMarkConfig")
  public Response<WaterMarkConfigPO> getWaterMarkConfig() {
    return Response.success(dataConfigurationService.getWaterMarkConfig());
  }

  @ApiOperation(value = "保存、修改水印配置", notes = "保存、修改水印配置")
  @PostMapping(value = "/saveWaterMarkConfig")
  public Response<Integer> saveWaterMarkConfig(@RequestBody WaterMarkConfigPO request) {
    return Response.success(dataConfigurationService.saveWaterMarkConfig(request));
  }
}
