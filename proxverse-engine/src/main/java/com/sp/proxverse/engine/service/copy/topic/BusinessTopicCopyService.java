package com.sp.proxverse.engine.service.copy.topic;

import com.sp.proxverse.common.model.dict.BusinessKpiTypeEnum;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.engine.service.copy.BusinessCopyService;
import com.sp.proxverse.engine.service.copy.BusinessService;
import com.sp.proxverse.engine.service.copy.CopyService;
import com.sp.proxverse.engine.service.copy.CopySheetService;
import com.sp.proxverse.engine.service.copy.entity.Business;
import com.sp.proxverse.engine.service.copy.entity.CopyParent;
import com.sp.proxverse.engine.service.copy.entity.TopicSheet;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.SheetService;
import com.sp.proxverse.interfaces.dao.service.TopicFilterService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BusinessTopicCopyService extends BusinessCopyService<Business> {

  @Autowired private TopicSheetService topicSheetService;

  @Autowired private BusinessService businessService;

  @Autowired private SheetService sheetService;

  @Autowired private BusinessTopicDataService businessTopicDataService;

  @Autowired private TopicFilterService topicFilterService;

  @Override
  protected CopyParent copy(Business business) {

    Integer newTopicId =
        businessService.saveTopicBaseData(
            business.getTopicId(),
            business.getParentId(),
            BusinessKpiTypeEnum.PARAM_SET.getValue(),
            business.getFromParent());

    List<TopicSheetPO> sheetList = topicSheetService.getList(business.getTopicId());

    CopyService<TopicSheet> copyService =
        new CopySheetService(
            sheetService, businessTopicDataService, topicSheetService, topicFilterService);

    for (TopicSheetPO sheet : sheetList) {
      TopicSheet topicSheet = new TopicSheet();
      topicSheet.setSheetId(sheet.getId());
      topicSheet.setTopicId(newTopicId);
      copyService.copy(topicSheet);
    }

    return CopyParent.builder().topicId(newTopicId).build();
  }
}
