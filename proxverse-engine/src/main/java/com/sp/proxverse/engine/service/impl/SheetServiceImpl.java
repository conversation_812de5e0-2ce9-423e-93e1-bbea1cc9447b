package com.sp.proxverse.engine.service.impl;

import static com.sp.proxverse.common.model.enums.TimeUnitEnum.getTimeUnitEnumByName;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.prx.service.conformance.ConformanceServiceScala;
import com.prx.service.model.ModelDescFactory;
import com.prx.service.overview.ProcessOverview;
import com.prx.service.page.ITopicMultiProcessService;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.DataException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.mapper.TopicSnapshotLogMapper;
import com.sp.proxverse.common.model.TopicMultiProcessDTO;
import com.sp.proxverse.common.model.dict.BookMarkTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileTypeEnum;
import com.sp.proxverse.common.model.dict.KpiFromKnowledgeEnum;
import com.sp.proxverse.common.model.dict.KpiSaveTypeEnum;
import com.sp.proxverse.common.model.dict.TopicFilterBizTypeEnum;
import com.sp.proxverse.common.model.dict.TopicFilterTypeEnum;
import com.sp.proxverse.common.model.dict.kpi.VariableKpiRuleEnum;
import com.sp.proxverse.common.model.dict.prehandler.PreHandlerVariableEnum;
import com.sp.proxverse.common.model.dto.DataModelFileDTO;
import com.sp.proxverse.common.model.dto.KpiParserParamDTO;
import com.sp.proxverse.common.model.dto.SheetKpiDTO;
import com.sp.proxverse.common.model.dto.TopicFileDTO;
import com.sp.proxverse.common.model.dto.domain.Admin;
import com.sp.proxverse.common.model.dto.domain.DataAuthority;
import com.sp.proxverse.common.model.dto.domain.UserDataAuthority;
import com.sp.proxverse.common.model.dto.processoverview.DimensionAndKpi;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.dto.topicSheet.CopyIdDto;
import com.sp.proxverse.common.model.enums.ProcessChartValueTypeEnum;
import com.sp.proxverse.common.model.enums.TimeUnitEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.oauth2.UserDetailsToken;
import com.sp.proxverse.common.model.page.PageRespDTO;
import com.sp.proxverse.common.model.po.*;
import com.sp.proxverse.common.model.vo.TopicSheetKpiOutputVO;
import com.sp.proxverse.common.model.vo.processExplorer.ProcessExplorerRes;
import com.sp.proxverse.common.model.vo.request.KpiVariableSortDTO;
import com.sp.proxverse.common.model.vo.request.ParseKpiDTO;
import com.sp.proxverse.common.model.vo.request.QuerySheetCauseRequest;
import com.sp.proxverse.common.model.vo.request.RootCauseSaveRequest;
import com.sp.proxverse.common.model.vo.request.TopicSheetDataRequest;
import com.sp.proxverse.common.model.vo.request.VariableSaveSubRequest;
import com.sp.proxverse.common.model.vo.request.processAi.CancelSheetReleaseReq;
import com.sp.proxverse.common.model.vo.request.processAi.ContrastFilterResVo;
import com.sp.proxverse.common.model.vo.request.processAi.ContrastFilterSwapReqVo;
import com.sp.proxverse.common.model.vo.request.processAi.ContrastProcessTreeReqVo;
import com.sp.proxverse.common.model.vo.request.processAi.GetKpiStatisticsResultReqVo;
import com.sp.proxverse.common.model.vo.request.processAi.GetKpiStatisticsResultVo;
import com.sp.proxverse.common.model.vo.request.processAi.KpiStatisticsResultResVo;
import com.sp.proxverse.common.model.vo.request.processAi.RemoveLockReq;
import com.sp.proxverse.common.model.vo.request.processAi.SheetReleaseReqVo;
import com.sp.proxverse.common.model.vo.request.processAi.ThroughputTimeListReqVo;
import com.sp.proxverse.common.model.vo.request.processAi.ThroughputTimeResVo;
import com.sp.proxverse.common.model.vo.request.processAi.TryLockEditLockReq;
import com.sp.proxverse.common.model.vo.request.processAi.TryLockEditLockRes;
import com.sp.proxverse.common.model.vo.request.processAi.UpdateContrastFilterReqVo;
import com.sp.proxverse.common.model.vo.sheet.SnapshotLogResVo;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.engine.service.ProcessTreeService;
import com.sp.proxverse.engine.service.biz.ProcessTreeReadBizService;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.engine.service.copy.BusinessService;
import com.sp.proxverse.interfaces.dao.impl.ComponentEditLockServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TopicSnapshotLogServiceImpl;
import com.sp.proxverse.interfaces.dao.service.*;
import com.sp.proxverse.interfaces.service.data.DataAPIService;
import com.sp.proxverse.interfaces.service.data.FilterService;
import com.sp.proxverse.interfaces.service.data.pql.PQLParameterManager;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import com.sp.proxverse.interfaces.service.data.pql.TopicFilterManager;
import com.sp.proxverse.oauth2.service.permission.UserInfoFactory;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @create 2022-04-08 11:32 上午
 */
@Slf4j
@Service
@Primary
public class SheetServiceImpl implements SheetService {

  @Value("${prx.components.editLock.components:300}")
  private Long editLockComponents;

  @Autowired private ComponentEditLockServiceImpl componentEditLockService;

  @Autowired private BusinessTopicService businessTopicService;

  @Autowired private BusinessService businessService;

  @Autowired private TopicSheetService topicSheetService;

  @Autowired private SheetMenuService sheetMenuService;

  @Autowired TopicSheetNewService topicSheetNewService;

  @Autowired TopicSheetKpiService topicSheetKpiService;

  @Autowired TopicSheetConformanceService topicSheetConformanceService;

  @Autowired TopicSheetVariableService topicSheetVariableService;

  @Autowired TopicSheetCauseService topicSheetCauseService;

  @Autowired KpiService kpiService;

  @Autowired RootCauseService rootCauseService;

  @Autowired FileVariantService fileVariantService;

  @Autowired TopicSnapshotLogServiceImpl topicSnapshotLogService;

  @Autowired TopicSnapshotLogMapper topicSnapshotLogMapper;

  @Autowired BusinessTopicDataService businessTopicDataService;

  @Autowired CauseKpiService causeKpiService;

  @Autowired ProcessTreeWriteBizService processTreeWriteBizService;

  @Autowired CauseRelatedVariablesService causeRelatedVariablesService;

  @Autowired private SheetComponentService sheetComponentService;

  @Autowired ProcessTreeReadBizService processTreeReadBizService;

  @Autowired ProcessTreeService processTreeService;

  @Autowired private IAdminService adminService;

  @Autowired DataAPIService dataApiClient;

  @Autowired private ModelDescFactory modelDescFactory;

  @Autowired TopicFilterService topicFilterService;

  @Autowired CloudFileInfoService cloudFileInfoService;

  @Autowired PQLService kpi;

  @Autowired private TopicParamService topicParamService;

  @Autowired private KpiParamService kpiParamService;

  @Autowired private UserInfoFactory userInfoFactory;

  @Autowired private BusinessTopicKpiService businessTopicKpiService;

  @Autowired private TopicConfigService topicConfigService;

  @Autowired private TopicConfigGlobalService topicConfigGlobalService;

  @Autowired private ComponentConfigService componentConfigService;

  @Autowired PQLParameterManager parameterManager;

  @Autowired TopicFilterManager topicFilterManager;

  @Autowired private PQLService pqlService;

  @Autowired private IDataAuthorityService dataAuthorityService;

  @Autowired private SheetParamService sheetParamService;

  @Autowired private SheetDimensionService sheetDimensionService;

  @Autowired private IUserDataAuthorityService userDataAuthorityService;

  @Autowired private AllowProcessService allowProcessService;

  @Value("${prx.edit.Lock.enable:true}")
  private Boolean editLockEnable;

  @Autowired private UserInfoUtil userInfoUtil;

  @Autowired private EngineKpiServiceImpl engineKpiService;

  @Autowired ConformanceServiceScala conformanceServiceScala;

  @Autowired private ITopicMultiProcessService iTopicMultiProcessService;
  private static final String LIMIT_1 = "LIMIT 1";

  @Override
  public boolean sheetRelease(SheetReleaseReqVo sheetReleaseReqVo) {

    String topicId = sheetReleaseReqVo.getTopicId();
    BusinessTopicPO businessTopicPO = businessTopicService.getById(topicId);
    Integer parentId = businessTopicPO.getParentId();
    if (parentId == 0) {
      throw new DataException(
          ErrorCode.DATA_ERROR.getCode(), I18nUtil.getMessage(I18nConst.PUBLISH_SECOND_TOPIC));
    }

    // 检查是否有sheet页，没有的话不让发布
    long count =
        topicSheetService.count(
            new LambdaQueryWrapper<TopicSheetPO>()
                .eq(TopicSheetPO::getTopicId, topicId)
                .eq(TopicSheetPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (count == 0) {
      throw new DataException(
          ErrorCode.DATA_ERROR.getCode(), I18nUtil.getMessage(I18nConst.PUBLISH_EMPTY_DISABLE));
    }

    TopicFileDTO topicFileDTO =
        businessTopicDataService.getTopicFileByTopicId(businessTopicPO.getId());

    StopWatch sw = new StopWatch("sheet发布耗时统计");

    sw.start("doCopyTopic");
    // 拷贝topic
    BusinessTopicPO snapshotBusinessTopicPO = doCopyTopic(businessTopicPO);
    sw.stop();

    sw.start("doCopyTopicData");
    // 拷贝topicData
    doCopyTopicData(businessTopicPO, snapshotBusinessTopicPO);
    sw.stop();

    // copy user topic authrity
    this.copyUserAuthrity(businessTopicPO.getId(), snapshotBusinessTopicPO.getId());

    sw.start("doCopyTopicSheet");
    // 拷贝sheet,返回结果topicSheetId
    Map<Integer, Integer> sheetIdMap = doCopyTopicSheet(businessTopicPO, snapshotBusinessTopicPO);
    sw.stop();

    sw.start("copyMultiProcess");
    // 拷贝多级流程图
    copyMultiProcess(businessTopicPO.getId(), snapshotBusinessTopicPO.getId());
    sw.stop();

    this.doCopyFilter(sheetIdMap, businessTopicPO.getId(), snapshotBusinessTopicPO.getId());

    Integer snapshotTopicId = snapshotBusinessTopicPO.getId();

    sw.start("doCopyTopicSheetModule");
    // 拷贝sheet组件
    doCopyTopicSheetModule(sheetIdMap, snapshotTopicId, topicFileDTO);
    sw.stop();

    // copy 参数设置
    doCopyTopicParam(Integer.valueOf(topicId), snapshotTopicId);

    sw.start("saveSnapshotLog");
    // 保存发布记录
    saveSnapshotLog(
        sheetReleaseReqVo,
        snapshotBusinessTopicPO.getId(),
        TopicSnapshotLogPo.ReleaseTypeEnum.RELEASE);
    sw.stop();

    parameterManager.invalidate(snapshotBusinessTopicPO.getId());

    topicFilterManager.invalidate(snapshotBusinessTopicPO.getId());

    topicFilterManager.invalidateScript(snapshotBusinessTopicPO.getId());

    FilterService.userTopicFilterCache = new ConcurrentHashMap<>();

    userInfoFactory.invalidPermissionCache(userInfoUtil.getUserInfo().getUserId());
    businessService.clearCacheToTopicParent(parentId);
    log.info("发布整体耗时=>" + sw.prettyPrint());
    return true;
  }

  private void copyUserAuthrity(Integer oldTopicId, Integer newTopicId) {
    DataAuthority dataAuthority =
        dataAuthorityService.getOne(
            new LambdaQueryWrapper<DataAuthority>()
                .eq(DataAuthority::getDataId, oldTopicId.toString())
                .eq(DataAuthority::getDataType, 1)
                .eq(DataAuthority::getDeleted, 0)
                .last(LIMIT_1));
    if (dataAuthority == null) {
      dataAuthority = new DataAuthority();
      dataAuthority.setDataId(oldTopicId);
      dataAuthority.setDataType(1);
      dataAuthorityService.save(dataAuthority);
    }

    List<UserDataAuthority> list =
        userDataAuthorityService.list(
            new LambdaQueryWrapper<UserDataAuthority>()
                .eq(UserDataAuthority::getDataAuthorityId, dataAuthority.getId())
                .eq(UserDataAuthority::getDeleted, 0));
    if (CollectionUtils.isNotEmpty(list)) {

      DataAuthority dataAuthorityNew =
          dataAuthorityService.getOne(
              new LambdaQueryWrapper<DataAuthority>()
                  .eq(DataAuthority::getDataId, newTopicId.toString())
                  .eq(DataAuthority::getDataType, 1)
                  .eq(DataAuthority::getDeleted, 0)
                  .last(LIMIT_1));
      if (dataAuthorityNew == null) {
        dataAuthority.setId(null);
        dataAuthority.setDataId(newTopicId);
        dataAuthorityService.save(dataAuthority);
      } else {
        dataAuthority = dataAuthorityNew;
      }

      DataAuthority finalDataAuthority = dataAuthority;

      List<UserDataAuthority> listNew =
          userDataAuthorityService.list(
              new LambdaQueryWrapper<UserDataAuthority>()
                  .eq(UserDataAuthority::getDataAuthorityId, finalDataAuthority.getId())
                  .eq(UserDataAuthority::getDeleted, 0));

      List<Integer> delIdList =
          listNew.stream().map(UserDataAuthority::getId).collect(Collectors.toList());
      userDataAuthorityService.removeByIds(delIdList);

      list.forEach(
          f -> {
            f.setId(null);
            f.setDataAuthorityId(finalDataAuthority.getId());
          });
      userDataAuthorityService.saveBatch(list);
    }
  }

  @Override
  public PageRespDTO<ArrayList<SnapshotLogResVo>> getSnapshotLog(Integer topicId) {
    LambdaQueryWrapper<TopicSnapshotLogPo> topicSnapshotLogPoLambdaQueryWrapper =
        new LambdaQueryWrapper<TopicSnapshotLogPo>()
            .eq(TopicSnapshotLogPo::getTopicId, topicId)
            .orderByDesc(TopicSnapshotLogPo::getId);

    IPage<TopicSnapshotLogPo> page = new Page<>(1, 10);

    IPage<TopicSnapshotLogPo> topicSnapshotLogPoIPage =
        topicSnapshotLogMapper.selectPage(page, topicSnapshotLogPoLambdaQueryWrapper);

    List<Integer> userIds =
        topicSnapshotLogPoIPage.getRecords().stream()
            .map(TopicSnapshotLogPo::getCreateUser)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    Map<Integer, Admin> userInfoMap = adminService.getUserInfoByIds(userIds);
    ArrayList<SnapshotLogResVo> rows = new ArrayList<>();

    for (TopicSnapshotLogPo record : topicSnapshotLogPoIPage.getRecords()) {
      Admin admin = userInfoMap.getOrDefault(record.getCreateUser(), new Admin());
      SnapshotLogResVo snapshotLogResVo = new SnapshotLogResVo();
      snapshotLogResVo.setUserName(admin.getUserName());
      snapshotLogResVo.setName(admin.getName());
      snapshotLogResVo.setCreateTime(DateTimeUtil.date2String(record.getCreateTime()));
      snapshotLogResVo.setType(record.getReleaseType());
      rows.add(snapshotLogResVo);
    }

    PageRespDTO<ArrayList<SnapshotLogResVo>> result = new PageRespDTO<>();
    result.setDto(rows);
    result.setTotal(topicSnapshotLogPoIPage.getTotal());
    result.setPageNum(1);
    result.setPageSize(10);

    return result;
  }

  /**
   * 记录发布日志
   *
   * @param sheetReleaseReqVo
   * @param snapshotTopicId
   */
  @Override
  public void saveSnapshotLog(
      SheetReleaseReqVo sheetReleaseReqVo,
      Integer snapshotTopicId,
      TopicSnapshotLogPo.ReleaseTypeEnum releaseTypeEnum) {
    TopicSnapshotLogPo topicSnapshotLogPo = new TopicSnapshotLogPo();

    topicSnapshotLogPo.setTopicId(Integer.valueOf(sheetReleaseReqVo.getTopicId()));
    topicSnapshotLogPo.setSnapshotTopicId(snapshotTopicId);
    if (StringUtils.isNotBlank(sheetReleaseReqVo.getUserId())) {
      topicSnapshotLogPo.setCreateUser(Integer.valueOf(sheetReleaseReqVo.getUserId()));
    }
    topicSnapshotLogPo.setReleaseType(releaseTypeEnum.getValue());
    topicSnapshotLogService.save(topicSnapshotLogPo);
  }

  /**
   * 拷贝topicData数据绑定
   *
   * @param businessTopicPO
   * @param snapshotBusinessTopicPO
   */
  private void doCopyTopicData(
      BusinessTopicPO businessTopicPO, BusinessTopicPO snapshotBusinessTopicPO) {
    LambdaQueryWrapper<BusinessTopicDataPO> businessTopicDataPOLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    businessTopicDataPOLambdaQueryWrapper
        .eq(BusinessTopicDataPO::getTopicId, businessTopicPO.getId())
        .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue());

    List<BusinessTopicDataPO> businessTopicDataPOS =
        businessTopicDataService.list(businessTopicDataPOLambdaQueryWrapper);
    if (CollectionUtils.isEmpty(businessTopicDataPOS)) {
      throw new DataException(ErrorCode.DATA_ERROR, I18nUtil.getMessage(I18nConst.DATA_ERROR));
    }
    BusinessTopicDataPO businessTopicDataPO = businessTopicDataPOS.get(0);

    LambdaUpdateWrapper<BusinessTopicDataPO> businessTopicDataPOLambdaUpdateWrapper =
        new LambdaUpdateWrapper<>();
    businessTopicDataPOLambdaUpdateWrapper
        .eq(BusinessTopicDataPO::getTopicId, snapshotBusinessTopicPO.getId())
        .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
        .set(BusinessTopicDataPO::getTopicId, snapshotBusinessTopicPO.getId())
        .set(BusinessTopicDataPO::getModelId, businessTopicDataPO.getModelId())
        .set(BusinessTopicDataPO::getDataSourceType, businessTopicDataPO.getDataSourceType());
    BusinessTopicDataPO savePo =
        businessTopicDataService.getOne(businessTopicDataPOLambdaUpdateWrapper);
    if (savePo == null) {
      savePo = new BusinessTopicDataPO();
      savePo.setTopicId(snapshotBusinessTopicPO.getId());
      savePo.setModelId(businessTopicDataPO.getModelId());
      savePo.setDataSourceType(businessTopicDataPO.getDataSourceType());
      businessTopicDataService.save(savePo);
    } else {
      savePo.setTopicId(snapshotBusinessTopicPO.getId());
      savePo.setModelId(businessTopicDataPO.getModelId());
      savePo.setDataSourceType(businessTopicDataPO.getDataSourceType());
      businessTopicDataService.updateById(savePo);
    }
  }

  public void doCopyFilter(Map<Integer, Integer> sheetIdMap, Integer topicId, Integer snapTopicId) {
    Integer userId = userInfoUtil.getUserInfo().getUserId();
    // copy topic_filter  init_filter
    topicFilterService.remove(
        new LambdaQueryWrapper<TopicFilterPO>()
            .eq(TopicFilterPO::getTopicId, snapTopicId)
            .eq(TopicFilterPO::getMarkType, BookMarkTypeEnum.STANDARD.getValue())
            .eq(TopicFilterPO::getUserId, userId));
    topicFilterService.remove(
        new LambdaQueryWrapper<TopicFilterPO>()
            .eq(TopicFilterPO::getTopicId, snapTopicId)
            .eq(TopicFilterPO::getMarkType, BookMarkTypeEnum.STANDARD.getValue())
            .isNull(TopicFilterPO::getUserId));

    List<Integer> newSheetIdList = new ArrayList<>(sheetIdMap.values());
    if (CollectionUtils.isNotEmpty(newSheetIdList)) {
      topicFilterService.remove(
          new LambdaQueryWrapper<TopicFilterPO>()
              .in(TopicFilterPO::getSheetId, newSheetIdList)
              .eq(TopicFilterPO::getUserId, userId)
              .notIn(
                  TopicFilterPO::getBizType,
                  Lists.newArrayList(
                      TopicFilterBizTypeEnum.PROCESS_LEFT.getValue(),
                      TopicFilterBizTypeEnum.PROCESS_RIGHT.getValue())));
      topicFilterService.remove(
          new LambdaQueryWrapper<TopicFilterPO>()
              .in(TopicFilterPO::getSheetId, newSheetIdList)
              .isNull(TopicFilterPO::getUserId)
              .notIn(
                  TopicFilterPO::getBizType,
                  Lists.newArrayList(
                      TopicFilterBizTypeEnum.PROCESS_LEFT.getValue(),
                      TopicFilterBizTypeEnum.PROCESS_RIGHT.getValue())));
    }
    List<TopicFilterPO> topicFilterList =
        topicFilterService.getTopicFilterListWithScript(topicId, userId);

    List<TopicFilterPO> topicFilterListSave =
        topicFilterList.stream()
            .filter(f -> f.getComponentId() == null)
            .peek(
                m -> {
                  m.setId(null);
                  m.setTopicId(snapTopicId);
                })
            .collect(Collectors.toList());

    topicFilterService.saveBatch(topicFilterListSave);

    List<TopicFilterPO> topicFilterListNoUser =
        topicFilterService.getTopicFilterList(snapTopicId, userId);

    log.info("topicFilterListNoUser size ={}", topicFilterListNoUser.size());
    if (CollectionUtils.isNotEmpty(topicFilterListNoUser)) {
      topicFilterListNoUser.forEach(
          f -> {
            f.setId(null);
            f.setUserId(null);
            f.setTopicId(snapTopicId);
            if (f.getSheetId() != null) {
              Integer sheetId = sheetIdMap.get(f.getSheetId());
              if (sheetId != null) {
                f.setSheetId(sheetId);
              }
            }
          });
      topicFilterService.saveBatch(topicFilterListNoUser);
    }

    Set<Integer> sheetIdList = sheetIdMap.keySet();
    List<TopicFilterPO> sheetFilterList =
        topicFilterService.getSheetFilterBySheetIdList(new ArrayList<>(sheetIdList));
    if (CollectionUtils.isNotEmpty(sheetFilterList)) {
      sheetFilterList.forEach(
          f -> {
            f.setId(null);
            if (f.getSheetId() != null) {
              Integer sheetId = sheetIdMap.get(f.getSheetId());
              if (sheetId != null) {
                f.setSheetId(sheetId);
              }
            }
          });
      topicFilterService.saveBatch(sheetFilterList);
    }
  }

  public boolean doCopyTopicSheetModule(
      Integer topicSheetId, Integer snapshotTopicSheetId, Integer snapshotTopicId,Map<Integer, Integer> sheetIdMap) {

    // 拷贝xml文件
    List<CloudFileInfoPO> cloudFileInfoPOS =
        cloudFileInfoService.list(
            new LambdaQueryWrapper<CloudFileInfoPO>()
                .eq(CloudFileInfoPO::getSheetId, topicSheetId)
                .eq(CloudFileInfoPO::getFileType, FileTypeEnum.XML_BASE.name())
                .eq(CloudFileInfoPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    List<CopyIdDto> imageIdArr = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(cloudFileInfoPOS)) {
      CloudFileInfoPO cloudFileInfoPO = cloudFileInfoPOS.get(0);

      CopyIdDto build = CopyIdDto.builder().copyId(cloudFileInfoPO.getId()).build();

      CloudFileInfoPO snapshotCloudFileInfoPO = new CloudFileInfoPO();
      BeanUtils.copyProperties(cloudFileInfoPO, snapshotCloudFileInfoPO);
      snapshotCloudFileInfoPO.setId(null);
      snapshotCloudFileInfoPO.setSheetId(snapshotTopicSheetId);
      snapshotCloudFileInfoPO.setCreateTime(null);
      snapshotCloudFileInfoPO.setUpdateTime(null);
      cloudFileInfoService.remove(
          new LambdaUpdateWrapper<CloudFileInfoPO>()
              .eq(CloudFileInfoPO::getSheetId, snapshotTopicSheetId)
              .eq(CloudFileInfoPO::getFileType, FileTypeEnum.XML_BASE.name()));
      cloudFileInfoService.save(snapshotCloudFileInfoPO);
      build.setCopyId2(snapshotCloudFileInfoPO.getId());
      imageIdArr.add(build);
    }

    List<SheetComponentPO> componentList = sheetComponentService.listBySheetId(topicSheetId);

    List<CopyIdDto> componentIdArr = new ArrayList<>();
    componentList.forEach(
        f -> {
          CopyIdDto build = CopyIdDto.builder().copyId(f.getId()).build();
          componentIdArr.add(build);

          f.setId(null);
          f.setSheetId(snapshotTopicSheetId);
        });

    sheetComponentService.saveBatch(componentList);

    for (int i = 0; i < componentList.size(); i++) {

      SheetComponentPO component = componentList.get(i);

      CopyIdDto copyIdDto = componentIdArr.get(i);
      copyIdDto.setCopyId2(component.getId());

      ComponentConfigPO componentConfig =
          componentConfigService.getOneByComponentId(copyIdDto.getCopyId());
      if (componentConfig != null) {
        componentConfig.setId(null);
        componentConfig.setComponentId(component.getId());
        componentConfigService.save(componentConfig);
      }
    }

    // SheetModuleEnum.KPI  注意KPI需要更新KPI参数，同时更新根因数据
    List<SheetKpiDTO> sheetKpiDTOS = topicSheetKpiService.getTopicSheetKpiList(topicSheetId);
    List<SheetKpiDTO> snapshotSheetKpiDTOS =
        topicSheetKpiService.getTopicSheetKpiList(snapshotTopicSheetId);

    List<Integer> snapshotKpiIds =
        snapshotSheetKpiDTOS.stream()
            .map(SheetKpiDTO::getId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    if (!snapshotKpiIds.isEmpty()) {
      engineKpiService.removeByIds(snapshotKpiIds);
      topicSheetKpiService.remove(
          new LambdaQueryWrapper<TopicSheetKpiPO>().in(TopicSheetKpiPO::getKpiId, snapshotKpiIds));
    }

    List<CopyIdDto> kpiIdArr = new ArrayList<>();

    // 获取kpi绑定的根因分析
    for (int i = 0; i < sheetKpiDTOS.size(); i++) {
      SheetKpiDTO sheetKpiDTO = sheetKpiDTOS.get(i);

      CopyIdDto build = CopyIdDto.builder().copyId(sheetKpiDTO.getId()).build();

      KpiPO kpiPoNew = new KpiPO();
      TopicSheetKpiPO topicSheetKpiPoNew = new TopicSheetKpiPO();

      // 复制kpi
      kpiPoNew.setName(sheetKpiDTO.getName());
      kpiPoNew.setUnit(sheetKpiDTO.getUnit());
      kpiPoNew.setType(sheetKpiDTO.getType());
      kpiPoNew.setBaseLine(sheetKpiDTO.getBaseLine());
      kpiPoNew.setExpression(sheetKpiDTO.getExpression());
      kpiPoNew.setFormatting(sheetKpiDTO.getFormatting());
      kpiPoNew.setFormat(sheetKpiDTO.getFormat());
      kpiPoNew.setColumnType(sheetKpiDTO.getColumnType());
      kpiService.saveOrUpdate(kpiPoNew);

      build.setCopyId2(kpiPoNew.getId());
      kpiIdArr.add(build);

      // 复制kpiSheet
      topicSheetKpiPoNew.setFromKnowledge(sheetKpiDTO.getFromKnowledge());
      topicSheetKpiPoNew.setTopicSheetId(snapshotTopicSheetId);
      topicSheetKpiPoNew.setKpiId(kpiPoNew.getId());
      topicSheetKpiService.saveOrUpdate(topicSheetKpiPoNew);

      // 查需要被拷贝的根因数据
      QuerySheetCauseRequest querySheetCauseRequest = new QuerySheetCauseRequest();
      querySheetCauseRequest.setTopicSheetId(topicSheetId);
      Integer sheetCause = processTreeReadBizService.getSheetCause(querySheetCauseRequest);
      if (sheetCause != null) {

        // 获取根因数据信息
        RootCausePO rootCausePO = rootCauseService.getById(sheetCause);

        if (Objects.equals(rootCausePO.getSortRule(), sheetKpiDTO.getId())) {

          // 获取CauseRelatedVariablesPO
          List<CauseRelatedVariablesPO> causeRelatedVariablesPOs =
              causeRelatedVariablesService.list(
                  new LambdaQueryWrapper<CauseRelatedVariablesPO>()
                      .eq(CauseRelatedVariablesPO::getCauseId, rootCausePO.getId())
                      .eq(CauseRelatedVariablesPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

          List<Integer> kpiList = Lists.newArrayList();
          kpiList.add(kpiPoNew.getId());
          List<VariableSaveSubRequest> variableList = Lists.newArrayList();

          for (CauseRelatedVariablesPO causeRelatedVariablesPO : causeRelatedVariablesPOs) {
            VariableSaveSubRequest variableSaveSubRequest = new VariableSaveSubRequest();
            variableSaveSubRequest.setVariable(causeRelatedVariablesPO.getVariable());
            variableSaveSubRequest.setFileId(causeRelatedVariablesPO.getFileId());
            variableSaveSubRequest.setFileName(causeRelatedVariablesPO.getFileName());
            variableList.add(variableSaveSubRequest);
          }
          RootCauseSaveRequest request = new RootCauseSaveRequest();
          request.setName(rootCausePO.getCauseName());
          request.setVariableList(variableList);
          request.setKpiList(kpiList);
          request.setSort(kpiList.get(0));
          request.setTopicSheetId(snapshotTopicSheetId);
          processTreeWriteBizService.saveRootCause(request);
        }
      }
    }

    // SheetModuleEnum.CONFORMANCE 未拷贝variantId
    List<TopicSheetConformancePO> topicSheetConformancePOS =
        topicSheetConformanceService.list(
            new LambdaQueryWrapper<TopicSheetConformancePO>()
                .eq(TopicSheetConformancePO::getTopicSheetId, topicSheetId)
                .eq(TopicSheetConformancePO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isNotEmpty(topicSheetConformancePOS)) {

      for (int i = 0; i < topicSheetConformancePOS.size(); i++) {
        TopicSheetConformancePO sheetConformanceDto = topicSheetConformancePOS.get(i);
        TopicSheetConformancePO topicSheetConformancePO = new TopicSheetConformancePO();

        topicSheetConformancePO.setTopicSheetId(snapshotTopicSheetId);
        topicSheetConformancePO.setFileVariantId(sheetConformanceDto.getFileVariantId());
        topicSheetConformancePO.setVariantId(sheetConformanceDto.getVariantId());
        topicSheetConformanceService.saveOrUpdate(topicSheetConformancePO);
      }
    }
    // SheetModuleEnum.VARIABLE
    List<TopicSheetVariablePO> topicSheetVariablePOS =
        topicSheetVariableService.listBySheetId(topicSheetId);
    topicSheetVariableService.remove(
        new LambdaQueryWrapper<TopicSheetVariablePO>()
            .eq(TopicSheetVariablePO::getTopicSheetId, snapshotTopicSheetId));
    for (TopicSheetVariablePO topicSheetVariablePO : topicSheetVariablePOS) {
      TopicSheetVariablePO topicSheetVariablePONew = new TopicSheetVariablePO();
      topicSheetVariablePONew.setTopicSheetId(snapshotTopicSheetId);
      topicSheetVariablePONew.setVariable(topicSheetVariablePO.getVariable());
      topicSheetVariablePONew.setFileId(topicSheetVariablePO.getFileId());
      topicSheetVariablePONew.setFieldId(topicSheetVariablePO.getFieldId());
      topicSheetVariablePONew.setFileName(topicSheetVariablePO.getFileName());
      topicSheetVariableService.save(topicSheetVariablePONew);
    }
    // SheetModuleEnum.NEW_SHEET
    List<TopicSheetNewPo> topicSheetNewPos =
        topicSheetNewService.list(
            new LambdaQueryWrapper<TopicSheetNewPo>()
                .eq(TopicSheetNewPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .eq(TopicSheetNewPo::getTopicSheetId, topicSheetId));

    Map<Integer, CopyIdDto> componentIdDtoMap =
        componentIdArr.stream()
            .collect(Collectors.toMap(CopyIdDto::getCopyId, Function.identity(), (k1, k2) -> k1));
    Map<Integer, CopyIdDto> kpiIdDtoMap =
        kpiIdArr.stream()
            .collect(Collectors.toMap(CopyIdDto::getCopyId, Function.identity(), (k1, k2) -> k1));
    Map<Integer, CopyIdDto> imageIdDtoMap =
        imageIdArr.stream()
            .collect(Collectors.toMap(CopyIdDto::getCopyId, Function.identity(), (k1, k2) -> k1));

    for (TopicSheetNewPo topicSheetNewPo : topicSheetNewPos) {
      LambdaUpdateWrapper<TopicSheetNewPo> sheetNewPoLambdaUpdateWrapper =
              new LambdaUpdateWrapper<TopicSheetNewPo>()
                      .set(TopicSheetNewPo::getComponentsValue, topicSheetNewPo.getComponentsValue())
                      .set(TopicSheetNewPo::getComponentsInfo, topicSheetNewPo.getComponentsInfo())
                      .eq(TopicSheetNewPo::getTopicSheetId, snapshotTopicSheetId)
                      .eq(TopicSheetNewPo::getDeleted, DeletedEnum.NO_DELETED.getValue());

      TopicSheetNewPo topicSheetNewPoNew = new TopicSheetNewPo();
      topicSheetNewPoNew.setTopicSheetId(snapshotTopicSheetId);

      String componentsValue = topicSheetNewPo.getComponentsValue();

      JSONArray jsonArray = JSON.parseArray(componentsValue);
      for (int i = 0; i < jsonArray.size(); i++) {
        JSONObject jsonObject = jsonArray.getJSONObject(i);
        Integer componentId = jsonObject.getInteger("id");
        CopyIdDto copyIdDto = componentIdDtoMap.get(componentId);
        if (Objects.nonNull(copyIdDto) && Objects.nonNull(copyIdDto.getCopyId2())) {
          componentId = copyIdDto.getCopyId2();
        }
        jsonObject.put("id", componentId);

        String type = jsonObject.getString("type");
        if (!Objects.equals(type, "process-com") && !Objects.equals(type, "variant-com")) {
          JSONObject data = jsonObject.getJSONObject("data");
          if (Objects.nonNull(data)) {
            JSONArray kpiArray = data.getJSONArray("kpiList");
            if (Objects.nonNull(kpiArray) && kpiArray.size() > 0) {
              for (int j = 0; j < kpiArray.size(); j++) {
                JSONObject kpiObj = kpiArray.getJSONObject(j);
                Integer kpiId = kpiObj.getInteger("kpiId");
                if (Objects.nonNull(kpiId)) {
                  CopyIdDto kpiCopyDto = kpiIdDtoMap.get(kpiId);
                  if (Objects.nonNull(kpiCopyDto) && Objects.nonNull(kpiCopyDto.getCopyId2())) {
                    kpiId = kpiCopyDto.getCopyId2();
                    kpiObj.put("kpiId", kpiId);
                  }
                }
              }
            }
          }

          JSONObject data2 = jsonObject.getJSONObject("data");
          if (Objects.nonNull(data2)) {
            Integer imagePath = data2.getInteger("path");
            if (Objects.nonNull(imagePath)) {
              CopyIdDto imagePathCopy = imageIdDtoMap.get(imagePath);
              if (Objects.nonNull(imagePathCopy) && Objects.nonNull(imagePathCopy.getCopyId2())) {
                imagePath = imagePathCopy.getCopyId2();
                jsonObject.getJSONObject("data").put("path", imagePath);
              }
            }
          }
        }

        if (Objects.equals(type, "button-com")) {
          processButtonComponent(jsonObject, sheetIdMap, snapshotTopicId);
        }
      }

      topicSheetNewPoNew.setComponentsValue(jsonArray.toJSONString());
      topicSheetNewPoNew.setComponentsInfo(topicSheetNewPo.getComponentsInfo());

      if (!topicSheetNewService.update(sheetNewPoLambdaUpdateWrapper)) {
        topicSheetNewService.save(topicSheetNewPoNew);
      }
    }

    // copy sheet dimension
    sheetDimensionService.remove(
        new LambdaQueryWrapper<SheetDimensionPO>()
            .eq(SheetDimensionPO::getSheetId, snapshotTopicSheetId));
    List<DimensionAndKpi> dimensionAndKpiList = sheetDimensionService.listBySheetId(topicSheetId);
    for (DimensionAndKpi dimensionAndKpi : dimensionAndKpiList) {
      SheetDimensionPO sheetDimension = new SheetDimensionPO();
      sheetDimension.setSheetId(snapshotTopicSheetId);
      sheetDimension.setTitleType(dimensionAndKpi.getTitleType());
      sheetDimension.setTableName(dimensionAndKpi.getTableName());
      sheetDimension.setColumnName(dimensionAndKpi.getColumnName());
      if (dimensionAndKpi.getKpiId() != null) {
        sheetDimension.setKpiId(kpiIdDtoMap.get(dimensionAndKpi.getKpiId()).getCopyId2());
      }
      sheetDimensionService.save(sheetDimension);
    }
    return true;
  }


  /**
   * 处理按钮组件的页面ID和URL更新
   *
   * @param jsonObject 组件的JSON对象
   * @param sheetIdMap sheet ID映射关系
   * @param snapshotTopicId 快照topic ID
   */
  private void processButtonComponent(JSONObject jsonObject, Map<Integer, Integer> sheetIdMap, Integer snapshotTopicId) {

    JSONObject data = jsonObject.getJSONObject("data");

    if (data == null) {
      log.info("按钮组件缺少data对象，跳过处理");
      return;
    }

    Integer pageId = data.getInteger("pageId");
    if (pageId == null) {
      log.info("按钮组件缺少pageId，跳过处理");
      return;
    }

    Integer newSheetId = sheetIdMap.get(pageId);
    if (newSheetId != null) {
      data.put("pageId", newSheetId);
      String originPageUrl = data.getString("pageUrl");

      String pageUrl = buildWorkbenchUrl(originPageUrl, snapshotTopicId, newSheetId);
      data.put("pageUrl", pageUrl);
      log.info("更新按钮组件: pageId={} -> {}, pageUrl={}", pageId, newSheetId, pageUrl);
    } else {
      log.warn("未找到pageId={}对应的新sheetId，跳过更新", pageId);
    }
  }

  private static final String WORKBENCH_URL_TEMPLATE = "/workbench-new/business/case?id={id}&topicSheetId={topicSheetId}";

  /**
   * 构建工作台URL
   *
   * @param topicId topic ID
   * @param sheetId sheet ID
   * @return 构建的URL
   */
  private String buildWorkbenchUrl(String originPageUrl, Integer topicId, Integer sheetId) {

    if (originPageUrl == null || topicId == null || sheetId == null) {
      log.warn("构建URL时参数为空:originPageUrl={}, topicId={}, sheetId={}", originPageUrl, topicId, sheetId);
      return "";
    }

    return WORKBENCH_URL_TEMPLATE
            .replace("{id}", String.valueOf(topicId))
            .replace("{topicSheetId}", String.valueOf(sheetId));
  }

  public void doCopyTopicParam(Integer topicId, Integer snapTopicId) {
    List<BusinessTopicKpiPO> topicKpiList = businessTopicKpiService.getTopicKpiList(topicId);
    businessTopicKpiService.remove(
        new LambdaQueryWrapper<BusinessTopicKpiPO>()
            .eq(BusinessTopicKpiPO::getTopicId, snapTopicId));

    if (CollectionUtils.isNotEmpty(topicKpiList)) {
      List<Integer> kpiIdList =
          topicKpiList.stream().map(BusinessTopicKpiPO::getKpiId).collect(Collectors.toList());
      List<KpiPO> kpiList = kpiService.listByIds(kpiIdList);

      Map<Integer, KpiPO> kpiMap =
          kpiList.stream()
              .collect(Collectors.toMap(KpiPO::getId, Function.identity(), (k1, k2) -> k1));

      kpiList.forEach(f -> f.setId(null));
      kpiService.saveBatch(kpiList);

      Map<String, KpiPO> snapKpiMap =
          kpiList.stream()
              .collect(Collectors.toMap(KpiPO::getName, Function.identity(), (k1, k2) -> k1));

      List<KpiParamPO> kpiParamList = kpiParamService.getKpiParamList(kpiIdList);
      if (CollectionUtils.isNotEmpty(kpiParamList)) {
        kpiParamList.forEach(
            f -> {
              f.setId(null);
              KpiPO kpiLocal = kpiMap.get(f.getKpiId());
              KpiPO snapKpi = snapKpiMap.get(kpiLocal.getName());
              f.setKpiId(snapKpi.getId());
            });

        kpiParamService.saveBatch(kpiParamList);
      }
      List<BusinessTopicKpiPO> businessTopicKpiList =
          kpiList.stream()
              .map(
                  m ->
                      BusinessTopicKpiPO.builder()
                          .topicId(snapTopicId)
                          .kpiId(m.getId())
                          .type(1)
                          .build())
              .collect(Collectors.toList());
      businessTopicKpiService.saveBatch(businessTopicKpiList);
    }

    List<TopicParamPO> topicParamList = topicParamService.getTopicParamList(topicId);
    topicParamService.remove(
        new LambdaQueryWrapper<TopicParamPO>().eq(TopicParamPO::getTopicId, snapTopicId));
    if (CollectionUtils.isNotEmpty(topicParamList)) {
      topicParamList.forEach(
          f -> {
            f.setId(null);
            f.setTopicId(snapTopicId);
          });

      topicParamService.saveBatch(topicParamList);
    }

    TopicConfigPO topicConfig = topicConfigService.getMaxExport(topicId);
    topicConfigService.remove(
        new LambdaQueryWrapper<TopicConfigPO>().eq(TopicConfigPO::getTopicId, snapTopicId));
    if (topicConfig != null) {
      topicConfig.setId(null);
      topicConfig.setTopicId(snapTopicId);
      topicConfigService.save(topicConfig);
    }

    List<TopicConfigGlobalPO> topicConfigGlobalPOList =
        topicConfigGlobalService.getByTopicId(topicId);
    topicConfigGlobalService.remove(
        new LambdaQueryWrapper<TopicConfigGlobalPO>()
            .eq(TopicConfigGlobalPO::getTopicId, snapTopicId));
    if (!topicConfigGlobalPOList.isEmpty()) {
      topicConfigGlobalPOList.forEach(
          f -> {
            f.setId(null);
            f.setTopicId(snapTopicId);
          });
      topicConfigGlobalService.saveBatch(topicConfigGlobalPOList);
    }

    businessService.clearProcessKpi(snapTopicId);
    businessService.copyProcessKpi(topicId, snapTopicId);

    businessService.clearBookMark(snapTopicId);
    businessService.saveBookMark(topicId, snapTopicId);

    businessService.clearTopicConfigColor(snapTopicId);
    businessService.copyTopicConfigColor(topicId, snapTopicId);

    businessService.clearTempPQLTables(snapTopicId);
    businessService.copyTempPQLTables(topicId, snapTopicId);
  }

  /**
   * 拷贝sheetModule
   *
   * @param sheetIdMap key sheetId,value 快照的sheetId
   */
  @Override
  public void doCopyTopicSheetModule(
      Map<Integer, Integer> sheetIdMap, Integer snapshotTopicId, TopicFileDTO topicFileDTO) {

    for (Map.Entry<Integer, Integer> entry : sheetIdMap.entrySet()) {
      this.doCopyTopicSheetModule(entry.getKey(), entry.getValue(), snapshotTopicId, sheetIdMap);
    }
  }

  @Override
  public List<KpiStatisticsResultResVo> getKpiStatisticsResult(
      GetKpiStatisticsResultReqVo getKpiStatisticsResultReqVo) {
    DataModelFileDTO dataModelFilePO =
        topicSheetService.getTopicSheetFileBySheetId(getKpiStatisticsResultReqVo.getTopicSheetId());
    if (Objects.isNull(dataModelFilePO)) {
      log.warn("sheetId={}未查到数据模型数据", getKpiStatisticsResultReqVo.getTopicSheetId());
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_MODEL_NOT_EXIST));
    }
    List<GetKpiStatisticsResultVo> params = getKpiStatisticsResultReqVo.getParams();
    if (CollectionUtils.isEmpty(params)) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.NAME_EMPTY_DISABLE));
    }
    List<Integer> kpiId =
        params.stream().map(GetKpiStatisticsResultVo::getKpiId).collect(Collectors.toList());
    List<KpiPO> kpiPOS = kpiService.list(new LambdaQueryWrapper<KpiPO>().in(KpiPO::getId, kpiId));
    Map<Integer, KpiPO> collect = kpiPOS.stream().collect(Collectors.toMap(KpiPO::getId, po -> po));

    List<KpiStatisticsResultResVo> results = new ArrayList<>();
    for (int i = 0; i < params.size(); i++) {
      KpiPO kpiPO = collect.get(params.get(i).getKpiId());
      if (kpiPO == null) {
        throw new BizException(
            ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_ERROR));
      }

      KpiParserParamDTO build =
          KpiParserParamDTO.builder()
              .topicId(dataModelFilePO.getTopicId())
              .kpiPO(KpiPO.builder().id(kpiPO.getId()).expression(kpiPO.getExpression()).build())
              .fileId(dataModelFilePO.getFileId())
              .build();
      String result = null;
      try {
        result = kpi.calcExpression(build);
      } catch (Exception e) {
        log.error("");
        result = "--";
      }

      KpiStatisticsResultResVo kpiStatisticsResultResVo = new KpiStatisticsResultResVo();

      BeanUtils.copyProperties(kpiPO, kpiStatisticsResultResVo);
      kpiStatisticsResultResVo.setStatisticsResult(result);
      kpiStatisticsResultResVo.setParam(getKpiStatisticsResultReqVo.getParams().get(i));

      results.add(kpiStatisticsResultResVo);
    }

    return results;
  }

  @Override
  public ThroughputTimeResVo getThroughputTime(
      String topicId, Integer sheetId, String startTime, String endTime)
      throws InvocationTargetException, NoSuchMethodException, InstantiationException,
          IllegalAccessException, NoSuchFieldException {
    TopicFileDTO fileByTopicId =
        businessTopicDataService.getTopicFileByTopicId(Integer.valueOf(topicId));

    ModelDesc orCreate = modelDescFactory.getOrCreate(fileByTopicId.getDataModelId());

    String min =
        "min("
            + orCreate.baseVirtualCaseTable().tableName()
            + "."
            + PreHandlerVariableEnum.CASE_DURATION.getValue()
            + ")";
    String max =
        "max("
            + orCreate.baseVirtualCaseTable().tableName()
            + "."
            + PreHandlerVariableEnum.CASE_DURATION.getValue()
            + ")";
    String avg =
        "avg("
            + orCreate.baseVirtualCaseTable().tableName()
            + "."
            + PreHandlerVariableEnum.CASE_DURATION.getValue()
            + ")";
    String middle =
        "percentile_approx("
            + orCreate.baseVirtualCaseTable().tableName()
            + "."
            + PreHandlerVariableEnum.CASE_DURATION.getValue()
            + ",0.5)";

    List<KpiPO> dimensionKpiList = new ArrayList<>();
    dimensionKpiList.add(KpiPO.builder().name("min").expression(min).build());
    dimensionKpiList.add(KpiPO.builder().name("max").expression(max).build());
    dimensionKpiList.add(KpiPO.builder().name("avg").expression(avg).build());
    dimensionKpiList.add(KpiPO.builder().name("middle").expression(middle).build());

    ParseKpiDTO parseKpiBuild =
        ParseKpiDTO.builder()
            .topicId(fileByTopicId.getTopicId())
            .sheetId(sheetId)
            .dimensionColumnList(dimensionKpiList)
            .type(0)
            .rule(VariableKpiRuleEnum.LIMIT.getValue())
            .limit(1)
            .build();
    String timeRangeFilter =
        ProcessOverview.mkTimeFilter(startTime, endTime, orCreate.activeTableTimeColumn());
    if (StringUtils.isNotBlank(timeRangeFilter)) {
      parseKpiBuild.setAddTopicFilterList(
          Lists.newArrayList(
              TopicFilterPO.builder()
                  .type(TopicFilterTypeEnum.EXPRESSION.getValue())
                  .expression(timeRangeFilter)
                  .build()));
    }

    Result result = pqlService.calcExpression(parseKpiBuild);

    List<ThroughputTimeResVo> list = result.getList(ThroughputTimeResVo.class);

    if (CollectionUtils.isEmpty(list)) {
      ThroughputTimeResVo throughputTimeResVo = new ThroughputTimeResVo();
      throughputTimeResVo.setAvg("0");
      throughputTimeResVo.setMax("0");
      throughputTimeResVo.setMin("0");
      throughputTimeResVo.setMiddle("0");
      return throughputTimeResVo;
    } else {
      return list.get(0);
    }
  }

  @SneakyThrows
  @Override
  public Result getThroughputTimeList(ThroughputTimeListReqVo throughputTimeListReqVo) {

    DataModelFileDTO fileBySheetId =
        topicSheetService.getTopicSheetFileBySheetId(throughputTimeListReqVo.getTopicSheetId());
    if (Objects.isNull(fileBySheetId)) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_MODEL_NOT_EXIST));
    }

    List<KpiPO> kpiList = new ArrayList<>();
    kpiList.add(KpiPO.builder().name("caseId").expression("`case_table`.preCaseId").build());
    kpiList.add(KpiPO.builder().name("eventNum").expression("`case_table`.eventNum").build());
    kpiList.add(
        KpiPO.builder().name("reworkEventNum").expression("`case_table`.reworkEventNum").build());
    kpiList.add(
        KpiPO.builder().name("caseDuration").expression("`case_table`.caseDuration").build());

    ParseKpiDTO parseKpi =
        ParseKpiDTO.builder()
            .topicId(fileBySheetId.getTopicId())
            .sheetId(throughputTimeListReqVo.getTopicSheetId())
            .dimensionColumnList(kpiList)
            .sortList(
                Lists.newArrayList(
                    KpiVariableSortDTO.builder().sortName("caseDuration").sortType(2).build()))
            .type(0)
            .rule(VariableKpiRuleEnum.SCROLL.getValue())
            .pageNum(throughputTimeListReqVo.getPageNum())
            .pageSize(throughputTimeListReqVo.getPageSize())
            .build();

    return pqlService.calcExpression(parseKpi);
  }

  @Autowired private FileFieldService fileFieldService;

  @Override
  public Boolean updateContrastFilter(UpdateContrastFilterReqVo updateContrastFilterReqVo) {
    Integer leftAndRightFlag = updateContrastFilterReqVo.getLeftAndRightFlag();
    int bizType;
    if (leftAndRightFlag == 0) {
      bizType = 20;
    } else {
      bizType = 21;
    }
    List<TopicFilterPO> topicFilterPOS =
        topicFilterService.getFilter4Contrast(
            updateContrastFilterReqVo.getSheetId(), Lists.newArrayList(bizType));
    // 对比有两个sheet级别过滤项，如果个数不等于2，则数据格式错误
    if (topicFilterPOS.size() != 1) {
      throw new DataException(ErrorCode.ERROR_SYSTEM, I18nUtil.getMessage(I18nConst.DATA_ERROR));
    }
    FilePO filePO = fileService.getById(updateContrastFilterReqVo.getFileId());
    FileFieldPO fileFieldPO = fileFieldService.getById(updateContrastFilterReqVo.getFieldId());

    TopicFilterPO topicFilterPO = topicFilterPOS.get(0);

    // 填写属性列名称
    topicFilterPO.setParam(Objects.isNull(fileFieldPO) ? null : fileFieldPO.getFieldOrigin());
    topicFilterPO.setFileId(updateContrastFilterReqVo.getFileId());
    topicFilterPO.setFieldId(updateContrastFilterReqVo.getFieldId());
    topicFilterPO.setFileName(filePO.getFilename());

    String paramValue = updateContrastFilterReqVo.getParamValue();
    if (updateContrastFilterReqVo.getTimeUnitEnum() != null && paramValue != null) {
      switch (updateContrastFilterReqVo.getTimeUnitEnum()) {
        case HOUR:
          paramValue = String.valueOf(Integer.parseInt(paramValue) * 3600);
          break;

        case MINUTE:
          paramValue = String.valueOf(Integer.parseInt(paramValue) * 60);
          break;
        case DAY:
          paramValue = String.valueOf(Integer.parseInt(paramValue) * 3600 * 24);
          break;
        default:
          paramValue = "";
      }
    }
    if (updateContrastFilterReqVo.getTimeUnitEnum() != null) {
      topicFilterPO.setTimeUnit(updateContrastFilterReqVo.getTimeUnitEnum().name());
    } else {
      topicFilterPO.setTimeUnit(null);
    }
    // 填写统计类型
    if (updateContrastFilterReqVo.getOperationType() != null) {
      topicFilterPO.setOperation(updateContrastFilterReqVo.getOperationType());
    } else {
      topicFilterPO.setOperation(null);
    }

    // 填写值
    topicFilterPO.setValue(paramValue);

    return topicFilterService.updateById(topicFilterPO);
  }

  @Override
  public Boolean contrastFilterSwap(ContrastFilterSwapReqVo contrastFilterSwapReqVo) {
    List<TopicFilterPO> list =
        topicFilterService.getFilter4Contrast(
            contrastFilterSwapReqVo.getSheetId(),
            Lists.newArrayList(
                TopicFilterBizTypeEnum.PROCESS_LEFT.getValue(),
                TopicFilterBizTypeEnum.PROCESS_RIGHT.getValue()));

    // 对比有两个sheet级别过滤项，如果个数不等于2，则数据格式错误
    if (list.size() != 2) {
      throw new DataException(ErrorCode.ERROR_SYSTEM, I18nUtil.getMessage(I18nConst.DATA_ERROR));
    }
    for (TopicFilterPO topicFilterPO : list) {
      Integer bizType = topicFilterPO.getBizType();
      if (bizType == 20) {
        topicFilterPO.setBizType(21);
      }
      if (bizType == 21) {
        topicFilterPO.setBizType(20);
      }
    }
    boolean result = topicFilterService.updateBatchById(list);

    return result;
  }

  @Override
  public List<TopicSheetKpiOutputVO> getContrastSheetKpi(TopicSheetDataRequest request) {

    List<SheetKpiDTO> topicSheetKpiList = null;
    if (request.getTopicSheetId() != null) {
      topicSheetKpiList = topicSheetKpiService.getTopicSheetKpiList(request.getTopicSheetId());
    } else {
      throw new DataException(
          ErrorCode.ERROR_SYSTEM, I18nUtil.getMessage(I18nConst.DATA_NOT_EXIST));
    }

    List<TopicFilterPO> topicFilterPOS =
        topicFilterService.getFilter4Contrast(
            request.getTopicSheetId(),
            Lists.newArrayList(
                TopicFilterBizTypeEnum.PROCESS_LEFT.getValue(),
                TopicFilterBizTypeEnum.PROCESS_RIGHT.getValue()));

    // 对比有两个sheet级别过滤项，如果个数不等于2，则数据格式错误
    if (topicFilterPOS.size() != 2) {
      throw new DataException(ErrorCode.ERROR_SYSTEM, I18nUtil.getMessage(I18nConst.DATA_ERROR));
    }

    DataModelFileDTO dataModelFilePO =
        topicSheetService.getTopicSheetFileBySheetId(request.getTopicSheetId());
    if (Objects.isNull(dataModelFilePO)) {
      log.warn("sheetId={}未查到数据模型数据", request.getTopicSheetId());
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_MODEL_NOT_EXIST));
    }
    List<TopicSheetKpiOutputVO> results = new ArrayList<>();

    // 遍历计算kpi
    for (SheetKpiDTO sheetKpiDTO : topicSheetKpiList) {
      TopicFilterPO topicFilterPO = topicFilterPOS.get(0);
      TopicFilterPO topicFilterRightPO = topicFilterPOS.get(1);

      List<TopicFilterPO> addFilterList = new ArrayList<>();

      KpiPO kpiPO = new KpiPO();
      kpiPO.setId(sheetKpiDTO.getId());
      kpiPO.setExpression(sheetKpiDTO.getExpression());

      KpiParserParamDTO kpiParserParamDTO = new KpiParserParamDTO();
      kpiParserParamDTO.setKpiPO(kpiPO);
      kpiParserParamDTO.setTopicId(request.getTopicId());
      kpiParserParamDTO.setFileId(dataModelFilePO.getFileId());

      kpiParserParamDTO.setAddFilterList(addFilterList);
      kpiParserParamDTO.setDataModelFileDTO(dataModelFilePO);

      if (StringUtils.isNotBlank(topicFilterPO.getValue())) {
        addFilterList.add(topicFilterPO);
      }

      // 计算左右两次结果
      String kpiResult = null;
      try {
        kpiResult = kpi.calcExpression(kpiParserParamDTO);
      } catch (Exception e) {
        log.error("getContrastSheetKpi left kpi error ", e);
        kpiResult = "--";
      }
      addFilterList.clear();

      if (StringUtils.isNotBlank(topicFilterRightPO.getValue())) {
        addFilterList.add(topicFilterRightPO);
      }

      String kpiResultRight = null;
      try {
        kpiResultRight = kpi.calcExpression(kpiParserParamDTO);
      } catch (Exception e) {
        log.error("getContrastSheetKpi right kpi error ", e);
        kpiResultRight = "--";
      }

      // 封装结果
      TopicSheetKpiOutputVO topicSheetKpiOutputVO = new TopicSheetKpiOutputVO();
      topicSheetKpiOutputVO.setTopicSheetId(request.getTopicSheetId());
      topicSheetKpiOutputVO.setKpiId(sheetKpiDTO.getId());
      topicSheetKpiOutputVO.setName(
          dataApiClient.formatRefer(request.getTopicId(), sheetKpiDTO.getName()));
      topicSheetKpiOutputVO.setValue(kpiResult);
      topicSheetKpiOutputVO.setValueRight(kpiResultRight);
      topicSheetKpiOutputVO.setUnit(
          dataApiClient.formatToPQL(
              request.getTopicId(), request.getTopicSheetId(), sheetKpiDTO.getUnit()));
      topicSheetKpiOutputVO.setOriginalName(sheetKpiDTO.getName());
      topicSheetKpiOutputVO.setOriginalUnit(sheetKpiDTO.getUnit());
      topicSheetKpiOutputVO.setBaseLine(sheetKpiDTO.getBaseLine());
      topicSheetKpiOutputVO.setExpression(sheetKpiDTO.getExpression());
      topicSheetKpiOutputVO.setKpiType(sheetKpiDTO.getType());
      topicSheetKpiOutputVO.setSaveType(
          Objects.equals(sheetKpiDTO.getFromKnowledge(), KpiFromKnowledgeEnum.FROM.getValue())
              ? KpiSaveTypeEnum.KNOWLEDGE_MODEL.getValue()
              : KpiSaveTypeEnum.CUSTOMIZE.getValue());
      topicSheetKpiOutputVO.setFormatting(sheetKpiDTO.getFormatting());
      topicSheetKpiOutputVO.setFormat(sheetKpiDTO.getFormat());
      topicSheetKpiOutputVO.setColumnType(sheetKpiDTO.getColumnType());
      results.add(topicSheetKpiOutputVO);
    }

    return results;
  }

  @Override
  public ProcessExplorerRes getContrastProcessTree(ContrastProcessTreeReqVo request) {
    Boolean contrastRightFlag = request.getContrastRightFlag();
    int bizType;
    if (contrastRightFlag) {
      bizType = 21;
    } else {
      bizType = 20;
    }

    List<TopicFilterPO> topicFilterPOS =
        topicFilterService.getFilter4Contrast(
            request.getTopicSheetId(), Lists.newArrayList(bizType));

    topicFilterPOS =
        topicFilterPOS.stream()
            .filter(
                po ->
                    StringUtils.isNotBlank(po.getParam()) && StringUtils.isNotBlank(po.getValue()))
            .collect(Collectors.toList());
    // 对比有两个sheet级别过滤项，如果个数不等于1
    if (CollectionUtils.isNotEmpty(topicFilterPOS)) {
      request.setContrastRightFlag(true);

      request.setAddFilterList(topicFilterPOS);
    }

    if (request.getValueShowType() != null) {
      request.setValueShowType(request.getValueShowType());
    } else {
      request.setValueShowType(ProcessChartValueTypeEnum.NUMBER.getValue());
    }

    request.setSheetId(request.getTopicSheetId());
    ProcessExplorerRes processExplorerRes = processTreeService.processExplorer(request);
    return processExplorerRes;
  }

  @Autowired FileService fileService;

  @Override
  public List<ContrastFilterResVo> getContrastFilter(String sheetId) {

    List<TopicFilterPO> topicFilterPOS =
        topicFilterService.getFilter4Contrast(
            Integer.valueOf(sheetId),
            Lists.newArrayList(
                TopicFilterBizTypeEnum.PROCESS_LEFT.getValue(),
                TopicFilterBizTypeEnum.PROCESS_RIGHT.getValue()));

    List<Integer> fieldIds =
        topicFilterPOS.stream()
            .filter(po -> po.getFieldId() != null)
            .map(po -> po.getFieldId())
            .collect(Collectors.toList());
    List<Integer> fileIds =
        topicFilterPOS.stream()
            .filter(po -> po.getFileId() != null)
            .map(po -> po.getFileId())
            .collect(Collectors.toList());

    Map<Integer, FileFieldPO> fileFieldMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(fieldIds)) {
      List<FileFieldPO> fileFieldPOS = fileFieldService.listByIds(fieldIds);
      if (CollectionUtils.isNotEmpty(fileFieldPOS)) {
        fileFieldMap =
            fileFieldPOS.stream()
                .collect(Collectors.toMap(FileFieldPO::getId, Function.identity(), (k1, k2) -> k1));
      }
    }
    Map<Integer, String> fileMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(fileIds)) {
      List<FilePO> filePOS = fileService.listByIds(fileIds);
      if (CollectionUtils.isNotEmpty(filePOS)) {
        fileMap =
            filePOS.stream().collect(Collectors.toMap(po -> po.getId(), po -> po.getFilename()));
      }
    }

    List<ContrastFilterResVo> results = new ArrayList<>();

    for (TopicFilterPO topicFilterPO : topicFilterPOS) {
      ContrastFilterResVo contrastFilterResVo = new ContrastFilterResVo();
      results.add(contrastFilterResVo);
      Integer bizType = topicFilterPO.getBizType();
      if (bizType == 20) {
        contrastFilterResVo.setLeftAndRightFlag(0);
      }
      if (bizType == 21) {
        contrastFilterResVo.setLeftAndRightFlag(1);
      }

      Integer operation = topicFilterPO.getOperation();
      contrastFilterResVo.setOperation(operation);

      if (Objects.equals(operation, 41)) {
        contrastFilterResVo.setTimeFlag(true);
      } else {
        contrastFilterResVo.setTimeFlag(false);
      }

      if (topicFilterPO.getFieldId() != null) {
        contrastFilterResVo.setFieldId(topicFilterPO.getFieldId());
        FileFieldPO fieldPO = fileFieldMap.get(topicFilterPO.getFieldId());
        if (Objects.nonNull(fieldPO)) {
          contrastFilterResVo.setFieldName(fieldPO.getFieldOrigin());
          contrastFilterResVo.setFieldType(fieldPO.getFieldType());
        }
      }
      String timeUnit = topicFilterPO.getTimeUnit();
      TimeUnitEnum timeUnitEnumByName = getTimeUnitEnumByName(timeUnit);

      String value = topicFilterPO.getValue();

      if (timeUnitEnumByName != null && StringUtils.isNotBlank(value)) {

        switch (timeUnitEnumByName) {
          case HOUR:
            value = String.valueOf(Integer.parseInt(value) / 3600);
            break;

          case MINUTE:
            value = String.valueOf(Integer.parseInt(value) / 60);
            break;
          case DAY:
            value = String.valueOf(Integer.parseInt(value) / (3600 * 24));
            break;
          default:
            value = "";
        }
      }
      contrastFilterResVo.setTimeUnit(timeUnitEnumByName);

      contrastFilterResVo.setFileId(topicFilterPO.getFileId());
      contrastFilterResVo.setFileName(fileMap.get(topicFilterPO.getFileId()));
      contrastFilterResVo.setParamValue(value);
    }

    return results;
  }

  @Override
  public Boolean uploadEditLock(TryLockEditLockReq tryLockEditLockReq) {
    UserDetailsToken userInfo = userInfoUtil.getUserInfo();
    boolean update =
        componentEditLockService.update(
            new LambdaUpdateWrapper<ComponentEditLockPo>()
                .eq(
                    ComponentEditLockPo::getLockComponentId,
                    tryLockEditLockReq.getLockComponentId())
                .eq(ComponentEditLockPo::getEditLockEnum, tryLockEditLockReq.getEditLockEnum())
                .eq(ComponentEditLockPo::getHoldId, userInfo.getUserId())
                .set(ComponentEditLockPo::getLastUpdateTime, LocalDateTime.now()));
    return update;
  }

  @Override
  public TryLockEditLockRes tryLockEditLock(TryLockEditLockReq tryLockEditLockReq) {
    UserDetailsToken userInfo = userInfoUtil.getUserInfo();
    TryLockEditLockRes result = new TryLockEditLockRes();
    if (Objects.equals(editLockEnable, "false")) {
      result.setLockFlag(true);
      return result;
    }

    if (StringUtils.isNotBlank(tryLockEditLockReq.getUnlockComponentId())) {
      RemoveLockReq removeLockReq = new RemoveLockReq();
      removeLockReq.setLockComponentId(tryLockEditLockReq.getUnlockComponentId());
      removeLockReq.setEditLockEnum(tryLockEditLockReq.getEditLockEnum());
      unlockEditLock(removeLockReq);
    }

    List<ComponentEditLockPo> componentEditLockPos =
        componentEditLockService.list(
            new LambdaQueryWrapper<ComponentEditLockPo>()
                .eq(
                    ComponentEditLockPo::getLockComponentId,
                    tryLockEditLockReq.getLockComponentId()));

    List<ComponentEditLockPo> editLockPos =
        componentEditLockPos.stream()
            .filter(
                po ->
                    DateTimeUtil.timeDec(po.getLastUpdateTime(), LocalDateTime.now())
                        <= editLockComponents)
            .collect(Collectors.toList());
    componentEditLockPos.removeAll(editLockPos);

    // 收集过期并移除
    if (componentEditLockPos.size() > 0) {
      Set<Integer> collect =
          componentEditLockPos.stream().map(ComponentEditLockPo::getId).collect(Collectors.toSet());
      componentEditLockService.removeByIds(collect);
    }

    ComponentEditLockPo currentComponentEditLockPo;
    if (CollectionUtils.isEmpty(editLockPos)) {
      currentComponentEditLockPo = new ComponentEditLockPo();
      currentComponentEditLockPo.setHoldId(userInfo.getUserId());
      currentComponentEditLockPo.setHoldName(userInfo.getUserName());
      currentComponentEditLockPo.setLockComponentId(tryLockEditLockReq.getLockComponentId());
      currentComponentEditLockPo.setEditLockEnum(tryLockEditLockReq.getEditLockEnum().name());
      currentComponentEditLockPo.setLastUpdateTime(LocalDateTime.now());
      componentEditLockService.save(currentComponentEditLockPo);
      result.setLockFlag(true);
      result.setIsMyselfLocked(true);
    } else {
      currentComponentEditLockPo = editLockPos.get(0);
      // 本人锁
      if (Objects.equals(currentComponentEditLockPo.getHoldId(), userInfo.getUserId())) {
        result.setLockFlag(true);
        result.setIsMyselfLocked(true);
      }
    }
    result.setHoldName(currentComponentEditLockPo.getHoldName());
    result.setHoldId(currentComponentEditLockPo.getHoldId());
    result.setRemainingTime(
        editLockComponents
            - DateTimeUtil.timeDec(
                currentComponentEditLockPo.getLastUpdateTime(), LocalDateTime.now()));

    return result;
  }

  @Override
  public Boolean unlockEditLock(RemoveLockReq removeLockReq) {
    Integer userId = userInfoUtil.getUserInfo().getUserId();
    // 释放该用户之前的锁
    return componentEditLockService.remove(
        new LambdaQueryWrapper<ComponentEditLockPo>()
            .eq(ComponentEditLockPo::getEditLockEnum, removeLockReq.getEditLockEnum().name())
            .eq(ComponentEditLockPo::getLockComponentId, removeLockReq.getLockComponentId())
            .eq(ComponentEditLockPo::getHoldId, userId));
  }

  @Override
  public boolean cancelSheetRelease(CancelSheetReleaseReq cancelSheetReleaseReq) {
    List<BusinessTopicPO> businessTopicPOS =
        businessTopicService.list(
            new LambdaQueryWrapper<BusinessTopicPO>()
                .eq(BusinessTopicPO::getSnapshotFlag, 1)
                .eq(BusinessTopicPO::getSnapshotParentId, cancelSheetReleaseReq.getTopicId())
                .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (businessTopicPOS.size() == 0) {
      return false;
    }
    for (BusinessTopicPO businessTopicPO : businessTopicPOS) {
      businessTopicPO.setDeleted(DeletedEnum.HAS_DELETED.getValue());
    }
    businessTopicService.updateBatchById(businessTopicPOS);
    Integer snapShotId = businessTopicPOS.get(0).getId();
    SheetReleaseReqVo sheetReleaseReqVo = new SheetReleaseReqVo();
    sheetReleaseReqVo.setTopicId(cancelSheetReleaseReq.getTopicId().toString());
    sheetReleaseReqVo.setUserId(userInfoUtil.getUserInfo().getUserId().toString());

    saveSnapshotLog(
        sheetReleaseReqVo, snapShotId, TopicSnapshotLogPo.ReleaseTypeEnum.CANCEL_RELEASE);

    return true;
  }

  /**
   * 拷贝topic数据
   *
   * @param businessTopicPO
   * @return snapshot BusinessTopicPO
   */
  @Override
  public BusinessTopicPO doCopyTopic(BusinessTopicPO businessTopicPO) {

    // 查询有无topic快照
    List<BusinessTopicPO> businessTopics =
        businessTopicService.list(
            new LambdaQueryWrapper<BusinessTopicPO>()
                .eq(BusinessTopicPO::getSnapshotParentId, businessTopicPO.getId())
                .eq(BusinessTopicPO::getSnapshotFlag, BusinessTopicPO.SNAPSHOT_TRUE)
                .orderByDesc(BusinessTopicPO::getId));

    if (CollectionUtils.isNotEmpty(businessTopics)) {
      BusinessTopicPO one = businessTopics.get(0);
      one.setName(businessTopicPO.getName());
      one.setDeleted(businessTopicPO.getDeleted());
      businessTopicService.updateById(one);
      return one;
    }
    BusinessTopicPO businessTopicSnapshot = new BusinessTopicPO();
    businessTopicSnapshot.setName(businessTopicPO.getName());
    businessTopicSnapshot.setType(businessTopicPO.getType());
    businessTopicSnapshot.setLevel(businessTopicPO.getLevel());
    businessTopicSnapshot.setParentId(businessTopicPO.getParentId());
    businessTopicSnapshot.setSnapshotFlag(BusinessTopicPO.SNAPSHOT_TRUE);
    businessTopicSnapshot.setSnapshotParentId(businessTopicPO.getId());
    businessTopicService.save(businessTopicSnapshot);

    return businessTopicSnapshot;
  }

  /**
   * 拷贝topicSheet
   *
   * @param businessTopicPO 返回结果topicSheetId
   */
  @Override
  public Map<Integer, Integer> doCopyTopicSheet(
      BusinessTopicPO businessTopicPO, BusinessTopicPO snapshotBusinessTopicPO) {
    Integer topicId = businessTopicPO.getId();
    Integer snapshotTopicId = snapshotBusinessTopicPO.getId();
    // 获取当前topic的sheet信息
    List<TopicSheetPO> topicSheetPOS =
        topicSheetService.list(
            new LambdaQueryWrapper<TopicSheetPO>()
                .eq(TopicSheetPO::getTopicId, topicId)
                .eq(TopicSheetPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(TopicSheetPO::getId));

    // 获取快照topic的sheet信息
    List<TopicSheetPO> snapshotTopicSheetPOS =
        topicSheetService.list(
            new LambdaQueryWrapper<TopicSheetPO>()
                .eq(TopicSheetPO::getTopicId, snapshotTopicId)
                .eq(TopicSheetPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(TopicSheetPO::getId));

    List<Integer> topicSheetIds =
        topicSheetPOS.stream().map(TopicSheetPO::getId).collect(Collectors.toList());

    Map<Integer, TopicSheetPO> originalSnapshotTopicSheetMap = new HashMap<>();
    List<Integer> removeSheetId = new ArrayList<>();
    for (TopicSheetPO snapshotTopicSheetPO : snapshotTopicSheetPOS) {
      if (topicSheetIds.contains(snapshotTopicSheetPO.getSnapshotParentId())) {
        originalSnapshotTopicSheetMap.put(
            snapshotTopicSheetPO.getSnapshotParentId(), snapshotTopicSheetPO);
      } else {
        removeSheetId.add(snapshotTopicSheetPO.getId());
      }
    }
    if (removeSheetId.size() > 0) {
      topicSheetService.removeByIds(removeSheetId);
    }

    return this.doCopyTopicSheet(topicSheetPOS, snapshotTopicId, originalSnapshotTopicSheetMap);
  }

  /**
   * @param topicSheetPOS
   * @return key sheetId,value 快照的sheetId
   */
  @Override
  public Map<Integer, Integer> doCopyTopicSheet(
      List<TopicSheetPO> topicSheetPOS,
      Integer snapshotTopicId,
      Map<Integer, TopicSheetPO> originalSnapshotTopicSheetMap) {
    Map<Integer, Integer> results = new HashMap<>();

    List<Integer> sheetIdList = new ArrayList<>();

    // 更新 or 创建
    for (int i = 0; i < topicSheetPOS.size(); i++) {
      boolean saveFlag = false;
      TopicSheetPO topicSheetPO = topicSheetPOS.get(i);
      TopicSheetPO snapshotTopicSheetPO;
      if (originalSnapshotTopicSheetMap.containsKey(topicSheetPO.getId())) {
        snapshotTopicSheetPO = originalSnapshotTopicSheetMap.get(topicSheetPO.getId());
      } else {
        snapshotTopicSheetPO = new TopicSheetPO();
        snapshotTopicSheetPO.setSnapshotParentId(topicSheetPO.getId());
        saveFlag = true;
      }

      snapshotTopicSheetPO.setTopicId(snapshotTopicId);
      snapshotTopicSheetPO.setType(topicSheetPO.getType());
      snapshotTopicSheetPO.setOrder(topicSheetPO.getOrder());
      snapshotTopicSheetPO.setName(topicSheetPO.getName());
      snapshotTopicSheetPO.setConfigMap(topicSheetPO.getConfigMap());
      if (saveFlag) {
        topicSheetService.save(snapshotTopicSheetPO);
      } else {
        topicSheetService.updateById(snapshotTopicSheetPO);
      }

      sheetIdList.add(topicSheetPO.getId());
      sheetMenuService.remove(
          new LambdaQueryWrapper<SheetMenuPO>()
              .eq(SheetMenuPO::getSheetId, snapshotTopicSheetPO.getId()));
      SheetMenuPO one =
          sheetMenuService.getOne(
              new LambdaQueryWrapper<SheetMenuPO>()
                  .eq(SheetMenuPO::getSheetId, topicSheetPO.getId())
                  .last(LIMIT_1));
      if (Objects.nonNull(one)) {
        one.setSheetId(snapshotTopicSheetPO.getId());
        one.setId(null);
        sheetMenuService.save(one);
      }

      // 吞吐时间的json参数信息
      sheetParamService.remove(
          new LambdaQueryWrapper<SheetParamPO>()
              .eq(SheetParamPO::getSheetId, snapshotTopicSheetPO.getId()));
      List<SheetParamPO> sheetParamList =
          sheetParamService.list(
              new LambdaQueryWrapper<SheetParamPO>()
                  .eq(SheetParamPO::getSheetId, topicSheetPO.getId())
                  .eq(SheetParamPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      if (CollectionUtils.isNotEmpty(sheetParamList)) {
        sheetParamList.forEach(
            f -> {
              f.setId(null);
              f.setSheetId(snapshotTopicSheetPO.getId());
            });
        sheetParamService.saveBatch(sheetParamList);
      }
      if (topicSheetPO.getType() != null && topicSheetPO.getType() == 6) {
        List<AllowProcessPO> list = allowProcessService.getList(topicSheetPO.getId());
        // clear first
        allowProcessService.remove(
            new LambdaQueryWrapper<AllowProcessPO>()
                .eq(AllowProcessPO::getSheetId, snapshotTopicSheetPO.getId()));
        if (!list.isEmpty()) {
          list.forEach(
              t -> {
                t.setId(null);
                t.setSheetId(snapshotTopicSheetPO.getId());
              });
          allowProcessService.saveBatch(list);
        }
        conformanceServiceScala.invalidSheetData(snapshotTopicSheetPO.getId());
      }

      // 如果是对比sheet则拷贝过滤项目
      if (topicSheetPO.getType() != null && topicSheetPO.getType() == 10) {
        // 流程对比

        List<TopicFilterPO> topicFilterPOS =
            topicFilterService.getFilter4Contrast(
                topicSheetPO.getId(),
                Lists.newArrayList(
                    TopicFilterBizTypeEnum.PROCESS_LEFT.getValue(),
                    TopicFilterBizTypeEnum.PROCESS_RIGHT.getValue()));

        topicFilterService.remove(
            new LambdaQueryWrapper<TopicFilterPO>()
                .eq(TopicFilterPO::getSheetId, snapshotTopicSheetPO.getId())
                .in(TopicFilterPO::getBizType, Arrays.asList(20, 21)));

        for (TopicFilterPO topicFilterPO : topicFilterPOS) {
          // 创建过滤项
          TopicFilterPO snapshotTopicFilterPO = topicFilterPO.copy();
          snapshotTopicFilterPO.setSheetId(snapshotTopicSheetPO.getId());
          topicFilterService.save(snapshotTopicFilterPO);
        }
      }

      results.put(topicSheetPO.getId(), snapshotTopicSheetPO.getId());
    }
    return results;
  }

  private void copyMultiProcess(Integer originTopicId, Integer newTopicId) {
    List<TopicMultiProcessDTO> topicMultiProcessList =
        iTopicMultiProcessService.getTopicMultiProcessList(originTopicId);
    if (CollectionUtils.isEmpty(topicMultiProcessList)) {
      return;
    }

    topicMultiProcessList =
        topicMultiProcessList.stream()
            .map(
                dto -> {
                  dto.setTopicId(newTopicId);
                  return dto;
                })
            .collect(Collectors.toList());
    iTopicMultiProcessService.saveTopicMultiProcess(topicMultiProcessList);
  }
}
