package com.sp.proxverse.engine.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.action.bo.ActionBo;
import com.sp.proxverse.common.action.enums.ActionTypeEnum;
import com.sp.proxverse.common.exception.DataException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.mapper.ActionLogMapper;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.bo.actionLog.CreatActionLogBo;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dto.domain.Admin;
import com.sp.proxverse.common.model.dto.domain.BaseEntity;
import com.sp.proxverse.common.model.enums.ActionRunStartEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.page.PageRespList;
import com.sp.proxverse.common.model.po.*;
import com.sp.proxverse.common.model.vo.action.ActionFlowLogResVo;
import com.sp.proxverse.common.model.vo.action.ActionLogResVo;
import com.sp.proxverse.common.model.vo.action.ActionRunStartVo;
import com.sp.proxverse.common.model.vo.action.GetActionDetailLogReqVo;
import com.sp.proxverse.common.model.vo.action.GetActionFlowLogsReqVo;
import com.sp.proxverse.common.model.vo.action.GetActionLogsReqVo;
import com.sp.proxverse.common.model.vo.action.RunActionFlowStatusResVo;
import com.sp.proxverse.common.model.vo.action.RunActionLogResVo;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.common.common.CommonBean;
import com.sp.proxverse.interfaces.common.util.JsonUtil;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicService;
import com.sp.proxverse.interfaces.dao.service.IActionFlowService;
import com.sp.proxverse.interfaces.dao.service.IActionLogService;
import com.sp.proxverse.interfaces.dao.service.IActionService;
import com.sp.proxverse.interfaces.dao.service.IAdminService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 执行动作日志 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Service
@Primary
public class ActionLogServiceImpl extends ServiceImpl<ActionLogMapper, ActionLogPo>
    implements IActionLogService {

  @Autowired IActionFlowService actionFlowService;

  @Autowired CommonBean commonBean;

  @Autowired private IAdminService adminService;

  @Autowired IActionService actionService;

  @Autowired ActionLogMapper actionLogMapper;

  @Override
  public PageResp getActionDetailLogs(GetActionDetailLogReqVo getActionRunLogReqVo) {

    PageRespList<RunActionLogResVo> response = new PageRespList<>();

    ActionFlowPo actionFlowPo =
        actionFlowService.getActionFlowByTopicId(getActionRunLogReqVo.getTopicId());

    Integer actionFlowId = actionFlowPo.getId();
    if (getActionRunLogReqVo.getSourceType() != null && getActionRunLogReqVo.getSourceType() == 2) {
      List<BusinessTopicPO> snapshotTopic =
          businessTopicService.list(
              new LambdaQueryWrapper<BusinessTopicPO>()
                  .eq(BusinessTopicPO::getSnapshotParentId, getActionRunLogReqVo.getTopicId())
                  .eq(BusinessTopicPO::getSnapshotFlag, 1)
                  .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      if (CollectionUtils.isNotEmpty(snapshotTopic)) {
        ActionFlowPo actionFlowSnapshotByTopicId =
            actionFlowService.getActionFlowByTopicId(snapshotTopic.get(0).getId());
        actionFlowId = actionFlowSnapshotByTopicId.getId();
      }
    }

    List<ActionPo> actionByTopicId =
        actionService.getActionByTopicId(getActionRunLogReqVo.getTopicId());

    if (CollectionUtils.isEmpty(actionByTopicId)) {
      response.setTotal(0L);
      return PageResp.success(response);
    }
    Map<Integer, ActionPo> actionPoMap =
        actionByTopicId.stream().collect(Collectors.toMap(ActionPo::getId, ActionPo -> ActionPo));

    LambdaQueryWrapper<ActionLogPo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.eq(ActionLogPo::getActionFlowFlag, ActionLogPo.ACTION_FLOW_FALSE);
    lambdaQueryWrapper.eq(ActionLogPo::getActionFlowId, actionFlowId);

    if (getActionRunLogReqVo.getRunCode() != null) {
      lambdaQueryWrapper.eq(ActionLogPo::getRunCode, getActionRunLogReqVo.getRunCode());
    } else if (getActionRunLogReqVo.isCurrentFlag()) {
      lambdaQueryWrapper.eq(ActionLogPo::getRunCode, actionFlowPo.getRunCode());
    }

    boolean actionCodeFlag = false;
    if (getActionRunLogReqVo.getActionCode() != null && getActionRunLogReqVo.getActionCode() > 0) {
      lambdaQueryWrapper.eq(ActionLogPo::getActionCode, getActionRunLogReqVo.getActionCode());
      actionCodeFlag = true;
    }

    IPage<ActionLogPo> dataPage =
        this.page(
            new Page<>(getActionRunLogReqVo.getPageNum(), getActionRunLogReqVo.getPageSize()),
            lambdaQueryWrapper);

    List<RunActionLogResVo> collect = new ArrayList<>();

    for (ActionLogPo list : dataPage.getRecords()) {
      RunActionLogResVo runActionLogResVo = new RunActionLogResVo();
      runActionLogResVo.setActionType(
          ActionTypeEnum.getActionTypeByCode(list.getActiveType()).name());
      runActionLogResVo.setActionCode(list.getActionCode());
      runActionLogResVo.setResult(JsonUtil.stringToObject(list.getActionResult()));
      runActionLogResVo.setParam(JsonUtil.stringToObject(list.getActionParam()));
      runActionLogResVo.setSuccessFlag(list.getSuccessFlag());
      runActionLogResVo.setCreateTime(DateTimeUtil.date2String(list.getCreateTime()));
      runActionLogResVo.setActionName(list.getActionName());
      collect.add(runActionLogResVo);
    }
    for (ActionLogPo list : dataPage.getRecords()) {
      actionPoMap.remove(list.getActionId());
    }
    // 处理未运行的
    if (!actionCodeFlag) {
      for (Map.Entry<Integer, ActionPo> integerActionPoEntry : actionPoMap.entrySet()) {
        RunActionLogResVo runActionLogResVo = new RunActionLogResVo();
        runActionLogResVo.setActionType(
            integerActionPoEntry.getValue().getActiveTypeEnum().getDisplayName());
        runActionLogResVo.setActionCode(integerActionPoEntry.getValue().getActionCode());
        runActionLogResVo.setSuccessFlag(-1);
        runActionLogResVo.setActionName(integerActionPoEntry.getValue().getName());
        runActionLogResVo.setActionCode(integerActionPoEntry.getValue().getActionCode());
        collect.add(runActionLogResVo);
      }
    }

    response.setTotal(dataPage.getTotal());
    response.setList(collect);
    response.setPageNum((int) dataPage.getPages());
    response.setPageSize((int) dataPage.getSize());
    return PageResp.success(response);
  }

  @Override
  public RunActionFlowStatusResVo getRunActionFlowStatus(String topicId) {
    RunActionFlowStatusResVo result = new RunActionFlowStatusResVo();

    // 获取runCode
    ActionFlowPo actionFlowByTopicId =
        actionFlowService.getActionFlowByTopicId(Integer.valueOf(topicId));
    if (actionFlowByTopicId == null) {
      throw new DataException(
          ErrorCode.ERROR_SYSTEM, I18nUtil.getMessage(I18nConst.TOPIC_NOT_EXIST));
    }
    List<ActionPo> actionPos =
        actionService.list(
            new LambdaQueryWrapper<ActionPo>()
                .eq(ActionPo::getActionFlowId, actionFlowByTopicId.getId())
                .eq(ActionPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    if (commonBean.getActionResult(actionFlowByTopicId.getId(), actionFlowByTopicId.getRunCode())
        != null) {
      ActionBo actionResult =
          (ActionBo)
              commonBean.getActionResult(
                  actionFlowByTopicId.getId(), actionFlowByTopicId.getRunCode());
      Set<Integer> integers = actionResult.getActionResultRecord().keySet();
      List<ActionRunStartVo> actionRunStartVos = new ArrayList<>();
      for (ActionPo actionPo : actionPos) {
        ActionRunStartVo actionRunStartVo = new ActionRunStartVo();
        actionRunStartVo.setActionCode(actionPo.getActionCode());

        if (actionPo.getActionCode().equals(actionResult.getActionCode())) {
          actionRunStartVo.setStatus(ActionRunStartEnum.IN_EXECUTION);
        } else if (integers.contains(actionPo.getActionCode())) {
          actionRunStartVo.setStatus(ActionRunStartEnum.EXECUTED);
        } else {
          actionRunStartVo.setStatus(ActionRunStartEnum.NOT_EXECUTION);
        }
        actionRunStartVos.add(actionRunStartVo);
      }
      result.setActionStatus(actionRunStartVos);
      result.setRunStatus(ActionRunStartEnum.IN_EXECUTION);
    } else {
      result.setRunStatus(ActionRunStartEnum.EXECUTED);
    }

    return result;
  }

  @Override
  public Boolean creatActionRunLog(CreatActionLogBo creatActionLogBo) {
    ActionLogPo actionLogPo = new ActionLogPo();
    BeanUtils.copyProperties(creatActionLogBo, actionLogPo);
    actionLogPo.setCreateTime(new Date());
    this.save(actionLogPo);
    return true;
  }

  @Override
  public Integer startActionRunLog(CreatActionLogBo creatActionLogBo, Integer tenantId) {
    ActionLogPo actionLogPo = new ActionLogPo();
    actionLogPo.setActionCode(creatActionLogBo.getActionCode());
    actionLogPo.setActionId(creatActionLogBo.getActionId());
    actionLogPo.setActionFlowId(creatActionLogBo.getActionFlowId());
    actionLogPo.setState(ActionRunStartEnum.IN_EXECUTION.name());
    actionLogPo.setActionParam(creatActionLogBo.getActionParam());
    actionLogPo.setRunCode(creatActionLogBo.getRunCoed());
    actionLogPo.setActiveType(creatActionLogBo.getActiveType());
    actionLogPo.setActionName(creatActionLogBo.getActionName());
    actionLogPo.setActionFlowFlag(creatActionLogBo.getActionFlowFlag());
    actionLogPo.setSingleNodeFlag(creatActionLogBo.getSingleNodeFlagInt());
    actionLogPo.setNodeInfo(creatActionLogBo.getNodeInfo());
    actionLogPo.setSourceType(creatActionLogBo.getSourceType());
    actionLogPo.setCreateTime(new Date());

    if (creatActionLogBo.getUserId() != null) {
      actionLogPo.setCreateUser(creatActionLogBo.getUserId());
    }
    if (tenantId != null) {
      actionLogPo.setTenantId(tenantId);
    }
    this.save(actionLogPo);
    return actionLogPo.getId();
  }

  @Override
  public Boolean endActionRunLog(CreatActionLogBo creatActionLogBo) {
    this.update(
        new LambdaUpdateWrapper<ActionLogPo>()
            .eq(ActionLogPo::getId, creatActionLogBo.getActionLogId())
            .set(ActionLogPo::getActionResult, creatActionLogBo.getActionResult())
            .set(ActionLogPo::getState, ActionRunStartEnum.EXECUTED.name())
            .set(ActionLogPo::getUpdateTime, new Date())
            .set(ActionLogPo::getSuccessFlag, creatActionLogBo.getSuccessFlag()));
    return true;
  }

  @Override
  public List<ActionLogPo> getNewestRunLog(Integer actionFlowId) {
    return actionLogMapper.getNewestList(actionFlowId);
  }

  @Override
  public PageResp getActionLogs(GetActionLogsReqVo getActionLogsReqVo) {
    PageRespList<ActionLogResVo> response = new PageRespList<>();
    ActionFlowPo actionFlowByTopicId =
        actionFlowService.getActionFlowByTopicId(getActionLogsReqVo.getTopicId());

    Integer actionFlowId = actionFlowByTopicId.getId();

    if (getActionLogsReqVo.getSourceType() != null && getActionLogsReqVo.getSourceType() == 2) {
      List<BusinessTopicPO> snapshotTopic =
          businessTopicService.list(
              new LambdaQueryWrapper<BusinessTopicPO>()
                  .eq(BusinessTopicPO::getSnapshotParentId, getActionLogsReqVo.getTopicId())
                  .eq(BusinessTopicPO::getSnapshotFlag, 1)
                  .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      if (CollectionUtils.isNotEmpty(snapshotTopic)) {
        ActionFlowPo actionFlowSnapshotByTopicId =
            actionFlowService.getActionFlowByTopicId(snapshotTopic.get(0).getId());
        actionFlowId = actionFlowSnapshotByTopicId.getId();
      }
    }

    LambdaQueryWrapper<ActionLogPo> lambdaQueryWrapper =
        new LambdaQueryWrapper<ActionLogPo>()
            .eq(ActionLogPo::getActionFlowId, actionFlowId)
            .eq(ActionLogPo::getActionFlowFlag, ActionLogPo.ACTION_FLOW_FALSE)
            .orderByAsc(ActionLogPo::getId);
    if (getActionLogsReqVo.getActionCode() != null && getActionLogsReqVo.getActionCode() > 0) {
      lambdaQueryWrapper.eq(ActionLogPo::getActionCode, getActionLogsReqVo.getActionCode());
    }
    if (getActionLogsReqVo.getRunCode() != null) {
      lambdaQueryWrapper.eq(ActionLogPo::getRunCode, getActionLogsReqVo.getRunCode());
    }

    // 过滤掉路由
    lambdaQueryWrapper.ne(ActionLogPo::getActiveType, ActionTypeEnum.ROUTE.getCode());

    Page<ActionLogPo> page =
        this.page(
            new Page<>(getActionLogsReqVo.getPageNum(), getActionLogsReqVo.getPageSize()),
            lambdaQueryWrapper);
    List<Integer> userIds =
        page.getRecords().stream()
            .map(po -> po.getCreateUser())
            .distinct()
            .collect(Collectors.toList());
    Map<Integer, String> userNameMap = commonBean.getUserName(userIds);

    List<ActionLogResVo> collect = new ArrayList<>();
    for (ActionLogPo record : page.getRecords()) {
      ActionLogResVo actionLogResVo = new ActionLogResVo();
      actionLogResVo.setActionType(
          ActionTypeEnum.getActionTypeByCode(record.getActiveType()).getDescribe());
      actionLogResVo.setActionCode(record.getActionCode());
      actionLogResVo.setRunTime(DateTimeUtil.date2String(record.getCreateTime()));
      actionLogResVo.setActionRunStartEnum(
          ActionRunStartEnum.getActionRunStartEnumByCode(record.getSuccessFlag()));
      actionLogResVo.setRunUser(
          userNameMap.getOrDefault(
              record.getCreateUser(), I18nUtil.getMessage(I18nConst.SIGNAL_AUTOMATIC_EXECUTION)));
      actionLogResVo.setConsumingTime(
          DateTimeUtil.timeDec(record.getCreateTime(), record.getUpdateTime()).toString());
      actionLogResVo.setActionName(record.getActionName());
      collect.add(actionLogResVo);
    }

    response.setTotal(page.getTotal());
    response.setList(collect);
    response.setPageNum((int) page.getPages());
    response.setPageSize((int) page.getSize());
    return PageResp.success(response);
  }

  @Autowired BusinessTopicService businessTopicService;

  @Override
  public PageResp getActionFlowLogs(GetActionFlowLogsReqVo getActionFlowLogsReqVo) {
    ActionFlowPo actionFlowByTopicId =
        actionFlowService.getActionFlowByTopicId(getActionFlowLogsReqVo.getTopicId());
    if (actionFlowByTopicId == null) {
      throw new DataException(
          ErrorCode.ERROR_SYSTEM, I18nUtil.getMessage(I18nConst.ACTION_FLOW_NOT_EXIST));
    }
    List<Integer> actionFlowIds = new ArrayList<>();
    actionFlowIds.add(actionFlowByTopicId.getId());

    List<BusinessTopicPO> snapshotTopic =
        businessTopicService.list(
            new LambdaQueryWrapper<BusinessTopicPO>()
                .eq(BusinessTopicPO::getSnapshotParentId, getActionFlowLogsReqVo.getTopicId())
                .eq(BusinessTopicPO::getSnapshotFlag, 1)
                .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isNotEmpty(snapshotTopic)) {
      ActionFlowPo actionFlowSnapshotByTopicId =
          actionFlowService.getActionFlowByTopicId(snapshotTopic.get(0).getId());
      actionFlowIds.add(actionFlowSnapshotByTopicId.getId());
    }

    // root 操作日志不允许展示给用户
    List<Admin> adminList =
        adminService.list(new LambdaQueryWrapper<Admin>().like(Admin::getUserName, "ROOT@"));
    List<Integer> userIdList =
        adminList.stream().map(BaseEntity::getId).collect(Collectors.toList());

    LambdaQueryWrapper<ActionLogPo> queryWrapper =
        new LambdaQueryWrapper<ActionLogPo>()
            .in(ActionLogPo::getActionFlowId, actionFlowIds)
            .notIn(ActionLogPo::getCreateUser, userIdList)
            .and(
                wapper ->
                    wapper
                        .eq(ActionLogPo::getActionFlowFlag, ActionLogPo.ACTION_FLOW_TRUE)
                        .or()
                        .eq(ActionLogPo::getSingleNodeFlag, ActionLogPo.SINGLE_NODE_TRUE));

    long count = this.count(queryWrapper);

    queryWrapper = queryWrapper.orderByDesc(ActionLogPo::getId);

    Page<ActionLogPo> page =
        this.page(
            new Page<>(getActionFlowLogsReqVo.getPageNum(), getActionFlowLogsReqVo.getPageSize()),
            queryWrapper);
    List<Integer> userIds =
        page.getRecords().stream()
            .map(ActionLogPo::getCreateUser)
            .distinct()
            .collect(Collectors.toList());
    Map<Integer, Admin> userInfoMap = adminService.getUserInfoByIds(userIds);

    List<ActionFlowLogResVo> collect = new ArrayList<>();
    for (ActionLogPo record : page.getRecords()) {
      Admin admin = userInfoMap.getOrDefault(record.getCreateUser(), new Admin());
      ActionFlowLogResVo actionFlowLogResVo = new ActionFlowLogResVo();
      actionFlowLogResVo.setRunTime(DateTimeUtil.date2String(record.getCreateTime()));
      actionFlowLogResVo.setActionRunStartEnum(
          ActionRunStartEnum.getActionRunStartEnumByCode(record.getSuccessFlag()));
      actionFlowLogResVo.setRunUser(admin.getUserName() == null ? "admin" : admin.getUserName());
      actionFlowLogResVo.setName(admin.getName());
      actionFlowLogResVo.setConsumingTime(
          DateTimeUtil.timeDec(record.getCreateTime(), record.getUpdateTime()).toString());
      actionFlowLogResVo.setRunCode(record.getRunCode());
      actionFlowLogResVo.setActionCode(record.getActionCode());
      actionFlowLogResVo.setSingleNodeFlag(record.getSingleNodeFlagBoolean());
      actionFlowLogResVo.setNodeInfo(record.getNodeInfo());
      actionFlowLogResVo.setSourceType(record.getSourceType());
      collect.add(actionFlowLogResVo);
    }

    PageRespList<ActionFlowLogResVo> response = new PageRespList<>();
    response.setTotal(count);
    response.setList(collect);
    response.setPageNum((int) page.getPages());
    response.setPageSize((int) page.getSize());
    return PageResp.success(response);
  }

  @Override
  public ActionLogPo getSingleNodeLast(Integer actionFlowId, Integer actionCode) {
    LambdaQueryWrapper<ActionLogPo> last =
        new LambdaQueryWrapper<ActionLogPo>()
            .eq(ActionLogPo::getActionFlowId, actionFlowId)
            .eq(ActionLogPo::getSingleNodeFlag, ActionLogPo.SINGLE_NODE_TRUE)
            .eq(ActionLogPo::getActionCode, actionCode)
            .orderByDesc(ActionLogPo::getId)
            .last("LIMIT 1");

    return this.getOne(last);
  }
}
