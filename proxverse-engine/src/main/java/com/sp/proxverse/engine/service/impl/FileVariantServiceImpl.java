package com.sp.proxverse.engine.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.common.mapper.FileVariantPOMapper;
import com.sp.proxverse.common.model.dict.TopicFilterTypeEnum;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.po.FileVariantPO;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.engine.service.biz.ProcessService;
import com.sp.proxverse.interfaces.dao.service.ConformanceService;
import com.sp.proxverse.interfaces.dao.service.FileVariantService;
import com.sp.proxverse.interfaces.dao.service.TopicFilterService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class FileVariantServiceImpl extends ServiceImpl<FileVariantPOMapper, FileVariantPO>
    implements FileVariantService {

  @Autowired ConformanceService conformanceService;

  @Autowired TopicFilterService topicFilterService;

  @Autowired private PQLService pqlService;

  @Autowired private ProcessService processService;

  @Autowired private ModelDescFactory modelDescFactory;

  @SneakyThrows
  @Override
  public List<FileVariantPO> getCurrentVariantList(
      Integer fileId,
      Integer topicId,
      Integer sheetId,
      Integer dataModelId,
      List<String> variantIdList) {

    ModelDesc orCreate = modelDescFactory.getOrCreate(dataModelId);

    String variantExpre = processService.makeVariantExpre(orCreate, topicId);
    List<KpiPO> dimensionKpiList = new ArrayList<>();
    dimensionKpiList.add(KpiPO.builder().name("variant").expression(variantExpre).build());
    dimensionKpiList.add(
        KpiPO.builder()
            .name("count")
            .expression(
                "count("
                    + orCreate.baseVirtualCaseTable().tableName()
                    + "."
                    + orCreate.baseVirtualCaseTable().caseColumn()
                    + ")")
            .build());

    List<TopicFilterPO> filters =
        variantIdList.stream()
            .map(
                m ->
                    TopicFilterPO.builder()
                        .type(TopicFilterTypeEnum.VARIANT.getValue())
                        .value(m)
                        .build())
            .collect(Collectors.toList());

    Result result =
        processService.queryPQLResult(dataModelId, topicId, sheetId, dimensionKpiList, filters);

    List<FileVariantPO> list = result.getList(FileVariantPO.class);

    return list;
  }
}
