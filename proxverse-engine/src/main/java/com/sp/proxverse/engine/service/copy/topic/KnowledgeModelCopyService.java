package com.sp.proxverse.engine.service.copy.topic;

import com.sp.proxverse.common.model.dict.BusinessKpiTypeEnum;
import com.sp.proxverse.engine.service.copy.BusinessCopyService;
import com.sp.proxverse.engine.service.copy.BusinessService;
import com.sp.proxverse.engine.service.copy.entity.CopyParent;
import com.sp.proxverse.engine.service.copy.entity.KnowledgeModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class KnowledgeModelCopyService extends BusinessCopyService<KnowledgeModel> {

  @Autowired private BusinessService businessService;

  @Override
  protected CopyParent copy(KnowledgeModel knowledgeModel) {

    Integer newTopicId =
        businessService.saveTopicBaseData(
            knowledgeModel.getTopicId(),
            knowledgeModel.getParentId(),
            BusinessKpiTypeEnum.KNOWLEDGE.getValue(),
            knowledgeModel.getFromParent());

    return CopyParent.builder().topicId(newTopicId).build();
  }
}
