package com.sp.proxverse.engine.service.copy.topic;

import com.sp.proxverse.common.model.po.BusinessTopicPO;
import com.sp.proxverse.common.model.po.SimulationEventAttrPo;
import com.sp.proxverse.common.model.po.SimulationEventPo;
import com.sp.proxverse.common.model.po.SimulationPo;
import com.sp.proxverse.common.model.po.SimulationProgrammePo;
import com.sp.proxverse.common.model.po.SimulationResourcePo;
import com.sp.proxverse.common.model.po.SimulationResourcePoolPo;
import com.sp.proxverse.common.model.po.SimulationRunFrequencyPo;
import com.sp.proxverse.engine.service.copy.BusinessCopyService;
import com.sp.proxverse.engine.service.copy.BusinessService;
import com.sp.proxverse.engine.service.copy.entity.CopyParent;
import com.sp.proxverse.engine.service.copy.entity.Simulation;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicService;
import com.sp.proxverse.interfaces.dao.service.ISimulationEventAttrService;
import com.sp.proxverse.interfaces.dao.service.ISimulationEventService;
import com.sp.proxverse.interfaces.dao.service.ISimulationProgrammeService;
import com.sp.proxverse.interfaces.dao.service.ISimulationResourcePoolService;
import com.sp.proxverse.interfaces.dao.service.ISimulationResourceService;
import com.sp.proxverse.interfaces.dao.service.ISimulationRunFrequencyService;
import com.sp.proxverse.interfaces.dao.service.ISimulationService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SimulationCopyService extends BusinessCopyService<Simulation> {

  @Autowired private BusinessTopicService businessTopicService;

  @Autowired private ISimulationService simulationService;

  @Autowired private ISimulationEventService simulationEventService;

  @Autowired private ISimulationEventAttrService simulationEventAttrService;

  @Autowired private ISimulationProgrammeService simulationProgrammeService;

  @Autowired private ISimulationResourceService simulationResourceService;

  @Autowired private ISimulationResourcePoolService simulationResourcePoolService;

  @Autowired private ISimulationRunFrequencyService simulationRunFrequencyService;

  @Autowired private BusinessService businessService;

  @Override
  protected CopyParent copy(Simulation simulation) {

    BusinessTopicPO topic = businessTopicService.getById(simulation.getTopicId());

    SimulationPo simulationPo = simulationService.getOneByTopicId(simulation.topicId);

    List<SimulationProgrammePo> programmeList =
        simulationProgrammeService.getList(simulationPo.getId());

    List<SimulationResourcePoolPo> simulationResourcePoolList =
        simulationResourcePoolService.getList(simulationPo.getId());

    List<SimulationEventPo> eventList =
        simulationEventService.selectBySimulationId(simulationPo.getId());

    List<SimulationEventAttrPo> eventAttrList =
        simulationEventAttrService.getList(simulationPo.getId());

    List<SimulationResourcePo> resourceList =
        simulationResourceService.getList(simulationPo.getId());

    List<SimulationRunFrequencyPo> frequencyList =
        simulationRunFrequencyService.getList(simulationPo.getId());

    String name = topic.getName();

    if (simulation.getFromParent() == null || !simulation.getFromParent()) {
      String suffix = businessService.querySuffix(simulation.getParentId(), topic.getName());
      name = topic.getName() + suffix;
    }

    topic.setId(null);
    topic.setParentId(simulation.getParentId());
    topic.setName(name);
    businessTopicService.save(topic);

    simulationPo.setId(null);
    simulationPo.setTopicId(topic.getId());
    simulationService.save(simulationPo);

    Map<Integer, Integer> programmeIdMap = new HashMap<>();

    programmeList.forEach(
        f -> {
          Integer originId = f.getId();
          f.setId(null);
          f.setSimulationId(simulationPo.getId());
          simulationProgrammeService.save(f);
          programmeIdMap.put(originId, f.getId());
        });

    Map<Integer, Integer> poolIdMap = new HashMap<>();

    simulationResourcePoolList.forEach(
        f -> {
          Integer originId = f.getId();
          f.setId(null);
          f.setSimulationId(simulationPo.getId());
          simulationResourcePoolService.save(f);
          poolIdMap.put(originId, f.getId());
        });

    Map<Integer, Integer> eventIdMap = new HashMap<>();

    eventList.forEach(
        f -> {
          Integer originId = f.getId();
          f.setId(null);
          f.setSimulationId(simulationPo.getId());

          Integer newPoolId = poolIdMap.get(f.getResourcePoolId());
          f.setResourcePoolId(newPoolId);

          simulationEventService.save(f);
          eventIdMap.put(originId, f.getId());
        });

    eventAttrList.forEach(
        f -> {
          f.setId(null);
          f.setSimulationId(simulationPo.getId());

          Integer newProgrammeId = programmeIdMap.get(f.getProgrammeId());
          f.setProgrammeId(newProgrammeId);

          Integer newEventId = eventIdMap.get(f.getSimulationEventId());
          f.setSimulationEventId(newEventId);
          simulationEventAttrService.save(f);
        });

    resourceList.forEach(
        f -> {
          f.setId(null);
          f.setSimulationId(simulationPo.getId());

          Integer newProgrammeId = programmeIdMap.get(f.getProgrammeId());
          f.setProgrammeId(newProgrammeId);

          Integer newPoolId = poolIdMap.get(f.getResourcePoolId());
          f.setResourcePoolId(newPoolId);

          simulationResourceService.save(f);
        });

    frequencyList.forEach(
        f -> {
          f.setId(null);

          f.setSimulationId(simulationPo.getId());

          Integer newProgrammeId = programmeIdMap.get(f.getProgrammeId());
          f.setProgrammeId(newProgrammeId);

          simulationRunFrequencyService.save(f);
        });

    return CopyParent.builder().topicId(topic.getId()).build();
  }
}
