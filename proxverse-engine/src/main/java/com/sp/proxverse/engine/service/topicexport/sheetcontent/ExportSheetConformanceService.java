package com.sp.proxverse.engine.service.topicexport.sheetcontent;

import com.sp.proxverse.common.model.dict.TopicSheetTypeEnum;
import com.sp.proxverse.common.model.dto.TopicSheetExport;
import com.sp.proxverse.common.model.po.AllowProcessPO;
import com.sp.proxverse.common.model.po.CloudFileInfoPO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.engine.service.topicexport.ExportSheetService;
import com.sp.proxverse.interfaces.dao.service.AllowProcessService;
import com.sp.proxverse.interfaces.dao.service.CloudFileInfoService;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ExportSheetConformanceService extends ExportSheetService {

  @Autowired private CloudFileInfoService cloudFileInfoService;

  @Autowired private AllowProcessService allowProcessService;

  @Override
  public void export(TopicSheetExport topicSheetExport, TopicSheetPO sheet) {
    if (Objects.equals(sheet.getType(), TopicSheetTypeEnum.PROCESS_CONFORMANCE.getValue())) {
      CloudFileInfoPO cloudFileInfo = cloudFileInfoService.getBySheetId(sheet.getId());
      topicSheetExport.setCloudFileInfo(cloudFileInfo);
      List<AllowProcessPO> allowProcessList = allowProcessService.getList(sheet.getId());
      topicSheetExport.setAllowProcessList(allowProcessList);
    }
  }

  @Override
  protected void importContent(TopicSheetExport sheetExport, TopicSheetPO sheet) {
    // bpmn file_code
    CloudFileInfoPO cloudFileInfo = sheetExport.getCloudFileInfo();
    if (cloudFileInfo != null) {
      cloudFileInfo.setId(null);
      cloudFileInfo.setTenantId(null);
      cloudFileInfo.setSheetId(sheet.getId());
      cloudFileInfoService.save(cloudFileInfo);
    }
    // allow process
    List<AllowProcessPO> allowProcessList = sheetExport.getAllowProcessList();
    for (AllowProcessPO allowProcess : allowProcessList) {
      allowProcess.setId(null);
      allowProcess.setTenantId(null);
      allowProcess.setTopicId(sheet.getTopicId());
      allowProcess.setSheetId(sheet.getId());
      allowProcessService.save(allowProcess);
    }
  }
}
