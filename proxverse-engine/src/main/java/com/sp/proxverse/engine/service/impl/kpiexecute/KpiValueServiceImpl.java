package com.sp.proxverse.engine.service.impl.kpiexecute;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.KpiValueMapper;
import com.sp.proxverse.common.model.po.KpiValuePO;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiValueService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * kpi的值 kpi在各时间段的值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 11:03:15
 */
@Service
@Primary
public class KpiValueServiceImpl extends ServiceImpl<KpiValueMapper, KpiValuePO>
    implements KpiValueService {}
