package com.sp.proxverse.engine.service.topicexport.sheetcontent;

import com.sp.proxverse.common.model.dict.TopicSheetTypeEnum;
import com.sp.proxverse.common.model.dto.SheetKpiDTO;
import com.sp.proxverse.common.model.dto.TopicSheetExport;
import com.sp.proxverse.common.model.dto.processoverview.DimensionAndKpi;
import com.sp.proxverse.common.model.po.SheetDimensionPO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.common.model.po.TopicSheetVariablePO;
import com.sp.proxverse.engine.service.topicexport.ExportSheetService;
import com.sp.proxverse.interfaces.dao.service.SheetDimensionService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetKpiService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetVariableService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ExportSheetVariableService extends ExportSheetService {

  @Autowired private TopicSheetVariableService topicSheetVariableService;

  @Autowired private SheetDimensionService sheetDimensionService;

  @Autowired private TopicSheetKpiService topicSheetKpiService;

  @Override
  public void export(TopicSheetExport topicSheetExport, TopicSheetPO sheet) {
    if (Objects.equals(sheet.getType(), TopicSheetTypeEnum.PROCESS_AI.getValue())
        || Objects.equals(sheet.getType(), TopicSheetTypeEnum.THROUGHPUT_TIME.getValue())
        || Objects.equals(sheet.getType(), TopicSheetTypeEnum.ROOT_CAUSE_ANALYSIS.getValue())
        || Objects.equals(sheet.getType(), TopicSheetTypeEnum.PROCESS_OVERVIEW.getValue())) {
      List<TopicSheetVariablePO> sheetVariableList =
          topicSheetVariableService.listBySheetId(sheet.getId());
      topicSheetExport.setSheetVariableList(sheetVariableList);
      List<DimensionAndKpi> dimensionAndKpiList =
          sheetDimensionService.listBySheetId(sheet.getId());
      topicSheetExport.setDimensionAndKpiList(dimensionAndKpiList);
    }
  }

  @Override
  protected void importContent(TopicSheetExport sheetExport, TopicSheetPO sheet) {
    List<TopicSheetVariablePO> sheetVariableList = sheetExport.getSheetVariableList();
    for (TopicSheetVariablePO variable : sheetVariableList) {
      variable.setId(null);
      variable.setTenantId(null);
      variable.setFileId(null);
      variable.setFieldId(null);
      variable.setTopicSheetId(sheet.getId());
      topicSheetVariableService.save(variable);
    }

    List<DimensionAndKpi> dimensionAndKpiList = sheetExport.getDimensionAndKpiList();
    List<SheetKpiDTO> sheetKpiList = topicSheetKpiService.getTopicSheetKpiList(sheet.getId());
    Map<String, SheetKpiDTO> kpiMap =
        sheetKpiList.stream()
            .collect(Collectors.toMap(SheetKpiDTO::getName, Function.identity(), (k1, k2) -> k2));
    for (DimensionAndKpi dimensionAndKpi : dimensionAndKpiList) {
      SheetDimensionPO sheetDimension = new SheetDimensionPO();
      sheetDimension.setSheetId(sheet.getId());
      sheetDimension.setTitleType(dimensionAndKpi.getTitleType());
      sheetDimension.setTableName(dimensionAndKpi.getTableName());
      sheetDimension.setColumnName(dimensionAndKpi.getColumnName());
      if (StringUtils.isNotBlank(dimensionAndKpi.getName())) {
        sheetDimension.setKpiId(kpiMap.get(dimensionAndKpi.getName()).getId());
      }
      sheetDimensionService.save(sheetDimension);
    }
  }
}
