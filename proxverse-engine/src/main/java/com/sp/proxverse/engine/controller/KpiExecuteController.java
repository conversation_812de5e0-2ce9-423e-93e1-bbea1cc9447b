package com.sp.proxverse.engine.controller;

import static com.sp.proxverse.common.exception.ErrorCode.BUSINESS_ERROR;
import static com.sp.proxverse.common.exception.ErrorCode.OPERATION_NOT_ALLOWED;

import com.sp.proxverse.common.model.api.KpiViewDataDTO;
import com.sp.proxverse.common.model.api.QueryKpi4DataModelDTO;
import com.sp.proxverse.common.model.api.SaveKpiDTO;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.base.PageResponse;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.page.PageRequest;
import com.sp.proxverse.common.model.vo.CreateAuditLog;
import com.sp.proxverse.common.model.vo.KpiTaskRequestVO;
import com.sp.proxverse.common.model.vo.kpi.request.DataModelRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiClassifyRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiCommentRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiDataModelColumnRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiReportFormsRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiReportFormsStatusRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiTaskRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiTaskStatusRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiValueAndAvgRequest;
import com.sp.proxverse.common.model.vo.kpi.response.KpiCommentResponse;
import com.sp.proxverse.common.model.vo.kpi.response.KpiInfoResponse;
import com.sp.proxverse.common.model.vo.kpi.response.KpiReportFormsInfoResponse;
import com.sp.proxverse.common.model.vo.kpi.response.KpiStatusResponse;
import com.sp.proxverse.common.model.vo.kpi.response.KpiTaskResponse;
import com.sp.proxverse.common.model.vo.kpi.response.TableResponse;
import com.sp.proxverse.common.model.vo.request.QueryDataModelSourceRequest;
import com.sp.proxverse.engine.service.biz.BusinessTopicBizServiceImpl;
import com.sp.proxverse.engine.service.biz.KpiBizService;
import com.sp.proxverse.interfaces.common.auditlog.AuditLogUtil;
import com.sp.proxverse.interfaces.dao.service.KpiRelationService;
import com.sp.proxverse.interfaces.dao.service.KpiService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiCommentService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiLogService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiReportFormsService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiTaskService;
import com.sp.proxverse.interfaces.service.datamerge.DateMergeApiService;
import com.sp.proxverse.interfaces.service.oauth2.Oauth2ApiService;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.AdminDTO;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UserInfoPageResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "kpi执行", tags = "kpi执行")
@RequestMapping(value = "/sp-engine")
@RestController
public class KpiExecuteController {

  @Autowired KpiCommentService kpiCommentService;

  @Autowired KpiLogService kpiLogService;

  @Autowired KpiTaskService kpiTaskService;

  @Autowired KpiReportFormsService kpiReportFormsService;

  @Autowired DateMergeApiService dateMergeApiService;

  @Autowired Oauth2ApiService oauth2ApiService;

  @Autowired KpiService kpiService;

  @Autowired KpiRelationService kpiRelationService;

  @Autowired KpiBizService kpiBizService;

  @Autowired private BusinessTopicBizServiceImpl businessTopicBizServiceImpl;
  private static final String KPI_RELATION_ID = "kpiRelationId";
  private static final String USER_ID = "userId";

  @ApiOperation(
      value = "(KPI执行（创建KPI）-1页面)获取数据模型名称（数据池名称）",
      notes = "(KPI执行（创建KPI）-1页面)获取数据模型名称（数据池名称）")
  @PostMapping(value = "/getDataModel")
  public PageResp getDataModel(@RequestBody QueryDataModelSourceRequest request) {
    request.setTopicId(null);
    return businessTopicBizServiceImpl.getDataModelSource4Page(request);
  }

  @ApiOperation(value = "(KPI执行（创建KPI）-2页面)点击计算kpi结果", notes = "(KPI执行（创建KPI）-2页面)点击计算kpi结果")
  @PostMapping(value = "/kpiCal4DataModelInfo")
  public Response<BigDecimal> kpiCal4DataModel(@Valid @RequestBody QueryKpi4DataModelDTO request) {
    return kpiBizService.kpiCal4DataModel(request);
  }

  @ApiOperation(value = "(KPI执行（创建KPI）-2页面)创建kpi", notes = "(KPI执行（创建KPI）-2页面)创建kpi")
  @PostMapping(value = "/addKpi")
  public Response<Integer> addKpi(@Valid @RequestBody SaveKpiDTO request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/addKpi")
            .addParameters("id", request.getId())
            .addParameters("expression", request.getExpression())
            .addParameters("name", request.getName())
            .addParameters("type", request.getType())
            .addParameters("unit", request.getUnit())
            .addParameters("target", request.getTarget())
            .addParameters("timeColumnId", request.getTimeColumnId())
            .addParameters("dataModelId", request.getDataModelId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiService.addKpi(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（创建KPI）-2页面)更新kpi", notes = "(KPI执行（创建KPI）-2页面)更新kpi")
  @PostMapping(value = "/updateKpi")
  public Response<Integer> updateKpi(@Valid @RequestBody SaveKpiDTO request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/updateKpi")
            .addParameters("id", request.getId())
            .addParameters("expression", request.getExpression())
            .addParameters("name", request.getName())
            .addParameters("type", request.getType())
            .addParameters("unit", request.getUnit())
            .addParameters("target", request.getTarget())
            .addParameters("timeColumnId", request.getTimeColumnId())
            .addParameters("dataModelId", request.getDataModelId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiService.updateKpi(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行页面)根据kpiRelationId删除kpi", notes = "(KPI执行页面)根据kpiRelationId删除kpi")
  @GetMapping(value = "/deleteKpiInfoById")
  public Response<Integer> deleteKpiInfoById(@RequestParam Integer kpiRelationId) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteKpiInfoById")
            .addParameters(KPI_RELATION_ID, kpiRelationId);
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiService.deleteKpiInfoById(kpiRelationId);
    if (Objects.equals(integer, 2)) {
      return Response.fail(OPERATION_NOT_ALLOWED);
    } else if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（创建KPI）-2页面)根据kpiId查询详情", notes = "(KPI执行（创建KPI）-2页面)根据kpiId查询详情")
  @GetMapping(value = "/getKpiInfoById")
  public Response<KpiInfoResponse> getKpiInfoById(@RequestParam Integer kpiRelationId) {
    return Response.success(kpiService.getKpiInfoById(kpiRelationId));
  }

  @ApiOperation(value = "(KPI执行 页面)-获取kpiinfo列表", notes = "(KPI执行 页面)-获取kpiinfo列表")
  @PostMapping(value = "/getKpiInfoList")
  public Response getKpiInfoList() {
    return Response.success(kpiBizService.getKpiInfo());
  }

  @ApiOperation(value = "(KPI报表 (新报表) - 2页面)获取kpi列表", notes = "(KPI报表 (新报表) - 2页面)获取kpi列表")
  @PostMapping(value = "/getKpiExecuteList")
  public PageResp getKpiExecuteList(@RequestBody PageRequest request) {
    return PageResp.success(kpiService.getKpiExecuteList(request));
  }

  @ApiOperation(value = "(KPI报表 (新报表) - 3页面)获取用户列表", notes = "(KPI报表 (新报表) - 3页面)获取用户列表")
  @GetMapping(value = "/getUserList")
  public PageResponse getUserList(PageRequest request) {
    UserInfoPageResponseDTO userInfoPageResponseDTO = new UserInfoPageResponseDTO();
    userInfoPageResponseDTO.setPageNum(request.getPageNum());
    userInfoPageResponseDTO.setPageSize(request.getPageSize());
    userInfoPageResponseDTO.setStatus(1);
    PageResponse<ArrayList<AdminDTO>> userList =
        oauth2ApiService.getUserList(userInfoPageResponseDTO);
    return userList;
  }

  @ApiOperation(value = "(KPI创建分类页面)根据数据模型id获取数据表和列", notes = "(KPI创建分类页面)根据数据模型id获取数据表和列")
  @PostMapping(value = "/getDataModelColumnById")
  public Response<List<TableResponse>> getDataModelField(
      @Valid @RequestBody DataModelRequest responseDTO) {
    return kpiRelationService.getDataModelField(responseDTO);
  }

  @ApiOperation(value = "(KPI创建分类页面)保存kpi数据模型列", notes = "(KPI创建分类页面)保存kpi数据模型列")
  @PostMapping(value = "/saveDataModelColumn")
  public Response<Integer> saveDataModelColumn(
      @Valid @RequestBody KpiDataModelColumnRequest responseDTO) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/saveDataModelColumn")
            .addParameters(KPI_RELATION_ID, responseDTO.getKpiRelationId());
    AuditLogUtil.save(createAuditLog);
    return Response.success(kpiRelationService.saveDataModelColumn(responseDTO));
  }

  @ApiOperation(value = "(KPI创建分类页面)修改kpi数据模型列 1.6.3", notes = "(KPI创建分类页面)修改kpi数据模型列")
  @PostMapping(value = "/updateDataModelColumn")
  public Response<Integer> updateDataModelColumn(
      @Valid @RequestBody KpiDataModelColumnRequest responseDTO) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/updateDataModelColumn")
            .addParameters(KPI_RELATION_ID, responseDTO.getKpiRelationId())
            .addParameters("columnId", responseDTO.getColumnId());
    AuditLogUtil.save(createAuditLog);
    return Response.success(kpiRelationService.saveDataModelColumn(responseDTO));
  }

  @ApiOperation(value = "(KPI执行（详情）页面)根据列id获取kpi分类消息", notes = "(KPI执行（详情）页面)根据列id获取kpi分类消息")
  @PostMapping(value = "/getKpiClassifyById")
  public PageResp getKpiClassifyById(@Valid @RequestBody KpiClassifyRequest kpiClassifyRequest) {
    return kpiService.getKpiClassifyById(kpiClassifyRequest);
  }

  @ApiOperation(value = "(KPI执行（详情）页面)获取kpi的折线图", notes = "(KPI执行（详情）页面)获取kpi的折线图")
  @PostMapping(value = "/getKpiLineChart")
  public Response<KpiViewDataDTO> getKpiLineChart(
      @Valid @RequestBody KpiValueAndAvgRequest request) {
    return Response.success(kpiService.getKpiLineChart(request));
  }

  @ApiOperation(value = "(KPI执行（详情）页面)添加kpi评论", notes = "(KPI执行（详情）页面)添加kpi评论")
  @PostMapping(value = "/addKpiComment")
  public Response<Integer> addKpiComment(@Valid @RequestBody KpiCommentRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/addKpiComment")
            .addParameters(KPI_RELATION_ID, request.getKpiRelationId())
            .addParameters("comment", request.getComment());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiCommentService.addKpiComment(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（详情）页面)获取kpi评论列表", notes = "(KPI执行（详情）页面)获取kpi评论列表")
  @GetMapping(value = "/getKpiCommentById")
  public Response<List<KpiCommentResponse>> getKpiCommentById(@RequestParam Integer kpiRelationId) {
    return Response.success(kpiCommentService.getKpiCommentById(kpiRelationId));
  }

  @ApiOperation(value = "(KPI执行（详情）页面)删除kpi评论", notes = "(KPI执行（详情）页面)删除kpi评论")
  @GetMapping(value = "/deleteKpiCommentById")
  public Response<Integer> deleteKpiCommentById(@RequestParam Integer id) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteKpiCommentById").addParameters("id", id);
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiCommentService.deleteKpiCommentById(id);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（详情）页面)获取kpi状态列表", notes = "(KPI执行（详情）页面)获取kpi状态列表")
  @GetMapping(value = "/getKpiStatusById")
  public Response<List<KpiStatusResponse>> getKpiStatusById(@RequestParam Integer kpiRelationId) {
    return Response.success(kpiLogService.getKpiStatusById(kpiRelationId));
  }

  @ApiOperation(value = "(KPI执行（详情）页面)删除kpi状态", notes = "(KPI执行（详情）页面)删除kpi状态")
  @GetMapping(value = "/deleteKpiStatusById")
  public Response<Integer> deleteKpiStatusById(@RequestParam Integer id) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteKpiStatusById").addParameters("id", id);
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiLogService.deleteKpiStatusById(id);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（详情）（任务）页面)添加kpi任务", notes = "(KPI执行（详情）（任务）页面)添加kpi任务")
  @PostMapping(value = "/addKpiTask")
  public Response<Integer> addKpiTask(@Valid @RequestBody KpiTaskRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/addKpiTask")
            .addParameters(KPI_RELATION_ID, request.getKpiRelationId())
            .addParameters("taskName", request.getTaskName())
            .addParameters("id", request.getId())
            .addParameters(USER_ID, request.getUserId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiTaskService.addKpiTask(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(
      value = "(KPI执行（详情） (任务)页面)根据kpi关联主键获取kpi任务列表",
      notes = "(KPI执行（详情） (任务)页面)根据kpi关联主键获取kpi任务列表")
  @PostMapping(value = "/getKpiTaskByKpirelationId")
  public PageResp getKpiTaskByKpirelationId(@RequestBody KpiTaskRequestVO requestVO) {
    return kpiTaskService.getKpiTaskByKpirelationId(requestVO);
  }

  @ApiOperation(
      value = "(KPI执行（详情） (任务)页面)根据kpi任务主键获取详情消息",
      notes = "(KPI执行（详情） (任务)页面)根据kpi任务主键获取详情消息")
  @GetMapping(value = "/getKpiTaskById")
  public Response<KpiTaskResponse> getKpiTaskById(@RequestParam Integer id) {
    return Response.success(kpiTaskService.getKpiTaskById(id));
  }

  @ApiOperation(value = "(KPI执行（详情） (任务)页面)删除kpi任务", notes = "(KPI执行（详情） (任务)页面)删除kpi任务")
  @GetMapping(value = "/deleteKpiTaskById")
  public Response<Integer> deleteKpiTaskById(@RequestParam Integer id) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteKpiTaskById").addParameters("id", id);
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiTaskService.deleteKpiTaskById(id);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（详情） (任务)页面)修改kpi任务", notes = "(KPI执行（详情） (任务)页面)修改kpi任务")
  @PostMapping(value = "/modifyKpiTask")
  public Response<Integer> modifyKpiTask(@Valid @RequestBody KpiTaskRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/modifyKpiTask")
            .addParameters(KPI_RELATION_ID, request.getKpiRelationId())
            .addParameters("taskName", request.getTaskName())
            .addParameters("id", request.getId())
            .addParameters(USER_ID, request.getUserId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiTaskService.modifyKpiTask(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI执行（详情） (任务)页面)修改kpi任务状态", notes = "(KPI执行（详情） (任务)页面)修改kpi任务状态")
  @PostMapping(value = "/modifyKpiTaskStatus")
  public Response<Integer> modifyKpiTaskStatus(@Valid @RequestBody KpiTaskStatusRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/modifyKpiTaskStatus")
            .addParameters("status", request.getStatus())
            .addParameters("id", request.getId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiTaskService.modifyKpiTaskStatus(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI报表页面)获取kpi报表列表", notes = "(KPI报表页面)获取kpi报表列表")
  @GetMapping(value = "/getKpiReportForms")
  public PageResp getKpiReportForms(PageRequest request) {
    return kpiReportFormsService.getKpiReportForms(request);
  }

  @ApiOperation(value = "(KPI报表页面)根据报表id获取kpi报表详情", notes = "(KPI报表页面)根据报表id获取kpi报表详情")
  @GetMapping(value = "/getKpiReportFormsById")
  public Response<KpiReportFormsInfoResponse> getKpiReportFormsById(@RequestParam Integer id) {
    return Response.success(kpiReportFormsService.getKpiReportFormsById(id));
  }

  @ApiOperation(value = "(KPI报表页面)修改kpi报表状态", notes = "(KPI报表页面)修改kpi报表状态")
  @PostMapping(value = "/modifyKpiReportFormsStatus")
  public Response<Integer> modifyKpiReportFormsStatus(
      @Valid @RequestBody KpiReportFormsStatusRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/modifyKpiReportFormsStatus")
            .addParameters("status", request.getStatus())
            .addParameters("id", request.getId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiReportFormsService.modifyKpiReportFormsStatus(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI报表(新报表)-3页面)添加kpi报表", notes = "(KPI报表(新报表)-3页面)添加kpi报表")
  @PostMapping(value = "/addKpiReportForms")
  public Response<Integer> addKpiReportForms(@Valid @RequestBody KpiReportFormsRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/addKpiReportForms")
            .addParameters("kipIds", request.getKpiIds())
            .addParameters("date", request.getDate())
            .addParameters("reportFormsName", request.getReportFormsName())
            .addParameters("id", request.getId())
            .addParameters(USER_ID, request.getUserId());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiReportFormsService.addKpiReportForms(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI报表页面)修改kpi报表", notes = "(KPI报表页面)修改kpi报表")
  @PostMapping(value = "/modifyKpiReportForms")
  public Response<Integer> modifyKpiReportForms(@Valid @RequestBody KpiReportFormsRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/modifyKpiReportForms")
            .addParameters("dateType", request.getDateType())
            .addParameters("date", request.getDate())
            .addParameters("id", request.getId())
            .addParameters(USER_ID, request.getUserId())
            .addParameters("workDay", request.getWorkDay())
            .addParameters("time", request.getTime())
            .addParameters("reportFormsName", request.getReportFormsName())
            .addParameters("kpiIds", request.getKpiIds());
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiReportFormsService.modifyKpiReportForms(request);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(KPI报表页面)删除kpi报表", notes = "(KPI报表页面)删除kpi报表")
  @GetMapping(value = "/deleteKpiReportFormsById")
  public Response<Integer> deleteKpiReportFormsById(@RequestParam Integer id) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteKpiReportFormsById").addParameters("id", id);
    AuditLogUtil.save(createAuditLog);
    Integer integer = kpiReportFormsService.deleteKpiReportFormsById(id);
    if (Objects.equals(integer, 0)) {
      return Response.fail(BUSINESS_ERROR);
    } else {
      return Response.success(integer);
    }
  }

  @ApiOperation(value = "(指挥舱页面)获取kpi指挥舱列表", notes = "(指挥舱页面)获取kpi指挥舱列表")
  @GetMapping(value = "/getCommandCabinList")
  public Response getCommandCabinList() {
    return Response.success(kpiReportFormsService.getCommandCabinList());
  }
}
