package com.sp.proxverse.engine.controller;

import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.interfaces.dao.service.FileService;
import com.sp.proxverse.interfaces.service.data.FileStatistics;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/** <AUTHOR> */
@ApiIgnore
@Api(value = "admin控制器", tags = "admin登录前台服务接口")
@RequestMapping(value = "/sp-engine")
@RestController
public class FileSizeController {

  @Autowired FileService fileService;

  @Autowired FileStatistics fileStatistics;

  @ApiOperation(value = "获取用户使用空间大小")
  @GetMapping(value = "/getPaceSize")
  public Response<Double> getPaceSize() {
    double paceSizeMb = getPaceSizeMb();
    return Response.success(paceSizeMb);
  }

  @ApiOperation(value = "检查数据空间容量")
  @GetMapping(value = "/checkDataSpace")
  public Response<Boolean> checkDataSpace() {
    Boolean paceSizeMb = fileStatistics.checkDataSpace();
    return Response.success(paceSizeMb);
  }

  public double getPaceSizeMb() {
    Long paceSize = fileStatistics.getParquetPaceSize();
    BigDecimal divisor = BigDecimal.valueOf(1024);
    return BigDecimal.valueOf(paceSize).divide(divisor, 2, RoundingMode.HALF_UP).doubleValue();
  }
}
