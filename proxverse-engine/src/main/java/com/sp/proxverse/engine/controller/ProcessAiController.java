package com.sp.proxverse.engine.controller;

import com.prx.service.page.DeviationPageServiceImpl;
import com.prx.service.page.ICustomerRootCauseService;
import com.prx.service.page.ITopicMultiProcessService;
import com.prx.service.page.RootCauseOfDeviationPageServiceImpl;
import com.prx.service.page.entity.*;
import com.prx.service.processtree.ProcessTreeRedDotService;
import com.sp.proxverse.common.model.TopicMultiProcessDTO;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.dto.RelatedVariableFileDTO;
import com.sp.proxverse.common.model.dto.RootCauseResultDTO;
import com.sp.proxverse.common.model.dto.process.*;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.common.model.vo.*;
import com.sp.proxverse.common.model.vo.base.DeleteReqVo;
import com.sp.proxverse.common.model.vo.datatask.TopicParamKpiListRes;
import com.sp.proxverse.common.model.vo.kpi.response.KpiCalclateResponse;
import com.sp.proxverse.common.model.vo.processExplorer.ProcessExplorerRes;
import com.sp.proxverse.common.model.vo.processai.TopicProcessKpiResVo;
import com.sp.proxverse.common.model.vo.processai.request.QueryRelatedFieldsRequest;
import com.sp.proxverse.common.model.vo.processai.request.TopicProcessDetailsReqVo;
import com.sp.proxverse.common.model.vo.processai.request.UpdateTopicProcessDetailsKpiReqVo;
import com.sp.proxverse.common.model.vo.processai.request.UpdateTopicProcessKpiReqVo;
import com.sp.proxverse.common.model.vo.request.*;
import com.sp.proxverse.common.model.vo.request.topic.QueryTopicParamKpiRequest;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.engine.service.ProcessTreeService;
import com.sp.proxverse.engine.service.biz.ProcessTreeReadBizService;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.interfaces.common.auditlog.AuditLogUtil;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.pql.PQLEnv;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(value = "业务分析图谱控制器", tags = "流程AI")
@RequestMapping(value = "/sp-engine")
@RestController
public class ProcessAiController {

  @Autowired private ProcessTreeReadBizService processTreeReadBizService;

  @Autowired private DeviationPageServiceImpl devicationPageServiceImpl;

  @Autowired private RootCauseOfDeviationPageServiceImpl rootCauseOfDeviationPageService;

  @Autowired private ProcessTreeWriteBizService processTreeWriteBizService;

  @Autowired PQLService PQLService;

  @Autowired ProcessTreeService processTree;

  @Autowired private ProcessTreeRedDotService processTreeRedDotService;

  @Autowired private ICustomerRootCauseService customerRootCauseService;

  @Autowired private ITopicMultiProcessService iTopicMultiProcessService;
  private static final String KPI_ID = "kpiId";
  private static final String TOPIC_SHEET_ID = "topicSheetId";

  @ApiOperation(value = "开始（start）计算根因的结果", notes = "开始计算根因的结果(事件、吞吐时间、一致性变体)")
  @PostMapping(value = "/queryRootCauseCalResult")
  public Response<List<RelatedOutputVO>> queryRootCauseCalResult(
      @Valid @RequestBody RelatedRequest request) {

    return Response.success(processTreeReadBizService.queryRootCauseCalResult(request));
  }

  @ApiOperation(value = "获取业务分析图谱主题下的表格数据", notes = "获取业务分析图谱主题下的表格数据")
  @PostMapping(value = "/getTopicSheetData")
  public Response<List<TopicSheetOutputVO>> getTopicSheetData(
      @Valid @RequestBody TopicDataRequest request) {

    return Response.success(processTreeReadBizService.getTopicSheetData(request.getTopicId()));
  }

  @ApiOperation(value = "为业务分析主题添加一个sheet表格", notes = "为业务分析主题添加一个sheet表格")
  @PostMapping(value = "/addTopicSheet")
  public Response<Integer> addTopicSheet(@Valid @RequestBody AddTopicSheetRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/addTopicSheet")
            .addParameters("type", request.getType())
            .addParameters("topicId", request.getTopicId());
    AuditLogUtil.save(createAuditLog);
    return Response.success(processTreeWriteBizService.addTopicSheet(request));
  }

  @ApiOperation(value = "为业务分析主题添加一个sheet表格", notes = "")
  @GetMapping(value = "/getTopicSheet")
  public Response<TopicSheetPO> getTopicSheet(@Valid @RequestParam Integer topicSheetId) {
    return Response.success(processTreeWriteBizService.getTopicSheet(topicSheetId));
  }

  @ApiOperation(value = "获取流程图资源管理")
  @PostMapping(value = "/processExplorer")
  public Response<ProcessExplorerRes> processExplorer(
      @Valid @RequestBody ProcessExplorerRequest request) {
    try {
      PQLEnv.setForceInnerJoinCaseTable(true);
      ProcessExplorerRes result = processTree.processExplorer(request);
      return Response.success(result);
    } finally {
      PQLEnv.removeForceInnerJoinCaseTable();
    }
  }

  @ApiOperation(value = "获取流程树遍体数据", notes = "获取流程树遍体数据")
  @PostMapping(value = "/processTreeVariant")
  public Response<ProcessTreeVariantOut> processTreeVariant(
      @Valid @RequestBody ProcessTreeVariantRequest request) {
    try {
      PQLEnv.setForceInnerJoinCaseTable(true);
      ProcessTreeVariantOut processTreeVariantOut = processTree.processTreeVariant(request);
      return Response.success(processTreeVariantOut);
    } finally {
      PQLEnv.removeForceInnerJoinCaseTable();
    }
  }

  @ApiOperation(value = "获取流程树节点数据", notes = "获取流程树节点数据")
  @PostMapping(value = "/processTreeChart")
  public Response<ProcessTreeChartOut> processTreeChart(
      @Valid @RequestBody ProcessTreeChartRequest request) {
    try {
      PQLEnv.setForceInnerJoinCaseTable(true);
      ProcessTreeChartOut processTreeVariantOut = processTree.processTreeChart(request);
      return Response.success(processTreeVariantOut);
    } finally {
      PQLEnv.removeForceInnerJoinCaseTable();
    }
  }

  @ApiOperation(value = "获取流程树红点运行数据", notes = "获取流程树红点运行数据")
  @PostMapping(value = "/getProcessTreeRedDotWithoutGroup")
  public Response<Object[][]> getProcessTreeRedDotWithoutGroup(
      @Valid @RequestBody ProcessTreeRedDotRequest request) {
    return Response.success(
        processTreeRedDotService.getProcessTreeRedDotWithoutGroup(
            request.getSheetId(), request.getEvent()));
  }

  @ApiOperation(value = "获取流程树红点运行数据-group by day", notes = "获取流程树红点运行数据-group by day")
  @PostMapping(value = "/getProcessTreeRedDotWithGroupByDay")
  public Response<Object[][]> getProcessTreeRedDotWithGroupByDay(
      @Valid @RequestBody ProcessTreeRedDotRequest request) {
    return Response.success(
        processTreeRedDotService.getProcessTreeRedDotWithGroupByDay(
            request.getSheetId(), request.getEvent()));
  }

  @ApiOperation(value = "查询流程树的过滤项列表", notes = "查询流程树的过滤项列表")
  @PostMapping(value = "/getTopicFilterList")
  public Response<List<TopicFilterOutputVO>> getTopicFilterList(
      @Valid @RequestBody QueryTopicFilterRequest request) {

    return Response.success(processTreeReadBizService.getTopicFilterList(request));
  }

  @ApiOperation(value = "获取Expression解析", notes = "获取Expression解析")
  @PostMapping(value = "/getExpressionInfo")
  public Response<TopicParamKpiListRes> getExpressionInfo(
      @Valid @RequestBody QueryTopicParamKpiRequest request) {
    return Response.success(processTreeReadBizService.getExpressionInfo(request));
  }

  @ApiOperation(value = "删除流程树的过滤项", notes = "删除流程树的过滤项")
  @PostMapping(value = "/deleteTopicFilterList")
  public Response<Boolean> deleteTopicFilterList(
      @Valid @RequestBody DeleteTopicFilterRequest request) {
    return Response.success(processTreeWriteBizService.deleteTopicFilterList(request));
  }

  @ApiOperation(value = "获取业务主题表格的kpi数据", notes = "获取业务主题表格的kpi数据")
  @PostMapping(value = "/getTopicSheetKpi")
  public Response<List<TopicSheetKpiOutputVO>> getTopicSheetKpi(
      @Valid @RequestBody TopicSheetDataRequest request) {

    return Response.success(processTreeReadBizService.getTopicSheetKpi(request.getTopicSheetId()));
  }

  @ApiOperation(value = "获取业务主题的偏差事件数据v1.1修改", notes = "获取业务主题的偏差事件数据")
  @PostMapping(value = "/getTopicSheetDeviation")
  public DeviationOutput getTopicDeviation(@Valid @RequestBody Deviation request) {

    try {
      return devicationPageServiceImpl.page(request);
    } catch (Exception e) {
      if (Objects.equals(
          e.getMessage(), I18nUtil.getMessage(I18nConst.DEVIATION_KPI_CONTAIN_AGGREGATION))) {
        throw new UnsupportedOperationException(
            I18nUtil.getMessage(I18nConst.DEVIATION_KPI_CONTAIN_AGGREGATION));
      }
      log.error("getTopicSheetDeviation error ", e);
    }
    return DeviationOutput.none();
  }

  @ApiOperation(value = "获取供主题表格的选择的相关变量v1.1", notes = "获取供主题表格的选择的相关变量")
  @PostMapping(value = "/getSheetRelatedVariables4select")
  public Response<List<RelatedVariableFileDTO>> getSheetRelatedVariables4select(
      @Valid @RequestBody QueryRelatedFieldsRequest request) {
    return Response.success(processTreeReadBizService.getSheetRelatedVariables4select(request));
  }

  @ApiOperation(value = "新建kpi", notes = "新建kpi")
  @PostMapping(value = "/saveKpi")
  public Response<Integer> saveKpi(@RequestBody @Valid KpiSaveRequest request) {

    return Response.success(processTreeWriteBizService.saveKpi(request));
  }

  @ApiOperation(value = "获取知识模型KPI", notes = "获取知识模型KPI")
  @GetMapping(value = "/getKnowledgeModelKpi")
  public Response<List<KpiOutputVO>> getKnowledgeModelKpi(@RequestParam String topicId) {

    return Response.success(processTreeReadBizService.getKnowledgeModelKpi(topicId));
  }

  @ApiOperation(value = "编辑kpi", notes = "编辑kpi")
  @PostMapping(value = "/editKpi")
  public Response<Boolean> editKpi(@Valid @RequestBody KpiEditRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/editKpi")
            .addParameters(KPI_ID, request.getKpiId())
            .addParameters("editKpiId", request.getEditKpiId())
            .addParameters("name", request.getName())
            .addParameters("unit", request.getUnit())
            .addParameters("kpiType", request.getKpiType())
            .addParameters("baseLine", request.getBaseLine())
            .addParameters("expression", request.getExpression())
            .addParameters("businessDataId", request.getBusinessDataId())
            .addParameters("saveType", request.getSaveType());
    AuditLogUtil.save(createAuditLog);
    return Response.success(processTreeWriteBizService.editKpi(request));
  }

  @ApiOperation(value = "删除sheet表格的某个kpi", notes = "删除sheet表格的某个kpi")
  @PostMapping(value = "/deleteSheetKpi")
  public Response<Boolean> deleteSheetKpi(@Valid @RequestBody DeleteSheetKpiRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteSheetKpi")
            .addParameters(TOPIC_SHEET_ID, request.getTopicSheetId())
            .addParameters("componentId", request.getComponentId())
            .addParameters(KPI_ID, request.getKpiId());
    AuditLogUtil.save(createAuditLog);
    return Response.success(processTreeWriteBizService.deleteSheetKpi(request));
  }

  @ApiOperation(value = "KPI点击计算结果", notes = "KPI点击计算结果")
  @PostMapping(value = "/kpiCalculate")
  public Response<KpiCalclateResponse> kpiCalculate(
      @Valid @RequestBody KpiCalculateRequest request) {
    return Response.success(processTreeReadBizService.KpiCalclate(request));
  }

  @ApiOperation(value = "获取根因分析结果数据", notes = "获取根因分析结果数据")
  @PostMapping(value = "/getTopicSheetRootCause")
  public Response<List<RootCauseResultDTO>> getTopicSheetRootCause(
      @Valid @RequestBody RootCauseRequest request) {

    try {
      return Response.success(processTreeReadBizService.getTopicSheetRootCause(request));
    } catch (Exception e) {
      log.error("getTopicSheetRootCause error", e);
    }
    return Response.success(new ArrayList<>());
  }

  @ApiOperation(value = "删除业务分析主题的一个sheet表格", notes = "删除业务分析主题的一个sheet表格")
  @PostMapping(value = "/deleteTopicSheet")
  public Response<Boolean> deleteTopicSheet(@Valid @RequestBody DeleteTopicSheetRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteTopicSheet")
            .addParameters(TOPIC_SHEET_ID, request.getTopicSheetId());
    AuditLogUtil.save(createAuditLog);
    return Response.success(processTreeWriteBizService.deleteTopicSheet(request));
  }

  @ApiOperation(value = "重命名业务分析主题的sheet表格", notes = "重命名业务分析主题的sheet表格")
  @PostMapping(value = "/saveTopicSheetConfig")
  public Response<Boolean> saveTopicSheetConfig(
      @Valid @RequestBody SaveTopicSheetConfigReqVo saveTopicSheetConfigReqVo) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/renameTopicSheet")
            .addParameters(TOPIC_SHEET_ID, saveTopicSheetConfigReqVo.getTopicSheetId())
            .addParameters("topicSheetName", saveTopicSheetConfigReqVo.getTopicSheetName())
            .addParameters("configMap", saveTopicSheetConfigReqVo.getConfigMap());
    AuditLogUtil.save(createAuditLog);
    return Response.success(
        processTreeWriteBizService.saveTopicSheetConfig(saveTopicSheetConfigReqVo));
  }

  @ApiOperation(value = "查询案例总数/当前数", notes = "查询案例总数/当前数")
  @PostMapping(value = "/getCaseTotalCurrent")
  public Response<CaseTotalOutputVO> getCaseTotalCurrent(
      @Valid @RequestBody QueryCaseTotalRequest request) {
    return Response.success(processTreeReadBizService.getCaseTotalCurrent(request));
  }

  @ApiOperation(
      value = "查询在业务分析里创建KPI是否能选用知识模型里的kpi(true：可以，false：不可以)",
      notes = "查询在业务分析里创建KPI是否能选用知识模型里的kpi(true：可以，false：不可以)")
  @PostMapping(value = "/hasKnowledgeKpi")
  public Response<Boolean> hasKnowledgeKpi(
      @Valid @RequestBody QueryHasKnowledgeKpiRequest request) {

    return Response.success(processTreeReadBizService.HasKnowledgeKpi(request));
  }

  @ApiOperation(value = "获取流程图KPI列表", notes = "获取流程图KPI列表")
  @GetMapping(value = "/getTopicProcessKpiList")
  public Response<List<TopicProcessKpiResVo>> getTopicProcessKpiList(
      @RequestParam Integer topicId) {
    List<TopicProcessKpiResVo> results = processTree.getAllTopicProcessKpiIsExist(topicId);
    return Response.success(results);
  }

  @ApiOperation(value = "获取流程图KPI详细信息", notes = "获取流程图KPI详细信息")
  @GetMapping(value = "/getTopicProcessDetails")
  public Response<TopicProcessDetailsReqVo> getTopicProcessDetails(
      @RequestParam Integer treeKpiId) {
    TopicProcessDetailsReqVo results = processTree.getTopicProcessDetails(treeKpiId);
    return Response.success(results);
  }

  @ApiOperation(value = "更新流程图Kpi", notes = "更新流程图Kpi")
  @PostMapping(value = "/updateTopicProcessKpi")
  public Response<Integer> updateTopicProcessKpi(
      @RequestBody UpdateTopicProcessKpiReqVo updateTopicProcessKpiReqVo) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/updateTopicProcessKpi")
            .addParameters("id", updateTopicProcessKpiReqVo.getId())
            .addParameters("topicId", updateTopicProcessKpiReqVo.getTopicId())
            .addParameters("name", updateTopicProcessKpiReqVo.getName())
            .addParameters("eventColourInfo", updateTopicProcessKpiReqVo.getEventColourInfo())
            .addParameters("icon", updateTopicProcessKpiReqVo.getIcon())
            .addParameters("eventColourKpiName", updateTopicProcessKpiReqVo.getEventColourKpiName())
            .addParameters("lineColourKpiName", updateTopicProcessKpiReqVo.getLineColourKpiName())
            .addParameters(
                "updateTopicProcessDetailsKpiReqVos",
                updateTopicProcessKpiReqVo.getUpdateTopicProcessDetailsKpiReqVos());
    AuditLogUtil.save(createAuditLog);
    Integer result = processTree.updateTopicProcessKpi(updateTopicProcessKpiReqVo);
    return Response.success(result);
  }

  @ApiOperation(value = "更新流程图详细Kpi", notes = "更新流程图详细Kpi")
  @PostMapping(value = "/updateTopicProcessDetailsKpi")
  public Response<Integer> updateTopicProcessDetailsKpi(
      @RequestBody UpdateTopicProcessDetailsKpiReqVo updateTopicProcessDetailsKpiReqVo) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/updateTopicProcessDetailsKpi")
            .addParameters(KPI_ID, updateTopicProcessDetailsKpiReqVo.getKpiId())
            .addParameters(
                "processTreeKpiId", updateTopicProcessDetailsKpiReqVo.getProcessTreeKpiId())
            .addParameters(
                "processTreeKpiRelationId",
                updateTopicProcessDetailsKpiReqVo.getProcessTreeKpiRelationId())
            .addParameters("expression", updateTopicProcessDetailsKpiReqVo.getExpression())
            .addParameters("kpiName", updateTopicProcessDetailsKpiReqVo.getKpiName())
            .addParameters("unit", updateTopicProcessDetailsKpiReqVo.getUnit())
            .addParameters("type", updateTopicProcessDetailsKpiReqVo.getType());
    AuditLogUtil.save(createAuditLog);
    Integer result = processTree.updateTopicProcessDetailKpi(updateTopicProcessDetailsKpiReqVo);
    return Response.success(result);
  }

  @ApiOperation(value = "删除流程图Kpi", notes = "删除流程图Kpi")
  @PostMapping(value = "/deleteProcessTreeKpi")
  public Response<Boolean> deleteProcessTreeKpi(@RequestBody DeleteReqVo deleteReqVo) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteProcessTreeKpi")
            .addParameters(KPI_ID, deleteReqVo.getId());
    AuditLogUtil.save(createAuditLog);
    Boolean result = processTree.deleteProcessTreeKpi(deleteReqVo);
    return Response.success(result);
  }

  @ApiOperation(value = "删除流程图详细Kpi(根据KPIId)", notes = "删除流程图详细Kpi")
  @PostMapping(value = "/deleteProcessTreeDetailsKpi")
  public Response<Boolean> deleteProcessTreeDetailsKpi(@RequestBody DeleteReqVo deleteReqVo) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/deleteProcessTreeDetailsKpi")
            .addParameters(KPI_ID, deleteReqVo.getId());
    AuditLogUtil.save(createAuditLog);
    Boolean result = processTree.deleteProcessTreeDetailsKpi(deleteReqVo);
    return Response.success(result);
  }

  /**
   * 路径根因分析 事件根因分析 连线根因分析 返工根因分析
   *
   * @param request
   * @return
   */
  @ApiOperation(value = "获取业务主题的偏差根因分析", notes = "获取业务主题的偏差根因分析")
  @PostMapping(value = "/getTopicRootCauseDeviation")
  public DeviationOutput getTopicRootCauseDeviation(
      @Valid @RequestBody RootCauseOfDeviation request) {
    try {
      return rootCauseOfDeviationPageService.page(request);
    } catch (Exception e) {
      if (Objects.equals(
          e.getMessage(), I18nUtil.getMessage(I18nConst.DEVIATION_KPI_CONTAIN_AGGREGATION))) {
        throw new UnsupportedOperationException(
            I18nUtil.getMessage(I18nConst.DEVIATION_KPI_CONTAIN_AGGREGATION));
      }
      log.error("getTopicSheetDeviation error ", e);
    }
    return DeviationOutput.none();
  }

  @ApiOperation(value = "计算根因分析的结果", notes = "计算根因分析的结果(事件、吞吐时间、一致性变体)")
  @PostMapping(value = "/calculateRootCauseCalResult")
  public Response<List<RelatedOutputVO>> calculateRootCauseCalResult(
      @Valid @RequestBody CalculateRootCauseRequest request) {

    return Response.success(rootCauseOfDeviationPageService.queryRootCauseCalResult(request));
  }

  @ApiOperation(value = "新建自定义根因", notes = "新建自定义根因")
  @PostMapping(value = "/saveCustomerRootCause")
  public Response<Integer> saveCustomerRootCause(
      @RequestBody @Valid CustomerRootCauseRequest request) {
    return Response.success(customerRootCauseService.saveCustomerRootCause(request));
  }

  @ApiOperation(value = "删除自定义根因", notes = "删除自定义根因")
  @PostMapping(value = "/deleteCustomerRootCause")
  public Response<Boolean> deleteCustomerRootCause(@RequestParam @Valid Integer id) {
    return Response.success(customerRootCauseService.deleteCustomerRootCause(id));
  }

  @ApiOperation(value = "修改自定义根因", notes = "修改自定义根因")
  @PostMapping(value = "/updateCustomerRootCause")
  public Response<Boolean> updateCustomerRootCause(
      @RequestBody @Valid CustomerRootCauseRequest request) {
    return Response.success(customerRootCauseService.updateCustomerRootCause(request));
  }

  @ApiOperation(value = "自定义根因分析详情", notes = "自定义根因分析详情")
  @PostMapping(value = "/listCustomerRootCause")
  public Response<List<CustomerRootCauseDto>> listCustomerRootCause(
      @RequestParam @Valid Integer topicSheetId) {
    return Response.success(customerRootCauseService.listCustomerRootCause(topicSheetId));
  }

  @ApiOperation(value = "多层流程图详情", notes = "多层流程图详情")
  @PostMapping(value = "/listTopicMultiProcess")
  public Response<List<TopicMultiProcessDTO>> listTopicMultiProcess(
      @RequestParam @Valid Integer topicId) {
    return Response.success(iTopicMultiProcessService.getTopicMultiProcessList(topicId));
  }

  @ApiOperation(value = "新增多层流程图详情", notes = "新增多层流程图详情")
  @PostMapping(value = "/saveTopicMultiProcess")
  public Response<List<Integer>> saveTopicMultiProcess(
      @RequestBody @Valid List<TopicMultiProcessDTO> topicMultiProcessDTOList) {
    return Response.success(
        iTopicMultiProcessService.saveTopicMultiProcess(topicMultiProcessDTOList));
  }
}
