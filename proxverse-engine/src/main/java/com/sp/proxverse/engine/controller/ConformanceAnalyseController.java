package com.sp.proxverse.engine.controller;

import com.prx.service.conformance.ConformanceServiceScala;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.dto.conformance.ConformanceGeneralData;
import com.sp.proxverse.common.model.dto.conformance.ConformanceKpi;
import com.sp.proxverse.common.model.dto.conformance.UnConformance;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.CreateAuditLog;
import com.sp.proxverse.common.model.vo.conformance.ConformanceViewDataOutputVO;
import com.sp.proxverse.common.model.vo.conformance.request.QueryConformanceGeneralRequest;
import com.sp.proxverse.common.model.vo.conformance.request.QueryConformanceKpi4GeneralRequest;
import com.sp.proxverse.common.model.vo.conformance.request.QueryConformanceKpiRequest;
import com.sp.proxverse.common.model.vo.conformance.request.QueryConformanceViewDataRequest;
import com.sp.proxverse.common.model.vo.conformance.request.QueryUnConformanceListRequest;
import com.sp.proxverse.common.model.vo.conformance.request.SetConformanceRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterSubRequest;
import com.sp.proxverse.engine.service.biz.ConformanceAnalyseBizService;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.interfaces.common.auditlog.AuditLogUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "一致性探索控制器", tags = "一致性探索")
@Slf4j
@RequestMapping(value = "/sp-engine")
@RestController
public class ConformanceAnalyseController {

  @Autowired private ConformanceAnalyseBizService conformanceAnalyseBizService;

  @Autowired ConformanceServiceScala conformanceService;

  @Autowired ProcessTreeWriteBizService processTreeWriteBizService;

  @PostMapping(value = "/invalidConformanceCache")
  public void invalidCache() {
    conformanceAnalyseBizService.invalidCache();
  }

  @ApiOperation(value = "查询一致性流程的概览数据", notes = "查询一致性流程的概览数据")
  @PostMapping(value = "/getConformanceGeneralData")
  public Response<ConformanceGeneralData> getConformanceGeneralData(
      @Valid @RequestBody QueryConformanceGeneralRequest request) {

    try {
      ConformanceGeneralData conformanceGeneralData =
          conformanceService.getConformanceGeneralData(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getStartTime(),
              request.getEndTime());
      return Response.success(conformanceGeneralData);
    } catch (Exception e) {
      log.error("getConformanceGeneralData error ", e);
    }
    return Response.success(
        ConformanceGeneralData.builder()
            .conformanceCaseNum("--")
            .conformanceRate("--")
            .unConformanceCaseNum("--")
            .allowVariantNum("--")
            .unAllowVariantNum("--")
            .build());
  }

  @ApiOperation(value = "查询非一致流程列表", notes = "查询非一致流程列表")
  @PostMapping(value = "/getUnConformanceList")
  public Response<List<UnConformance>> getUnConformanceList(
      @Valid @RequestBody QueryUnConformanceListRequest request) {

    try {
      List<UnConformance> unConformanceList =
          conformanceService.getUnConformanceList(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getPageNum(),
              request.getPageSize(),
              request.getStartTime(),
              request.getEndTime());
      return Response.success(unConformanceList);
    } catch (Exception e) {
      log.error("getUnConformanceList error", e);
    }
    return Response.success(null);
  }

  @ApiOperation(value = "查询允许流程列表", notes = "查询允许流程列表")
  @PostMapping(value = "/getAllowProcessList")
  public Response<List<UnConformance>> getAllowProcessList(
      @Valid @RequestBody QueryUnConformanceListRequest request) {

    try {
      return Response.success(
          conformanceService.getAllowProcessList(request.getTopicId(), request.getTopicSheetId()));
    } catch (Exception e) {
      log.error("getUnConformanceList error", e);
    }
    return Response.success(null);
  }

  @ApiOperation(value = "查询一致性流程的kpi列表（概览页面的kpi列表）", notes = "查询一致性流程的kpi列表（概览页面的kpi列表）")
  @PostMapping(value = "/getConformanceKpi4General")
  public Response<List<ConformanceKpi>> getConformanceKpi4General(
      @Valid @RequestBody QueryConformanceKpi4GeneralRequest request) {
    try {
      List<ConformanceKpi> conformanceKpi4General =
          conformanceService.getConformanceKpi4General(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getStartTime(),
              request.getEndTime());
      return Response.success(conformanceKpi4General);
    } catch (Exception e) {
      log.error("getConformanceViewData error ", e);
    }
    return Response.success(null);
  }

  @ApiOperation(value = "查询一致性流程的二维图数据", notes = "查询一致性流程的二维图数据")
  @PostMapping(value = "/getConformanceViewData")
  public Response<ConformanceViewDataOutputVO> getConformanceViewData(
      @Valid @RequestBody QueryConformanceViewDataRequest request) {

    try {
      ConformanceViewDataOutputVO conformanceViewData =
          conformanceService.getConformanceViewData(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getReason(),
              request.getReasonType(),
              request.getTimeFormat(),
              request.getStartTime(),
              request.getEndTime());
      return Response.success(conformanceViewData);
    } catch (Exception e) {
      log.error("getConformanceViewData error ", e);
    }
    return Response.success(null);
  }

  @ApiOperation(value = "设置为允许流程", notes = "设置为允许流程")
  @PostMapping(value = "/setAllowProcess")
  public Response<Boolean> setAllowProcess(@Valid @RequestBody SetConformanceRequest request) {

    return Response.success(
        conformanceAnalyseBizService.setAllowProcess(
            request.getTopicId(),
            request.getTopicSheetId(),
            request.getResult(),
            request.getResultNumber(),
            request.getReasonType(),
            request.getConditionName()));
  }

  @ApiOperation(value = "移除允许流程", notes = "移除允许流程")
  @PostMapping(value = "/removeAllowProcess")
  public Response<Boolean> removeAllowProcess(@Valid @RequestBody SetConformanceRequest request) {
    CreateAuditLog createAuditLog =
        AuditLogUtil.buildAuditLog("/sp-engine/removeAllowProcess")
            .addParameters("topicSheetId", request.getTopicSheetId())
            .addParameters("topicId", request.getTopicId())
            .addParameters("variant", request.getVariant())
            .addParameters("result", request.getResult())
            .addParameters("resultNumber", request.getResultNumber());
    AuditLogUtil.save(createAuditLog);
    return Response.success(
        conformanceAnalyseBizService.removeAllowProcess(
            request.getTopicSheetId(), request.getResult()));
  }

  @ApiOperation(value = "查询一致性流程的kpi列表（带树图页面的kpi列表）", notes = "查询一致性流程的kpi列表（带树图页面的kpi列表）")
  @PostMapping(value = "/getConformanceKpi")
  public Response<List<ConformanceKpi>> getConformanceKpi(
      @Valid @RequestBody QueryConformanceKpiRequest request) {
    try {
      return Response.success(
          conformanceService.getConformanceKpi4General(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getReason(),
              request.getReasonType()));
    } catch (Exception e) {
      log.error("getConformanceKpi error ", e);
    }
    return Response.success(null);
  }

  @ApiOperation(value = "查询一致性流程的kpi列表（概览页面的kpi列表）", notes = "查询一致性流程的kpi列表（概览页面的kpi列表）")
  @PostMapping(value = "/addReasonToTopicFilter")
  public Response<Boolean> addReasonToTopicFilter(
      @Valid @RequestBody QueryConformanceViewDataRequest request) {
    try {
      String filter =
          conformanceService.addReasonTopicFilter(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getReason(),
              request.getReasonType());
      if (filter.isEmpty()) {
        return Response.success(true);
      }
      SaveTopicFilterSubRequest build =
          SaveTopicFilterSubRequest.builder()
              .type(2000)
              .expression(filter)
              .description(request.getReason())
              .extension(request.getExtension())
              .build();
      ArrayList<SaveTopicFilterSubRequest> saveTopicFilterSubRequests = new ArrayList<>();
      saveTopicFilterSubRequests.add(build);
      Boolean result =
          processTreeWriteBizService.saveTopicFilterV2(
              SaveTopicFilterRequest.builder()
                  .topicId(request.getTopicId())
                  .list(saveTopicFilterSubRequests)
                  .build());
      return Response.success(result);
    } catch (Exception e) {
      log.error("addReasonToTopicFilter error ", e);
    }
    return Response.success(null);
  }

  @ApiOperation(value = "指挥舱一致性过滤reason转为PQL", notes = "指挥舱一致性过滤reason转为PQL")
  @PostMapping(value = "/reasonConvertPQL")
  public Response<List<TopicFilterPO>> reasonConvertPQL(
      @Valid @RequestBody QueryConformanceViewDataRequest request) {
    try {
      String filter =
          conformanceService.addReasonTopicFilter(
              request.getTopicId(),
              request.getTopicSheetId(),
              request.getReason(),
              request.getReasonType());
      if (filter.isEmpty()) {
        return Response.success(null);
      }
      List<TopicFilterPO> topicFilterPOList = new ArrayList<>();
      TopicFilterPO build =
          TopicFilterPO.builder()
              .type(2000)
              .sheetId(request.getTopicSheetId())
              .expression(filter)
              .description(request.getReason())
              .build();
      topicFilterPOList.add(build);
      return Response.success(topicFilterPOList);
    } catch (Exception e) {
      log.error("reasonConvertPQL error ", e);
    }
    return Response.success(null);
  }
}
