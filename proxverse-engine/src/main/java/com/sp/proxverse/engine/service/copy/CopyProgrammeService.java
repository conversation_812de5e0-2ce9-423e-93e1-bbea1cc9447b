package com.sp.proxverse.engine.service.copy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.SimulationEventAttrPo;
import com.sp.proxverse.common.model.po.SimulationProgrammePo;
import com.sp.proxverse.common.model.po.SimulationResourcePo;
import com.sp.proxverse.common.model.po.SimulationRunFrequencyPo;
import com.sp.proxverse.engine.service.copy.entity.Programme;
import com.sp.proxverse.interfaces.dao.impl.SimulationEventAttrServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.SimulationProgrammeServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.SimulationResourceServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.SimulationRunFrequencyServiceImpl;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/23 上午11:19
 */
public class CopyProgrammeService implements CopyService<Programme> {

  private SimulationProgrammeServiceImpl simulationProgrammeService;

  private SimulationEventAttrServiceImpl simulationEventAttrService;

  private SimulationRunFrequencyServiceImpl simulationRunFrequencyService;

  private SimulationResourceServiceImpl simulationResourceService;

  public CopyProgrammeService(
      SimulationProgrammeServiceImpl simulationProgrammeService,
      SimulationEventAttrServiceImpl simulationEventAttrService,
      SimulationRunFrequencyServiceImpl simulationRunFrequencyService,
      SimulationResourceServiceImpl simulationResourceService) {
    this.simulationProgrammeService = simulationProgrammeService;
    this.simulationEventAttrService = simulationEventAttrService;
    this.simulationRunFrequencyService = simulationRunFrequencyService;
    this.simulationResourceService = simulationResourceService;
  }

  @Override
  public List<Integer> copy(Programme programme) {
    SimulationProgrammePo simulationProgrammeSource =
        simulationProgrammeService.getById(programme.getProgrammeId());
    if (simulationProgrammeSource == null) {
      throw new IllegalArgumentException(
          "Error programmeId not found " + programme.getProgrammeId());
    }
    SimulationProgrammePo newSimulationProgramme = new SimulationProgrammePo();
    newSimulationProgramme.setSimulationId(simulationProgrammeSource.getSimulationId());
    newSimulationProgramme.setName(programme.getName());

    simulationProgrammeService.save(newSimulationProgramme);

    List<SimulationEventAttrPo> simulationEventAttrPos =
        simulationEventAttrService.list(
            new LambdaQueryWrapper<SimulationEventAttrPo>()
                .eq(SimulationEventAttrPo::getProgrammeId, programme.getProgrammeId())
                .eq(SimulationEventAttrPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    if (!simulationEventAttrPos.isEmpty()) {
      List<SimulationEventAttrPo> newSimulationEventAttrPos = new ArrayList<>();
      for (SimulationEventAttrPo simulationEventAttrPo : simulationEventAttrPos) {
        SimulationEventAttrPo newSimulationEventAttrPon = simulationEventAttrPo.copy();
        newSimulationEventAttrPon.setProgrammeId(newSimulationProgramme.getId());
        newSimulationEventAttrPos.add(newSimulationEventAttrPon);
      }
      simulationEventAttrService.saveBatch(newSimulationEventAttrPos);
    }

    List<SimulationRunFrequencyPo> simulationRunFrequencyPos =
        simulationRunFrequencyService.list(
            new LambdaQueryWrapper<SimulationRunFrequencyPo>()
                .eq(SimulationRunFrequencyPo::getProgrammeId, simulationProgrammeSource.getId())
                .eq(SimulationRunFrequencyPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(SimulationRunFrequencyPo::getId));
    if (!simulationRunFrequencyPos.isEmpty()) {
      List<SimulationRunFrequencyPo> newSimulationRunFrequencyPos = new ArrayList<>();
      for (SimulationRunFrequencyPo simulationRunFrequencyPo : simulationRunFrequencyPos) {
        SimulationRunFrequencyPo newSimulationRunFrequencyPo = simulationRunFrequencyPo.copy();
        newSimulationRunFrequencyPo.setProgrammeId(newSimulationProgramme.getId());
        newSimulationRunFrequencyPos.add(newSimulationRunFrequencyPo);
      }
      simulationRunFrequencyService.saveBatch(newSimulationRunFrequencyPos);
    }

    List<SimulationResourcePo> simulationResourcePos =
        simulationResourceService.list(
            new LambdaQueryWrapper<SimulationResourcePo>()
                .eq(SimulationResourcePo::getProgrammeId, simulationProgrammeSource.getId())
                .eq(SimulationResourcePo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(SimulationResourcePo::getId));
    if (!simulationResourcePos.isEmpty()) {
      List<SimulationResourcePo> newSimulationResourcePos = new ArrayList<>();
      for (SimulationResourcePo resourcePo : simulationResourcePos) {
        SimulationResourcePo newSimulationResourcePo = resourcePo.copy();
        newSimulationResourcePo.setProgrammeId(newSimulationProgramme.getId());
        newSimulationResourcePos.add(newSimulationResourcePo);
      }
      simulationResourceService.saveBatch(newSimulationResourcePos);
    }
    return Lists.newArrayList(newSimulationProgramme.getId());
  }
}
