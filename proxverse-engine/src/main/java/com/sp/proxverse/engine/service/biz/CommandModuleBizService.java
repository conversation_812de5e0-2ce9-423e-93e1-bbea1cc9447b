package com.sp.proxverse.engine.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.UserLevelEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.BusinessTopicPO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.SheetMenuPO;
import com.sp.proxverse.common.model.po.TopicMenuPO;
import com.sp.proxverse.common.model.vo.OverallOutputVO;
import com.sp.proxverse.common.model.vo.TopicModuleOutputVO;
import com.sp.proxverse.common.model.vo.commandModule.GetOverallListReqVo;
import com.sp.proxverse.common.model.vo.commandModule.GetTopicModuleReqVo;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.dao.service.KpiService;
import com.sp.proxverse.interfaces.dao.service.SheetMenuService;
import com.sp.proxverse.interfaces.dao.service.TopicMenuService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetKpiService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetService;
import com.sp.proxverse.interfaces.service.biz.DataBizService;
import com.sp.proxverse.interfaces.service.data.DataAPIService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import com.sp.proxverse.interfaces.service.oauth2.Oauth2ApiService;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UserInfoResVo;
import com.sp.proxverse.oauth2.model.UserPermissionInfo;
import com.sp.proxverse.oauth2.service.permission.UserInfoFactory;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommandModuleBizService {

  @Autowired private BusinessTopicService businessTopicService;

  @Autowired private BusinessTopicDataService businessTopicDataService;

  @Autowired private TopicSheetService topicSheetService;

  @Autowired private SheetMenuService sheetMenuService;

  @Autowired private TopicSheetKpiService topicSheetKpiService;

  @Autowired private DataBizService dataBizService;

  @Autowired private DataModelService dataModelService;

  @Autowired private KpiService kpiService;

  @Autowired private TopicMenuService topicMenuService;

  @Autowired private UserInfoFactory userInfoFactory;

  @Autowired private DataAPIService dataAPIService;

  @Autowired PQLService kpi;

  @Autowired private Oauth2ApiService oauth2ApiService;

  public List<OverallOutputVO> getOverallList(GetOverallListReqVo getOverallListReqVo) {

    // 校验用户信息
    UserInfoResVo userInfoResVo =
        oauth2ApiService.selectUserInfoById(getOverallListReqVo.getUserId().toString()).getData();
    if (userInfoResVo == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.USER_INFO_NOT_EXIST));
    }

    Integer userId = getOverallListReqVo.getUserId();

    UserPermissionInfo userInfo = userInfoFactory.getOrCreate(userId);
    Set<Integer> permissionTopicIds = new HashSet<>();
    if (!UserLevelEnum.isRoot(userInfo.getLevel())) {
      if (CollectionUtils.isNotEmpty(userInfo.getTopicIdPermissionList())) {
        return new ArrayList<>();
      }
      permissionTopicIds = userInfo.getTopicIdPermissionList();
    }

    List<BusinessTopicPO> list =
        businessTopicService.list(
            new LambdaQueryWrapper<BusinessTopicPO>()
                .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .eq(BusinessTopicPO::getType, BusinessTopicTypeEnum.OVERALL_BUSINESS.getValue())
                .in(
                    CollectionUtils.isNotEmpty(permissionTopicIds),
                    BusinessTopicPO::getId,
                    permissionTopicIds)
                .eq(
                    BusinessTopicPO::getSnapshotFlag,
                    getOverallListReqVo.getSnapshotFlag()
                        ? BusinessTopicPO.SNAPSHOT_TRUE
                        : BusinessTopicPO.SNAPSHOT_FALSE));
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }

    return list.stream()
        .map(
            m ->
                OverallOutputVO.builder()
                    .name(m.getName())
                    .updateTimeZh(DateTimeUtil.date2String(m.getCreateTime()))
                    .topicId(m.getId())
                    .type(m.getType())
                    .build())
        .collect(Collectors.toList());
  }

  private Integer sortMenu(Map<Integer, SheetMenuPO> sortMenuMap, Integer sheetId) {
    SheetMenuPO menuPo = sortMenuMap.get(sheetId);
    if (Objects.isNull(menuPo)) {
      // 则把此topic放在最后面
      return 50000;
    }
    return menuPo.getSort();
  }

  public List<TopicModuleOutputVO> getTopicModuleList(GetTopicModuleReqVo getTopicModuleReqVo) {
    // 校验用户信息
    UserInfoResVo userInfoResVo =
        oauth2ApiService.selectUserInfoById(getTopicModuleReqVo.getUserId().toString()).getData();
    if (userInfoResVo == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.USER_INFO_NOT_EXIST));
    }

    Integer userId = getTopicModuleReqVo.getUserId();

    UserPermissionInfo userInfo = userInfoFactory.getOrCreate(userId);
    Set<Integer> permissionTopicIds = new HashSet<>();
    if (!UserLevelEnum.isRoot(userInfo.getLevel())) {
      if (CollectionUtils.isEmpty(userInfo.getTopicIdPermissionList())) {
        return new ArrayList<>();
      }
      permissionTopicIds = userInfo.getTopicIdPermissionList();
    }

    List<BusinessTopicPO> list =
        businessTopicService.list(
            new LambdaQueryWrapper<BusinessTopicPO>()
                .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .eq(BusinessTopicPO::getType, BusinessTopicTypeEnum.BUSINESS_TREE.getValue())
                .in(
                    CollectionUtils.isNotEmpty(permissionTopicIds),
                    BusinessTopicPO::getId,
                    permissionTopicIds)
                .eq(
                    BusinessTopicPO::getSnapshotFlag,
                    getTopicModuleReqVo.getSnapshotFlag()
                        ? BusinessTopicPO.SNAPSHOT_TRUE
                        : BusinessTopicPO.SNAPSHOT_FALSE));
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }

    List<Integer> topicIdList =
        list.stream().map(BusinessTopicPO::getId).collect(Collectors.toList());

    List<TopicModuleOutputVO> outputVOList = new ArrayList<>();

    Map<Integer, Integer> modelIdMap = businessTopicDataService.getDataModelIdList(topicIdList);

    Collection<Integer> modelIdList = modelIdMap.values();

    List<DataModelPO> dataModelList = dataModelService.listByIds(modelIdList);

    Map<Integer, DataModelPO> dataModelMap =
        dataModelList.stream()
            .collect(Collectors.toMap(DataModelPO::getId, Function.identity(), (k1, k2) -> k1));

    for (BusinessTopicPO topicPO : list) {

      Integer modelId = modelIdMap.get(topicPO.getId());

      DataModelPO dataModel =
          Optional.ofNullable(dataModelMap.get(modelId)).orElse(new DataModelPO());

      Date updateTime =
          dataModel.getUpdateTime() == null ? dataModel.getCreateTime() : dataModel.getUpdateTime();

      TopicModuleOutputVO outputVO = new TopicModuleOutputVO();
      outputVO.setName(topicPO.getName());

      outputVO.setUpdateTimeZh(DateTimeUtil.date2String(updateTime));
      outputVO.setTopicId(topicPO.getId());
      outputVO.setParentId(topicPO.getParentId());
      outputVO.setTopicType(topicPO.getType());

      outputVOList.add(outputVO);
    }

    outputVOList = this.sortModuleList(outputVOList, userId);
    return outputVOList.stream()
        .filter(f -> Objects.nonNull(f.getTopicId()))
        .collect(Collectors.toList());
  }

  private List<TopicModuleOutputVO> sortModuleList(
      List<TopicModuleOutputVO> outputVOList, Integer userId) {
    // 对这个collect进行索引排序
    List<TopicMenuPO> indexList =
        topicMenuService.list(
            new LambdaQueryWrapper<TopicMenuPO>().eq(TopicMenuPO::getUserId, userId));
    if (CollectionUtils.isEmpty(indexList)) {
      // 说明此人没有拖拽过
      return outputVOList;
    }

    Map<Integer, TopicMenuPO> sortMenuMap =
        indexList.stream()
            .collect(
                Collectors.toMap(TopicMenuPO::getTopicId, Function.identity(), (k1, k2) -> k1));
    return outputVOList.stream()
        .sorted(
            Comparator.comparing(m -> this.sortMenu(sortMenuMap, m.getTopicId(), m.getParentId())))
        .collect(Collectors.toList());
  }

  private Integer sortMenu(
      Map<Integer, TopicMenuPO> sortMenuMap, Integer topicId, Integer parentId) {
    TopicMenuPO menuPo = sortMenuMap.get(topicId);
    if (Objects.isNull(menuPo)) {
      // 则把此topic放在最后面
      return 50000 + topicId;
    }
    TopicMenuPO parent = sortMenuMap.get(parentId);
    Integer add = Objects.isNull(parent) ? 0 : parent.getSort();
    return menuPo.getSort() + add;
  }
}
