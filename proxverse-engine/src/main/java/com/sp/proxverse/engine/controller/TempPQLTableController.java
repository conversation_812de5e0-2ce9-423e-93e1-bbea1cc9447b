package com.sp.proxverse.engine.controller;

import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.dto.TempPQLTableDto;
import com.sp.proxverse.data.service.pql.TempPQLTableService;
import io.swagger.annotations.Api;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(value = "案例探索控制器", tags = "业务视图")
@RequestMapping(value = "/sp-engine")
@RestController
public class TempPQLTableController {

  @Autowired TempPQLTableService pqlTableService;

  @GetMapping(value = "/topics/{topic_id}/tempViews")
  public Response<List<TempPQLTableDto>> getTempTables(@PathVariable("topic_id") Integer topicId) {
    return new Response<>(pqlTableService.getTempPQLTablesIsExist(topicId));
  }

  @PostMapping(value = "/topics/{topic_id}/tempViews")
  public Response<TempPQLTableDto> createTempTable(
      @PathVariable("topic_id") Integer topicId, @Valid @RequestBody TempPQLTableDto req) {
    return new Response<>(pqlTableService.createTempPQLTable(topicId, req));
  }

  @PutMapping(value = "/topics/{topic_id}/tempViews")
  public Response<TempPQLTableDto> updateTempTable(
      @PathVariable("topic_id") Integer topicId, @Valid @RequestBody TempPQLTableDto req) {
    return new Response<>(pqlTableService.updateTempPQLTable(topicId, req));
  }

  @DeleteMapping(value = "/topics/{topic_id}/tempViews/{temp_view_id}")
  public Response<Boolean> deleteTempTable(
      @PathVariable("topic_id") Integer topicId, @PathVariable("temp_view_id") Integer tempViewId) {
    return new Response<>(pqlTableService.delete(tempViewId));
  }
}
