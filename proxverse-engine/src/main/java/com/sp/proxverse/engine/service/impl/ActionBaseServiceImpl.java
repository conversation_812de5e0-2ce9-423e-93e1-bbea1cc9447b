package com.sp.proxverse.engine.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.action.ActionBaseService;
import com.sp.proxverse.common.action.bo.ActionBo;
import com.sp.proxverse.common.action.bo.ActionDoParamBo;
import com.sp.proxverse.common.action.bo.ActionDoResultBo;
import com.sp.proxverse.common.action.bo.FilterParamBo;
import com.sp.proxverse.common.action.bo.FilterParamProcessBo;
import com.sp.proxverse.common.action.bo.HttpActionDoParamBo;
import com.sp.proxverse.common.action.bo.email.SendEmailActionDoParamBo;
import com.sp.proxverse.common.action.bo.wechat.SendWechatMsgActionDoParamBo;
import com.sp.proxverse.common.action.enums.ActionTypeEnum;
import com.sp.proxverse.common.action.enums.FilterRelationEnum;
import com.sp.proxverse.common.action.enums.FilterRuleEnum;
import com.sp.proxverse.common.exception.DataException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.bo.actionLog.CreatActionLogBo;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.ActionFlowPo;
import com.sp.proxverse.common.model.po.ActionLogPo;
import com.sp.proxverse.common.model.po.ActionPo;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.engine.service.function.EqualFuncServiceImpl;
import com.sp.proxverse.engine.service.function.LessThanEqualFuncServiceImpl;
import com.sp.proxverse.engine.service.function.LessThanFuncServiceImpl;
import com.sp.proxverse.engine.service.function.MoreThanEqualFuncServiceImpl;
import com.sp.proxverse.engine.service.function.MoreThanFuncServiceImpl;
import com.sp.proxverse.engine.service.function.NotEqualFuncServiceImpl;
import com.sp.proxverse.engine.util.CheckUtil;
import com.sp.proxverse.interfaces.common.common.CommonBean;
import com.sp.proxverse.interfaces.common.util.JsonUtil;
import com.sp.proxverse.interfaces.dao.service.IActionFlowService;
import com.sp.proxverse.interfaces.dao.service.IActionLogService;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2022-05-10 10:42 上午
 */
@Service
public class ActionBaseServiceImpl implements ActionBaseService {

  private static final Logger log = LoggerFactory.getLogger(ActionBaseServiceImpl.class);

  @Autowired HttpActionServiceImpl httpActionService;

  @Autowired SendEmailServiceImpl sendEmailService;

  @Autowired ActionBaseService actionBaseService;

  @Autowired IActionLogService actionLogService;

  @Autowired IActionFlowService actionFlowService;

  @Autowired CommonBean commonBean;

  @Autowired EqualFuncServiceImpl equalFuncService;

  @Autowired NotEqualFuncServiceImpl notEqualFuncService;

  @Autowired SendWechatMsgServiceImpl sendWechatMsgService;

  @Autowired private UserInfoUtil userInfoUtil;
  private static final String EXCEPTION_INFO = "not support：";

  @Override
  public ActionBo getActionBo(List<ActionPo> actionPos) {
    if (CollectionUtils.isEmpty(actionPos)) {
      throw new DataException(ErrorCode.ERROR_PARAM);
    }

    Map<Integer, List<ActionPo>> actionPoByParenId =
        actionPos.stream()
            .filter(po -> po.getParentActionCode() != null)
            .collect(Collectors.groupingBy(ActionPo::getParentActionCode, Collectors.toList()));
    // 获取起始节点

    List<ActionPo> collect =
        actionPos.stream()
            .filter(
                po -> {
                  if (po.getStartFlag()) {
                    return true;
                  }
                  return false;
                })
            .sorted(Comparator.comparing(ActionPo::getActionCode))
            .collect(Collectors.toList());

    if (CollectionUtils.isEmpty(collect)) {
      collect =
          actionPos.stream()
              .filter(
                  po -> {
                    if (po.getParentActionCode() == null || po.getParentActionCode() == -1) {
                      return true;
                    }
                    return false;
                  })
              .sorted(Comparator.comparing(ActionPo::getActionCode))
              .collect(Collectors.toList());
    }
    if (CollectionUtils.isEmpty(collect)) {
      throw new DataException(ErrorCode.ERROR_PARAM, I18nUtil.getMessage(I18nConst.NO_START_END));
    }

    ActionPo actionPo = collect.get(0);

    ActionFlowPo actionFlowPo = actionFlowService.getById(actionPo.getActionFlowId());

    ActionBo actionBo = new ActionBo();
    actionBo.setRunCode(actionFlowPo.getRunCode());

    // 递归处理
    processAction(actionPo, actionBo, actionPoByParenId);

    return actionBo;
  }

  @Override
  public Object runAction(ActionBo actionBo, Integer userId, Integer tenantId) {
    ActionTypeEnum actionTypeEnum = actionBo.getActionTypeEnum();

    // 解析参数
    doProcessActionParam(actionBo);

    // 校验当前action是否被过滤，如果过滤直接返回结果，不运行
    if (!filterAction(actionBo)) {
      actionBo.setChildActions(new ArrayList<>());
      return actionBo;
    }
    // 日志开始运行
    Integer runLogId =
        actionLogService.startActionRunLog(
            CreatActionLogBo.builder()
                .actionId(actionBo.getActionId())
                .actionFlowId(actionBo.getActionFlowId())
                .runCoed(actionBo.getRunCode())
                .activeType(actionBo.getActionTypeEnum().getCode())
                .userId(userId)
                .actionCode(actionBo.getActionCode())
                .actionName(actionBo.getActionName())
                .nodeInfo(actionBo.getNodeInfo())
                .singleNodeFlag(actionBo.getSingleNodeFlag())
                .actionFlowFlag(ActionLogPo.ACTION_FLOW_FALSE)
                .actionParam(JSON.toJSONString(actionBo.getActionDoParamBo()))
                .build(),
            tenantId);

    // 结束日志
    CreatActionLogBo.CreatActionLogBoBuilder creatActionLogBoBuilder =
        CreatActionLogBo.builder().actionLogId(runLogId).successFlag(0);

    ActionDoResultBo actionDoResultBo = null;

    try {
      Thread.sleep(1500);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 执行动作的异常统一在这里拦截
    try {
      switch (actionTypeEnum) {
        case HTTP:
          actionDoResultBo = httpActionService.doAction(actionBo.getActionDoParamBo());
          break;

        case SEND_EMAIL:
          actionDoResultBo = sendEmailService.doAction(actionBo.getActionDoParamBo());
          break;

        case ROUTE:
          actionDoResultBo = new ActionDoResultBo();
          actionDoResultBo.setActionCode(actionBo.getActionCode());
          break;

        case UNKNOWN:
          actionDoResultBo = new ActionDoResultBo();
          actionDoResultBo.setActionCode(actionBo.getActionCode());
          actionDoResultBo.setRequestStart(false);
          actionDoResultBo.setResultsDesc(I18nUtil.getMessage(I18nConst.INFORMANCE_INCOMPLETE));
          break;
        case SEND_WECHAT_MSG:
          actionDoResultBo = sendWechatMsgService.doAction(actionBo.getActionDoParamBo());
          break;
        default:
          throw new DataException(
              ErrorCode.ERROR_DATA.getCode(), "not support:" + actionTypeEnum.displayName);
      }
      actionBo.setActionDoResultBo(actionDoResultBo);
      actionBo.addActionDoResultBo(actionDoResultBo);

      creatActionLogBoBuilder.actionResult(JSON.toJSONString(actionDoResultBo));
      actionLogService.endActionRunLog(creatActionLogBoBuilder.build());

    } catch (Exception e) {
      log.error("runAction error =>", e);
      String message = I18nUtil.getMessage(I18nConst.ACTION_RUNNING_ERROR);
      if (StringUtils.isNotBlank(e.getMessage())) {
        message = e.getMessage();
      }
      creatActionLogBoBuilder.actionResult(message).successFlag(1);
      actionLogService.endActionRunLog(creatActionLogBoBuilder.build());

      throw new DataException(ErrorCode.ACTION_RUN_ERROR, message);
    }

    // 执行孩子
    if (!CollectionUtils.isEmpty(actionBo.getChildActions())) {
      actionBo.getChildAction().setActionResultRecord(actionBo.getActionResultRecord());
      List<ActionBo> childActions = actionBo.getChildActions();
      for (ActionBo childAction : childActions) {
        commonBean.addActionResult(actionBo.getActionFlowId(), actionBo.getRunCode(), childAction);
        actionBaseService.runAction(childAction, userId, tenantId);
      }
    }
    return actionBo;
  }

  @Override
  public ActionDoParamBo analysisActionParam(ActionTypeEnum actionTypeEnum, Object param) {
    try {
      ActionDoParamBo actionDoParamBo;
      switch (actionTypeEnum) {
        case HTTP:
          actionDoParamBo = JSON.parseObject(JSON.toJSONString(param), HttpActionDoParamBo.class);
          return actionDoParamBo;

        case ROUTE:
        case UNKNOWN:
          break;

        case SEND_EMAIL:
          actionDoParamBo =
              JSON.parseObject(JSON.toJSONString(param), SendEmailActionDoParamBo.class);
          return actionDoParamBo;

        case SEND_WECHAT_MSG:
          actionDoParamBo =
              JSON.parseObject(JSON.toJSONString(param), SendWechatMsgActionDoParamBo.class);
          return actionDoParamBo;
        default:
          throw new DataException(
              ErrorCode.ERROR_SYSTEM, EXCEPTION_INFO + actionTypeEnum.displayName);
      }
      return new ActionDoParamBo();
    } catch (Exception e) {
      throw new DataException(ErrorCode.ERROR_SYSTEM, "not support！" + e.getMessage());
    }
  }

  @Autowired MoreThanFuncServiceImpl moreThanFuncService;

  @Autowired MoreThanEqualFuncServiceImpl moreThanEqualFuncService;

  @Autowired LessThanFuncServiceImpl lessThanFuncService;

  @Autowired LessThanEqualFuncServiceImpl lessThanEqualFuncService;

  /**
   * 校验该节点是否被过滤掉
   *
   * @param actionBo
   * @return false 不执行，true 执行
   */
  private boolean filterAction(ActionBo actionBo) {
    if (!actionBo.getFilterFlag()) {
      return true;
    }
    List<FilterParamBo> filterParams = actionBo.getFilterParams();
    if (CollectionUtils.isEmpty(filterParams)) {
      return true;
    }
    List<FilterParamProcessBo> filterParamProcessBos = new ArrayList<>();
    Map<Integer, FilterParamProcessBo> filterParamProcessByIndex = new HashMap<>();

    // 预处理
    for (int i = 0; i < filterParams.size(); i++) {
      FilterParamProcessBo filterParamProcessBo = new FilterParamProcessBo();
      BeanUtils.copyProperties(filterParams.get(i), filterParamProcessBo);
      filterParamProcessBo.setFilterCode(i);

      List<String> paramStr = new ArrayList<>();
      paramStr.add(filterParamProcessBo.getParamFirst());
      paramStr.add(filterParamProcessBo.getParamLast());
      List<Object> paramObj = CheckUtil.checkFormat(paramStr);
      FilterRuleEnum filterRuleEnum = filterParamProcessBo.getFilterRuleEnum();

      boolean currentResult = false;
      switch (filterRuleEnum) {
        case EQUAL:
          currentResult = equalFuncService.doFunction(paramObj);
          break;
        case NOT_EQUAL:
          currentResult = notEqualFuncService.doFunction(paramObj);
          break;
        case MORE_THAN:
          currentResult = moreThanFuncService.doFunction(paramObj);
          break;

        case MORE_THAN_EQUAL:
          currentResult = moreThanEqualFuncService.doFunction(paramObj);
          break;

        case LESS_THAN:
          currentResult = lessThanFuncService.doFunction(paramObj);
          break;

        case LESS_THAN_EQUAL:
          currentResult = lessThanEqualFuncService.doFunction(paramObj);
          break;

        default:
          throw new DataException(
              ErrorCode.ERROR_SYSTEM, EXCEPTION_INFO + filterRuleEnum.getDescribe());
      }
      filterParamProcessBo.setCurrentResult(currentResult);
      filterParamProcessByIndex.put(i, filterParamProcessBo);
      filterParamProcessBos.add(filterParamProcessBo);
    }
    List<FilterParamProcessBo> filterParamAnds =
        filterParamProcessBos.stream()
            .filter(
                bo -> {
                  return bo.getRelation().equals(FilterRelationEnum.AND);
                })
            .collect(Collectors.toList());

    for (FilterParamProcessBo filterParamAnd : filterParamAnds) {
      boolean andResult = false;

      FilterParamProcessBo filterParamProcessBo =
          removeUpperLevel(filterParamProcessByIndex, filterParamAnd);

      if (filterParamProcessByIndex.get(filterParamAnd.getFilterCode()).getCurrentResult()
          && filterParamProcessBo.getCurrentResult()) {
        andResult = true;
      }
      filterParamAnd.setCurrentResult(andResult);
    }

    boolean result = false;
    for (Map.Entry<Integer, FilterParamProcessBo> integerFilterParamProcessBoEntry :
        filterParamProcessByIndex.entrySet()) {
      if (integerFilterParamProcessBoEntry.getValue().getCurrentResult()) {
        result = true;
        break;
      }
    }
    return result;
  }

  /**
   * 移除上一级结果进行归并
   *
   * @param filterParamProcessByIndex
   * @param filterParamAnd
   */
  private FilterParamProcessBo removeUpperLevel(
      Map<Integer, FilterParamProcessBo> filterParamProcessByIndex,
      FilterParamProcessBo filterParamAnd) {

    int i = 1;
    while (true) {
      if (filterParamProcessByIndex.containsKey(filterParamAnd.getFilterCode() - i)) {
        FilterParamProcessBo remove =
            filterParamProcessByIndex.remove(filterParamAnd.getFilterCode() - 1);
        return remove;
      }
      if (i > 100) {
        FilterParamProcessBo filterParamProcessBo = new FilterParamProcessBo();
        filterParamProcessBo.setCurrentResult(false);
        return filterParamProcessBo;
      }
    }
  }

  /**
   * 递归处理流程分析链路
   *
   * @param startActionPo
   * @param actionBo
   * @param actionPoByParenId
   * @return
   */
  private void processAction(
      ActionPo startActionPo, ActionBo actionBo, Map<Integer, List<ActionPo>> actionPoByParenId) {
    actionBo.setParamStr(startActionPo.getActionParam());
    actionBo.setActionTypeEnum(startActionPo.getActiveTypeEnum());
    actionBo.setStartFlag(startActionPo.getStartFlag());
    actionBo.setActionFlowId(startActionPo.getActionFlowId());
    actionBo.setActionId(startActionPo.getId());
    actionBo.setActionCode(startActionPo.getActionCode());
    actionBo.setActionName(startActionPo.getName());
    actionBo.setFilterFlag(startActionPo.getFilterFlagBoolean());
    if (startActionPo.getFilterFlagBoolean()) {
      actionBo.setFilterParams(
          JSONObject.parseArray(startActionPo.getFilterContent(), FilterParamBo.class));
    }

    if (!actionPoByParenId.containsKey(startActionPo.getActionCode())) {
      return;
    } else {
      // 遍历孩子节点
      List<ActionPo> actionPos = actionPoByParenId.get(startActionPo.getActionCode());
      for (ActionPo po : actionPos) {
        ActionBo actionBoChild = new ActionBo();
        actionBo.addChildAction(actionBoChild);
        actionBoChild.setActionResultRecord(actionBo.getActionResultRecord());
        actionBoChild.setRunCode(actionBo.getRunCode());
        processAction(po, actionBoChild, actionPoByParenId);
      }
    }
  }

  /**
   * 解析参数信息 入参会被修改
   *
   * @param actionBo
   */
  private void doProcessActionParam(ActionBo actionBo) {
    ActionDoParamBo actionDoParamBo = null;
    // 解析动态参数
    String actionParam = actionBo.getParamStr();
    try {
      actionParam = JsonUtil.actionReplaceParam(actionParam, actionBo.getActionResultRecord());

      if (actionBo.getFilterFlag()) {
        // 解析过滤条件中的参数
        String param =
            JsonUtil.actionReplaceParam(
                JSON.toJSONString(actionBo.getFilterParams()), actionBo.getActionResultRecord());
        actionBo.setFilterParams(JSONObject.parseArray(param, FilterParamBo.class));
      }
      actionParam = JsonUtil.actionReplaceParam(actionParam, actionBo.getActionResultRecord());
    } catch (Exception e) {
      log.error(
          ErrorCode.ACTION_START_PARAM_ERROR
              + "not support：flowId:"
              + actionBo.getActionFlowId()
              + " actionCode:"
              + actionBo.getActionCode(),
          e);
    }

    JSONObject jsonObject = JSONObject.parseObject(actionParam);
    switch (actionBo.getActionTypeEnum()) {
      case HTTP:
        if (jsonObject == null) {
          actionDoParamBo = new HttpActionDoParamBo();
        } else {
          actionDoParamBo = JSON.parseObject(actionParam, HttpActionDoParamBo.class);
        }
        break;

      case SEND_EMAIL:
        if (jsonObject == null) {
          actionDoParamBo = new SendEmailActionDoParamBo();
        } else {
          actionDoParamBo = JSON.parseObject(actionParam, SendEmailActionDoParamBo.class);
        }
        break;
      case ROUTE:
        actionDoParamBo = new ActionDoParamBo();
        break;

      case SEND_WECHAT_MSG:
        if (jsonObject == null) {
          actionDoParamBo = new SendWechatMsgActionDoParamBo();
        } else {
          actionDoParamBo = JSON.parseObject(actionParam, SendWechatMsgActionDoParamBo.class);
        }
        break;
      default:
        throw new DataException(
            ErrorCode.ERROR_SYSTEM, EXCEPTION_INFO + actionBo.getActionTypeEnum().displayName);
    }
    actionDoParamBo.setActionCode(actionBo.getActionCode());
    actionBo.setActionDoParamBo(actionDoParamBo);
  }
}
