package com.sp.proxverse.engine.service;

import com.prx.service.BaseFileService;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.dict.FileTypeEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.CloudFileInfoPO;
import com.sp.proxverse.common.model.po.DataPoolFilePO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.util.ChineseUtils;
import com.sp.proxverse.common.util.DataSourceUtils;
import com.sp.proxverse.common.util.HadoopUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.impl.DataPoolFileServiceImpl;
import com.sp.proxverse.interfaces.dao.service.CloudFileInfoService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.catalyst.TableIdentifier;
import org.apache.spark.sql.catalyst.analysis.UnresolvedAttribute;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriUtils;
import scala.Option;
import scala.collection.Seq;

/**
 * <AUTHOR>
 * @date 2024/1/8 14:00
 */
@Service
public class BaseFileServiceImpl implements BaseFileService {

  @Autowired private FileService fileService;

  @Autowired private CloudFileInfoService cloudFileInfoService;

  @Autowired private DataPoolFileServiceImpl dataPoolFileService;

  private static final Logger logger = LoggerFactory.getLogger(BaseFileServiceImpl.class);

  @Override
  public void loadFileComplete(
      Integer fileId, FileTypeEnum fileTypeEnum, HttpServletResponse response) {
    if (fileTypeEnum == null) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_TYPE_CANNOT_BE_EMPTY));
    }
    switch (fileTypeEnum) {
      case ATTACHMENT:
      case PROCESS_DESCRIPTION:
        loadAttachmentFile(fileId, response);
        return;

      case PARQUET:
        loadParquetFile(fileId, response);
        return;
      default:
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_DOWNLOAD_FAILED));
    }
  }

  @Override
  public String getBase64EncodedImage(Integer fileId) {
    if (fileId == null) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_NOT_EXIST));
    }
    CloudFileInfoPO cloudFileInfoPO = cloudFileInfoService.getFileInfo(fileId);
    if (StringUtils.isNotBlank(cloudFileInfoPO.getFileUrl())) {
      return getBase64EncodedImage(cloudFileInfoPO.getFileUrl());
    }
    if (StringUtils.isNotBlank(cloudFileInfoPO.getFileCode())) {
      return cloudFileInfoPO.getFileCode();
    }

    throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_NOT_EXIST));
  }

  @Override
  public void loadDataSource(String tableName, HttpServletResponse response) {
    if (StringUtils.isBlank(tableName)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
    }
    Seq<String> tableNameSeq = UnresolvedAttribute.parseAttributeName(tableName);

    URI uri =
        SparkSessionEnv.getSparkSession()
            .sessionState()
            .catalog()
            .defaultTablePath(
                new TableIdentifier(tableNameSeq.last(), Option.apply(tableNameSeq.head())));
    handleFileDownload(response, uri.getPath());
  }

  private void loadAttachmentFile(Integer fileId, HttpServletResponse response) {
    CloudFileInfoPO cloudFileInfoPO = cloudFileInfoService.getFileInfo(fileId);

    if (Objects.equals(cloudFileInfoPO.getFileType(), FileTypeEnum.ATTACHMENT.name())
        || Objects.equals(cloudFileInfoPO.getFileType(), FileTypeEnum.PROCESS_DESCRIPTION.name())) {
      handleFileDownload(response, cloudFileInfoPO.getFileUrl());
      return;
    }
    throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_DOWNLOAD_FAILED));
  }

  public void loadParquetFile(Integer fileId, HttpServletResponse response) {
    FilePO filePO = fileService.getById(fileId);
    if (filePO == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.FILE_NOT_EXIST));
    }
    DataPoolFilePO dataPoolFilePO = dataPoolFileService.getOneByFileId(fileId);
    if (dataPoolFilePO == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.FILE_NOT_EXIST));
    }
    String sourceTableName;
    if (FileTypeEnum.TRANSFORMATION.getValue().equals(filePO.getType())) {
      sourceTableName =
          DataSourceUtils.makeFullTableName(
              filePO.getFilename(),
              DataSourceUtils.makeDatabaseName(dataPoolFilePO.getDataPoolId()));
    } else {
      sourceTableName =
          DataSourceUtils.makeFullTableName(
              DataSourceUtils.makeFileTableName(
                  ChineseUtils.transformChineseToPinyinIfNeed(filePO.getFilename()),
                  filePO.getId().toString()),
              DataSourceUtils.makeDatabaseName(dataPoolFilePO.getDataPoolId()));
    }
    Seq<String> name = UnresolvedAttribute.parseAttributeName(sourceTableName);

    URI uri =
        SparkSessionEnv.getSparkSession()
            .sessionState()
            .catalog()
            .defaultTablePath(new TableIdentifier(name.last(), Option.apply(name.head())));
    handleFileDownload(response, uri.getPath());
  }

  @Override
  public void handleFileDownload(HttpServletResponse response, String filePath) {
    FileSystem fs = HadoopUtil.getDefaultFileSystem();
    Path path = new Path(filePath);
    String pathName = path.getName();
    String encodedFileName = UriUtils.encode(pathName, StandardCharsets.UTF_8);

    try {
      if (!fs.exists(path)) {
        throw new BizException(
            5000, I18nUtil.getMessage(I18nConst.FILE_NOT_EXIST) + ": " + pathName);
      }
      FileStatus fileStatus = fs.getFileStatus(path);

      if (fileStatus.isDirectory()) {
        sendResponseDataZip(response, path, encodedFileName);
      } else {
        sendResponseDataFile(response, path, encodedFileName);
      }
    } catch (IOException e) {
      logger.info("handleFileDownload error:", e);
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.FILE_FORMAT_INCORRECT));
    }
  }

  @Override
  public String getBase64EncodedImage(String filePath) {
    FileSystem fs = HadoopUtil.getDefaultFileSystem();
    Path path = new Path(filePath);
    try (BufferedInputStream bis = new BufferedInputStream(fs.open(path));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      if (!fs.exists(path)) {
        throw new BizException(
            5000, I18nUtil.getMessage(I18nConst.FILE_NOT_EXIST) + ": " + path.getName());
      }

      byte[] buffer = new byte[4096];
      int bytesRead;
      while ((bytesRead = bis.read(buffer)) != -1) {
        outputStream.write(buffer, 0, bytesRead);
      }
      byte[] fileBytes = outputStream.toByteArray();
      return "data:image;base64," + Base64.getEncoder().encodeToString(fileBytes);
    } catch (IOException e) {
      logger.info("Failed to convert file to Base64: ", e);
      throw new BizException(5000, "Failed to convert file to Base64: " + e.getMessage());
    }
  }

  private void sendResponseDataZip(
      HttpServletResponse httpServletResponse, Path folder, String encodedFileName)
      throws IOException {
    FileSystem fs = HadoopUtil.getDefaultFileSystem();
    RemoteIterator<LocatedFileStatus> fileIterator = fs.listFiles(folder, false);
    if (!fileIterator.hasNext()) {
      throw new BizException(
          5000,
          I18nUtil.getMessage(I18nConst.FILE_DOES_NOT_EXIST_IN_DIRECTORY)
              + ": "
              + folder.getName());
    }
    ServletOutputStream outputStream = httpServletResponse.getOutputStream();
    try (ZipOutputStream zippedOut = new ZipOutputStream(outputStream)) {
      encodedFileName = encodedFileName + ".zip";
      httpServletResponse.setContentType("application/octet-stream");
      httpServletResponse.setHeader(
          HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodedFileName);

      while (fileIterator.hasNext()) {
        LocatedFileStatus fileStatus = fileIterator.next();
        org.apache.hadoop.fs.Path filePath = fileStatus.getPath();
        if (fs.isFile(filePath)) {
          try (BufferedInputStream bis = new BufferedInputStream(fs.open(filePath))) {
            ZipEntry zipEntry = new ZipEntry(filePath.getName());
            zippedOut.putNextEntry(zipEntry);
            byte[] bytes = new byte[4096];
            int length;
            while ((length = bis.read(bytes)) >= 0) {
              zippedOut.write(bytes, 0, length);
            }
          } finally {
            zippedOut.closeEntry();
          }
        }
      }
    } catch (IOException e) {
      logger.info("sendResponseDataZip error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  private void sendResponseDataFile(
      HttpServletResponse httpServletResponse, Path filePath, String encodedFileName)
      throws IOException {
    ServletOutputStream outputStream = httpServletResponse.getOutputStream();
    FileSystem fs = HadoopUtil.getDefaultFileSystem();
    try (BufferedInputStream bis = new BufferedInputStream(fs.open(filePath))) {
      httpServletResponse.setContentType("application/octet-stream");
      httpServletResponse.setHeader(
          HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodedFileName);
      byte[] bytes = new byte[4096];
      int length;
      while ((length = bis.read(bytes)) >= 0) {
        outputStream.write(bytes, 0, length);
      }
    } catch (IOException e) {
      logger.info("sendResponseDataFile error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }
}
