package com.sp.proxverse.engine.service.function;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.sp.proxverse.common.action.FunctionService;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2022-06-06 10:40 上午
 */
@Service
public class NotEqualFuncServiceImpl implements FunctionService {
  @Override
  public Boolean doFunction(List<Object> paramObj) {
    if (paramObj.get(0) instanceof String && paramObj.get(1) instanceof String) {
      // String
      return !ObjectUtil.equal((String) paramObj.get(0), (String) paramObj.get(1));

    } else if (paramObj.get(0) instanceof DateTime && paramObj.get(1) instanceof DateTime) {
      // 时间
      DateTime dateTime = (DateTime) paramObj.get(0);
      DateTime dateTimeEnd = (DateTime) paramObj.get(1);
      return dateTimeEnd.getTime() != dateTime.getTime();

    } else if (paramObj.get(0) instanceof Double && paramObj.get(1) instanceof Double) {
      // 数字
      Double aDouble = (Double) paramObj.get(0);
      Double aDoubleEnd = (Double) paramObj.get(1);
      return !ObjectUtil.equal(paramObj.get(0).toString(), paramObj.get(1).toString());

    } else {
      if (StringUtils.isBlank(paramObj.get(0).toString())
          || StringUtils.isBlank(paramObj.get(1).toString())) {
        return false;
      }
      // 其他
      return !ObjectUtil.equal(paramObj.get(0).toString(), paramObj.get(1).toString());
    }
  }
}
