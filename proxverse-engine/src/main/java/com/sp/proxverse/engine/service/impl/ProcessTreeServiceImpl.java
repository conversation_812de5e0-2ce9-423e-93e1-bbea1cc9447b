package com.sp.proxverse.engine.service.impl;

import static com.sp.proxverse.common.model.dict.TopicFilterTypeEnum.CROP_SELECTION;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.prx.service.conformance.ConformanceServiceScala;
import com.prx.service.page.ITopicMultiProcessService;
import com.sp.proxverse.cache.ProcessExplorerCache;
import com.sp.proxverse.cache.ProcessExplorerCacheKey;
import com.sp.proxverse.common.common.LocalParameter;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.CustomException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.bo.tree.ProcessExplorerInitParam;
import com.sp.proxverse.common.model.bo.tree.ProcessInitBase;
import com.sp.proxverse.common.model.bo.tree.ProcessInitParam;
import com.sp.proxverse.common.model.bo.tree.StartAndEndNodes;
import com.sp.proxverse.common.model.bo.tree.TreeInfoPqlBo;
import com.sp.proxverse.common.model.bo.tree.TreeLineBo;
import com.sp.proxverse.common.model.bo.tree.TreeVariantBo;
import com.sp.proxverse.common.model.dict.DataSourceTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.KpiSaveTypeEnum;
import com.sp.proxverse.common.model.dict.TopicFilterTypeEnum;
import com.sp.proxverse.common.model.dict.prehandler.PreHandlerVariableEnum;
import com.sp.proxverse.common.model.dto.VariantResultDTO;
import com.sp.proxverse.common.model.dto.process.ProcessTreeChartOut;
import com.sp.proxverse.common.model.dto.process.ProcessTreeChartRequest;
import com.sp.proxverse.common.model.dto.process.ProcessTreeVariantOut;
import com.sp.proxverse.common.model.dto.process.ProcessTreeVariantRequest;
import com.sp.proxverse.common.model.dto.process.VariantCalculateDTO;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.enums.ProcessChartValueTypeEnum;
import com.sp.proxverse.common.model.enums.ProcessKpiTypeEnum;
import com.sp.proxverse.common.model.enums.ProcessPathKpiType;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.BusinessTopicDataPO;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.po.ProcessTreeKpiPo;
import com.sp.proxverse.common.model.po.ProcessTreeKpiRelationPo;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.base.DeleteReqVo;
import com.sp.proxverse.common.model.vo.processExplorer.EventNode;
import com.sp.proxverse.common.model.vo.processExplorer.LineNode;
import com.sp.proxverse.common.model.vo.processExplorer.ProcessExplorerRes;
import com.sp.proxverse.common.model.vo.processai.TopicProcessDetailsKpi;
import com.sp.proxverse.common.model.vo.processai.TopicProcessKpiResVo;
import com.sp.proxverse.common.model.vo.processai.request.CalculationSymbol;
import com.sp.proxverse.common.model.vo.processai.request.ColourInfo;
import com.sp.proxverse.common.model.vo.processai.request.TopicProcessDetailsReqVo;
import com.sp.proxverse.common.model.vo.processai.request.UpdateTopicProcessDetailsKpiReqVo;
import com.sp.proxverse.common.model.vo.processai.request.UpdateTopicProcessKpiReqVo;
import com.sp.proxverse.common.model.vo.request.ParseKpiDTO;
import com.sp.proxverse.common.model.vo.request.ProcessExplorerRequest;
import com.sp.proxverse.common.model.vo.request.ProcessTreeBase;
import com.sp.proxverse.common.model.vo.request.ProcessTreeMultiLevel;
import com.sp.proxverse.common.util.CalculationUtil;
import com.sp.proxverse.common.util.ExpressionBuilderUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.common.util.ProcessCrop;
import com.sp.proxverse.common.util.ProcessTreeExprGenerator;
import com.sp.proxverse.data.service.pql.PQLCatalogManager;
import com.sp.proxverse.data.service.pql.TopicFilterManagerImpl;
import com.sp.proxverse.engine.service.ProcessTreeService;
import com.sp.proxverse.engine.service.biz.ProcessService;
import com.sp.proxverse.engine.service.impl.processTree.ProcessInitChartParam;
import com.sp.proxverse.engine.util.PQLResultProcess;
import com.sp.proxverse.interfaces.dao.impl.ProcessTreeKpiRelationServiceImpl;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicKpiService;
import com.sp.proxverse.interfaces.dao.service.KpiService;
import com.sp.proxverse.interfaces.dao.service.ProcessTreeKpiServiceImpl;
import com.sp.proxverse.interfaces.dao.service.TopicFilterService;
import com.sp.proxverse.interfaces.service.data.DataAPIService;
import com.sp.proxverse.interfaces.service.data.FilterService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.spark.cache.CustomCacheManage;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.PQLConf;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.RowFactory;
import org.apache.spark.sql.catalyst.analysis.UnresolvedAttribute;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.pql.ModelCatalog;
import org.apache.spark.sql.pql.PQLBuilder;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.apache.spark.sql.pql.model.PQLContext;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import process.ProcessTreeAnalyze;
import process.ProcessTreePQL;
import scala.Tuple2;

/**
 * <AUTHOR>
 * @create 2022-11-18 14:23
 */
@Slf4j
@Service
public class ProcessTreeServiceImpl implements ProcessTreeService {

  @Qualifier("businessTopicKpiService")
  @Autowired
  private BusinessTopicKpiService businessTopicKpiService;

  @Autowired private ProcessService processService;
  private static final String ACTIVITY_COLUMN = "ACTIVITY_COLUMN()";
  private static final String NOT_EQ_EMPTY = " != '' ";
  private static final String VARIANT_NAME = "variantName";
  private static final String COUNT = "count(ENCODED_CASE_CASE_ID_COLUMN())";
  private static final String VARIANT_COUNT = "variantCount";
  private static final String CASE_DURATION = "caseDuration";

  @Data
  public class TreeVariantInfo {

    private List<TreeVariantBo> treeVariantBos = new ArrayList<>();

    private Integer variantNumber;

    private Long otherNumber;
  }

  @Autowired public ProcessTreeKpiServiceImpl processTreeKpiService;
  @Autowired private BusinessTopicDataService businessTopicDataService;
  @Autowired public ProcessTreeKpiRelationServiceImpl processTreeKpiRelationService;
  @Autowired public TopicFilterManagerImpl topicFilterManager;
  @Autowired public PQLService pqlService;
  @Autowired public PQLCatalogManager pqlCatalogManager;
  @Autowired public TopicFilterService topicFilterService;
  @Autowired public KpiService kpiService;
  @Autowired private DataAPIService dataAPIService;
  @Autowired private ConformanceServiceScala conformanceServiceScala;
  @Autowired private ITopicMultiProcessService topicMultiProcessService;

  @Value("${prx.workbench.processTree.maxQueryEvents:500}")
  private Integer processEventNumberLimit;

  @Value("${prx.workbench.processTree.maxQueryConnects:500}")
  private Integer processConnectNumberLimit;

  @Autowired private FilterService filterService;

  /**
   * 初始化基础流程图参数
   *
   * @param request
   * @param processInitBase
   */
  private void initProcessParamBase(ProcessTreeBase request, ProcessInitBase processInitBase) {
    ModelDesc orCreate = pqlCatalogManager.createCatalog(request.getTopicId()).modelDesc();
    processInitBase.setShortened(request.getShortened());
    processInitBase.setTimeType(request.getTimeType());
    processInitBase.setIgnoreTopicProcessCropFilter(request.getIgnoreTopicProcessCropFilter());
    processInitBase.setDefaultChartFlag(request.getDefaultChartFlag());
    processInitBase.setIsReversedFilterEvents(request.getIsReversedFilterEvents());
    processInitBase.setFilterEvents(request.getFilterEvents());
    processInitBase.setModelId(orCreate.modelId());

    String activeEventColumn;
    String caseTableCaseColumn;
    String activeTimeColumn = orCreate.activeTableTimeColumn();
    String currentEventColumn = ACTIVITY_COLUMN;
    if (StringUtils.isBlank(request.getColumnName())) {
      activeEventColumn = orCreate.activeTableEventColumn();
    } else {
      currentEventColumn = request.getColumnName();
      activeEventColumn = dataAPIService.formatRefer(request.getTopicId(), request.getColumnName());
    }
    processInitBase.setInitActiveEventColumn(activeEventColumn);

    activeEventColumn =
        ExpressionBuilderUtil.getActiveCheckGroup(activeEventColumn, request.getGroupInfo());

    if (orCreate.caseTable() == null) {
      caseTableCaseColumn = PreHandlerVariableEnum.CASE_ID.getValue();
    } else {
      caseTableCaseColumn = orCreate.caseTableEncodedColumn();
    }

    processInitBase.setEventTableName(
        orCreate.lookupSparkTableName(orCreate.activeOriginTableName()));
    processInitBase.setEventColumnName(orCreate.activeTable().eventColumn());
    processInitBase.setTopicId(request.getTopicId());
    processInitBase.setSheetId(request.getSheetId());
    processInitBase.setActiveEventColumn(activeEventColumn);
    processInitBase.setCaseTableCaseColumn(caseTableCaseColumn);
    processInitBase.setActiveTimeColumn(activeTimeColumn);
    processInitBase.setCurrentEventColumn(currentEventColumn);
    processInitBase.setActiveCaseColumn(orCreate.activeTableEncodedCaseColumn());
    processInitBase.setGroupInfo(request.getGroupInfo());
    processInitBase.setDefaultEventColumn(ACTIVITY_COLUMN);

    // 流程图 remap
    processInitBase.setActiveEventColumn(activeEventColumn);
    processInitBase.setGroupFlag(ExpressionBuilderUtil.checkGroup(request.getGroupInfo()));

    if (CollectionUtils.isNotEmpty(request.getAddFilterList())) {
      for (TopicFilterPO topicFilterPO : request.getAddFilterList()) {
        if (!processInitBase.getCustomFilterPOS().contains(topicFilterPO)) {
          processInitBase.getCustomFilterPOS().add(topicFilterPO);
        }
      }
    }
    // 防止LocalParameter.disableFilterThreadLocal为空时报错
    Boolean disableFilter = false;
    if (Objects.nonNull(LocalParameter.disableFilterThreadLocal.get())) {
      disableFilter = LocalParameter.disableFilterThreadLocal.get();
    }
    // 组件过滤，指挥舱固定页面过滤通过disableFilter判断获取componentFilter
    if (request.getComponentId() != null || disableFilter) {
      List<String> componentFilterThreadLocalList = LocalParameter.componentFilterThreadLocal.get();
      List<TopicFilterPO> customFilterPOS = processInitBase.getCustomFilterPOS();
      if (componentFilterThreadLocalList != null && !componentFilterThreadLocalList.isEmpty()) {
        for (String componentFilterThreadLocal : componentFilterThreadLocalList) {
          TopicFilterPO topicFilterPO = new TopicFilterPO();
          topicFilterPO.setType(TopicFilterTypeEnum.EXPRESSION.getValue());
          topicFilterPO.setExpression(componentFilterThreadLocal);
          customFilterPOS.add(topicFilterPO);
        }
      }
    }

    ProcessCrop processCrop = processCropInit(processInitBase, request, activeEventColumn);
    processInitBase.setProcessCrop(processCrop);
    processCropInit(processInitBase, request);
    List<String> filterList =
        this.topicFilterManager.processTopicFilter(orCreate, processInitBase.getCustomFilterPOS());
    Integer topicId = request.getTopicId();
    ProcessTreeMultiLevel processTreeMultiLevel = request.getProcessTreeMultiLevel();

    String filterExpression =
        topicMultiProcessService.switchFilterExpression(topicId, processTreeMultiLevel);
    if (StringUtils.isNotBlank(filterExpression)) {
      filterList.add(filterExpression);
    }
    processInitBase.setCustomFilters(filterList);
    if (StringUtils.isNotBlank(request.getReason())) {
      processInitBase
          .getCustomFilters()
          .add(
              conformanceServiceScala.addReasonTopicFilter(
                  request.getTopicId(),
                  request.getSheetId(),
                  request.getReason(),
                  request.getReasonType()));
    }

    String switchEventColumnPql =
        topicMultiProcessService.switchEventColumn(
            request.getTopicId(), request.getProcessTreeMultiLevel());
    if (StringUtils.isNotBlank(switchEventColumnPql)) {
      processInitBase.setSwitchEventColumnPql(switchEventColumnPql);
    }

    Tuple2<String, String> startEndTimeColumn =
        topicMultiProcessService.getStartEndTimeColumn(
            request.getTopicId(), request.getProcessTreeMultiLevel());
    if (Objects.nonNull(startEndTimeColumn)) {
      processInitBase.setIndexStartTimeColumn(startEndTimeColumn._1());
      processInitBase.setIndexEndTimeColumn(startEndTimeColumn._2());
    }
  }

  @Override
  public ProcessCrop getProcessCrop(Integer topicId) {
    List<TopicFilterPO> topicFilters =
        topicFilterService.getFilterWithType(
            topicId, null, null, TopicFilterTypeEnum.CROP_SELECTION.getValue(), null);
    if (topicFilters.size() == 0) {
      return null;
    }
    return this.getProcessCrop(topicFilters.get(0));
  }

  @Override
  public ProcessCrop getProcessCrop(TopicFilterPO topicFilterPO) {
    ProcessCrop processCrop =
        new ProcessCrop(
            topicFilterPO.getParam(),
            topicFilterPO.getParamType() == 10 ? "FIRST_OCCURRENCE" : "LAST_OCCURRENCE",
            topicFilterPO.getParam2(),
            topicFilterPO.getParamType2() == 10 ? "FIRST_OCCURRENCE" : "LAST_OCCURRENCE");
    return processCrop;
  }

  @Override
  public List<TopicProcessKpiResVo> getTopicProcessKpiList(Integer topicId) {

    List<ProcessTreeKpiPo> processTreeKpiPos =
        processTreeKpiService.list(
            new LambdaQueryWrapper<ProcessTreeKpiPo>()
                .eq(ProcessTreeKpiPo::getTopicId, topicId)
                .eq(ProcessTreeKpiPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(ProcessTreeKpiPo::getId));

    if (processTreeKpiPos.isEmpty()) {
      return new ArrayList<>();
    }
    List<Integer> collect =
        processTreeKpiPos.stream().map(ProcessTreeKpiPo::getId).collect(Collectors.toList());

    List<ProcessTreeKpiRelationPo> processTreeKpiRelationPos =
        processTreeKpiRelationService.list(
            new LambdaQueryWrapper<ProcessTreeKpiRelationPo>()
                .in(ProcessTreeKpiRelationPo::getProcessTreeKpiId, collect)
                .eq(ProcessTreeKpiRelationPo::getType, 2)
                .eq(ProcessTreeKpiRelationPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    Map<Integer, List<ProcessTreeKpiRelationPo>> pathKpi =
        processTreeKpiRelationPos.stream()
            .collect(Collectors.groupingBy(ProcessTreeKpiRelationPo::getProcessTreeKpiId));

    List<TopicProcessKpiResVo> results = new ArrayList<>();

    for (ProcessTreeKpiPo processTreeKpiPo : processTreeKpiPos) {
      TopicProcessKpiResVo result = new TopicProcessKpiResVo();
      if (pathKpi.containsKey(processTreeKpiPo.getId())) {
        List<ProcessTreeKpiRelationPo> processTreeKpiRelations =
            pathKpi.get(processTreeKpiPo.getId());
        List<String> kipInfo =
            processTreeKpiRelations.stream()
                .map(ProcessTreeKpiRelationPo::getName)
                .collect(Collectors.toList());
        result.setPathKpi(kipInfo);
      }
      result.setName(processTreeKpiPo.getName());
      result.setIcon(processTreeKpiPo.getIcon());
      result.setProcessKpiId(processTreeKpiPo.getId());
      results.add(result);
    }
    return results;
  }

  @Override
  public List<TopicProcessKpiResVo> getAllTopicProcessKpiIsExist(Integer topicId) {
    List<TopicProcessKpiResVo> topicProcessKpiList =
        this.getTopicProcessKpiList(topicId).stream()
            .peek(res -> res.setKpiSaveTypeEnum(KpiSaveTypeEnum.CUSTOMIZE))
            .collect(Collectors.toList());

    BusinessTopicDataPO knownleadeModel =
        businessTopicDataService.getOne(
            new LambdaQueryWrapper<BusinessTopicDataPO>()
                .eq(BusinessTopicDataPO::getTopicId, topicId)
                .eq(
                    BusinessTopicDataPO::getDataSourceType,
                    DataSourceTypeEnum.KNOWNLEADE_MODEL.getValue())
                .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (knownleadeModel != null) {
      List<TopicProcessKpiResVo> knownleadeModelProcessKpi =
          this.getTopicProcessKpiList(knownleadeModel.getModelId()).stream()
              .map(
                  res -> {
                    res.setKpiSaveTypeEnum(KpiSaveTypeEnum.KNOWLEDGE_MODEL);
                    return res;
                  })
              .collect(Collectors.toList());
      topicProcessKpiList.addAll(0, knownleadeModelProcessKpi);
    }
    return topicProcessKpiList;
  }

  @Override
  public TopicProcessDetailsReqVo getTopicProcessDetails(Integer treeKpiId) {
    TopicProcessDetailsReqVo result = new TopicProcessDetailsReqVo();

    ProcessTreeKpiPo processTreeKpiPo = processTreeKpiService.getById(treeKpiId);
    result.setProcessKpiId(processTreeKpiPo.getId());
    result.setName(processTreeKpiPo.getName());
    result.setIcon(processTreeKpiPo.getIcon());
    result.setEventColourInfo(processTreeKpiPo.getEventColour());
    result.setLineColourInfo(processTreeKpiPo.getLineColour());

    List<ProcessTreeKpiRelationPo> processTreeKpiRelationPos =
        processTreeKpiRelationService.list(
            new LambdaQueryWrapper<ProcessTreeKpiRelationPo>()
                .eq(ProcessTreeKpiRelationPo::getProcessTreeKpiId, treeKpiId)
                .eq(ProcessTreeKpiRelationPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isEmpty(processTreeKpiRelationPos)) {
      result.setBindEventKpiName("");
      result.setBindLineKpiName("");
      return result;
    }

    List<Integer> kpiIds =
        processTreeKpiRelationPos.stream()
            .map(ProcessTreeKpiRelationPo::getKpiId)
            .filter(kpiId -> kpiId != null)
            .collect(Collectors.toList());

    List<KpiPO> kpiPOS = kpiService.listByIds(kpiIds);
    Map<Integer, KpiPO> kpiPOMap =
        kpiPOS.stream().collect(Collectors.toMap(KpiPO::getId, po -> po));

    for (ProcessTreeKpiRelationPo processTreeKpiRelationPo : processTreeKpiRelationPos) {
      TopicProcessDetailsKpi topicProcessDetailsKpi = new TopicProcessDetailsKpi();
      ProcessKpiTypeEnum processKpiTypeEnumByCode =
          ProcessKpiTypeEnum.getProcessKpiTypeEnumByCode(processTreeKpiRelationPo.getType());
      if (processKpiTypeEnumByCode == null) {
        continue;
      }
      switch (processKpiTypeEnumByCode) {
        case EVENT:
          result.getEventKpiInfos().add(topicProcessDetailsKpi);
          break;

        case LINE:
          result.getLineKpiInfos().add(topicProcessDetailsKpi);
          break;
        case PATH:
          result.getPathKpiInfos().add(topicProcessDetailsKpi);
      }

      KpiPO kpiPO = kpiPOMap.get(processTreeKpiRelationPo.getKpiId());

      topicProcessDetailsKpi.setName(processTreeKpiRelationPo.getName());
      topicProcessDetailsKpi.setExpression(kpiPO.getExpression());
      topicProcessDetailsKpi.setKpiId(kpiPO.getId());
      topicProcessDetailsKpi.setProcessTreeKpiRelationId(processTreeKpiRelationPo.getId());
      topicProcessDetailsKpi.setUnit(kpiPO.getUnit());
      topicProcessDetailsKpi.setFormatting(kpiPO.getFormatting());
      topicProcessDetailsKpi.setFormat(kpiPO.getFormat());
    }
    if (processTreeKpiPo.getBondColourEventKpiId() != null
        && kpiPOMap.containsKey(processTreeKpiPo.getBondColourEventKpiId())) {
      result.setBindEventKpiName(
          kpiPOMap.get(processTreeKpiPo.getBondColourEventKpiId()).getName());
    }

    if (processTreeKpiPo.getBondColourLineKpiId() != null
        && kpiPOMap.containsKey(processTreeKpiPo.getBondColourLineKpiId())) {
      result.setBindLineKpiName(kpiPOMap.get(processTreeKpiPo.getBondColourLineKpiId()).getName());
    }
    return result;
  }

  @Override
  public Integer updateTopicProcessKpi(UpdateTopicProcessKpiReqVo updateTopicProcessKpiReqVo) {
    this.checkProcessKpiNameDuplication(
        updateTopicProcessKpiReqVo.getId(),
        updateTopicProcessKpiReqVo.getTopicId(),
        updateTopicProcessKpiReqVo.getName());
    ProcessTreeKpiPo processTreeKpiPo = new ProcessTreeKpiPo();
    processTreeKpiPo.setName(updateTopicProcessKpiReqVo.getName());
    processTreeKpiPo.setTopicId(updateTopicProcessKpiReqVo.getTopicId());
    processTreeKpiPo.setIcon(updateTopicProcessKpiReqVo.getIcon());
    processTreeKpiPo.setEventColour(
        JSON.toJSONString(updateTopicProcessKpiReqVo.getEventColourInfo()));
    processTreeKpiPo.setLineColour(
        JSON.toJSONString(updateTopicProcessKpiReqVo.getLineColourInfo()));
    if (updateTopicProcessKpiReqVo.getId() == null) {
      processTreeKpiService.save(processTreeKpiPo);
    } else {
      processTreeKpiPo.setId(updateTopicProcessKpiReqVo.getId());
      processTreeKpiService.updateById(processTreeKpiPo);
    }
    // 保存孩子KPI信息
    if (CollectionUtils.isNotEmpty(
        updateTopicProcessKpiReqVo.getUpdateTopicProcessDetailsKpiReqVos())) {
      for (UpdateTopicProcessDetailsKpiReqVo updateTopicProcessDetailsKpiReqVo :
          updateTopicProcessKpiReqVo.getUpdateTopicProcessDetailsKpiReqVos()) {
        updateTopicProcessDetailsKpiReqVo.setProcessTreeKpiId(processTreeKpiPo.getId());
        this.updateTopicProcessDetailKpi(updateTopicProcessDetailsKpiReqVo);
      }
    }
    List<String> bondKpiNames = new ArrayList<>();
    if (StringUtils.isNotEmpty(updateTopicProcessKpiReqVo.getEventColourKpiName())) {
      bondKpiNames.add(updateTopicProcessKpiReqVo.getEventColourKpiName());
    }
    if (StringUtils.isNotEmpty(updateTopicProcessKpiReqVo.getLineColourKpiName())) {
      bondKpiNames.add(updateTopicProcessKpiReqVo.getLineColourKpiName());
    }
    if (bondKpiNames.size() > 0) {
      List<ProcessTreeKpiRelationPo> processTreeKpiRelationPos =
          processTreeKpiRelationService.list(
              new LambdaQueryWrapper<ProcessTreeKpiRelationPo>()
                  .in(ProcessTreeKpiRelationPo::getName, bondKpiNames)
                  .eq(ProcessTreeKpiRelationPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                  .eq(ProcessTreeKpiRelationPo::getProcessTreeKpiId, processTreeKpiPo.getId()));
      for (ProcessTreeKpiRelationPo processTreeKpiRelationPo : processTreeKpiRelationPos) {
        if (processTreeKpiRelationPo.getType() == 0) {
          processTreeKpiPo.setBondColourEventKpiId(processTreeKpiRelationPo.getKpiId());
        } else {
          processTreeKpiPo.setBondColourLineKpiId(processTreeKpiRelationPo.getKpiId());
        }
      }
    }
    processTreeKpiService.updateById(processTreeKpiPo);
    if (CollectionUtils.isNotEmpty(updateTopicProcessKpiReqVo.getRemoveKpiByKpiId())) {
      for (Integer integer : updateTopicProcessKpiReqVo.getRemoveKpiByKpiId()) {
        DeleteReqVo deleteReqVo = new DeleteReqVo();
        deleteReqVo.setId(integer);
        this.deleteProcessTreeDetailsKpi(deleteReqVo);
      }
    }
    return processTreeKpiPo.getId();
  }

  private synchronized void checkProcessKpiNameDuplication(
      Integer id, Integer topicId, String name) {
    long count =
        processTreeKpiService.count(
            new LambdaQueryWrapper<ProcessTreeKpiPo>()
                .eq(ProcessTreeKpiPo::getTopicId, topicId)
                .eq(ProcessTreeKpiPo::getName, name)
                .ne(id != null, ProcessTreeKpiPo::getId, id)
                .eq(ProcessTreeKpiPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (count > 0) {
      throw new UnsupportedOperationException(I18nUtil.getMessage(I18nConst.KPI_HAS_EXIST));
    }
  }

  @Override
  public Integer updateTopicProcessDetailKpi(
      UpdateTopicProcessDetailsKpiReqVo updateTopicProcessDetailsKpiReqVo) {
    KpiPO kpiPO = new KpiPO();
    kpiPO.setExpression(updateTopicProcessDetailsKpiReqVo.getExpression());
    kpiPO.setName(updateTopicProcessDetailsKpiReqVo.getKpiName());
    kpiPO.setUnit(updateTopicProcessDetailsKpiReqVo.getUnit());
    kpiPO.setFormatting(updateTopicProcessDetailsKpiReqVo.getFormatting());
    kpiPO.setFormat(updateTopicProcessDetailsKpiReqVo.getFormat());

    ProcessTreeKpiRelationPo processTreeKpiRelationPo = new ProcessTreeKpiRelationPo();
    processTreeKpiRelationPo.setProcessTreeKpiId(
        updateTopicProcessDetailsKpiReqVo.getProcessTreeKpiId());
    processTreeKpiRelationPo.setName(updateTopicProcessDetailsKpiReqVo.getKpiName());
    processTreeKpiRelationPo.setType(updateTopicProcessDetailsKpiReqVo.getType());

    if (updateTopicProcessDetailsKpiReqVo.getKpiId() == null
        || updateTopicProcessDetailsKpiReqVo.getProcessTreeKpiRelationId() == null) {
      kpiService.save(kpiPO);
      processTreeKpiRelationPo.setKpiId(kpiPO.getId());
      processTreeKpiRelationService.save(processTreeKpiRelationPo);
    } else {
      kpiPO.setId(updateTopicProcessDetailsKpiReqVo.getKpiId());
      kpiService.updateById(kpiPO);
      processTreeKpiRelationPo.setId(
          updateTopicProcessDetailsKpiReqVo.getProcessTreeKpiRelationId());
      processTreeKpiRelationService.updateById(processTreeKpiRelationPo);
    }
    return kpiPO.getId();
  }

  @Override
  public Boolean deleteProcessTreeKpi(DeleteReqVo deleteReqVo) {
    return processTreeKpiService.update(
        new LambdaUpdateWrapper<ProcessTreeKpiPo>()
            .eq(ProcessTreeKpiPo::getId, deleteReqVo.getId())
            .set(ProcessTreeKpiPo::getDeleted, DeletedEnum.HAS_DELETED.getValue()));
  }

  @Override
  public Boolean deleteProcessTreeDetailsKpi(DeleteReqVo deleteReqVo) {
    kpiService.update(
        new LambdaUpdateWrapper<KpiPO>()
            .eq(KpiPO::getId, deleteReqVo.getId())
            .set(KpiPO::getDeleted, DeletedEnum.HAS_DELETED.getValue()));
    return processTreeKpiRelationService.update(
        new LambdaUpdateWrapper<ProcessTreeKpiRelationPo>()
            .eq(ProcessTreeKpiRelationPo::getKpiId, deleteReqVo.getId())
            .set(ProcessTreeKpiRelationPo::getDeleted, DeletedEnum.HAS_DELETED.getValue()));
  }

  @Override
  public ProcessCrop getCropFilter(Integer topicId, List<TopicFilterPO> addFilters) {
    ProcessCrop processCrop = null;

    if (addFilters != null
        && addFilters.stream()
                .filter(po -> Objects.equals(po.getType(), CROP_SELECTION.getValue()))
                .count()
            > 0) {
      processCrop =
          this.getProcessCrop(
              addFilters.stream()
                  .filter(po -> Objects.equals(po.getType(), CROP_SELECTION.getValue()))
                  .collect(Collectors.toList())
                  .get(0));
    } else {
      processCrop = this.getProcessCrop(topicId);
    }
    return processCrop;
  }

  @Override
  public ProcessExplorerRes processExplorer(ProcessExplorerRequest request) {
    ProcessExplorerInitParam processExplorerInitParam = this.initProcessExplorerParam(request);
    try {
      ProcessExplorerRes process;
      ProcessTreeAnalyze processExplorer =
          new ProcessTreeAnalyze(
              processExplorerInitParam,
              this,
              processExplorerInitParam.getEventKpis(),
              processExplorerInitParam.getLineKpis());
      if (CustomCacheManage.cacheEnabled("ProcessExplorerCache")) {
        Set<String> filters;
        // 防止LocalParameter.disableFilterThreadLocal为空时报错
        Boolean disableFilter = false;
        if (Objects.nonNull(LocalParameter.disableFilterThreadLocal.get())) {
          disableFilter = LocalParameter.disableFilterThreadLocal.get();
        }
        // 指挥舱根据disableFilter判断从哪里获取PQL
        if (!disableFilter) {
          filters = new HashSet<>(topicFilterManager.getAllTopicFilters(request.getTopicId()));
        } else {
          filters = new HashSet<>(LocalParameter.componentFilterThreadLocal.get());
        }
        filterService.getSheetFilter(request.getSheetId()).ifPresent(filters::add);
        filterService
            .getRestrictionFilterForCurrentUser(request.getTopicId())
            .ifPresent(filters::add);
        if (CollectionUtils.isNotEmpty(processExplorerInitParam.getCustomFilters())) {
          filters.addAll(processExplorerInitParam.getCustomFilters());
        }
        if (Objects.nonNull(request.getValueShowType())) {
          filters.add(request.getValueShowType().toString());
        }
        if (Objects.nonNull(request.getTimeType())) {
          filters.add(request.getTimeType().toString());
        }

        ProcessExplorerCacheKey processExplorerCacheKey =
            new ProcessExplorerCacheKey(
                processExplorerInitParam.getModelId(),
                processExplorerInitParam.generateHashCode(filters));
        ProcessExplorerCache processExplorerCache =
            (ProcessExplorerCache) CustomCacheManage.getCustomCache("ProcessExplorerCache");
        process =
            processExplorerCache.getCacheExecution(
                processExplorerCacheKey, processExplorer::getProcess);
      } else {
        process = processExplorer.getProcess();
      }
      process.setEventNumberLimit(this.processEventNumberLimit);
      process.setLineNumberLimit(this.processConnectNumberLimit);
      process.setEventKpis(processExplorerInitParam.getEventKpis());
      process.setLineKpis(processExplorerInitParam.getLineKpis());
      if (process.getEventKpis() != null) {
        process.getEventKpis().stream()
            .filter(kpiPO -> kpiPO.getColumnType() != null)
            .forEach(kpiService::updateById);
        List<KpiPO> eventKpiLst =
            process.getEventKpis().stream()
                .map(eventKpi -> kpiService.getById(eventKpi.getId()))
                .collect(Collectors.toList());
        process.setEventKpis(eventKpiLst);
      }
      if (process.getLineKpis() != null) {
        process.getLineKpis().stream()
            .filter(kpiPO -> kpiPO.getColumnType() != null)
            .forEach(kpiService::updateById);
        List<KpiPO> lineKpiLst =
            process.getLineKpis().stream()
                .map(lineKpi -> kpiService.getById(lineKpi.getId()))
                .collect(Collectors.toList());
        process.setLineKpis(lineKpiLst);
      }
      return process;
    } catch (Exception e) {
      log.error("processTree error=>", e);
      ProcessExplorerRes process = new ProcessExplorerRes();
      process.setErrorMsg(e.getMessage());
      return process;
    }
  }

  @Override
  public ProcessTreeVariantOut processTreeVariant(ProcessTreeVariantRequest request) {
    ProcessTreeVariantOut processTreeVariantOut = new ProcessTreeVariantOut();
    try {
      // init param
      ProcessInitParam processInitParam = this.initProcessVariantParam(request);
      if (processInitParam.isDefaultChartFlag()) {
        processTreeVariantOut.setVariantList(getDefaultProcessChartVariant(processInitParam));
      } else {
        TreeVariantInfo treeVariantInfo = getTreeVariantList(processInitParam);
        processTreeVariantOut.setVariantList(getVariantList(treeVariantInfo.getTreeVariantBos()));
        processTreeVariantOut.setVariantCalculate(
            buildVariantCalculate(processInitParam, treeVariantInfo));
      }

      processTreeVariantOut.setPathKpis(processInitParam.getPathKpiList());
      processTreeVariantOut.setCurrentEventColumn(processInitParam.getCurrentEventColumn());
      processTreeVariantOut.setDefaultEventColumn(processInitParam.getDefaultEventColumn());
    } catch (Exception e) {
      log.error("processTreeVariant calculation error", e);
      processTreeVariantOut.setErrorMsg(e.getMessage());
    }
    return processTreeVariantOut;
  }

  @Override
  public ProcessTreeChartOut processTreeChart(ProcessTreeChartRequest request) {
    ProcessTreeChartOut processTreeChartOut = new ProcessTreeChartOut();
    defaultProcessTreeChartCheck(request);
    if (request.getErrorMsgFlag()) {
      processTreeChartOut.setErrorMsg(request.getErrorMsg());
      return processTreeChartOut;
    }

    // init param
    ProcessInitChartParam processInitParam = this.initProcessChartParam(request);
    if (processInitParam.getVariantMap().isEmpty()) {
      return processTreeChartOut;
    }

    TreeInfoPqlBo treeInfoPqlBo = new TreeInfoPqlBo();
    initProcessCharPql(processInitParam, treeInfoPqlBo);
    try {
      List<TreeLineBo> treeLineBos = calcTreeLine(processInitParam, treeInfoPqlBo);
      treeInfoPqlBo.setLines(treeLineBos);

      StartAndEndNodes startAndEndNodes = findStartAndEndNodes(treeLineBos);
      treeInfoPqlBo.setStartAndEndNodes(startAndEndNodes);

      Map<String, Pair<Long, Double>> eventInfo = calcTreeEvent(processInitParam, treeInfoPqlBo);
      treeInfoPqlBo.setNodeInfos(eventInfo);
    } catch (Exception e) {
      processTreeChartOut.setErrorMsg(e.getMessage());
      log.error("processTreeChart calculation error", e);
      return processTreeChartOut;
    }

    if (StringUtils.isNotBlank(treeInfoPqlBo.getVariantFilterExpression())) {
      processInitParam
          .getCustomFilterPOS()
          .add(
              TopicFilterPO.builder()
                  .expression(treeInfoPqlBo.getVariantFilterExpression())
                  .type(TopicFilterTypeEnum.EXPRESSION.getValue())
                  .build());
    }

    // 自定义节点KPI
    Map<String, List<String>> customEventKpi =
        processTreeKpiCalculate(processInitParam, processInitParam.getEventKpis());
    treeInfoPqlBo.setCustomEventKpi(customEventKpi);

    processTreeChartOut.setMaxEvents(processInitParam.getMaxEvents());
    processTreeChartOut.setMaxLines(processInitParam.getMaxLines());

    List<LineNode> lineNodes = builderLineNodes(treeInfoPqlBo, processInitParam);
    List<EventNode> eventNodes = builderEventNodes(treeInfoPqlBo, processInitParam);

    processTreeChartOut.setEventNodes(eventNodes);
    processTreeChartOut.setLineNodes(lineNodes);
    processTreeChartOut.setEventKpis(processInitParam.getEventKpis());
    processTreeChartOut.setLineKpis(processInitParam.getLineKpis());

    checkProcessTreeChartOut(processTreeChartOut, processInitParam.getCustomerError());
    processTreeChartOut.setLimitEnabled(treeInfoPqlBo.getLimitEnabled());
    return processTreeChartOut;
  }

  private void initProcessCharPql(
      ProcessInitChartParam processInitParam, TreeInfoPqlBo treeInfoPqlBo) {
    ModelCatalog modelCatalog = pqlCatalogManager.createCatalog(processInitParam.getTopicId());

    String variantFilterExpression = buildVariantFilterSql(processInitParam);
    treeInfoPqlBo.setVariantFilterExpression(variantFilterExpression);

    String activeColumnSql =
        ProcessTreeExprGenerator.buildActiveColumnSql(
            processInitParam.getProcessCrop(),
            processInitParam.getGroupInfo(),
            processInitParam.getInitActiveEventColumn(),
            processInitParam.getFilterEvents(),
            processInitParam.getIsReversedFilterEvents());
    treeInfoPqlBo.setActiveColumnSql(activeColumnSql);

    String activeCalCropColumnSql =
        ProcessTreeExprGenerator.makeCalCropRepeatToNull(
            activeColumnSql, processInitParam.getShortened());

    treeInfoPqlBo.setIsDefaultEventColumn(
        this.isDefaultEventColumn(
            activeCalCropColumnSql, modelCatalog.modelDesc(), processInitParam));

    String remapEventToNullPql =
        ProcessTreeExprGenerator.builderRemapEventToNullPql(
            processInitParam.getInitActiveEventColumn(),
            processInitParam.getFilterEvents(),
            processInitParam.getIsReversedFilterEvents(),
            processInitParam.getProcessCrop());
    treeInfoPqlBo.setRemapEventToNullPql(remapEventToNullPql);

    Tuple2<String, String> sourceTargetPql =
        ProcessTreeExprGenerator.builderSourceTarget(
            processInitParam.getGroupInfo(),
            processInitParam.getInitActiveEventColumn(),
            remapEventToNullPql,
            processInitParam.getShortened(),
            processInitParam.getSwitchEventColumnPql());
    treeInfoPqlBo.setSourceTargetPql(sourceTargetPql);
  }

  private Map<String, Pair<Long, Double>> calcTreeEvent(
      ProcessInitChartParam processInitParam, TreeInfoPqlBo treeInfoPqlBo) {
    Map<String, Pair<Long, Double>> eventMap =
        calcEventCountForPql(
            processInitParam, treeInfoPqlBo, treeInfoPqlBo.getStartAndEndNodes().getAllEventName());

    if (!treeInfoPqlBo.getStartAndEndNodes().getStartNodes().isEmpty()) {
      eventMap.put(
          I18nUtil.getMessage(I18nConst.START),
          Pair.of(processInitParam.getCurrentCaseCount(), 0d));
    }

    if (!treeInfoPqlBo.getStartAndEndNodes().getEndNodes().isEmpty()) {
      eventMap.put(
          I18nUtil.getMessage(I18nConst.END), Pair.of(processInitParam.getCurrentCaseCount(), 0d));
    }
    return eventMap;
  }

  private Boolean canSplitVariant(
      ProcessInitChartParam processInitParam, TreeInfoPqlBo treeInfoPqlBo) {
    if (processInitParam.getSelectOtherVariant()) {
      return true;
    }

    if (treeInfoPqlBo.getStartAndEndNodes().getHasSpecialCharacter()) {
      return true;
    }

    if (processInitParam.getVariantMap().entrySet().stream()
        .anyMatch(entry -> entry.getValue() == null)) {
      return true;
    }
    return false;
  }

  public Map<String, Pair<Long, Double>> calcEventCountForPql(
      ProcessInitParam processInitParam, TreeInfoPqlBo treeInfoPqlBo, Set<String> allEventName) {
    PQLBuilder pqlNodeBuilder =
        pqlService.newPQLBuilderWithOutCalcFilter(
            processInitParam.getTopicId(),
            processInitParam.getSheetId(),
            processInitParam.getCustomFilters());
    pqlNodeBuilder.setCaseLevelFilter(true);

    if (StringUtils.isNotBlank(treeInfoPqlBo.getVariantFilterExpression())) {
      pqlNodeBuilder.addComponentFilter(treeInfoPqlBo.getVariantFilterExpression());
    }

    pqlNodeBuilder.addColumn(treeInfoPqlBo.getActiveColumnSql());

    if (ProcessChartValueTypeEnum.EVENT_NUMBER == processInitParam.getValueShowType()) {
      pqlNodeBuilder.addColumn("count( encoded_column(case_id_column()))");
    } else {
      pqlNodeBuilder.addColumn("count(distinct encoded_column(case_id_column()))");
    }

    String indexStartTimeColumn = processInitParam.getIndexStartTimeColumn();
    String indexEndTimeColumn = processInitParam.getIndexEndTimeColumn();

    if (processInitParam.getValueShowType() == ProcessChartValueTypeEnum.AVERAGE_DURATION) {
      if (StringUtils.isEmpty(indexStartTimeColumn)) {
        pqlNodeBuilder.addColumn(
            "avg(SECONDS_BETWEEN(START_TIMESTAMP_COLUMN(), TIMESTAMP_COLUMN())) / "
                + processInitParam.getTimeTypeTimeUnit()
                + "",
            "timeBetween");
      } else {
        pqlNodeBuilder.addColumn(
            "avg(SECONDS_BETWEEN("
                + indexStartTimeColumn
                + ","
                + indexEndTimeColumn
                + " )) / "
                + processInitParam.getTimeTypeTimeUnit()
                + "",
            "timeBetween");
      }
    }
    if (processInitParam.getValueShowType() == ProcessChartValueTypeEnum.MEDIAN_DURATION) {
      if (StringUtils.isEmpty(indexStartTimeColumn)) {
        pqlNodeBuilder.addColumn(
            "median(SECONDS_BETWEEN(START_TIMESTAMP_COLUMN(), TIMESTAMP_COLUMN())) / "
                + processInitParam.getTimeTypeTimeUnit()
                + "",
            "timeBetween");
      } else {
        pqlNodeBuilder.addColumn(
            "median(SECONDS_BETWEEN("
                + indexStartTimeColumn
                + ","
                + indexEndTimeColumn
                + " )) / "
                + processInitParam.getTimeTypeTimeUnit()
                + "",
            "timeBetween");
      }
    }

    pqlNodeBuilder.addDirectFilter(treeInfoPqlBo.getActiveColumnSql() + " IS NOT NULL");
    Row[] collect = pqlNodeBuilder.collect();
    Map<String, Pair<Long, Double>> eventMap = new HashMap<>();

    for (Row row : collect) {
      if (row.get(0) == null) {
        continue;
      }
      if (!allEventName.contains(row.get(0).toString())) {
        continue;
      }
      // 需要计算 timeBetween值
      if (row.size() == 3) {
        eventMap.put(row.get(0).toString(), Pair.of((Long) row.get(1), (Double) row.get(2)));
      } else {
        eventMap.put(row.get(0).toString(), Pair.of((Long) row.get(1), 0d));
      }
    }
    return eventMap;
  }

  @Override
  public StartAndEndNodes findStartAndEndNodes(List<TreeLineBo> treeLineBos) {
    StartAndEndNodes startAndEndNodesBo = new StartAndEndNodes();
    Set<String> sourceEvents = new HashSet<>();
    Set<String> targetEvents = new HashSet<>();
    Set<String> sourceEventsDistinct = new HashSet<>();
    Set<String> targetEventsDistinct = new HashSet<>();

    boolean hasSpecialCharacter = false;
    for (TreeLineBo bo : treeLineBos) {
      sourceEvents.add(bo.getSourceEvent());
      targetEvents.add(bo.getTargetEvent());

      if (!hasSpecialCharacter) {
        if (bo.getTargetEvent().contains(",") || bo.getSourceEvent().contains(",")) {
          hasSpecialCharacter = true;
        }
      }

      if (!bo.getTargetEvent().equals(bo.getSourceEvent())) {
        sourceEventsDistinct.add(bo.getSourceEvent());
        targetEventsDistinct.add(bo.getTargetEvent());
      }
    }
    Set<String> startNodes = new HashSet<>(sourceEvents);
    startNodes.removeAll(targetEventsDistinct);

    Set<String> endNodes = new HashSet<>(targetEvents);
    endNodes.removeAll(sourceEventsDistinct);

    Set<String> allEventName = new HashSet<>(sourceEvents);
    allEventName.addAll(targetEvents);

    startAndEndNodesBo.setEndNodes(endNodes);
    startAndEndNodesBo.setStartNodes(startNodes);
    startAndEndNodesBo.setAllEventName(allEventName);
    startAndEndNodesBo.setHasSpecialCharacter(hasSpecialCharacter);
    return startAndEndNodesBo;
  }

  private String buildVariantFilterSql(ProcessInitChartParam processInitParam) {
    String activeColumnSql =
        ProcessTreeExprGenerator.buildActiveColumnSql(
            processInitParam.getProcessCrop(),
            processInitParam.getGroupInfo(),
            processInitParam.getInitActiveEventColumn(),
            processInitParam.getFilterEvents(),
            processInitParam.getIsReversedFilterEvents());
    String variantfilterExpression = "";

    if (Boolean.TRUE.equals(processInitParam.getSelectOtherVariant())) {
      if (!processInitParam.getExcludeVariantList().isEmpty()) {
        variantfilterExpression =
            ProcessTreePQL.makeVariantFilter(
                processInitParam.getExcludeVariantList(),
                activeColumnSql,
                processInitParam.getShortened(),
                true);
      }
    } else {
      List<String> variants = new ArrayList<>(processInitParam.getVariantMap().keySet());
      variantfilterExpression =
          ProcessTreePQL.makeVariantFilter(
              variants, activeColumnSql, processInitParam.getShortened(), false);
    }
    return variantfilterExpression;
  }

  private void checkProcessTreeChartOut(
      ProcessTreeChartOut processTreeChartOut, String customerError) {
    if (CollectionUtils.isEmpty(processTreeChartOut.getLineNodes())) {
      processTreeChartOut.setEventNodes(new ArrayList<>());
    }
    if (StringUtils.isNotBlank(customerError)) {
      processTreeChartOut.setErrorMsg(customerError);
    }
  }

  private void defaultProcessTreeChartCheck(ProcessTreeChartRequest request) {
    if (request.getDefaultChartFlag()) {
      ProcessTreeVariantRequest processTreeVariantRequest = new ProcessTreeVariantRequest();
      processTreeVariantRequest.setAddFilterList(request.getAddFilterList());
      processTreeVariantRequest.setTopicId(request.getTopicId());
      processTreeVariantRequest.setShortened(request.getShortened());
      processTreeVariantRequest.setDefaultChartFlag(request.getDefaultChartFlag());
      processTreeVariantRequest.setSheetId(request.getSheetId());
      processTreeVariantRequest.setIgnoreTopicProcessCropFilter(
          request.getIgnoreTopicProcessCropFilter());
      ProcessTreeVariantOut processTreeVariantOut =
          this.processTreeVariant(processTreeVariantRequest);

      if (StringUtils.isNotBlank(processTreeVariantOut.getErrorMsg())) {
        request.setErrorMsg(processTreeVariantOut.getErrorMsg());
        return;
      }
      Map<String, Long> variantMap = new HashMap<>();
      if (CollectionUtils.isNotEmpty(processTreeVariantOut.getVariantList())) {
        for (VariantResultDTO variantResultDTO : processTreeVariantOut.getVariantList()) {
          variantMap.put(variantResultDTO.getVariant(), variantResultDTO.getCount());
        }
      }
      request.setVariantMap(variantMap);
    }

    if (CollectionUtils.isNotEmpty(request.getVariantList())) {
      if (request.getVariantMap() == null) {
        request.setVariantMap(new HashMap<>());
      }
      for (String variant : request.getVariantList()) {
        request.getVariantMap().put(variant, 0L);
      }
      request.setCalculateEventCountFlag(true);
    }
  }

  private ProcessInitChartParam initProcessChartParam(ProcessTreeChartRequest request) {
    ProcessInitChartParam processInitChartParam = new ProcessInitChartParam();
    processInitChartParam.setMaxEvents(
        request.getProcessEventNumberLimit() != null
            ? request.getProcessEventNumberLimit()
            : this.processEventNumberLimit);
    processInitChartParam.setMaxLines(
        request.getProcessConnectNumberLimit() != null
            ? request.getProcessConnectNumberLimit()
            : this.processConnectNumberLimit);

    processInitChartParam.setShortened(request.getShortened());
    if (MapUtils.isNotEmpty(request.getVariantMap())) {
      for (Map.Entry<String, Long> stringIntegerEntry : request.getVariantMap().entrySet()) {
        if (Objects.equals(
            stringIntegerEntry.getKey(), I18nUtil.getMessage(I18nConst.VARIANT_OTHER))) {
          processInitChartParam.setSelectOtherVariant(true);
          break;
        }
      }
    }

    initProcessParamBase(request, processInitChartParam);
    processInitChartParam.setVariantMap(request.getVariantMap());
    if (!CollectionUtils.isEmpty(request.getExcludeVariantList())) {
      processInitChartParam.setExcludeVariantList(request.getExcludeVariantList());
    }
    processInitChartParam.setValueShowType(request.getValueShowType());
    processInitChartParam.setVariantAllFlag(request.getVariantAllFlag());

    if (CollectionUtils.isNotEmpty(request.getAddFilterList())) {
      processInitChartParam.getCustomFilterPOS().addAll(request.getAddFilterList());
      List<String> filterEvents = new ArrayList<>();
      request.getAddFilterList().stream()
          .filter(po -> (po.getFilterEvent() != null && !po.getFilterEvent().isEmpty()))
          .forEach(
              po -> {
                String[] split = po.getFilterEvent().split("|");
                Collections.addAll(filterEvents, split);
              });
      Map<String, Long> variantMap = processInitChartParam.getVariantMap();
      List<String> excludeVariantList = processInitChartParam.getExcludeVariantList();
      for (String filterEvent : filterEvents) {

        Iterator<Map.Entry<String, Long>> iterator = variantMap.entrySet().iterator();
        while (iterator.hasNext()) {
          Map.Entry<String, Long> entry = iterator.next();
          String key = entry.getKey();

          iterator.remove();
          String[] split = key.split(",");
          List<String> list = new ArrayList<>(Arrays.asList(split));
          list.removeIf(s -> s.equals(filterEvent));
          String join = String.join(",", list);

          Long ordValue = variantMap.getOrDefault(join, 0L);
          variantMap.put(join, ordValue + entry.getValue());
        }

        List<String> newExcludeVariantList = new ArrayList<>();
        for (String excludeVariant : excludeVariantList) {
          String[] split = excludeVariant.split(",");
          List<String> list = new ArrayList<>(Arrays.asList(split));
          list.remove(filterEvent);
          String join = String.join(",", list);
          newExcludeVariantList.add(join);
        }
        excludeVariantList = newExcludeVariantList;
      }
      processInitChartParam.setVariantMap(variantMap);
      processInitChartParam.setExcludeVariantList(excludeVariantList);
    }
    long currentCaseCount =
        processInitChartParam.getVariantMap().values().stream()
            .filter(Objects::nonNull)
            .mapToLong(Long::longValue)
            .sum();

    processInitChartParam.setCurrentCaseCount(currentCaseCount);
    return processInitChartParam;
  }

  private KpiPO getKpiPOByKpiId(List<KpiPO> kpiList, String kpiName) {
    List<KpiPO> collect =
        kpiList.stream().filter(po -> po.getName().equals(kpiName)).collect(Collectors.toList());
    if (collect.isEmpty()) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
    }
    return collect.get(0);
  }

  private ProcessInitParam initProcessVariantParam(ProcessTreeVariantRequest request) {
    ProcessInitParam processInitParam = new ProcessInitParam();
    initProcessParamBase(request, processInitParam);
    processInitParam.setSortingRules(request.getSortingRules());
    processInitParam.setVariantNumber(request.getVariantNumber());
    processInitParam.setSelectOtherVariant(request.getVariantOtherFlag());
    processInitParam.setVariantAllFlag(
        request.getVariantAllFlag() != null && request.getVariantAllFlag());
    processInitParam.setShowPathKipType(request.getShowPathKpiType());
    processInitParam.setSortingPathKipType(request.getSortingPathKpiType());

    if (ProcessPathKpiType.CUSTOMER_KPI.equals(request.getShowPathKpiType())) {
      if (request.getShowPathKpiName() == null) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
      }
      processInitParam.setShowPathKpi(
          getKpiPOByKpiId(processInitParam.getPathKpiList(), request.getShowPathKpiName()));
    }
    if (ProcessPathKpiType.CUSTOMER_KPI.equals(request.getSortingPathKpiType())) {
      if (request.getSortingPathKpiName() == null) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
      }
      processInitParam.setSortPathKpi(
          getKpiPOByKpiId(processInitParam.getPathKpiList(), request.getSortingPathKpiName()));
    }
    processInitParam.setIsCustomKpiIdConsistent(
        Objects.equals(request.getShowPathKpiName(), request.getSortingPathKpiName()));

    return processInitParam;
  }

  private boolean isCalculateVariantNumber(
      TreeVariantInfo treeVariantInfo, Boolean selectOtherVariant) {
    if (treeVariantInfo.getTreeVariantBos().isEmpty()) {
      return false;
    }
    if (treeVariantInfo.getVariantNumber() == null) {
      return true;
    }
    if (selectOtherVariant && treeVariantInfo.getOtherNumber() == null) {
      return true;
    }
    return false;
  }

  public VariantCalculateDTO buildVariantCalculate(
      ProcessInitParam processInitParam, TreeVariantInfo treeVariantInfo) {
    Long variantCount = 0L;
    if (isCalculateVariantNumber(treeVariantInfo, processInitParam.getSelectOtherVariant())) {
      variantCount =
          ProcessTreePQL.queryVariantCount(
              ProcessTreeExprGenerator.buildVariantSql(
                  processInitParam.getProcessCrop(),
                  processInitParam.getGroupInfo(),
                  processInitParam.getInitActiveEventColumn(),
                  processInitParam.getShortened(),
                  processInitParam.getFilterEvents(),
                  processInitParam.getIsReversedFilterEvents()),
              pqlService.newPQLBuilder(
                  processInitParam.getTopicId(),
                  processInitParam.getSheetId(),
                  processInitParam.getCustomFilters()));
    } else {
      if (CollectionUtils.isNotEmpty(treeVariantInfo.getTreeVariantBos())) {
        if (processInitParam.getSelectOtherVariant()) {
          variantCount = treeVariantInfo.getOtherNumber() + treeVariantInfo.getVariantNumber();
        } else {
          variantCount = treeVariantInfo.getVariantNumber().longValue();
        }
      }
    }
    String totalCaseCount =
        pqlService.calcExpression(
            processInitParam.getTopicId(),
            processInitParam.getSheetId(),
            "count(case_table().encoded_case_id)");

    VariantCalculateDTO variantCalculateDTO = new VariantCalculateDTO();
    variantCalculateDTO.setVariantCount(variantCount);
    variantCalculateDTO.setCaseCountStr(totalCaseCount);
    return variantCalculateDTO;
  }

  private ProcessExplorerInitParam initProcessExplorerParam(
      ProcessExplorerRequest processExplorerRequest) {
    ProcessExplorerInitParam processExplorerInitParam = new ProcessExplorerInitParam();
    this.initProcessParamBase(processExplorerRequest, processExplorerInitParam);
    processExplorerInitParam.setValueShowType(
        ProcessChartValueTypeEnum.getProcessChartValueTypeEnumByValue(
            processExplorerRequest.getValueShowType()));
    return processExplorerInitParam;
  }

  @NotNull
  private List<TreeLineBo> calcTreeLine(
      ProcessInitParam processInitParam, TreeInfoPqlBo treeInfoPqlBo) {

    PQLBuilder pqlNodeBuilder =
        pqlService.newPQLBuilderWithOutCalcFilter(
            processInitParam.getTopicId(),
            processInitParam.getSheetId(),
            processInitParam.getCustomFilters());
    pqlNodeBuilder.setCaseLevelFilter(true);
    if (StringUtils.isNotBlank(treeInfoPqlBo.getVariantFilterExpression())) {
      pqlNodeBuilder.addComponentFilter(treeInfoPqlBo.getVariantFilterExpression());
    }

    pqlNodeBuilder.addColumn(treeInfoPqlBo.getSourceTargetPql()._1);
    pqlNodeBuilder.addColumn(treeInfoPqlBo.getSourceTargetPql()._2);
    String lineNumber = "lineNumber";
    String lineKpiExpression =
        builderLineValueExpression(
            processInitParam.getValueShowType(),
            CollectionUtils.isEmpty(processInitParam.getLineKpis()),
            processInitParam.getTimeTypeTimeUnit(),
            processInitParam.getIndexStartTimeColumn(),
            processInitParam.getIndexEndTimeColumn());
    pqlNodeBuilder.addColumn(lineKpiExpression, lineNumber);

    if (CollectionUtils.isNotEmpty(processInitParam.getLineKpis())) {
      for (KpiPO lineKpi : processInitParam.getLineKpis()) {
        pqlNodeBuilder.addColumn(lineKpi.getExpression());
      }
    }
    if (processInitParam.getMaxLines() > 0) {
      pqlNodeBuilder.addSort(lineNumber, 0);
    }
    List<TreeLineBo> treeLineBos =
        PQLResultProcess.treeLineHandle(pqlNodeBuilder.collect(), processInitParam);
    if (treeLineBos.size() > processInitParam.getMaxLines()) {
      treeInfoPqlBo.setLimitEnabled(true);
      treeLineBos = treeLineBos.subList(0, processInitParam.getMaxLines());
    }
    return treeLineBos;
  }

  public String builderLineValueExpression(
      ProcessChartValueTypeEnum valueShowType,
      boolean lineKpisIsEmpty,
      Integer timeTypeTimeUnit,
      String indexStartTimeColumn,
      String indexEndTimeColumn) {
    switch (valueShowType) {
      case NUMBER:
        if (PQLConf.nativeCalcEcnabled()
            && lineKpisIsEmpty
            && PQLContext.get().targetSourceContext().targetSourceCache().size() == 2) {
          PQLContext.get().targetSourceContext().setDistinctEdge(true);
          return "count(1)";
        } else {
          return "count(distinct source_target_caseid_column())";
        }
      case EVENT_NUMBER:
        return "count( source_target_caseid_column())";
      case AVERAGE_DURATION:
        if (StringUtils.isEmpty(indexStartTimeColumn)) {
          return "avg (datediff(ss,source(TIMESTAMP_COLUMN()),target(START_TIMESTAMP_COLUMN()))) / "
              + timeTypeTimeUnit
              + " ";
        } else {
          return "avg (datediff(ss,source("
              + indexEndTimeColumn
              + "),target("
              + indexStartTimeColumn
              + "))) / "
              + timeTypeTimeUnit
              + " ";
        }
      case MEDIAN_DURATION:
        if (StringUtils.isEmpty(indexStartTimeColumn)) {
          return "MEDIAN (datediff(ss,source(TIMESTAMP_COLUMN()),target(START_TIMESTAMP_COLUMN()))) / "
              + timeTypeTimeUnit
              + " ";
        } else {
          return "MEDIAN (datediff(ss,source("
              + indexEndTimeColumn
              + "),target("
              + indexStartTimeColumn
              + "))) / "
              + timeTypeTimeUnit
              + " ";
        }
      default:
        throw new CustomException(
            ErrorCode.ERROR_PARAM, "ProcessChartValueTypeEnum not supported : " + valueShowType);
    }
  }

  public boolean isDefaultEventColumn(
      String eventColumn, ModelDesc modelDesc, ProcessInitParam processInitParam) {

    if (Boolean.TRUE.equals(ExpressionBuilderUtil.checkGroup(processInitParam.getGroupInfo()))) {
      return false;
    }
    if (processInitParam.getProcessCrop() != null) {
      return false;
    }

    try {
      List<KpiPO> lineKpis = processInitParam.getLineKpis();
      if (lineKpis != null && !lineKpis.isEmpty()) {
        return false;
      }
      if (!UnresolvedAttribute.parseAttributeName(eventColumn)
              .last()
              .equals(modelDesc.activeTable().eventColumn())
          && !ACTIVITY_COLUMN.equals(eventColumn)) {
        return false;
      }
      return modelDesc.baseVirtualCaseTable().hasVariant();
    } catch (Exception e) {
      log.warn("checkAttribute error=>", e);
      return false;
    }
  }

  private List<VariantResultDTO> getDefaultProcessChartVariant(ProcessInitParam processInitParam) {
    String variantNameSql =
        ProcessTreeExprGenerator.buildVariantSql(
            processInitParam.getProcessCrop(),
            processInitParam.getGroupInfo(),
            processInitParam.getInitActiveEventColumn(),
            processInitParam.getShortened(),
            processInitParam.getFilterEvents(),
            processInitParam.getIsReversedFilterEvents());
    PQLBuilder processVariantPql =
        builderDefaultVariantPQLBuilder(processInitParam, variantNameSql);
    if (Boolean.FALSE.equals(processInitParam.isNormalEventColumn())) {
      processVariantPql.addTopicFilter(variantNameSql + NOT_EQ_EMPTY);
    }
    Row[] treeVariantsRows = processVariantPql.collect();

    List<VariantResultDTO> variantResultDTOS = new ArrayList<>();
    for (Row treeVariantsRow : treeVariantsRows) {
      String variantValue = treeVariantsRow.getString(0);
      Long variantNumber = treeVariantsRow.getLong(1);
      VariantResultDTO variantResultDTO = new VariantResultDTO();
      variantResultDTO.setVariant(variantValue);
      variantResultDTO.setCount(variantNumber);
      variantResultDTO.setEventList(variantValue);
      variantResultDTOS.add(variantResultDTO);
    }
    return variantResultDTOS;
  }

  private PQLBuilder builderDefaultVariantPQLBuilder(
      ProcessInitParam processInitParam, String variantNameSql) {
    PQLBuilder processVariantPql =
        pqlService.newPQLBuilderWithOutCalcFilter(
            processInitParam.getTopicId(),
            processInitParam.getSheetId(),
            processInitParam.getCustomFilters());
    processVariantPql.addColumn(variantNameSql, VARIANT_NAME);
    processVariantPql.addColumn(COUNT, VARIANT_COUNT);
    processVariantPql.addSort(VARIANT_COUNT, 0);
    processVariantPql.addTopicFilter(variantNameSql + " is not null");
    processVariantPql.addLimit(processInitParam.getDefaultChartVariantNumber());
    return processVariantPql;
  }

  @NotNull
  private TreeVariantInfo getTreeVariantList(ProcessInitParam processInitParam) {
    TreeVariantInfo treeVariantInfo = new TreeVariantInfo();
    String caseDurationSql =
        ProcessTreeExprGenerator.buildCaseDurationSql(
            processInitParam.getProcessCrop(), processInitParam.getActiveTimeColumn());
    String variantNameSql =
        ProcessTreeExprGenerator.buildVariantSql(
            processInitParam.getProcessCrop(),
            processInitParam.getGroupInfo(),
            processInitParam.getInitActiveEventColumn(),
            processInitParam.getShortened(),
            processInitParam.getFilterEvents(),
            processInitParam.getIsReversedFilterEvents());

    Row[] treeVariantsRows;
    if (Boolean.TRUE.equals(processInitParam.isNormalEventColumn())) {
      Row[] topNVariant = calcTopNVariant(processInitParam, variantNameSql, caseDurationSql);
      treeVariantInfo.setVariantNumber(topNVariant.length);
      treeVariantsRows =
          calcDefaultVariant(processInitParam, variantNameSql, caseDurationSql, topNVariant);
      treeVariantInfo.setTreeVariantBos(
          PQLResultProcess.treeVariantHandlePro(treeVariantsRows, processInitParam));
      return treeVariantInfo;
    }

    PQLBuilder processVariantPql =
        builderVariantPQLBuilder(processInitParam, variantNameSql, caseDurationSql, true);
    processVariantPql.addLimit(processInitParam.getVariantNumber());
    if (processInitParam.getProcessCrop() != null) {
      processVariantPql.addTopicFilter(variantNameSql + NOT_EQ_EMPTY);
    }

    if (Boolean.TRUE.equals(processInitParam.getSelectOtherVariant())
        && Boolean.FALSE.equals(processInitParam.getVariantAllFlag())) {
      processVariantPql.topK(processInitParam.getVariantNumber());
    }
    treeVariantsRows =
        processVariantPql.collect(
            df -> {
              Dataset<Row> dataset = variantBase64Add(df);
              if (Boolean.TRUE.equals(!processInitParam.getSelectOtherVariant())
                  && Boolean.FALSE.equals(processInitParam.getVariantAllFlag())) {
                return dataset.limit(processInitParam.getVariantNumber());
              }
              return dataset;
            });
    treeVariantInfo.setTreeVariantBos(
        PQLResultProcess.treeVariantHandlePro(treeVariantsRows, processInitParam));
    return treeVariantInfo;
  }

  private Row[] calcTopNVariant(
      ProcessInitParam processInitParam, String variantNameSql, String caseDurationSql) {
    PQLBuilder processVariantPql =
        builderVariantPQLBuilder(processInitParam, variantNameSql, caseDurationSql, true);
    processVariantPql.addLimit(processInitParam.getVariantNumber());
    return processVariantPql.collect(this::variantBase64Add);
  }

  private Row[] calcDefaultVariant(
      ProcessInitParam processInitParam,
      String variantNameSql,
      String caseDurationSql,
      Row[] topNVariant) {
    if (Boolean.FALSE.equals(processInitParam.getSelectOtherVariant())
        || topNVariant.length < processInitParam.getVariantNumber()) {
      return topNVariant;
    }
    ArrayList<String> variants = new ArrayList<>();
    for (Row row : topNVariant) {
      if (!row.isNullAt(0)) {
        variants.add("'" + row.getString(0) + "'");
      }
    }
    if (variants.isEmpty()) {
      return null;
    }
    String filter =
        variantNameSql
            + " not in ("
            + String.join(",", variants)
            + ") and "
            + "("
            + variantNameSql
            + " != \"\") ";
    PQLBuilder processVariantPql =
        buildOtherVariantPQLBuilder(processInitParam, variantNameSql, caseDurationSql, filter);
    Row[] other = processVariantPql.collect();
    if (other.length == 1 && other[0].getLong(0) != 0) {
      Object[] objArray = new Object[topNVariant[0].schema().size()];
      objArray[0] = I18nUtil.getMessage(I18nConst.VARIANT_OTHER);
      objArray[objArray.length - 1] = I18nUtil.getMessage(I18nConst.VARIANT_OTHER);
      for (int i = 0; i < other[0].schema().size(); i++) {
        objArray[1 + i] = other[0].get(i);
      }
      Row row = RowFactory.create(objArray);
      topNVariant = Stream.concat(Arrays.stream(topNVariant), Stream.of(row)).toArray(Row[]::new);
    }
    return topNVariant;
  }

  private Dataset<Row> variantBase64Add(Dataset<Row> df) {
    List<Column> collect =
        Arrays.stream(df.schema().names()).map(Column::new).collect(Collectors.toList());
    collect.add(functions.base64(new Column(VARIANT_NAME)));
    Column[] objects = collect.toArray(new Column[0]);
    Dataset<Row> select = df.select(objects);
    return select;
  }

  private PQLBuilder buildOtherVariantPQLBuilder(
      ProcessInitParam processInitParam,
      String variantNameSql,
      String caseDurationSql,
      String filter) {
    PQLBuilder processVariantPql =
        pqlService.newPQLBuilderWithOutCalcFilter(
            processInitParam.getTopicId(),
            processInitParam.getSheetId(),
            processInitParam.getCustomFilters());
    if (StringUtils.isNotBlank(filter)) {
      processVariantPql.addComponentFilter(filter);
    }
    processVariantPql.addTopicFilter("NOT(" + variantNameSql + " IS NULL) ");
    if (CollectionUtils.isNotEmpty(processInitParam.getPathKpiList())) {
      processVariantPql.addColumn("count(distinct ENCODED_CASE_CASE_ID_COLUMN())", VARIANT_COUNT);
    } else {
      processVariantPql.addColumn(COUNT, VARIANT_COUNT);
    }

    processVariantPql.addColumn(caseDurationSql, CASE_DURATION);
    addPathKpiToPql(processVariantPql, processInitParam, false);
    pqlAddSort(processVariantPql, processInitParam);
    return processVariantPql;
  }

  private PQLBuilder builderVariantPQLBuilder(
      ProcessInitParam processInitParam,
      String variantNameSql,
      String caseDurationSql,
      boolean increment) {
    PQLBuilder processVariantPql =
        pqlService.newPQLBuilderWithOutCalcFilter(
            processInitParam.getTopicId(),
            processInitParam.getSheetId(),
            processInitParam.getCustomFilters());
    processVariantPql.addColumn(variantNameSql, VARIANT_NAME);
    processVariantPql.addTopicFilter(variantNameSql + NOT_EQ_EMPTY);

    if (processInitParam.getIgnoreTopicProcessCropFilter()) {
      processVariantPql.addColumn(COUNT, VARIANT_COUNT);
    } else {
      processVariantPql.addColumn("count(distinct ENCODED_CASE_CASE_ID_COLUMN())", VARIANT_COUNT);
    }
    processVariantPql.addColumn(caseDurationSql, CASE_DURATION);

    addPathKpiToPql(processVariantPql, processInitParam, increment);

    pqlAddSort(processVariantPql, processInitParam);
    return processVariantPql;
  }

  private void addPathKpiToPql(
      PQLBuilder pqlBuilder, ProcessInitParam processInitParam, boolean increment) {
    KpiPO showPathKpi = processInitParam.getShowPathKpi();
    KpiPO sortPathKpi = processInitParam.getSortPathKpi();
    if (showPathKpi != null && showPathKpi == sortPathKpi) {
      if (increment) {
        processInitParam.setShowPathKpiIndex(processInitParam.getVariantMd5Index());
        processInitParam.variantMd5IndexIncremental();
      }
      pqlBuilder.addColumn(
          showPathKpi.getExpression(),
          ProcessPathKpiType.CUSTOMER_KPI.name() + showPathKpi.getId());
    } else {
      if (showPathKpi != null) {
        if (increment) {
          processInitParam.setShowPathKpiIndex(processInitParam.getVariantMd5Index());
          processInitParam.variantMd5IndexIncremental();
        }
        pqlBuilder.addColumn(
            showPathKpi.getExpression(),
            ProcessPathKpiType.CUSTOMER_KPI.name() + showPathKpi.getId());
      }

      if (sortPathKpi != null) {
        if (increment) {
          processInitParam.variantMd5IndexIncremental();
        }
        pqlBuilder.addColumn(
            sortPathKpi.getExpression(),
            ProcessPathKpiType.CUSTOMER_KPI.name() + sortPathKpi.getId());
      }
    }
  }

  private void pqlAddSort(PQLBuilder processVariantPql, ProcessInitParam processInitParam) {
    Integer sortType = processInitParam.getSortingRules();
    ProcessPathKpiType sortingPathKipType = processInitParam.getSortingPathKipType();
    switch (sortingPathKipType) {
      case CASE_NUMBER:
        processVariantPql.addSort(VARIANT_COUNT, sortType);
        processVariantPql.addSort(CASE_DURATION, 1);
        break;

      case THROUGHPUT_TIME:
        processVariantPql.addSort(CASE_DURATION, sortType);
        processVariantPql.addSort(VARIANT_COUNT, 1);
        break;

      case CUSTOMER_KPI:
        processVariantPql.addSort(
            ProcessPathKpiType.CUSTOMER_KPI.name() + processInitParam.getSortPathKpi().getId(),
            sortType);
        processVariantPql.addSort(VARIANT_COUNT, 1);
        break;
    }
  }

  public Map<String, List<String>> processTreeKpiCalculate(
      ProcessInitBase processInitParam, List<KpiPO> kpiPOS) {
    if (CollectionUtils.isEmpty(kpiPOS)) {
      return new HashMap<>();
    }
    ParseKpiDTO parseKpiDTO = new ParseKpiDTO();
    List<KpiPO> eventKpis = new ArrayList<>();
    KpiPO dimension = new KpiPO();
    dimension.setName("DIMENSION1");
    dimension.setExpression(processInitParam.getActiveEventColumn());

    eventKpis.add(dimension);
    eventKpis.addAll(kpiPOS);

    parseKpiDTO.setTopicId(processInitParam.getTopicId());
    parseKpiDTO.setDimensionColumnList(eventKpis);
    parseKpiDTO.setType(0);
    parseKpiDTO.setRule(1);
    parseKpiDTO.setAddTopicFilterList(
        processInitParam.getCustomFilterPOS().stream()
            .filter(
                po -> {
                  if (Objects.equals(po.getType(), TopicFilterTypeEnum.EXPRESSION.getValue())
                      && StringUtils.isBlank(po.getExpression())) {
                    return false;
                  }
                  return true;
                })
            .collect(Collectors.toList()));
    parseKpiDTO.setTopicFilterDisabled(false);
    parseKpiDTO.setPageSize(processConnectNumberLimit);
    Result ret;
    Map<String, List<String>> resultMap = new HashMap<>();
    try {
      ret = pqlService.calcExpression(parseKpiDTO);
      String[][] data = ret.getData();
      for (int j = 0; j < data.length; j++) {
        String[] datum = data[j];
        for (int i = 0; i < datum.length; i++) {
          if (i == 0) {
            List<String> calculateValue = new ArrayList<>();
            resultMap.put(datum[0], calculateValue);
          } else {
            List<String> strings = resultMap.get(datum[0]);
            int size = strings.size();
            if (StringUtils.isEmpty(kpiPOS.get(size).getColumnType())) {
              kpiPOS.get(size).setColumnType(ret.getMetadata()[i].getColumnType().toString());
            }
            strings.add(datum[i]);
          }
        }
      }
    } catch (Exception e) {
      e.printStackTrace();
      processInitParam.setCustomerError(e.getMessage());
      return new HashMap<>();
    }
    return resultMap;
  }

  private String getCalculateColor(
      TreeInfoPqlBo treeInfoPql,
      ProcessInitParam processInitParam,
      String eventName,
      boolean isEventColor) {
    if (Boolean.FALSE.equals(processInitParam.isExistEventKpi())) {
      return "";
    }
    Map<String, List<String>> customEventKpi = treeInfoPql.getCustomEventKpi();

    if (customEventKpi == null || customEventKpi.isEmpty()) {
      return "";
    }
    Map<String, List<String>> customEventKpiMap = treeInfoPql.getCustomEventKpi();
    if (customEventKpiMap.get(eventName) == null) {
      return "";
    }
    return calculateColor(
        treeInfoPql.getCustomEventKpi().get(eventName), processInitParam, isEventColor);
  }

  private List<LineNode> builderLineNodes(
      TreeInfoPqlBo treeInfoPql, ProcessInitParam processInitParam) {
    List<TreeLineBo> lines = treeInfoPql.getLines();
    List<LineNode> resultLineList = new ArrayList<>();
    for (String startNode : treeInfoPql.getStartAndEndNodes().getStartNodes()) {
      if (I18nUtil.getMessage(I18nConst.START).equals(startNode)) {
        continue;
      }
      LineNode lineNode = new LineNode();
      lineNode.setSource(I18nUtil.getMessage(I18nConst.START));
      lineNode.setTarget(startNode);
      if (processInitParam.getTimeType() == null) {
        lineNode.setSecond(treeInfoPql.getNodeInfos().get(startNode).toString());
      }
      lineNode.setNumber(treeInfoPql.getNodeInfos().get(startNode).getLeft());
      lineNode.setStartOrEnd(true);
      resultLineList.add(lineNode);
    }

    for (String endNode : treeInfoPql.getStartAndEndNodes().getEndNodes()) {
      if (I18nUtil.getMessage(I18nConst.END).equals(endNode)) {
        continue;
      }
      LineNode lineNode = new LineNode();
      lineNode.setSource(endNode);
      if (processInitParam.getTimeType() == null) {
        lineNode.setSecond(treeInfoPql.getNodeInfos().get(endNode).toString());
      }
      lineNode.setTarget(I18nUtil.getMessage(I18nConst.END));
      lineNode.setNumber(treeInfoPql.getNodeInfos().get(endNode).getLeft());
      lineNode.setStartOrEnd(true);
      resultLineList.add(lineNode);
    }

    for (TreeLineBo line : lines) {
      boolean isStartOrEnd =
          Objects.equals(I18nUtil.getMessage(I18nConst.START), line.getSourceEvent())
              || Objects.equals(I18nUtil.getMessage(I18nConst.END), line.getTargetEvent());
      LineNode lineNode = new LineNode();
      lineNode.setSource(line.getSourceEvent());
      lineNode.setTarget(line.getTargetEvent());
      lineNode.setSecond(line.getData());
      lineNode.setPenWidth(line.getLineWide());
      lineNode.setKpiValues(line.getKpiValues());
      lineNode.setStartOrEnd(isStartOrEnd);
      lineNode.setColor(this.calculateColor(line.getKpiValues(), processInitParam, false));
      resultLineList.add(lineNode);
    }
    return resultLineList;
  }

  private List<EventNode> builderEventNodes(
      TreeInfoPqlBo treeInfoPql, ProcessInitParam processInitParam) {
    List<EventNode> resultEventList = new ArrayList<>();
    Map<String, List<String>> customEventKpi = treeInfoPql.getCustomEventKpi();
    for (Map.Entry<String, Pair<Long, Double>> nodeFrequency :
        treeInfoPql.getNodeInfos().entrySet()) {
      EventNode eventNode = new EventNode();
      String calculateColor =
          getCalculateColor(treeInfoPql, processInitParam, nodeFrequency.getKey(), true);
      eventNode.setColor(calculateColor);
      eventNode.setKpiValues(customEventKpi.get(nodeFrequency.getKey()));
      eventNode.setGradient(0);
      eventNode.setNumber(nodeFrequency.getValue().getLeft());

      Double timeBetween = nodeFrequency.getValue().getRight();
      String second = null;
      if (processInitParam.getValueShowType().getIsTime()) {
        if (Double.isNaN(timeBetween)) {
          second = 0.0d + processInitParam.getTimeTypeTimeUnitName();
        } else {
          second =
              BigDecimal.valueOf(timeBetween).setScale(1, RoundingMode.HALF_UP)
                  + processInitParam.getTimeTypeTimeUnitName();
        }
      }
      eventNode.setSecond(second);
      eventNode.setName(nodeFrequency.getKey());
      eventNode.setIsStart(
          Objects.equals(I18nUtil.getMessage(I18nConst.START), nodeFrequency.getKey()));
      eventNode.setIsEnd(
          Objects.equals(I18nUtil.getMessage(I18nConst.END), nodeFrequency.getKey()));
      resultEventList.add(eventNode);
    }
    return resultEventList;
  }

  /**
   * 计算颜色
   *
   * @return
   */
  public String calculateColor(
      List<String> customKpiResults, ProcessInitBase processInitParam, boolean eventColorFlag) {
    if (CollectionUtils.isEmpty(customKpiResults)) {
      return "";
    }
    Double value = 0d;
    List<ColourInfo> colourInfos;

    try {
      if (eventColorFlag) {
        colourInfos = processInitParam.getEventColourInfos();
        KpiPO kpiPO =
            processInitParam.getEventKpis().get(processInitParam.getEventColourKpiIndex());
        String kpiValue = customKpiResults.get(processInitParam.getEventColourKpiIndex());
        if (StringUtils.isNotBlank(kpiPO.getUnit()) && kpiValue.endsWith(kpiPO.getUnit())) {
          kpiValue = kpiValue.substring(0, kpiValue.lastIndexOf(kpiPO.getUnit())).trim();
        }
        value = Double.valueOf(kpiValue);
      } else {
        colourInfos = processInitParam.getLineColourInfos();
        String strUnit =
            processInitParam
                .getLineKpis()
                .get(processInitParam.getLineColourKpiIndex())
                .getStrUnit();
        if (StringUtils.isNotBlank(strUnit)) {
          String kpiValue = customKpiResults.get(processInitParam.getLineColourKpiIndex());
          int lastIndex = kpiValue.lastIndexOf(strUnit);
          if (lastIndex < 0) {
            value = 0.0D;
          } else {
            value = Double.valueOf(kpiValue.substring(0, lastIndex));
          }
        } else {
          value = Double.valueOf(customKpiResults.get(processInitParam.getLineColourKpiIndex()));
        }
      }
    } catch (NumberFormatException e) {
      return "";
    }

    if (value.isNaN()) {
      return "";
    }

    value = CalculationUtil.changeDecimal(value, 2);
    if (CollectionUtils.isEmpty(colourInfos)) {
      return "";
    }
    for (Object obj : colourInfos) {

      ColourInfo colourInfo = (ColourInfo) obj;
      Double contrastValue = CalculationUtil.changeDecimal(colourInfo.getValue(), 2);

      switch (CalculationSymbol.getCalculationSymbolBySymbol(colourInfo.getType())) {
        case LESS:
          if (value < contrastValue) {
            return colourInfo.getColourCode();
          }
          break;
        case GREATER:
          if (value > contrastValue) {
            return colourInfo.getColourCode();
          }
          break;
        case EQUAL:
          if (Objects.equals(value, contrastValue)) {
            return colourInfo.getColourCode();
          }
          break;
        case LESS_EQUAL:
          if (value < contrastValue || Objects.equals(value, contrastValue)) {
            return colourInfo.getColourCode();
          }
          break;

        case GREATER_EQUAL:
          if (value > contrastValue || Objects.equals(value, contrastValue)) {
            return colourInfo.getColourCode();
          }
          break;
        default:
      }
    }
    return "";
  }

  @NotNull
  private List<VariantResultDTO> getVariantList(List<TreeVariantBo> variants) {
    List<VariantResultDTO> variantResult = new ArrayList<>();

    for (TreeVariantBo variant : variants) {
      VariantResultDTO variantResultDTO = new VariantResultDTO();
      variantResultDTO.setVarName(variant.getVariantName());
      variantResultDTO.setValue(
          BigDecimal.valueOf(CalculationUtil.changeDecimal(variant.getRatio() * 100, 2)));
      variantResultDTO.setCount(variant.getNumber());
      variantResultDTO.setVariantMd5(variant.getVariantMd5());
      variantResultDTO.setEventSize(variant.getVariant().split(",").length);
      variantResultDTO.setCaseDuration(BigDecimal.valueOf(variant.getCaseDuration()));
      variantResultDTO.setEventList(variant.getVariant());
      variantResultDTO.setVariant(variant.getVariant());
      variantResultDTO.setPathKpiValue(variant.getPathKpiValue());

      List<Map<String, String>> valueList = new ArrayList<>();
      Map<String, String> map = new HashMap<>();
      map.put(
          "value",
          I18nUtil.getMessage(I18nConst.PERCENTAGE)
              + ":"
              + CalculationUtil.changeDecimal(variant.getRatio() * 100, 2)
              + "%");
      valueList.add(map);
      map = new HashMap<>();
      map.put("value", I18nUtil.getMessage(I18nConst.CASE_NUM) + ":" + variant.getNumber());
      valueList.add(map);
      variantResultDTO.setValueList(valueList);

      variantResult.add(variantResultDTO);
    }
    return variantResult;
  }

  /**
   * 流程图KPI参数初始化
   *
   * @param processInitParam
   * @param processTreeBase
   */
  private void processCropInit(ProcessInitBase processInitParam, ProcessTreeBase processTreeBase) {
    Integer processTreeKpiId = processTreeBase.getProcessTreeKpiId();
    if (processTreeKpiId == null) {
      return;
    }
    ProcessTreeKpiPo processTreeKpiPo = processTreeKpiService.getById(processTreeKpiId);
    processInitParam.setProcessTreeKpiPo(processTreeKpiPo);
    List<ProcessTreeKpiRelationPo> processTreeKpiRelationPos =
        processTreeKpiRelationService.list(
            new LambdaQueryWrapper<ProcessTreeKpiRelationPo>()
                .eq(ProcessTreeKpiRelationPo::getProcessTreeKpiId, processTreeKpiId)
                .eq(ProcessTreeKpiRelationPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(ProcessTreeKpiRelationPo::getId));
    if (processTreeKpiRelationPos.isEmpty()) {
      return;
    }

    List<KpiPO> eventKpis = new ArrayList<>();
    List<KpiPO> lineKpis = new ArrayList<>();
    List<KpiPO> pathKpis = new ArrayList<>();
    Map<Integer, Integer> processKpiRelationMap =
        processTreeKpiRelationPos.stream()
            .collect(
                Collectors.toMap(
                    ProcessTreeKpiRelationPo::getKpiId, ProcessTreeKpiRelationPo::getType));
    List<Integer> kpiIds =
        processTreeKpiRelationPos.stream()
            .map(ProcessTreeKpiRelationPo::getKpiId)
            .sorted()
            .collect(Collectors.toList());
    List<KpiPO> kpiPOS = kpiService.listByIds(kpiIds);

    for (KpiPO kpiPO : kpiPOS) {
      ProcessKpiTypeEnum processKpiTypeEnumByCode =
          ProcessKpiTypeEnum.getProcessKpiTypeEnumByCode(processKpiRelationMap.get(kpiPO.getId()));
      if (processKpiTypeEnumByCode == null) {
        continue;
      }
      switch (processKpiTypeEnumByCode) {
        case EVENT:
          eventKpis.add(kpiPO);
          break;
        case LINE:
          lineKpis.add(kpiPO);
          break;
        case PATH:
          pathKpis.add(kpiPO);
          break;
      }
    }

    processInitParam.setEventKpis(eventKpis);
    processInitParam.setPathKpiList(pathKpis);
    processInitParam.setLineKpis(lineKpis);

    List<Integer> eventKpiIds = eventKpis.stream().map(KpiPO::getId).collect(Collectors.toList());

    // 颜色绑定
    int eventIndexOf = 0;
    if (processTreeKpiPo.getBondColourEventKpiId() != null) {
      int index = eventKpiIds.indexOf(processTreeKpiPo.getBondColourEventKpiId());
      if (index > 0) {
        eventIndexOf = index;
      }
    }
    processInitParam.setEventColourKpiIndex(eventIndexOf);

    int lineIndexOf = 0;
    List<Integer> lineKpiIds = lineKpis.stream().map(KpiPO::getId).collect(Collectors.toList());
    if (processTreeKpiPo.getBondColourLineKpiId() != null) {
      int index = lineKpiIds.indexOf(processTreeKpiPo.getBondColourLineKpiId());
      if (index > 0) {
        lineIndexOf = index;
      }
    }
    processInitParam.setLineColourKpiIndex(lineIndexOf);

    List<ColourInfo> eventColumnInfo =
        JSON.parseArray(processTreeKpiPo.getEventColour(), ColourInfo.class);
    List<ColourInfo> lineColumnInfo =
        JSON.parseArray(processTreeKpiPo.getLineColour(), ColourInfo.class);

    processInitParam.setEventColourInfos(eventColumnInfo);
    processInitParam.setLineColourInfos(lineColumnInfo);
  }

  private ProcessCrop processCropInit(
      ProcessInitBase processInitBase, ProcessTreeBase request, String activeEventColumn) {

    // 过滤项添加完成开始转sql
    ProcessCrop processCrop = null;
    List<TopicFilterPO> addFilterCrop =
        processInitBase.getCustomFilterPOS().stream()
            .filter(po -> Objects.equals(po.getType(), CROP_SELECTION.getValue()))
            .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(addFilterCrop)) {
      TopicFilterPO topicFilterPO = addFilterCrop.get(addFilterCrop.size() - 1);
      processCrop = this.getProcessCrop(topicFilterPO);
      processInitBase.setCalcCropToNullSql(
          ProcessTreeExprGenerator.calcCropToNullSqlBuild(topicFilterPO, activeEventColumn));
    } else {
      if (request.getIgnoreTopicProcessCropFilter()) {
        return null;
      }
      Boolean disableFilter = LocalParameter.disableFilterThreadLocal.get();
      if (Objects.equals(true, disableFilter)) {
        return null;
      }

      List<TopicFilterPO> topicFilters =
          topicFilterService.getFilterWithType(
              request.getTopicId(), null, null, CROP_SELECTION.getValue(), null);
      if (!topicFilters.isEmpty()) {
        processCrop = this.getProcessCrop(topicFilters.get(0));
        processInitBase.setCalcCropToNullSql(
            ProcessTreeExprGenerator.calcCropToNullSqlBuild(
                topicFilters.get(0), activeEventColumn));
      }
    }
    return processCrop;
  }
}
