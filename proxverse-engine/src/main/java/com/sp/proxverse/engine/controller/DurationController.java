package com.sp.proxverse.engine.controller;

import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.dto.AggCalcTimexyDTO;
import com.sp.proxverse.common.model.vo.duration.DurationKpiOutputVO;
import com.sp.proxverse.common.model.vo.duration.EventDurationListRequest;
import com.sp.proxverse.engine.service.biz.DurationBizService;
import com.sp.proxverse.engine.service.biz.ProcessSearchBizService;
import com.sp.proxverse.interfaces.service.remote.model.data.QueryDurationValueByTypeDTO;
import com.sp.proxverse.interfaces.service.remote.model.data.QueryEventDurationDiagramDTO;
import com.sp.proxverse.interfaces.service.remote.model.data.QueryEventDurationRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(value = "吞吐时间探索控制器", tags = "吞吐时间探索")
@RequestMapping(value = "/sp-engine")
@RestController
public class DurationController {

  @Autowired private DurationBizService durationBizService;

  @Autowired private ProcessSearchBizService processSearchBizService;

  @ApiOperation(value = "获取事件吞吐时间列表数据", notes = "获取事件吞吐时间列表数据")
  @PostMapping(value = "/getEventDurationList")
  public Response<List<QueryEventDurationRespDTO>> getEventDurationList(
      @Valid @RequestBody EventDurationListRequest request) {

    try {
      return Response.success(durationBizService.getEventDurationList(request));
    } catch (Exception e) {
      log.error("getEventDurationList error", e);
    }
    return Response.success(new ArrayList<>());
  }

  @ApiOperation(value = "获取事件吞吐柱状图数据", notes = "获取事件吞吐柱状图数据")
  @PostMapping(value = "/getEventDurationDiagram")
  public Response<List<AggCalcTimexyDTO>> getEventDurationDiagram(
      @Valid @RequestBody QueryEventDurationDiagramDTO request) {

    try {
      return Response.success(processSearchBizService.getEventDurationDiagram(request));
    } catch (Exception e) {
      log.error("getEventDurationDiagram error", e);
    }
    return Response.success(new ArrayList<>());
  }

  @ApiOperation(value = "获取吞吐时间页面kpi数据", notes = "获取吞吐时间页面kpi数据")
  @PostMapping(value = "/getDurationKpiData")
  public Response<List<DurationKpiOutputVO>> getDurationKpiData(
      @Valid @RequestBody QueryEventDurationDiagramDTO request) {

    return Response.success(durationBizService.getDurationKpiData(request));
  }

  @ApiOperation(value = "查询吞吐时间页面最快、最慢、平均、中位数", notes = "查询吞吐时间页面最快、最慢、平均、中位数")
  @PostMapping(value = "/queryDurationValueByType")
  public Response<String> queryDurationValueByType(
      @Valid @RequestBody QueryDurationValueByTypeDTO request) {

    try {
      return Response.success(durationBizService.queryDurationValueByType(request));
    } catch (Exception e) {
      log.error("queryDurationValueByType error ", e);
    }
    return Response.success("--");
  }
}
