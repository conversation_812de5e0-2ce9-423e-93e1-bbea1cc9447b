package com.sp.proxverse.datamerge.model;

import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class DeleteRequestDTO implements Serializable {

  private static final long serialVersionUID = -6694565099720086363L;

  @Tolerate
  public DeleteRequestDTO() {
    // comment empty
  }

  private Integer fileId;
  private String sql;
}
