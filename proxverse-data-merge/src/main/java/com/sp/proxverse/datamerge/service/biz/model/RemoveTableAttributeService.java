package com.sp.proxverse.datamerge.service.biz.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.*;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.datamerge.service.biz.DataModelCommonService;
import com.sp.proxverse.datamerge.service.biz.model.entity.RemoveTableAttribute;
import com.sp.proxverse.interfaces.dao.service.*;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RemoveTableAttributeService extends UpdateModelTemplate<RemoveTableAttribute> {

  @Autowired private DataModelFileService dataModelFileService;

  @Autowired private DataModelSortService dataModelSortService;

  @Autowired private DataModelFileMasterService dataModelFileMasterService;

  @Autowired private DataModelCommonService dataModelCommonService;

  @Autowired private FileService fileService;

  @Override
  void update(RemoveTableAttribute removeTableAttribute) {
    if (removeTableAttribute.getActiveFileId() != null) {
      this.removeActive(
          removeTableAttribute.getDataModelId(), removeTableAttribute.getActiveFileId());
    } else if (removeTableAttribute.getCaseFileId() != null) {
      this.removeCase(removeTableAttribute.getDataModelId(), removeTableAttribute.getCaseFileId());
    } else {
      // return
    }
    loadModelPublishService.publishModifyModelStatusEvent(removeTableAttribute.getDataModelId());
  }

  private void removeActive(Integer dataModel, Integer activeFileId) {
    dataModelFileService.update(
        new LambdaUpdateWrapper<DataModelFilePO>()
            .eq(DataModelFilePO::getDataModelId, dataModel)
            .eq(DataModelFilePO::getFileId, activeFileId)
            .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .set(DataModelFilePO::getActive, FileActiveTypeEnum.SECOND.getValue()));

    // 如果有案例表，要同时移除案例表及其连线
    DataModelFilePO caseFile = dataModelFileService.getCase(dataModel);
    if (caseFile != null) {
      caseFile.setActive(FileActiveTypeEnum.SECOND.getValue());
      dataModelFileService.updateById(caseFile);
    }

    // remove 三要素
    DataModelPO dataModelPo = dataModelService.getById(dataModel);
    dataModelPo.setCaseid(-1);
    dataModelPo.setEvent(-1);
    dataModelPo.setTime(-1);
    dataModelService.updateById(dataModelPo);

    dataModelSortService.remove(
        new LambdaQueryWrapper<DataModelSortPO>().eq(DataModelSortPO::getDataModelId, dataModel));
  }

  private void removeCase(Integer dataModelId, Integer caseFileId) {
    DataModelFilePO one =
        dataModelFileService.getOne(
            new LambdaQueryWrapper<DataModelFilePO>()
                .eq(DataModelFilePO::getDataModelId, dataModelId)
                .eq(DataModelFilePO::getFileId, caseFileId)
                .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last("LIMIT 1 "));
    if (Objects.isNull(one)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_MODEL_NOT_EXIST));
    }
    one.setActive(FileActiveTypeEnum.SECOND.getValue());
    dataModelFileService.updateById(one);
    DataModelFileMasterPO masterPO =
        dataModelFileMasterService.getOne(
            new LambdaQueryWrapper<DataModelFileMasterPO>()
                .eq(DataModelFileMasterPO::getDataModelId, dataModelId)
                .eq(DataModelFileMasterPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last("LIMIT 1"));
    if (Objects.nonNull(masterPO)) {
      DataModelFilePO active =
          dataModelFileService.getOne(
              new LambdaQueryWrapper<DataModelFilePO>()
                  .eq(DataModelFilePO::getDataModelId, dataModelId)
                  .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                  .eq(DataModelFilePO::getActive, FileActiveTypeEnum.ACTIVE.getValue())
                  .last("LIMIT 1"));

      FilePO activeFile = fileService.getById(active.getFileId());

      // 这里要再构建 一份case维度的建模语句
      String mergeSql = dataModelCommonService.buildMergeSql(dataModelId, activeFile);

      String caseMergeSql = dataModelCommonService.buildCaseMergeSql(active);
      masterPO.setCaseMergeSql(caseMergeSql);
      masterPO.setMergeSql(mergeSql);
      dataModelFileMasterService.updateById(masterPO);
    }
  }
}
