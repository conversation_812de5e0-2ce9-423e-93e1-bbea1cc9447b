package com.sp.proxverse.datamerge.model;

import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class QueryConformanceViewDataDTO implements Serializable {
  private static final long serialVersionUID = 2090307504616884308L;

  @Tolerate
  public QueryConformanceViewDataDTO() {
    // comment empty
  }

  private Integer fileId;
}
