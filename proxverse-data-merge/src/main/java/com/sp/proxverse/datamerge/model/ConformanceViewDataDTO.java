package com.sp.proxverse.datamerge.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class ConformanceViewDataDTO implements Serializable {

  private static final long serialVersionUID = 5309935201134816729L;

  @Tolerate
  public ConformanceViewDataDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "聚合时间年月")
  private String time;

  @ApiModelProperty(value = "caseId")
  private String caseId;
}
