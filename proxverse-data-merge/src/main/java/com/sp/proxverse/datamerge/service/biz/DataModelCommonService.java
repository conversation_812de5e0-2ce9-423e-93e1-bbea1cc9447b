package com.sp.proxverse.datamerge.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.dict.prehandler.PreHandlerVariableEnum;
import com.sp.proxverse.common.model.dto.ForeignDTO;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelForeignPO;
import com.sp.proxverse.common.model.po.FileFieldPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.DataModelForeignService;
import com.sp.proxverse.interfaces.dao.service.FileFieldService;
import com.sp.proxverse.interfaces.service.datahouse.TimelyModelingService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.common.model.DataTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DataModelCommonService {

  @Autowired private FileFieldService fileFieldService;

  @Autowired private DataModelFileService dataModelFileService;

  @Autowired private DataModelForeignService dataModelForeignService;

  @Autowired private TimelyModelingService timelyModelingService;
  private static final String LIMIT_1 = "LIMIT 1";

  public void checkCaseFields(Integer caseId, Integer event, Integer time, Integer startTime) {
    List<FileFieldPO> caseFielsdlist =
        fileFieldService.list(
            new LambdaQueryWrapper<FileFieldPO>()
                .eq(FileFieldPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .in(FileFieldPO::getId, Lists.newArrayList(caseId, event, time, startTime)));
    Map<Integer, FileFieldPO> fieldPOMap =
        caseFielsdlist.stream()
            .collect(Collectors.toMap(FileFieldPO::getId, Function.identity(), (k1, k2) -> k1));
    FileFieldPO caseIdPO = fieldPOMap.get(caseId);
    FileFieldPO eventPO = fieldPOMap.get(event);
    FileFieldPO timePO = fieldPOMap.get(time);
    if (Objects.isNull(caseIdPO) || Objects.isNull(eventPO) || Objects.isNull(timePO)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.CASE_ATTR_CHECK));
    }

    if (!DataTypeEnum.isTime(timePO.getFieldType())) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.TIME_DATA_TYPE_CHECK));
    }

    if (Objects.equals(DataTypeEnum.BOOL.getValue(), caseIdPO.getFieldType())) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.EVENT_DATA_TYPE_CHECK));
    }

    // 校验时间列, 开始时间列属于可选字段
    if (Objects.nonNull(startTime)) {
      FileFieldPO startTimePO = fieldPOMap.get(startTime);
      if (Objects.isNull(startTimePO)) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.CASE_ATTR_CHECK));
      }
      if (!DataTypeEnum.isTime(startTimePO.getFieldType())) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.TIME_DATA_TYPE_CHECK));
      }
    }
  }

  public void checkSortingList(List<Integer> sortingList) {
    if (CollectionUtils.isEmpty(sortingList)) {
      return;
    }
    List<FileFieldPO> sortFieldList = fileFieldService.listByIds(sortingList);
    boolean anyMatch =
        sortFieldList.stream()
            .anyMatch(f -> Objects.equals(f.getFieldType(), DataTypeEnum.BOOL.getValue()));
    if (anyMatch) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.SORTING_TIME_TYPE_DISABLED));
    }
  }

  public String buildMergeSql(Integer dataModelId, FilePO active) {
    if (active == null) {
      return "";
    }
    List<DataModelForeignPO> foreignPOList =
        dataModelForeignService.list(
            new LambdaQueryWrapper<DataModelForeignPO>()
                .eq(DataModelForeignPO::getDataModelId, dataModelId)
                .eq(DataModelForeignPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    List<ForeignDTO> foreignDTOList = buildForeignDto(foreignPOList, active);
    Integer caseFileId = dataModelFileService.getCaseFile(dataModelId);
    return timelyModelingService.buildMergeSql(foreignDTOList, dataModelId, caseFileId);
  }

  public List<ForeignDTO> buildForeignDto(List<DataModelForeignPO> list, FilePO activeId) {

    List<ForeignDTO> foreignDTOList = new ArrayList<>();
    ForeignDTO dto = new ForeignDTO();
    dto.setActive(true);
    dto.setFileId(activeId.getId());

    foreignDTOList.add(dto);

    if (list.stream().map(DataModelForeignPO::getFileId).distinct().count() == 1) {
      // 只有一条数据的话，下面的递归就不能正常使用了
      List<Integer> secondList =
          list.stream()
              .filter(
                  f ->
                      (Objects.equals(f.getFileId(), dto.getFileId())
                          || Objects.equals(f.getPointFileId(), dto.getFileId())))
              .map(DataModelForeignPO::getPointFileId)
              .distinct()
              .collect(Collectors.toList());
      List<ForeignDTO> nextList = new ArrayList<>();
      for (Integer second : secondList) {
        ForeignDTO foreignDTO = new ForeignDTO();
        foreignDTO.setFileId(second);
        foreignDTO.setActive(Objects.equals(second, activeId.getId()));
        foreignDTO.setPreForeign(dto);
        nextList.add(foreignDTO);
      }
      dto.setNextForeignList(nextList);
      return foreignDTOList;
    }

    List<Integer> parentFileId = new ArrayList<>();
    parentFileId.add(dto.getFileId());

    loopForeign(foreignDTOList, list, activeId.getId(), parentFileId);

    return foreignDTOList;
  }

  public void loopForeign(
      List<ForeignDTO> foreignDTOList,
      List<DataModelForeignPO> list,
      Integer activeId,
      List<Integer> parentFileId) {
    if (CollectionUtils.isEmpty(foreignDTOList)) {
      return;
    }
    for (ForeignDTO dto : foreignDTOList) {
      parentFileId.add(dto.getFileId());
      List<Integer> secondList =
          list.stream()
              .filter(
                  f ->
                      Objects.equals(f.getFileId(), dto.getFileId())
                          || Objects.equals(f.getPointFileId(), dto.getFileId()))
              .map(DataModelForeignPO::getPointFileId)
              .distinct()
              .collect(Collectors.toList());
      List<Integer> secondList2 =
          list.stream()
              .filter(
                  f ->
                      Objects.equals(f.getFileId(), dto.getFileId())
                          || Objects.equals(f.getPointFileId(), dto.getFileId()))
              .map(DataModelForeignPO::getFileId)
              .distinct()
              .collect(Collectors.toList());
      secondList.addAll(secondList2);
      secondList =
          secondList.stream()
              .filter(f -> !parentFileId.contains(f))
              .distinct()
              .collect(Collectors.toList());
      List<ForeignDTO> nextList = new ArrayList<>();
      for (Integer second : secondList) {
        ForeignDTO foreignDTO = new ForeignDTO();
        foreignDTO.setFileId(second);
        foreignDTO.setActive(Objects.equals(second, activeId));
        foreignDTO.setPreForeign(dto);

        nextList.add(foreignDTO);
      }
      dto.setNextForeignList(nextList);
      loopForeign(nextList, list, activeId, parentFileId);
    }
  }

  public String buildCaseMergeSql(DataModelFilePO dataModel) {
    if (dataModel == null) {
      return "";
    }
    DataModelFilePO caseFile =
        dataModelFileService.getOne(
            new LambdaQueryWrapper<DataModelFilePO>()
                .eq(DataModelFilePO::getDataModelId, dataModel.getDataModelId())
                .eq(DataModelFilePO::getActive, FileActiveTypeEnum.CASE.getValue())
                .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    DataModelFilePO virtualCaseFile =
        dataModelFileService.getOne(
            new LambdaQueryWrapper<DataModelFilePO>()
                .eq(DataModelFilePO::getDataModelId, dataModel.getDataModelId())
                .eq(DataModelFilePO::getActive, FileActiveTypeEnum.VIRTUAL_CASE.getValue())
                .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    List<DataModelForeignPO> foreignPOList =
        dataModelForeignService.list(
            new LambdaQueryWrapper<DataModelForeignPO>()
                .eq(DataModelForeignPO::getDataModelId, dataModel.getDataModelId())
                .eq(DataModelForeignPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    ForeignDTO caseForeignDto = null;
    if (Objects.isNull(caseFile)) {
      // 说明没有Case表，则此时要以虚拟case表来构建
      if (Objects.nonNull(virtualCaseFile)) {
        return virtualCaseFile.getSparkTableName();
      } else {
        return "";
      }
    } else {
      // 把虚拟case表接到case表的前头
      if (Objects.nonNull(virtualCaseFile)) {
        caseForeignDto = ForeignDTO.builder().fileId(virtualCaseFile.getFileId()).build();
        FileFieldPO virtualCaseId =
            fileFieldService.getOne(
                new LambdaQueryWrapper<FileFieldPO>()
                    .eq(FileFieldPO::getFileId, virtualCaseFile.getFileId())
                    .eq(FileFieldPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                    .eq(FileFieldPO::getFieldOrigin, PreHandlerVariableEnum.CASE_ID.getValue())
                    .last(LIMIT_1));

        DataModelForeignPO foreignPO =
            foreignPOList.stream()
                .filter(
                    f ->
                        (Objects.equals(f.getFileId(), dataModel.getFileId())
                                && Objects.equals(f.getPointFileId(), caseFile.getFileId()))
                            || (Objects.equals(f.getPointFileId(), dataModel.getFileId())
                                && Objects.equals(f.getFileId(), caseFile.getFileId())))
                .findFirst()
                .orElse(null);
        // 这里认为foreignPO不会为空
        DataModelForeignPO build =
            DataModelForeignPO.builder()
                .fileId(virtualCaseId.getFileId())
                .fieldId(virtualCaseId.getId())
                .pointFileId(foreignPO.getPointFileId())
                .pointFieldId(foreignPO.getPointFieldId())
                .build();
        foreignPOList.add(build);
      } else {
        caseForeignDto = ForeignDTO.builder().fileId(caseFile.getFileId()).build();
      }
    }

    // 这里把主表去掉，然后在这里面，一层层的构建 case表
    List<DataModelForeignPO> noActiveList =
        foreignPOList.stream()
            .filter(
                f ->
                    !Objects.equals(f.getFileId(), dataModel.getFileId())
                        && !Objects.equals(f.getPointFileId(), dataModel.getFileId()))
            .collect(Collectors.toList());

    List<ForeignDTO> foreignDTOList = Lists.newArrayList(caseForeignDto);

    List<Integer> parentIdList = Lists.newArrayList(caseForeignDto.getFileId());

    this.loopForeign(foreignDTOList, noActiveList, dataModel.getFileId(), parentIdList);

    String caseMergeSql =
        timelyModelingService.buildMergeSql(foreignDTOList, dataModel.getDataModelId(), null);

    return caseMergeSql;
  }
}
