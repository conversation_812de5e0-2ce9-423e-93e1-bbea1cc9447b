package com.sp.proxverse.datamerge.service.biz;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.base.ResponseUtil;
import com.sp.proxverse.common.model.dict.DataConnectorJDBCTypeEnum;
import com.sp.proxverse.common.model.dict.DataConnectorTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.TableLogStatusEnum;
import com.sp.proxverse.common.model.dict.TaskRateTypeEnum;
import com.sp.proxverse.common.model.dict.TaskStatusEnum;
import com.sp.proxverse.common.model.dto.connector.JdbcUrlDriverDTO;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.dto.result.StructFieldInfo;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.job.SaveJdbcConnectorDTO;
import com.sp.proxverse.common.model.job.TaskEnum;
import com.sp.proxverse.common.model.job.TaskLog;
import com.sp.proxverse.common.model.job.create.JdbcTaskGeneratorInfo;
import com.sp.proxverse.common.model.job.create.KafkaTaskGeneratorInfo;
import com.sp.proxverse.common.model.page.PageRespList;
import com.sp.proxverse.common.model.po.DataConnectorJdbcPO;
import com.sp.proxverse.common.model.po.DataConnectorPO;
import com.sp.proxverse.common.model.po.DataExtractorPO;
import com.sp.proxverse.common.model.po.DataPoolPO;
import com.sp.proxverse.common.model.po.DataTaskChildPo;
import com.sp.proxverse.common.model.po.DataTaskExtractorPO;
import com.sp.proxverse.common.model.po.DataTaskPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.model.po.LineageRelationPo;
import com.sp.proxverse.common.model.po.TaskExecutorPo;
import com.sp.proxverse.common.model.po.TaskLogPo;
import com.sp.proxverse.common.model.po.TaskTableLogPO;
import com.sp.proxverse.common.model.po.TransformationSqlPo;
import com.sp.proxverse.common.model.vo.base.DeleteReqVo;
import com.sp.proxverse.common.model.vo.dataconnector.HistoryTaskExecOutputVO;
import com.sp.proxverse.common.model.vo.datamodel.request.QueryDataMonitorRequest;
import com.sp.proxverse.common.model.vo.datatask.CheckCronExpressionRes;
import com.sp.proxverse.common.model.vo.datatask.DataConnector4SelectOutputVO;
import com.sp.proxverse.common.model.vo.datatask.DataExtractor4SelectOutputVO;
import com.sp.proxverse.common.model.vo.datatask.DataExtractorByConnectIdReq;
import com.sp.proxverse.common.model.vo.datatask.DataExtractorRes;
import com.sp.proxverse.common.model.vo.datatask.DataTaskChildListExtract;
import com.sp.proxverse.common.model.vo.datatask.DataTaskChildListRes;
import com.sp.proxverse.common.model.vo.datatask.DataTaskChildListTransformation;
import com.sp.proxverse.common.model.vo.datatask.DataTaskInfoRes;
import com.sp.proxverse.common.model.vo.datatask.DeleteDataTaskRequest;
import com.sp.proxverse.common.model.vo.datatask.ETLLineageRelationReq;
import com.sp.proxverse.common.model.vo.datatask.ETLLineageRelationRes;
import com.sp.proxverse.common.model.vo.datatask.EditDataTaskRequest;
import com.sp.proxverse.common.model.vo.datatask.EditTaskInfoOutputVO;
import com.sp.proxverse.common.model.vo.datatask.GetDataTaskListReq;
import com.sp.proxverse.common.model.vo.datatask.GetDataTaskLogReq;
import com.sp.proxverse.common.model.vo.datatask.LineageRelation;
import com.sp.proxverse.common.model.vo.datatask.PauseDataTaskRequest;
import com.sp.proxverse.common.model.vo.datatask.QueryDataConnector4SelectRequest;
import com.sp.proxverse.common.model.vo.datatask.QueryDataExtractor4SelectRequest;
import com.sp.proxverse.common.model.vo.datatask.QueryDataTaskEditInfoRequest;
import com.sp.proxverse.common.model.vo.datatask.QueryDataTaskExtractorRequest;
import com.sp.proxverse.common.model.vo.datatask.QueryDataTaskListRequest;
import com.sp.proxverse.common.model.vo.datatask.QueryDataTaskLogRequest;
import com.sp.proxverse.common.model.vo.datatask.RunDataTaskExtractsReq;
import com.sp.proxverse.common.model.vo.datatask.RunDataTaskExtractsRes;
import com.sp.proxverse.common.model.vo.datatask.RunDataTaskReq;
import com.sp.proxverse.common.model.vo.datatask.SaveDataTaskExtractorRequest;
import com.sp.proxverse.common.model.vo.datatask.SaveDataTaskRequest;
import com.sp.proxverse.common.model.vo.datatask.SaveOrUpdateDataTaskChildReq;
import com.sp.proxverse.common.model.vo.datatask.StartDataTaskRequest;
import com.sp.proxverse.common.model.vo.datatask.TaskConnectorListOutputVO;
import com.sp.proxverse.common.model.vo.datatask.TaskConnectorSubTaskOutputVO;
import com.sp.proxverse.common.model.vo.datatask.TaskExtractorListOutputVO;
import com.sp.proxverse.common.model.vo.datatask.TaskLogInfo;
import com.sp.proxverse.common.model.vo.datatask.UpdateDataTaskChildSortReq;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.common.util.JdbcUtil;
import com.sp.proxverse.interfaces.dao.impl.DataModelFileServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.DataTaskChildServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.LineageRelationServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TaskExecutorServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TaskLogServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TransformationSqlServiceImpl;
import com.sp.proxverse.interfaces.dao.service.DataConnectorJdbcService;
import com.sp.proxverse.interfaces.dao.service.DataConnectorService;
import com.sp.proxverse.interfaces.dao.service.DataExtractorService;
import com.sp.proxverse.interfaces.dao.service.DataPoolService;
import com.sp.proxverse.interfaces.dao.service.DataTaskExtractorService;
import com.sp.proxverse.interfaces.dao.service.DataTaskService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import com.sp.proxverse.interfaces.dao.service.TaskTableLogService;
import com.sp.proxverse.interfaces.service.data.DataAPIService;
import com.sp.proxverse.interfaces.service.data.FileStatistics;
import com.sp.proxverse.job.schedule.ScheduleTask;
import com.sp.proxverse.job.service.DynamicTaskServiceImpl;
import com.sp.proxverse.job.service.impl.IncrementalFileTaskGenerator;
import com.sp.proxverse.job.service.impl.JDBCTaskGenerator;
import com.sp.proxverse.job.service.impl.KafkaTaskGenerator;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DataTaskBizService {

  @Autowired private DataConnectorService dataConnectorService;

  @Autowired private DataPoolService dataPoolService;

  @Autowired private DataTaskExtractorService dataTaskExtractorService;

  @Autowired private DataExtractorService dataExtractorService;

  @Autowired private DataConnectorJdbcService dataConnectorJdbcService;

  @Autowired private TaskTableLogService taskTableLogService;

  @Autowired private DataTaskService dataTaskService;

  @Autowired private TaskLogServiceImpl taskLogService;

  @Autowired private FileService fileService;

  @Autowired private DataAPIService dataAPIService;

  @Value("${datahouse.label.set:}")
  private String eventSet;

  @Autowired private DataTaskChildServiceImpl dataTaskChildService;

  @Autowired TaskExecutorServiceImpl taskExecutorService;
  @Resource TransformationSqlServiceImpl transformationSqlService;

  @Autowired LineageRelationServiceImpl lineageRelationService;

  @Autowired DataModelFileServiceImpl dataModelFileService;

  @Autowired JDBCTaskGenerator jdbcJobCreate;

  @Autowired KafkaTaskGenerator kafkaTaskGenerator;

  @Autowired IncrementalFileTaskGenerator incrementalFileTaskGenerator;

  @Autowired ScheduleTask scheduleTask;

  @Autowired FileStatistics fileStatistics;

  @Autowired UserInfoUtil userInfoUtil;

  @Value("${prx.model.task.cronMinInterval:15}")
  private Integer cronMinInterval;

  @Autowired private DynamicTaskServiceImpl dynamicTaskService;
  private static final String LIMIT_1 = "LIMIT 1"; // sonar扫描

  public PageResp queryDataMonitorList(QueryDataMonitorRequest request) {

    if (Objects.nonNull(request.getPoolId())) {
      return this.buildTaskListData(
          Lists.newArrayList(request.getPoolId()),
          request.getPageNum(),
          request.getPageSize(),
          request.getStartTime(),
          request.getEndTime(),
          request.getStatus());
    }

    List<DataPoolPO> list =
        dataPoolService.list(
            new LambdaQueryWrapper<DataPoolPO>()
                .eq(DataPoolPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    List<Integer> collect = list.stream().map(DataPoolPO::getId).collect(Collectors.toList());
    return this.buildTaskListData(
        collect,
        request.getPageNum(),
        request.getPageSize(),
        request.getStartTime(),
        request.getEndTime(),
        request.getStatus());
  }

  public PageResp getHistoryTaskListHas(QueryDataTaskLogRequest request) {

    DataPoolPO byId = dataPoolService.getById(request.getPoolId());
    if (Objects.isNull(byId)
        || Objects.equals(byId.getDeleted(), DeletedEnum.HAS_DELETED.getValue())) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.POOL_NOT_EXIST));
    }

    return this.buildTaskListData(
        Lists.newArrayList(request.getPoolId()),
        request.getPageNum(),
        request.getPageSize(),
        null,
        null,
        null);
  }

  private PageResp buildTaskListData(
      List<Integer> poolIdList,
      Integer pageNum,
      Integer pageSize,
      String startTime,
      String endTime,
      Integer status) {

    if (CollectionUtils.isEmpty(poolIdList)) {
      return PageResp.success(
          PageRespList.builder().pageNum(pageNum).pageSize(pageSize).total(0L).build());
    }
    List<DataConnectorPO> connectorPOList =
        dataConnectorService.list(
            new LambdaQueryWrapper<DataConnectorPO>()
                .in(DataConnectorPO::getPoolId, poolIdList)
                .eq(DataConnectorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isEmpty(connectorPOList)) {
      return PageResp.success(
          PageRespList.builder().pageNum(pageNum).pageSize(pageSize).total(0L).build());
    }

    List<Integer> connectorIdList =
        connectorPOList.stream().map(DataConnectorPO::getId).collect(Collectors.toList());

    List<DataTaskPO> dataTaskPOList =
        dataTaskService.list(
            new LambdaQueryWrapper<DataTaskPO>()
                .in(DataTaskPO::getConnectorId, connectorIdList)
                .eq(DataTaskPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    if (CollectionUtils.isEmpty(dataTaskPOList)) {
      return PageResp.success(
          PageRespList.builder().pageNum(pageNum).pageSize(pageSize).total(0L).build());
    }

    List<Integer> dataTaskIdList =
        dataTaskPOList.stream().map(DataTaskPO::getId).collect(Collectors.toList());
    Map<Integer, DataTaskPO> dataTaskPOMap =
        dataTaskPOList.stream()
            .collect(Collectors.toMap(DataTaskPO::getId, Function.identity(), (k1, k2) -> k1));

    List<DataPoolPO> dataPoolPOList = dataPoolService.listByIds(poolIdList);
    Map<Integer, DataPoolPO> dataPoolPOMap =
        dataPoolPOList.stream()
            .collect(Collectors.toMap(DataPoolPO::getId, Function.identity(), (k1, k2) -> k1));

    PageRespList<HistoryTaskExecOutputVO> response = new PageRespList();
    response.setTotal(0L);
    response.setList(null);
    response.setPageNum(pageNum);
    response.setPageSize(pageSize);
    return PageResp.success(response);
  }

  public List<TaskConnectorListOutputVO> getTaskConnectorList(QueryDataTaskListRequest request) {

    DataPoolPO byId = dataPoolService.getById(request.getPoolId());
    if (Objects.isNull(byId)
        || Objects.equals(byId.getDeleted(), DeletedEnum.HAS_DELETED.getValue())) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.POOL_NOT_EXIST));
    }

    List<DataConnectorPO> connectorPOList =
        dataConnectorService.list(
            new LambdaQueryWrapper<DataConnectorPO>()
                .eq(DataConnectorPO::getPoolId, request.getPoolId())
                .eq(DataConnectorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isEmpty(connectorPOList)) {
      return new ArrayList<>();
    }

    List<Integer> connectorIdList =
        connectorPOList.stream().map(DataConnectorPO::getId).collect(Collectors.toList());
    List<DataTaskPO> taskList =
        dataTaskService.list(
            new LambdaQueryWrapper<DataTaskPO>()
                .in(DataTaskPO::getConnectorId, connectorIdList)
                .eq(DataTaskPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    Map<Integer, List<DataTaskPO>> taskGroup =
        taskList.stream().collect(Collectors.groupingBy(DataTaskPO::getConnectorId));

    return connectorPOList.stream()
        .map(
            m -> {
              TaskConnectorListOutputVO build =
                  TaskConnectorListOutputVO.builder()
                      .dataConnectorId(m.getId())
                      .dataConnectorName(m.getName())
                      .build();
              List<DataTaskPO> dataTaskPOList = taskGroup.get(m.getId());
              if (CollectionUtils.isEmpty(dataTaskPOList)) {
                return null;
              }
              List<TaskConnectorSubTaskOutputVO> subTaskList =
                  dataTaskPOList.stream()
                      .map(
                          a ->
                              TaskConnectorSubTaskOutputVO.builder()
                                  .dataTaskId(a.getId())
                                  .dataTaskName(a.getName())
                                  .build())
                      .collect(Collectors.toList());
              build.setTaskList(subTaskList);
              return build;
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  public List<TaskExtractorListOutputVO> getExtractorListByTaskId(
      QueryDataTaskExtractorRequest request) {
    DataTaskPO byId = dataTaskService.getById(request.getTaskId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    List<DataTaskExtractorPO> extractorPOList =
        dataTaskExtractorService.list(
            new LambdaQueryWrapper<DataTaskExtractorPO>()
                .eq(DataTaskExtractorPO::getTaskId, request.getTaskId())
                .eq(DataTaskExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isEmpty(extractorPOList)) {
      return new ArrayList<>();
    }

    List<Integer> extractorIdList =
        extractorPOList.stream()
            .map(DataTaskExtractorPO::getExtractorId)
            .collect(Collectors.toList());

    List<DataExtractorPO> dataExtractorPOList = dataExtractorService.listByIds(extractorIdList);
    Map<Integer, DataExtractorPO> extractorPOMap =
        dataExtractorPOList.stream()
            .collect(Collectors.toMap(DataExtractorPO::getId, Function.identity(), (k1, k2) -> k1));

    return extractorPOList.stream()
        .map(
            m -> {
              TaskExtractorListOutputVO build =
                  TaskExtractorListOutputVO.builder().taskExtractorId(m.getExtractorId()).build();
              DataExtractorPO dataExtractorPO = extractorPOMap.get(m.getExtractorId());
              if (Objects.nonNull(dataExtractorPO)) {
                build.setTaskExtractorName(dataExtractorPO.getName());
                build.setType(dataExtractorPO.getType());

                DataConnectorPO dataConnectorPO =
                    dataConnectorService.getById(dataExtractorPO.getConnectorId());
                build.setDataConnectorName(dataConnectorPO.getName());
                build.setStatus(dataExtractorPO.getStart());
              }

              return build;
            })
        .collect(Collectors.toList());
  }

  public Result getTaskLogList(QueryDataTaskExtractorRequest request) {
    DataTaskPO byId = dataTaskService.getById(request.getTaskId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    List<DataTaskExtractorPO> extractorPOList =
        dataTaskExtractorService.list(
            new LambdaQueryWrapper<DataTaskExtractorPO>()
                .eq(DataTaskExtractorPO::getTaskId, request.getTaskId())
                .eq(DataTaskExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isEmpty(extractorPOList)) {
      return null;
    }

    List<Integer> extractorIdList =
        extractorPOList.stream()
            .map(DataTaskExtractorPO::getExtractorId)
            .collect(Collectors.toList());

    List<DataExtractorPO> dataExtractorPoList = dataExtractorService.listByIds(extractorIdList);
    Map<Integer, DataExtractorPO> extractorPoMap =
        dataExtractorPoList.stream()
            .collect(
                Collectors.toMap(DataExtractorPO::getFileId, Function.identity(), (k1, k2) -> k1));

    List<Integer> fileIdList =
        dataExtractorPoList.stream().map(DataExtractorPO::getFileId).collect(Collectors.toList());
    Page<TaskTableLogPO> page = new Page<>(request.getPageNum(), request.getPageSize());
    LambdaQueryWrapper<TaskTableLogPO> wrapper =
        new LambdaQueryWrapper<TaskTableLogPO>()
            .in(TaskTableLogPO::getTableId, fileIdList)
            .orderByDesc(TaskTableLogPO::getId);
    Page<TaskTableLogPO> retPage = taskTableLogService.page(page, wrapper);
    List<TaskTableLogPO> logPoList = retPage.getRecords();

    StructFieldInfo[] metadata =
        new StructFieldInfo[] {
          StructFieldInfo.builder().columnName("提取时间").build(),
          StructFieldInfo.builder().columnName("提取名称").build(),
          StructFieldInfo.builder().columnName("执行时长").build(),
          StructFieldInfo.builder().columnName("状态").build()
        };

    String[][] data = new String[logPoList.size()][];
    int index = 0;
    for (TaskTableLogPO one : logPoList) {
      DataExtractorPO dataExtractor = extractorPoMap.get(one.getTableId());
      String[] subData =
          new String[] {
            DateTimeUtil.date2String(one.getStartTime()),
            dataExtractor.getName(),
            DateTimeUtil.timeDec(one.getStartTime(), one.getEndTime()).toString(),
            one.getStatus().toString()
          };
      data[index] = subData;
      index++;
    }

    return Result.builder().metadata(metadata).data(data).count(retPage.getTotal()).build();
  }

  public Integer saveDataTask(SaveDataTaskRequest request) {

    DataTaskPO dataTaskPO =
        DataTaskPO.builder()
            .name(request.getName())
            .poolId(request.getPoolId())
            .rateType(request.getRateType())
            .minute(Objects.isNull(request.getMinute()) ? 0 : request.getMinute())
            .hour(request.getHour())
            .week(request.getWeek())
            .cronExpression(request.getCronExpression())
            .build();

    dataTaskPO.setNextExecutionTime(
        DateTimeUtil.calNextExecutionTime(
            request.getTateTypeEnum(), request.getMinute(), request.getHour(), request.getWeek()));
    dataTaskService.save(dataTaskPO);

    if (TaskRateTypeEnum.CRON.getValue().equals(dataTaskPO.getRateType())) {
      dynamicTaskService.rescheduleTask(dataTaskPO);
    }
    return dataTaskPO.getId();
  }

  private Date calPreDate(Date preDate, Integer minute) {
    String date = DateTimeUtil.date2String(preDate);
    if (date.length() == 0) {
      return new Date();
    }
    String min = "";
    if (minute > 9) {
      min = minute + "";
    } else {
      min = "0" + minute;
    }
    String substring = date.substring(0, date.length() - 2);
    Date string2Date = DateTimeUtil.string2Date(substring + min);
    return string2Date;
  }

  public EditTaskInfoOutputVO getDataTaskEditInfo(QueryDataTaskEditInfoRequest request) {
    DataTaskPO byId = dataTaskService.getById(request.getTaskId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    return EditTaskInfoOutputVO.builder()
        .taskId(byId.getId())
        .name(byId.getName())
        .rateType(byId.getRateType())
        .minute(byId.getMinute())
        .hour(byId.getHour())
        .week(byId.getWeek())
        .build();
  }

  public Integer editDataTask(EditDataTaskRequest request) {
    DataTaskPO dataTaskPO =
        DataTaskPO.builder()
            .id(request.getTaskId())
            .name(request.getName())
            .rateType(request.getRateType())
            .minute(Objects.isNull(request.getMinute()) ? 0 : request.getMinute())
            .hour(request.getHour())
            .week(request.getWeek())
            .cronExpression(request.getCronExpression())
            .tenantId(userInfoUtil.getGroupId())
            .build();

    List<DataTaskPO> dataTaskPOList = new ArrayList<>();

    dataTaskPOList.add(dataTaskPO);
    dataTaskService.updateNextExecutionTime(dataTaskPOList);

    dynamicTaskService.cancelTask(dataTaskPO.getId());
    if (TaskRateTypeEnum.CRON.getValue().equals(dataTaskPO.getRateType())) {
      dynamicTaskService.rescheduleTask(dataTaskPO);
    }
    return request.getTaskId();
  }

  public Boolean saveDataTaskExtractor(SaveDataTaskExtractorRequest request) {

    long count =
        dataTaskExtractorService.count(
            new LambdaQueryWrapper<DataTaskExtractorPO>()
                .eq(DataTaskExtractorPO::getExtractorId, request.getExtractorId())
                .eq(DataTaskExtractorPO::getTaskId, request.getTaskId())
                .eq(DataTaskExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (count > 0) {
      return Boolean.FALSE;
    }

    DataExtractorPO extractorPO = dataExtractorService.getById(request.getExtractorId());
    if (Objects.isNull(extractorPO)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.EXACTOR_NOT_EXIST));
    }
    DataTaskExtractorPO po =
        DataTaskExtractorPO.builder()
            .taskId(request.getTaskId())
            .fileId(extractorPO.getFileId())
            .extractorId(request.getExtractorId())
            .build();
    dataTaskExtractorService.save(po);

    String taskName = "taskjdbc_" + po.getId();
    po.setTaskName(taskName);
    dataTaskExtractorService.updateById(po);

    DataConnectorJdbcPO jdbcPO =
        dataConnectorJdbcService.getOne(
            new LambdaQueryWrapper<DataConnectorJdbcPO>()
                .eq(DataConnectorJdbcPO::getConnectorId, extractorPO.getConnectorId())
                .eq(DataConnectorJdbcPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));
    if (Objects.isNull(jdbcPO)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_CONNECT_NOT_EXIST));
    }

    DataConnectorPO connect = dataConnectorService.getById(jdbcPO.getConnectorId());

    TaskTableLogPO logPO = new TaskTableLogPO();
    logPO.setTableId(extractorPO.getFileId());
    logPO.setStartTime(new Date());

    // 这个URL要根据jdbc_type来构建了
    JdbcUrlDriverDTO jdbcUrlDriverDTO = JdbcUtil.buildUrl(connect, jdbcPO);
    SaveJdbcConnectorDTO dto =
        SaveJdbcConnectorDTO.builder()
            .fileId(extractorPO.getFileId())
            .url(jdbcUrlDriverDTO.getUrl())
            .user(jdbcPO.getUsername())
            .password(jdbcPO.getPassword())
            .eventset(eventSet)
            .name(taskName)
            .whereRule(extractorPO.getWhereRule())
            .source(extractorPO.getFileId())
            .poolId(extractorPO.getPoolId())
            .tempTableName("`temp_" + extractorPO.getSparkTableName().replace("`", "") + "`")
            .tableName(extractorPO.getSparkTableName())
            .schemaName(jdbcPO.getSchemaName())
            .jdbcTableName(extractorPO.getTableName())
            .jdbcDriver(jdbcUrlDriverDTO.getDriver())
            .build();
    if (Objects.equals(connect.getJdbcType(), DataConnectorJDBCTypeEnum.ORACLE.getValue())) {
      dto.setJdbcTableName(jdbcPO.getDbname() + "." + extractorPO.getTableName());
    }
    if (Objects.equals(connect.getJdbcType(), DataConnectorJDBCTypeEnum.DB2.getValue())) {
      dto.setJdbcTableName(jdbcPO.getSchemaName() + "." + extractorPO.getTableName());
    }
    log.info("创建jdbc链接器，param={}", JSON.toJSONString(dto));
    Response<Boolean> jdbcTable = dataAPIService.createJDBCTable(dto);
    if (!jdbcTable.getData()) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.SAVE_CONNECT_ERROR));
    }
    return true;
  }

  public List<DataConnector4SelectOutputVO> getDataConnectorList4Query(
      QueryDataConnector4SelectRequest request) {
    List<DataConnectorPO> list =
        dataConnectorService.list(
            new LambdaQueryWrapper<DataConnectorPO>()
                .eq(DataConnectorPO::getPoolId, request.getPoolId())
                .eq(DataConnectorPO::getType, DataConnectorTypeEnum.JDBC.getValue())
                .eq(DataConnectorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    return list.stream()
        .map(
            m ->
                DataConnector4SelectOutputVO.builder()
                    .dataConnectorId(m.getId())
                    .name(m.getName())
                    .build())
        .collect(Collectors.toList());
  }

  public List<DataExtractor4SelectOutputVO> getDataExtractorList4Query(
      QueryDataExtractor4SelectRequest request) {
    DataTaskPO byId = dataTaskService.getById(request.getTaskId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    List<DataTaskExtractorPO> taskExtractorPOS =
        dataTaskExtractorService.list(
            new LambdaQueryWrapper<DataTaskExtractorPO>()
                .eq(DataTaskExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    List<Integer> collect =
        taskExtractorPOS.stream()
            .map(DataTaskExtractorPO::getExtractorId)
            .collect(Collectors.toList());

    List<DataExtractorPO> list =
        dataExtractorService.list(
            new LambdaQueryWrapper<DataExtractorPO>()
                .eq(DataExtractorPO::getConnectorId, byId.getConnectorId())
                .notIn(CollectionUtils.isNotEmpty(collect), DataExtractorPO::getId, collect)
                .eq(DataExtractorPO::getStatus, 1)
                .eq(DataExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    List<DataExtractorPO> hasSelectList = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(collect)) {
      hasSelectList =
          dataExtractorService.list(
              new LambdaQueryWrapper<DataExtractorPO>()
                  .eq(DataExtractorPO::getConnectorId, byId.getConnectorId())
                  .in(CollectionUtils.isNotEmpty(collect), DataExtractorPO::getId, collect)
                  .eq(DataExtractorPO::getStatus, 1)
                  .eq(DataExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      list.addAll(hasSelectList);
    }

    Map<Integer, DataExtractorPO> hasSelectMap =
        hasSelectList.stream()
            .collect(Collectors.toMap(DataExtractorPO::getId, Function.identity(), (k1, k2) -> k1));

    return list.stream()
        .map(
            m -> {
              DataExtractor4SelectOutputVO build =
                  DataExtractor4SelectOutputVO.builder()
                      .dataExtractorId(m.getId())
                      .name(m.getName())
                      .build();
              DataExtractorPO dataExtractorPO = hasSelectMap.get(m.getId());
              build.setStatus(Objects.isNull(dataExtractorPO) ? 0 : 1);
              return build;
            })
        .collect(Collectors.toList());
  }

  public Boolean deleteDataTask(DeleteDataTaskRequest request) {
    DataTaskPO byId = dataTaskService.getById(request.getTaskId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    byId.setDeleted(DeletedEnum.HAS_DELETED.getValue());
    dataTaskService.updateById(byId);

    dataTaskChildService.update(
        new LambdaUpdateWrapper<DataTaskChildPo>()
            .eq(DataTaskChildPo::getDataTaskId, byId.getId())
            .set(DataTaskChildPo::getDeleted, DeletedEnum.HAS_DELETED.getValue()));

    // 这里要删除任务下与提取的关联关系
    dataTaskExtractorService.update(
        new LambdaUpdateWrapper<DataTaskExtractorPO>()
            .set(DataTaskExtractorPO::getDeleted, DeletedEnum.HAS_DELETED.getValue())
            .eq(DataTaskExtractorPO::getTaskId, request.getTaskId()));

    return Boolean.TRUE;
  }

  public Boolean pauseDataTask(PauseDataTaskRequest request) {
    DataExtractorPO byId = dataExtractorService.getById(request.getExtractorId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    byId.setStart(TaskStatusEnum.PAUSE.getValue());
    dataExtractorService.updateById(byId);

    // todo 手动再暂停一下数仓的jdbc链接器
    // todo 其实也不用在这里手动关数仓，因为我每执行一次数仓，都会在当次结束时关掉数仓的这个jdbc链接

    return Boolean.TRUE;
  }

  public Boolean startDataTask(StartDataTaskRequest request) {
    DataExtractorPO byId = dataExtractorService.getById(request.getExtractorId());
    if (Objects.isNull(byId)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DATA_TASK_NOT_EXIST));
    }
    byId.setStart(TaskStatusEnum.START.getValue());
    dataExtractorService.updateById(byId);

    return Boolean.TRUE;
  }

  public List<DataTaskInfoRes> getDataTaskList(GetDataTaskListReq getDataTaskListReq) {
    List<DataTaskPO> taskList =
        dataTaskService.list(
            new LambdaQueryWrapper<DataTaskPO>()
                .eq(DataTaskPO::getPoolId, getDataTaskListReq.getPoolId())
                .eq(DataTaskPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    List<DataTaskInfoRes> results = new ArrayList<>();
    for (DataTaskPO dataTaskPO : taskList) {

      DataTaskInfoRes result = new DataTaskInfoRes();
      result.setRateType(dataTaskPO.getRateType());
      result.setHour(dataTaskPO.getHour());
      result.setWeek(dataTaskPO.getWeek());
      result.setMinute(dataTaskPO.getMinute());
      result.setCronExpression(dataTaskPO.getCronExpression());
      result.setDataTaskId(dataTaskPO.getId());
      result.setDataTaskName(dataTaskPO.getName());
      results.add(result);
    }

    return results;
  }

  public Integer saveOrUpdateDataTaskChild(
      SaveOrUpdateDataTaskChildReq saveOrUpdateDataTaskChildReq) {

    Integer dataTaskChildId = saveOrUpdateDataTaskChildReq.getDataTaskChildId();
    DataTaskChildPo dataTaskChildPo = new DataTaskChildPo();
    dataTaskChildPo.setName(saveOrUpdateDataTaskChildReq.getName());
    dataTaskChildPo.setPoolId(saveOrUpdateDataTaskChildReq.getPoolId());
    dataTaskChildPo.setDataTaskId(saveOrUpdateDataTaskChildReq.getDataTaskId());
    dataTaskChildPo.setTaskType(saveOrUpdateDataTaskChildReq.getChildType());
    dataTaskChildPo.setState(saveOrUpdateDataTaskChildReq.getState());

    if (dataTaskChildId == null || dataTaskChildId <= 0) {
      // 计算sort
      List<DataTaskChildPo> dataTaskChildPos =
          dataTaskChildService.list(
              new LambdaQueryWrapper<DataTaskChildPo>()
                  .eq(DataTaskChildPo::getPoolId, saveOrUpdateDataTaskChildReq.getPoolId())
                  .eq(DataTaskChildPo::getDataTaskId, saveOrUpdateDataTaskChildReq.getDataTaskId())
                  .eq(DataTaskChildPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                  .orderByDesc(DataTaskChildPo::getSort)
                  .last(LIMIT_1));
      int sort = 1;
      if (CollectionUtils.isNotEmpty(dataTaskChildPos)) {
        sort = dataTaskChildPos.get(0).getSort() + 1;
      }
      dataTaskChildPo.setSort(sort);
      Integer bindingId;
      switch (saveOrUpdateDataTaskChildReq.getChildType()) {
        case 0:
          bindingId = saveOrUpdateDataTaskChildReq.getDataExtractorID();
          break;
        case 1:
          TransformationSqlPo transformationSqlPo = new TransformationSqlPo();
          transformationSqlPo.setDataPoolId(saveOrUpdateDataTaskChildReq.getPoolId());
          transformationSqlPo.setName(saveOrUpdateDataTaskChildReq.getName());
          transformationSqlService.save(transformationSqlPo);
          bindingId = transformationSqlPo.getId();
          break;

        default:
          throw new BizException(5000, "childType error");
      }
      dataTaskChildPo.setBindingId(bindingId);
      dataTaskChildService.save(dataTaskChildPo);

    } else {
      dataTaskChildPo.setId(dataTaskChildId);
      dataTaskChildService.updateById(dataTaskChildPo);
      DataTaskChildPo dataTaskChildPoSelect = dataTaskChildService.getById(dataTaskChildId);
      if (saveOrUpdateDataTaskChildReq.getChildType() == 1
          && StringUtils.isNotBlank(saveOrUpdateDataTaskChildReq.getName())) {
        TransformationSqlPo transformationSqlPo = new TransformationSqlPo();
        transformationSqlPo.setId(dataTaskChildPoSelect.getBindingId());
        transformationSqlPo.setName(saveOrUpdateDataTaskChildReq.getName());
        transformationSqlService.updateById(transformationSqlPo);
      }
    }
    return dataTaskChildPo.getId();
  }

  public Boolean updateDataTaskChildSort(UpdateDataTaskChildSortReq updateDataTaskChildSortReq) {
    DataTaskChildPo dataTaskChildPo =
        dataTaskChildService.getById(updateDataTaskChildSortReq.getBaseDataTaskChildId());
    if (dataTaskChildPo == null) {
      return false;
    }
    DataTaskChildPo swapDataTaskChildPo;
    switch (updateDataTaskChildSortReq.getType()) {
      case 0:
        List<DataTaskChildPo> dataTaskChildPos =
            dataTaskChildService.list(
                new LambdaQueryWrapper<DataTaskChildPo>()
                    .eq(DataTaskChildPo::getDataTaskId, dataTaskChildPo.getDataTaskId())
                    .eq(DataTaskChildPo::getTaskType, dataTaskChildPo.getTaskType())
                    .eq(DataTaskChildPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                    .lt(DataTaskChildPo::getSort, dataTaskChildPo.getSort())
                    .orderByDesc(DataTaskChildPo::getSort)
                    .last(LIMIT_1));
        if (dataTaskChildPos.size() == 0) {
          return false;
        }
        swapDataTaskChildPo = dataTaskChildPos.get(0);
        break;
      case 1:
        List<DataTaskChildPo> dataTaskChildPoDowns =
            dataTaskChildService.list(
                new LambdaQueryWrapper<DataTaskChildPo>()
                    .eq(DataTaskChildPo::getDataTaskId, dataTaskChildPo.getDataTaskId())
                    .eq(DataTaskChildPo::getTaskType, dataTaskChildPo.getTaskType())
                    .eq(DataTaskChildPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                    .gt(DataTaskChildPo::getSort, dataTaskChildPo.getSort())
                    .orderByAsc(DataTaskChildPo::getSort)
                    .last(LIMIT_1));
        if (dataTaskChildPoDowns.size() == 0) {
          return false;
        }
        swapDataTaskChildPo = dataTaskChildPoDowns.get(0);
        break;
      case 2:
        if (updateDataTaskChildSortReq.getSwapDataTaskChildId() == null) {
          return false;
        }
        swapDataTaskChildPo =
            dataTaskChildService.getById(updateDataTaskChildSortReq.getSwapDataTaskChildId());
        break;
      default:
        return false;
    }

    Integer sort = dataTaskChildPo.getSort();
    dataTaskChildPo.setSort(swapDataTaskChildPo.getSort());
    swapDataTaskChildPo.setSort(sort);
    dataTaskChildService.updateById(swapDataTaskChildPo);
    dataTaskChildService.updateById(dataTaskChildPo);

    return true;
  }

  public DataTaskChildListRes getDataTaskChildList(Integer dataTaskId) {
    List<DataTaskChildPo> dataTaskChildPos =
        dataTaskChildService.list(
            new LambdaQueryWrapper<DataTaskChildPo>()
                .eq(DataTaskChildPo::getDataTaskId, dataTaskId)
                .eq(DataTaskChildPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(DataTaskChildPo::getSort));
    Map<Integer, List<DataTaskChildPo>> collect =
        dataTaskChildPos.stream().collect(Collectors.groupingBy(DataTaskChildPo::getTaskType));

    DataTaskChildListRes dataTaskChildListRes = new DataTaskChildListRes();
    if (collect.containsKey(0)) {
      List<DataTaskChildPo> dataTaskChildPosExtract = collect.get(0);
      List<Integer> dataExtractorIds =
          dataTaskChildPosExtract.stream()
              .map(DataTaskChildPo::getBindingId)
              .collect(Collectors.toList());
      if (!dataExtractorIds.isEmpty()) {
        List<DataExtractorPO> dataExtractorPOS = dataExtractorService.listByIds(dataExtractorIds);
        Map<Integer, DataExtractorPO> dataExtractorPOMap =
            dataExtractorPOS.stream()
                .collect(
                    Collectors.toMap(DataExtractorPO::getId, DataExtractorPO -> DataExtractorPO));
        List<Integer> connectionIds =
            dataExtractorPOS.stream()
                .map(DataExtractorPO::getConnectorId)
                .collect(Collectors.toList());

        Map<Integer, DataConnectorPO> dataConnectorPOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(connectionIds)) {
          List<DataConnectorPO> dataConnectorPOS = dataConnectorService.listByIds(connectionIds);
          dataConnectorPOMap =
              dataConnectorPOS.stream().collect(Collectors.toMap(DataConnectorPO::getId, po -> po));
        }

        for (DataTaskChildPo dataTaskChildPo : dataTaskChildPosExtract) {
          if (dataConnectorPOMap.containsKey(
              dataExtractorPOMap.get(dataTaskChildPo.getBindingId()).getConnectorId())) {
            DataExtractorPO dataExtractorPO =
                dataExtractorPOMap.get(dataTaskChildPo.getBindingId());
            DataConnectorPO dataConnectorPO =
                dataConnectorPOMap.get(
                    dataExtractorPOMap.get(dataTaskChildPo.getBindingId()).getConnectorId());
            DataTaskChildListExtract extract = new DataTaskChildListExtract();
            extract.setDataTaskChildId(dataTaskChildPo.getId());
            extract.setDataExtractId(dataExtractorPO.getId());
            extract.setName(dataExtractorPO.getName());
            extract.setDataTaskId(dataTaskId);
            extract.setState(dataTaskChildPo.getState());
            extract.setConnectName(dataConnectorPO.getName());
            extract.setType(
                DataConnectorTypeEnum.getDataConnectorTypeEnumByValue(dataConnectorPO.getType())
                    .getName());
            dataTaskChildListRes.getExtracts().add(extract);
          }
        }
      }
    }

    if (collect.containsKey(1)) {
      List<DataTaskChildPo> dataTaskChildPosTransformation = collect.get(1);
      for (DataTaskChildPo dataTaskChildPo : dataTaskChildPosTransformation) {
        DataTaskChildListTransformation dataTaskChildListTransformation =
            new DataTaskChildListTransformation();
        dataTaskChildListTransformation.setTransformationId(dataTaskChildPo.getBindingId());
        dataTaskChildListTransformation.setDataTaskChildId(dataTaskChildPo.getId());
        dataTaskChildListTransformation.setName(dataTaskChildPo.getName());
        dataTaskChildListTransformation.setDataTaskId(dataTaskChildPo.getDataTaskId());
        dataTaskChildListTransformation.setState(dataTaskChildPo.getState());
        dataTaskChildListRes.getTransformations().add(dataTaskChildListTransformation);
      }
    }

    return dataTaskChildListRes;
  }

  public Boolean deleteDataTaskChild(DeleteReqVo deleteReqVo) {
    DataTaskChildPo dataTaskChildPo = new DataTaskChildPo();
    dataTaskChildPo.setId(deleteReqVo.getId());
    dataTaskChildPo.setDeleted(DeletedEnum.HAS_DELETED.getValue());
    return dataTaskChildService.updateById(dataTaskChildPo);
  }

  public List<DataExtractorRes> getDataExtractorByConnectId(
      DataExtractorByConnectIdReq dataExtractorByConnectIdReq) {
    Integer connectId = dataExtractorByConnectIdReq.getConnectId();
    List<DataExtractorPO> dataExtractorPOS =
        dataExtractorService.list(
            new LambdaQueryWrapper<DataExtractorPO>()
                .eq(connectId != null && connectId > 0, DataExtractorPO::getConnectorId, connectId)
                .eq(DataExtractorPO::getStatus, 1)
                .eq(DataExtractorPO::getPoolId, dataExtractorByConnectIdReq.getPool())
                .eq(DataExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    List<DataExtractorRes> dataExtractorRes = new ArrayList<>();
    for (DataExtractorPO dataExtractorPO : dataExtractorPOS) {
      DataExtractorRes result = new DataExtractorRes();
      result.setDataExtractorId(dataExtractorPO.getId());
      result.setName(dataExtractorPO.getName());
      dataExtractorRes.add(result);
    }
    return dataExtractorRes;
  }

  public PageResp getDataTaskLog(GetDataTaskLogReq getDataTaskLogReq) {
    Page<TaskLogPo> page =
        new Page<>(getDataTaskLogReq.getPageNum(), getDataTaskLogReq.getPageSize());
    LambdaQueryWrapper<TaskLogPo> taskLogPoLambdaQueryWrapper =
        new LambdaQueryWrapper<TaskLogPo>()
            .eq(TaskLogPo::getDataTaskId, getDataTaskLogReq.getDataTaskId());
    long count = taskLogService.count(taskLogPoLambdaQueryWrapper);
    taskLogPoLambdaQueryWrapper = taskLogPoLambdaQueryWrapper.orderByDesc(TaskLogPo::getId);
    Page<TaskLogPo> poPage = taskLogService.page(page, taskLogPoLambdaQueryWrapper);
    List<TaskLogPo> records = poPage.getRecords();
    List<TaskLogInfo> taskLogInfos = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(records)) {
      List<TaskExecutorPo> taskExecutorPos =
          taskExecutorService.listByIds(
              records.stream().map(TaskLogPo::getTaskExecutorId).collect(Collectors.toList()));
      Map<Integer, String> taskExecutorNameById =
          taskExecutorPos.stream()
              .collect(Collectors.toMap(TaskExecutorPo::getId, TaskExecutorPo::getTaskName));
      for (TaskLogPo record : records) {
        TaskLogInfo taskLogInfo = new TaskLogInfo();
        taskLogInfo.setId(record.getId());
        taskLogInfo.setTime(DateTimeUtil.date2String(record.getStartTime()));
        if (taskExecutorNameById.containsKey(record.getTaskExecutorId())) {
          taskLogInfo.setName(taskExecutorNameById.get(record.getTaskExecutorId()));
        } else {
          taskLogInfo.setName(record.getTaskName());
        }
        taskLogInfo.setRunType(record.getRunType());
        taskLogInfo.setRunTime(record.calcRunTime());
        taskLogInfo.setTaskType(TaskEnum.getTaskEnum(record.getTaskEnum()).getValue());
        taskLogInfo.setState(record.getStatus());
        taskLogInfo.setInfo(record.getLogInfo());
        taskLogInfos.add(taskLogInfo);
      }
    }

    PageRespList<TaskLogInfo> response = new PageRespList();
    response.setTotal(count);
    response.setList(taskLogInfos);
    response.setPageNum(getDataTaskLogReq.getPageNum());
    response.setPageSize(getDataTaskLogReq.getPageSize());
    return PageResp.success(response);
  }

  public RunDataTaskExtractsRes runDataTaskExtracts(RunDataTaskExtractsReq runDataTaskExtractsReq) {
    RunDataTaskExtractsRes runDataTaskExtractsRes = new RunDataTaskExtractsRes();

    LocalDateTime startTime = LocalDateTime.now();
    TaskLogPo taskLogPo = new TaskLogPo();
    taskLogPo.setStartTime(startTime);
    taskLogPo.setTaskEnum(TaskEnum.DATA_EXTRACTION.name());
    taskLogPo.setDataTaskId(runDataTaskExtractsReq.getDataTaskId());
    taskLogPo.setStatus(TableLogStatusEnum.LOADING.getValue());
    taskLogService.save(taskLogPo);

    DataExtractorPO extractorPO =
        dataExtractorService.getById(runDataTaskExtractsReq.getDataExtractId());
    DataConnectorPO connect = dataConnectorService.getById(extractorPO.getConnectorId());
    DataConnectorJdbcPO jdbcPO =
        dataConnectorJdbcService.getOne(
            new LambdaQueryWrapper<DataConnectorJdbcPO>()
                .eq(DataConnectorJdbcPO::getConnectorId, extractorPO.getConnectorId())
                .eq(DataConnectorJdbcPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    JdbcUrlDriverDTO jdbcUrlDriverDTO = JdbcUtil.buildUrl(connect, jdbcPO);

    SaveJdbcConnectorDTO dto =
        SaveJdbcConnectorDTO.builder()
            .fileId(extractorPO.getFileId())
            .url(jdbcUrlDriverDTO.getUrl())
            .user(jdbcPO.getUsername())
            .password(jdbcPO.getPassword())
            .eventset(eventSet)
            .whereRule(extractorPO.getWhereRule())
            .source(extractorPO.getFileId())
            .poolId(extractorPO.getPoolId())
            .tempTableName("`temp_" + extractorPO.getSparkTableName().replace("`", "") + "`")
            .tableName(extractorPO.getSparkTableName())
            .schemaName(jdbcPO.getSchemaName())
            .jdbcTableName(extractorPO.getTableName())
            .jdbcDriver(jdbcUrlDriverDTO.getDriver())
            .updateType(extractorPO.getUpdateType())
            .build();
    if (Objects.equals(connect.getJdbcType(), DataConnectorJDBCTypeEnum.ORACLE.getValue())) {
      dto.setJdbcTableName(jdbcPO.getDbname() + "." + extractorPO.getTableName());
    }
    if (Objects.equals(connect.getJdbcType(), DataConnectorJDBCTypeEnum.DB2.getValue())) {
      dto.setJdbcTableName(jdbcPO.getSchemaName() + "." + extractorPO.getTableName());
    }

    log.info("创建jdbc链接器，param={}", JSON.toJSONString(dto));
    if (!dataAPIService.isExistTable(dto).getData()) {
      dataAPIService.createJDBCTable(dto);
    }

    Response<Long> booleanResponse = dataAPIService.pullJdbcData(dto);
    TaskLog taskLog = new TaskLog();
    taskLog.setFileId(dto.getFileId());
    if (ResponseUtil.isSucc(booleanResponse)) {
      taskLogPo.setStatus(TableLogStatusEnum.SUCCESS.getValue());
      // 这里把file的status改为1
      fileService.updateById(
          FilePO.builder()
              .id(extractorPO.getFileId())
              .uploadStatus(1)
              .status(2)
              .updateTime(new Date())
              .build());
      runDataTaskExtractsRes.setMessage("success");
      runDataTaskExtractsRes.setDataCount(booleanResponse.getData());
      taskLog.setNewDataRowsCount(booleanResponse.getData());
      taskLog.setMessage("success");
    } else {
      runDataTaskExtractsRes.setMessage(booleanResponse.getMsg());
      taskLogPo.setStatus(TableLogStatusEnum.FAIL.getValue());
      taskLog.setMessage(booleanResponse.getMsg());
    }
    taskLogPo.setTaskName(extractorPO.getName());
    taskLogPo.setLogInfo(JSON.toJSONString(taskLog));
    taskLogPo.setRunType(TaskLogPo.RunType.HANDLE.getCode());
    taskLogPo.setRunTime(DateTimeUtil.timeDec(startTime, LocalDateTime.now()));
    taskLogService.updateById(taskLogPo);
    return runDataTaskExtractsRes;
  }

  public ETLLineageRelationRes getETLLineageRelation(ETLLineageRelationReq etlLineageRelationReq) {
    ETLLineageRelationRes results = new ETLLineageRelationRes();
    results.setLineageRelations(new ArrayList<>());

    Integer transformationId = etlLineageRelationReq.getTransformationSqlId();

    Integer dataTaskChildId = etlLineageRelationReq.getDataTaskChildId();

    DataTaskChildPo dataTaskChildPo;
    if (transformationId == null) {
      if (dataTaskChildId != null) {
        dataTaskChildPo = dataTaskChildService.getById(dataTaskChildId);
        transformationId = dataTaskChildPo.getBindingId();
      }
    }

    if (transformationId == null) {
      return results;
    }

    List<LineageRelationPo> lineageRelationPos =
        lineageRelationService.list(
            new LambdaQueryWrapper<LineageRelationPo>()
                .eq(
                    LineageRelationPo::getRelationType,
                    LineageRelationPo.RelationType.ETL.getCode())
                .eq(LineageRelationPo::getRelationshipId, transformationId)
                .eq(LineageRelationPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    TransformationSqlPo transformationSqlPo = transformationSqlService.getById(transformationId);

    results.setEtlName(transformationSqlPo.getName());
    results.setTransformationId(transformationSqlPo.getId());

    for (LineageRelationPo lineageRelationPo : lineageRelationPos) {
      LineageRelation lineageRelation = new LineageRelation();
      lineageRelation.setRelationshipId(lineageRelationPo.getRelationshipId());
      lineageRelation.setRelationType(lineageRelationPo.getRelationType());
      lineageRelation.setFromTableName(lineageRelationPo.getFromTable());
      lineageRelation.setToTableName(lineageRelationPo.getToTable());
      results.getLineageRelations().add(lineageRelation);
    }
    return results;
  }

  /**
   * 异步运行数据任务
   *
   * @param runDataTaskReq
   * @return
   */
  public Boolean runDataTask(RunDataTaskReq runDataTaskReq) {

    if (!fileStatistics.checkDataSpace()) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.MAXIMUM_DATA_SPACE_REACHED));
    }
    DataTaskPO dataTaskPO = dataTaskService.getById(runDataTaskReq.getDataTaskId());
    if (dataTaskPO == null) {
      return false;
    }
    List<DataTaskChildPo> dataTaskChildPos =
        dataTaskChildService.list(
            new LambdaQueryWrapper<DataTaskChildPo>()
                .eq(DataTaskChildPo::getDataTaskId, dataTaskPO.getId())
                .eq(DataTaskChildPo::getTaskType, 0)
                .eq(DataTaskChildPo::getState, 0)
                .eq(DataTaskChildPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(DataTaskChildPo::getSort));
    if (dataTaskChildPos.isEmpty()) {
      return true;
    }

    if (dataTaskChildPos.isEmpty()) {
      return true;
    }
    List<Integer> taskExtractorIds =
        dataTaskChildPos.stream().map(DataTaskChildPo::getBindingId).collect(Collectors.toList());
    Map<Integer, DataTaskChildPo> dataTaskChildPoMap =
        dataTaskChildPos.stream()
            .collect(
                Collectors.toMap(
                    DataTaskChildPo::getBindingId,
                    DataTaskChildPo -> DataTaskChildPo,
                    (po1, po2) -> po1));
    List<DataExtractorPO> dataExtractorPOS = dataExtractorService.listByIds(taskExtractorIds);
    for (DataExtractorPO dataExtractorPO : dataExtractorPOS) {
      DataTaskChildPo dataTaskChildPo = dataTaskChildPoMap.get(dataExtractorPO.getId());

      if (Objects.equals(DataConnectorTypeEnum.JDBC.getValue(), dataExtractorPO.getType())) {
        JdbcTaskGeneratorInfo jdbcCreateJob = new JdbcTaskGeneratorInfo();
        jdbcCreateJob.setDataTaskId(dataTaskPO.getId());
        jdbcCreateJob.setPoolId(dataTaskPO.getPoolId());
        jdbcCreateJob.setTenantId(dataTaskPO.getTenantId());
        jdbcCreateJob.setImmediatelyFlag(true);
        jdbcCreateJob.setAnalysisOfBlood(true);
        jdbcCreateJob.setDataExtractorPO(dataExtractorPO);
        jdbcCreateJob.setDataTaskChildPo(dataTaskChildPo);
        jdbcJobCreate.genTask(jdbcCreateJob);
      }
      if (Objects.equals(DataConnectorTypeEnum.KAFKA.getValue(), dataExtractorPO.getType())) {
        KafkaTaskGeneratorInfo kafkaTaskGeneratorInfo = new KafkaTaskGeneratorInfo();
        kafkaTaskGeneratorInfo.setPoolId(dataTaskPO.getPoolId());
        kafkaTaskGeneratorInfo.setDataTaskId(dataTaskPO.getId());
        kafkaTaskGeneratorInfo.setTenantId(userInfoUtil.getUserInfo().getGroupId());
        kafkaTaskGeneratorInfo.setImmediatelyFlag(true);
        kafkaTaskGeneratorInfo.setAnalysisOfBlood(true);
        kafkaTaskGeneratorInfo.setTaskExecutorId(-1);
        kafkaTaskGeneratorInfo.setDataTaskChildPo(
            dataTaskChildService.getById(dataTaskChildPo.getId()));
        kafkaTaskGeneratorInfo.setDataExtractorPO(
            dataExtractorService.getById(dataExtractorPO.getId()));
        kafkaTaskGenerator.genTask(kafkaTaskGeneratorInfo);
      }

      if (Objects.equals(
          DataConnectorTypeEnum.INCREMENTAL_FILE.getValue(), dataExtractorPO.getType())) {
        KafkaTaskGeneratorInfo kafkaTaskGeneratorInfo = new KafkaTaskGeneratorInfo();
        kafkaTaskGeneratorInfo.setPoolId(dataTaskPO.getPoolId());
        kafkaTaskGeneratorInfo.setDataTaskId(dataTaskPO.getId());
        kafkaTaskGeneratorInfo.setTenantId(userInfoUtil.getUserInfo().getGroupId());
        kafkaTaskGeneratorInfo.setImmediatelyFlag(true);
        kafkaTaskGeneratorInfo.setAnalysisOfBlood(true);
        kafkaTaskGeneratorInfo.setTaskExecutorId(-1);
        kafkaTaskGeneratorInfo.setDataTaskChildPo(
            dataTaskChildService.getById(dataTaskChildPo.getId()));
        kafkaTaskGeneratorInfo.setDataExtractorPO(
            dataExtractorService.getById(dataExtractorPO.getId()));
        incrementalFileTaskGenerator.genTask(kafkaTaskGeneratorInfo);
      }
    }

    scheduleTask.jobRun();
    return true;
  }

  public void refreshDataTask(Integer dataTaskId) {
    DataTaskPO dataTaskPO = dataTaskService.getById(dataTaskId);
    ArrayList<DataTaskPO> objects = new ArrayList<>();
    objects.add(dataTaskPO);
    dataTaskService.updateNextExecutionTime(objects);
  }

  public void tryRefreshDataTaskAll() {
    List<DataTaskPO> list =
        dataTaskService.list(
            new LambdaQueryWrapper<DataTaskPO>()
                .eq(DataTaskPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .ne(DataTaskPO::getRateType, TaskRateTypeEnum.HANDLE));
    dataTaskService.updateNextExecutionTime(list);
  }

  public CheckCronExpressionRes checkCronExpression(String cronExpression) {
    CheckCronExpressionRes checkCronExpressionRes = new CheckCronExpressionRes();
    try {
      CronSequenceGenerator cronSequenceGenerator = new CronSequenceGenerator(cronExpression);
      Date next = cronSequenceGenerator.next(new Date());
      Date nextNext = cronSequenceGenerator.next(next);

      String nextStr = DateTimeUtil.date2String(next);
      String nextNextStr = DateTimeUtil.date2String(nextNext);
      Long timeDec = DateTimeUtil.timeDec(next, nextNext) / 60;

      checkCronExpressionRes.setNextExecutionTime(nextStr);
      checkCronExpressionRes.setNextNextExecutionTime(nextNextStr);
      checkCronExpressionRes.setRunningInterval(timeDec);
      checkCronExpressionRes.setNextExecutionTimeDate(next);
      checkCronExpressionRes.setNextNextExecutionTimeDate(nextNext);
      if (cronMinInterval > timeDec) {
        checkCronExpressionRes.setMessage(
            I18nUtil.getMessage(I18nConst.CRON_EXPRESSION_INTERVAL_ERROR)
                .replace("${}", cronMinInterval.toString()));
        checkCronExpressionRes.setCheckPassed(false);
      } else {
        checkCronExpressionRes.setRunningInterval(timeDec);
      }
    } catch (IllegalArgumentException e) {
      checkCronExpressionRes.setCheckPassed(false);
      checkCronExpressionRes.setMessage(
          I18nUtil.getMessage(I18nConst.CRON_EXPRESSION_ERROR) + e.getMessage());
      log.error("checkCronError", e);
    }
    return checkCronExpressionRes;
  }
}
