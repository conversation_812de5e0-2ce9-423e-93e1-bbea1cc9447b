package com.sp.proxverse.datamerge.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class FileDataDTO implements Serializable {

  private static final long serialVersionUID = -3436576126164941514L;

  @Tolerate
  public FileDataDTO() {
    // comment empty
  }

  private Integer fileId;
  private List<Map<String, String>> fileDataList;
}
