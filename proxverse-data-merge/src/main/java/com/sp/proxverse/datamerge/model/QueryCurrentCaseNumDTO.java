package com.sp.proxverse.datamerge.model;

import com.sp.proxverse.common.model.dto.DataModelFileDTO;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class QueryCurrentCaseNumDTO implements Serializable {
  private static final long serialVersionUID = 2090307504616884308L;

  @Tolerate
  public QueryCurrentCaseNumDTO() {
    // comment empty
  }

  private DataModelFileDTO dataModelFileDTO;

  private List<Integer> variantIdList;
}
