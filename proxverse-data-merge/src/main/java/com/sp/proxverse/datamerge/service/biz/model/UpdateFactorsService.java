package com.sp.proxverse.datamerge.service.biz.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.DataModelSortPO;
import com.sp.proxverse.datamerge.service.biz.DataModelCommonService;
import com.sp.proxverse.datamerge.service.biz.model.entity.UpdateFactors;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.DataModelSortService;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpdateFactorsService extends UpdateModelTemplate<UpdateFactors> {

  @Autowired private DataModelCommonService dataModelCommonService;

  @Autowired private DataModelSortService dataModelSortService;

  @Autowired private DataModelFileService dataModelFileService;

  @Override
  void update(UpdateFactors updateFactors) {
    // 检查三要素字段合法性
    dataModelCommonService.checkCaseFields(
        updateFactors.getCaseId(),
        updateFactors.getEvent(),
        updateFactors.getTime(),
        updateFactors.getStartTime());

    dataModelCommonService.checkSortingList(updateFactors.getSortingList());

    DataModelPO dataModelOrigin = dataModelService.getById(updateFactors.getDataModelId());

    DataModelPO dataModel =
        DataModelPO.builder()
            .id(updateFactors.getDataModelId())
            .caseid(updateFactors.getCaseId())
            .event(updateFactors.getEvent())
            .time(updateFactors.getTime())
            // 如果前端传空则代表取消设置开始时间
            .startTime(
                Objects.isNull(updateFactors.getStartTime()) ? 0 : updateFactors.getStartTime())
            .build();

    dataModelService.updateById(dataModel);

    dataModelSortService.remove(
        new LambdaQueryWrapper<DataModelSortPO>()
            .eq(DataModelSortPO::getDataModelId, updateFactors.getDataModelId())
            .eq(DataModelSortPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    if (CollectionUtils.isNotEmpty(updateFactors.getSortingList())) {

      List<DataModelSortPO> sortList =
          updateFactors.getSortingList().stream()
              .map(
                  m ->
                      DataModelSortPO.builder()
                          .dataModelId(updateFactors.getDataModelId())
                          .sorting(m)
                          .build())
              .collect(Collectors.toList());
      dataModelSortService.saveBatch(sortList);
    }

    // 如果有案例表，要同时移除案例表及其连线
    DataModelFilePO caseFile = dataModelFileService.getCase(updateFactors.getDataModelId());
    if (caseFile != null
        && !Objects.equals(dataModelOrigin.getCaseid(), updateFactors.getCaseId())) {
      caseFile.setActive(FileActiveTypeEnum.SECOND.getValue());
      dataModelFileService.updateById(caseFile);
    }
    loadModelPublishService.publishModifyModelStatusEvent(updateFactors.getDataModelId());
  }
}
