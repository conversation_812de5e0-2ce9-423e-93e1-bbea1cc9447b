/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    id 'java-library'
    id 'maven-publish'
}

repositories {
    mavenLocal()
    maven {
        url = uri('http://121.37.155.119:8081/repository/maven-public/')
    }

    maven {
        url = uri('https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/')
    }

    maven {
        url = uri('https://repo.maven.apache.org/maven2/')
    }
}

group = 'com.sp'
version = '0.0.1'
java.sourceCompatibility = JavaVersion.VERSION_1_8

publishing {
    publications {
        maven(MavenPublication) {
            from(components.java)
        }
    }
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}
