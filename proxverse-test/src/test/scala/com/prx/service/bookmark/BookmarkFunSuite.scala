package com.prx.service.bookmark

import java.util

import com.google.common.collect.Lists
import com.sp.proxverse.common.model.dict.{BusinessTopicTypeEnum, TopicFilterTypeEnum}
import com.sp.proxverse.common.model.vo.processai.request.{SaveTopicFilterRequest, SaveTopicFilterSubRequest}
import com.sp.proxverse.common.model.vo.request.QueryTopicFilterRequest
import com.sp.proxverse.common.model.vo.request.processAi.SheetReleaseReqVo
import com.sp.proxverse.common.model.vo.request.topic.{BookMarkIdRequest, QueryBookMarkRequest, SaveBookMarkRequest}
import com.sp.proxverse.engine.service.biz.{CopyBizService, ProcessTreeReadBizService, ProcessTreeWriteBizService}
import com.sp.proxverse.interfaces.dao.service.{BusinessTopicService, SheetService}
import com.sp.proxverse.test.data.model.NormalActiveModel
import com.sp.proxverse.test.data.pql.TestPQLBase
import org.junit.jupiter.api._
import org.springframework.beans.factory.annotation.Autowired

import scala.collection.JavaConverters._

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(classOf[MethodOrderer.OrderAnnotation])
class BookmarkFunSuite extends TestPQLBase {

  @Autowired
  var bookMarkService: BookMarkService = _
  @Autowired
  var businessTopicService: BusinessTopicService = _
  @Autowired
  var processTreeWriteBizService: ProcessTreeWriteBizService = _
  @Autowired
  var processTreeReadBizService: ProcessTreeReadBizService = _
  @Autowired
  var sheetService: SheetService = _
  @Autowired
  var copyBizService: CopyBizService = _

  @Order(1)
  @Test
  def testBookmark(): Unit = {
    val modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME)
    val integers = createSheetBase(modelId, BusinessTopicTypeEnum.BUSINESS_TREE, 6)
    val topicID = integers.head
    // 创建一个过滤项
    val fileId = testModelUtils.findFileId("active_table")
    val fieldId = testModelUtils.findFieldId(fileId, "Id")
    val v2Req = SaveTopicFilterRequest.builder
      .topicId(topicID)
      .hasInvert(0)
      .operationType(10)
      .operationValue("2")
      .list(
        Lists.newArrayList(
          SaveTopicFilterSubRequest.builder
            .`type`(TopicFilterTypeEnum.VARIABLE.getValue)
            .fieldId(fieldId)
            .fileId(fileId)
            .paramValue(Lists.newArrayList("2"))
            .build))
      .build
    processTreeWriteBizService.saveTopicFilterV2(v2Req)
    // 创建2个bookmark
    bookMarkService.saveBookMark(
      SaveBookMarkRequest.builder
        .name("a")
        .topicId(topicID)
        .`type`(0)
        .build)
    bookMarkService.saveBookMark(
      SaveBookMarkRequest.builder
        .name("b")
        .topicId(topicID)
        .`type`(0)
        .build)

    // get 出来 id=1、2
    val array =
      bookMarkService.getBookMarkList(QueryBookMarkRequest.builder.topicId(topicID).build)

    Assertions.assertEquals(array(0).getId, 1)
    Assertions.assertEquals(array(1).getId, 2)

    val list = new util.ArrayList[Integer]()
    list.add(1)
    // 加载bookmark
    bookMarkService.loadBookMark(
      BookMarkIdRequest.builder
        .topicId(topicID)
        .idList(list)
        .build)

    val topicFilterList = processTreeReadBizService.getTopicFilterList(
      QueryTopicFilterRequest.builder
        .topicId(topicID)
        .build)
    Assertions.assertEquals(topicFilterList.get(0).getDesc, "Id >= 2")

    // 发布
    val sheetReleaseReqVo: SheetReleaseReqVo = new SheetReleaseReqVo
    sheetReleaseReqVo.setUserId("1")
    sheetReleaseReqVo.setTopicId(topicID.toString)
    sheetService.sheetRelease(sheetReleaseReqVo)
    // 检查snapshot的bookmark
    val list2 = new util.ArrayList[Integer]()
    list2.add(topicID)
    val snapId = businessTopicService.listSnapshotDataByTopicIds(list2).get(0).getId
    val array2 = bookMarkService
      .getBookMarkList(QueryBookMarkRequest.builder.topicId(snapId).build)
      .sortBy(_.getId)
    Assertions.assertEquals(array2.length, 2)
    Assertions.assertEquals(array2(0).getId, 1)
    Assertions.assertEquals(array2(1).getId, 2)

    // copy topic
    val newTopicId: Integer =
      copyBizService.copyTopic(topicID, null, BusinessTopicTypeEnum.BUSINESS_TREE.getValue)
    // 检查copy后的bookmark
    val array3 = bookMarkService
      .getBookMarkList(QueryBookMarkRequest.builder.topicId(newTopicId).build)
      .sortBy(_.getId)
    Assertions.assertEquals(array3.length, 2)
    Assertions.assertEquals(array3(0).getId, 1)
    Assertions.assertEquals(array3(1).getId, 2)
  }

}
