package com.sp.proxverse.test.data.pql

import com.sp.proxverse.test.data.model.OrderModel
import org.junit.jupiter.api.{Assertions, Test}
import com.prx.service.model.ModelCacheServiceImpl
import org.apache.spark.sql.execution.exchange.{ShuffleExchangeExec, ShuffleExchangeLike}
import org.apache.spark.sql.pql.PQLEnv
import org.apache.spark.sql.SparkSessionEnv
import org.springframework.beans.factory.annotation.Autowired

import java.util

class EncodedColumnFunSuite extends AbstractPQLFunSuite {

  @Autowired val cacheService: ModelCacheServiceImpl = null

  //@Test
  def testEncodedColumn(): Unit = {
    val modelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME)
    val builder = newBuilder(modelDescFactory.getOrCreate(modelId))
      .addColumn("encoded_column(orderEvent.event)")
      .addColumn("encoded_column(orderEvent.cASEId)")
      .addColumn("encoded_column(orderCase.`工单编号`)")
      .addColumn("encoded_column(`case_table`.preCaseId)")
      .collect()
    Assertions.assertTrue(builder.length > 0)
  }

}
