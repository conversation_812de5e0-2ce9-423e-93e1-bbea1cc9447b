package com.sp.proxverse.test.data.pql

import com.sp.proxverse.test.data.model.BillModel
import org.apache.spark.sql.execution.exchange.ShuffleExchangeExec
import org.apache.spark.sql.pql.PQLBuilder
import org.junit.jupiter.api.{Assertions, Test}

class BucketJoinSuite extends TestPQLBase {

  def withPQLBuilder(func: PQLBuilder => Unit): Unit = {
    val orCreate =
      modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME))
    val pqlBuilder = newBuilder(orCreate);
    func(pqlBuilder)
  }

  @Test def testProcessEqualsAnyToEvent(): Unit = {
    withPQLBuilder(pqlBuilder => {
      pqlBuilder.addColumn(s"SHORTENED( process_variant(activity_column() || '123'), 2)")
      pqlBuilder.addColumn(" avg(`case_table`.caseDuration) ")
      pqlBuilder.addColumn("  count(distinct ENCODED_CASE_CASE_ID_COLUMN())")
      pqlBuilder.run { df =>
        val seq = df.queryExecution.executedPlan
          .collect { case e: ShuffleExchangeExec =>
            e
          }
        Assertions.assertTrue(seq.isEmpty)
      }
    })
  }
}
