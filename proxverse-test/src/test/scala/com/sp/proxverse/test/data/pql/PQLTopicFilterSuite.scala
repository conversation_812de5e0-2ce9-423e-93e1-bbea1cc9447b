package com.sp.proxverse.test.data.pql

import com.sp.proxverse.test.data.model.BillModel
import org.apache.spark.sql.Row
import org.apache.spark.sql.common.QueryTest
import org.apache.spark.sql.functions._
import org.apache.spark.sql.pql.model.PQLContext
import org.apache.spark.sql.pql.{PQLBuilder, PQLTableUtils}
import org.junit.jupiter.api.{Assertions, Test}

import scala.collection.immutable.HashSet
import scala.collection.mutable

class PQLTopicFilterSuite extends TestPQLBase {

  def withPQLBuilder(func: PQLBuilder => Unit): Unit = {
    val orCreate =
      modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME))
    val pqlBuilder = newBuilder(orCreate)
    func(pqlBuilder)
  }

  @Test def testTableUtils(): Unit = {
    withPQLBuilder(pqlBuilder => {
      val resolved = (name: String) => PQLContext.context.get.resolveTable(name).get
      Assertions.assertEquals(
        resolved("bill_active"),
        PQLTableUtils.collectTables(expr("bill_active.activity = 'a'").expr).mkString(","))
      Assertions.assertEquals(
        mutable.Set(resolved("bill_case"), resolved("bill_active")),
        PQLTableUtils.collectTables(expr(
          "bill_active.activity = 'a' or bill_active.activity = 'b' and bill_case.activity = 'c'").expr))

      Assertions.assertTrue(
        PQLTableUtils.exprAllOnTables(
          expr("bill_active.activity = 'a'").expr,
          HashSet(resolved("bill_active"))))
      Assertions.assertFalse(PQLTableUtils
        .exprAllOnTables(expr("bill_case.activity = 'b'").expr, HashSet(resolved("bill_active"))))
      Assertions.assertFalse(
        PQLTableUtils.exprAllOnTables(
          expr("bill_active.activity = 'a' and bill_case.activity = 'b'").expr,
          HashSet(resolved("bill_active"))))
    })
  }

  @Test def testDirectFilters(): Unit = {
    withPQLBuilder(pqlBuilder => {
      pqlBuilder.addTopicFilter("`bill_active`.`CaseId` = 107047")
      pqlBuilder.addColumn("bill_case.caseId")
      pqlBuilder.addColumn("bill_active.activity")
      QueryTest
        .checkAnswerBySeq(
          pqlBuilder.resultDF(),
          Row(107047, "接收发票")
            :: Row(107047, "处理发票")
            :: Row(107047, "最终检查发票")
            :: Row(107047, "批准发票")
            :: Row(107047, "按票付款") :: Nil)
        .foreach(errMsg => Assertions.fail(errMsg))
    })
  }

  @Test def testInDirectFilters(): Unit = {
    withPQLBuilder(pqlBuilder => {
      pqlBuilder.addTopicFilter(
        "process_filter('请求缺失数据', '处理发票', 20, `case_table`.sortedEventList)")
      pqlBuilder.addColumn("bill_case.caseId")
      pqlBuilder.addColumn("count(bill_active.caseStatus)")
      QueryTest
        .checkAnswerBySeq(
          pqlBuilder.resultDF(),
          Row(109802, 9)
            :: Row(109800, 9)
            :: Row(109805, 9) :: Nil)
        .foreach(errMsg => Assertions.fail(errMsg))
    })

    // with CALC_CROP_TO_NULL
    withPQLBuilder(pqlBuilder => {
      pqlBuilder.addTopicFilter("bill_case.caseId == 109800")
      pqlBuilder.addColumn(
        "CALC_CROP_TO_NULL(FIRST_OCCURRENCE ['接收发票'] TO LAST_OCCURRENCE [ '处理发票' ], `bill_active`.`Activity`)")
      pqlBuilder.addColumn("bill_case.caseId")
      QueryTest
        .checkAnswerBySeq(
          pqlBuilder.resultDF(),
          Row("接收发票", 109800)
            :: Row("请求缺失数据", 109800)
            :: Row("核对合同条款", 109800)
            :: Row("最终检查发票", 109800)
            :: Row("处理发票", 109800)
            :: Row(null, 109800)
            :: Row(null, 109800)
            :: Row(null, 109800)
            :: Row(null, 109800) :: Nil)
        .foreach(errMsg => Assertions.fail(errMsg))
    })
  }

  @Test def testDirectAndIndirectFilters(): Unit = {
    withPQLBuilder(pqlBuilder => {
      pqlBuilder.addTopicFilter(
        "process_filter('请求缺失数据', '处理发票', 20, `case_table`.sortedEventList)")
      pqlBuilder.addTopicFilter("bill_active.Activity = '接收发票'")
      pqlBuilder.addColumn("bill_case.caseId")
      pqlBuilder.addColumn("bill_active.Activity")
      QueryTest
        .checkAnswerBySeq(
          pqlBuilder.resultDF(),
          Row(109800, "接收发票")
            :: Row(109802, "接收发票")
            :: Row(109805, "接收发票") :: Nil)
        .foreach(errMsg => Assertions.fail(errMsg))
    })
  }

  @Test def testProcessEquals(): Unit = {
    val runProcEquals = (procExpr: String, rows: Seq[Row]) =>
      withPQLBuilder(pqlBuilder => {
        pqlBuilder.addTopicFilter("caseId in (107047, 107048, 107135)")
        pqlBuilder.addTopicFilter(procExpr)
        pqlBuilder.addColumn("bill_case.caseId")
        QueryTest
          .checkAnswerBySeq(pqlBuilder.resultDF(), rows)
          .foreach(errMsg => Assertions.fail(errMsg))
      })

    runProcEquals("process equals '核对合同条款'", Row(107135) :: Nil)
    runProcEquals("process not equals '核对合同条款'", Row(107047) :: Row(107048) :: Nil)
    runProcEquals("process equals '处理发票' to '核对合同条款'", Row(107135) :: Nil)
    runProcEquals("process not equals '处理发票' to '核对合同条款'", Row(107047) :: Row(107048) :: Nil)
    runProcEquals(
      "process on `bill_active`.`Activity` || 1 equals '处理发票1' to '核对合同条款1'",
      Row(107135) :: Nil)
    runProcEquals(
      "process on `bill_active`.`Activity` || 1 not equals '处理发票1' to '核对合同条款1'",
      Row(107047) :: Row(107048) :: Nil)
  }

  @Test def testResultFilters(): Unit = {
    withPQLBuilder(pqlBuilder => {
      pqlBuilder.addTopicFilter(
        "process_filter('请求缺失数据', '处理发票', 20, `case_table`.sortedEventList)")
      pqlBuilder.addTopicFilter("bill_active.Activity = '接收发票'")
      pqlBuilder.addColumn("count(bill_case.caseId)", "cnt")
      pqlBuilder.addResultFilter("cnt > 2")
      pqlBuilder.addResultFilter("cnt < 4")
      QueryTest
        .checkAnswerBySeq(pqlBuilder.resultDF(), Row(3) :: Nil)
        .foreach(errMsg => Assertions.fail(errMsg))
    })
  }

}
