package com.sp.proxverse.test.data.pql

import com.sp.proxverse.test.data.model.OrderModel
import org.junit.jupiter.api.{Assertions, Test}
import com.prx.service.model.ModelCacheServiceImpl
import org.apache.spark.sql.execution.columnar.InMemoryTableScanExec
import org.apache.spark.sql.execution.exchange.{ShuffleExchangeExec, ShuffleExchangeLike}
import org.apache.spark.sql.internal.SQLConf
import org.apache.spark.sql.pql.PQLEnv
import org.apache.spark.sql.{PQLConf, SparkSession, SparkSessionEnv}
import org.junit.Ignore
import org.springframework.beans.factory.annotation.Autowired

import java.util

class ExchangeOptimizeFunSuite extends AbstractPQLFunSuite {

  @Autowired val cacheService: ModelCacheServiceImpl = null

  @Test
  def testNoExchange(): Unit = {
    val modelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME)
    val builder = newBuilder(modelDescFactory.getOrCreate(modelId))
      .addColumn("process_variant(orderEvent.event)")
      .addColumn("count(*)")
    builder.runWithRetry[Unit] { () =>
      val plan = builder.resultDF().queryExecution.executedPlan
      Assertions.assertFalse(plan.exists(_.isInstanceOf[ShuffleExchangeLike]))
    }
  }

  //@Test
  def testRewriteExchangeAndEnsureAllTableAreAnalyzed(): Unit = {
    val modelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME)
    PQLEnv.addLocalConf(new util.HashMap[String, String]())
    PQLEnv.addLocalConf("spark.sql.shuffle.partitions", "200")
    val builder = newBuilder(modelDescFactory.getOrCreate(modelId))
      .addColumn("process_variant(orderEvent.event)")
      .addColumn("count(*)")
    builder.runWithRetry[Unit] { () =>
      val sqlConf = SparkSessionEnv.getSparkSession.sessionState.conf
      sqlConf.setLocalProperty("spark.sql.pql.dbo.prePartitionRowNum", "400")
      sqlConf.setLocalProperty("spark.sql.rewriteJoinEnabled", "false")
      val plan = builder.resultDF().queryExecution.executedPlan
      Assertions.assertEquals(
        plan
          .find(_.isInstanceOf[ShuffleExchangeLike])
          .get
          .asInstanceOf[ShuffleExchangeExec]
          .outputPartitioning
          .numPartitions,
        2)
    }
    val builder2 = newBuilder(modelDescFactory.getOrCreate(modelId))
      .addColumn("orderEvent.event")
      .addColumn("count(*)")
    builder2.runWithRetry[Unit] { () =>
      val sqlConf = SparkSessionEnv.getSparkSession.sessionState.conf
      sqlConf.setLocalProperty("spark.sql.pql.dbo.prePartitionRowNum", "10")
      sqlConf.setLocalProperty("spark.sql.rewriteJoinEnabled", "false")
      val plan = builder2.resultDF().queryExecution.executedPlan
      val partitions = plan
        .find(_.isInstanceOf[ShuffleExchangeLike])
        .get
        .asInstanceOf[ShuffleExchangeExec]
        .outputPartitioning
        .numPartitions
      Assertions.assertEquals(partitions, 4)
    }
    val builder3 = newBuilder(modelDescFactory.getOrCreate(modelId))
      .addColumn("concat(orderCase.`工单编号`, orderCase.`优先级`)")
      .addColumn("count(*)")
    builder3.runWithRetry[Unit] { () =>
      val sqlConf = SparkSessionEnv.getSparkSession.sessionState.conf
      sqlConf.setLocalProperty("spark.sql.pql.dbo.prePartitionRowNum", "10")
      sqlConf.setLocalProperty("spark.sql.rewriteJoinEnabled", "false")
      val plan = builder3.resultDF().queryExecution.executedPlan
      val partitions = plan
        .find(_.isInstanceOf[ShuffleExchangeLike])
        .get
        .asInstanceOf[ShuffleExchangeExec]
        .outputPartitioning
        .numPartitions
      Assertions.assertEquals(partitions, 200)
    }
  }

}
