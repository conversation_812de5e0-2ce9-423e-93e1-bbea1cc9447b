package org.apache.spark.sql

import org.apache.spark.internal.Logging
import org.apache.spark.sql.pql.ModelCatalog
import org.apache.spark.sql.pql.model._
import org.apache.spark.sql.test.SQLTestUtils
import org.apache.spark.sql.types._

import java.io.File
import java.time.Instant
import scala.collection.JavaConversions._

trait DummyModelCatalogSuite extends SQLTestUtils with Logging {

  def activityTable = SparkSession.active.createDataFrame(
    Row(1, "Open", Instant.now(), "City") ::
      Row(1, "Close", Instant.now().plusSeconds(10), "City") :: Nil,
    StructType(
      Array(
        StructField("id", IntegerType),
        StructField("action", StringType),
        StructField("date", TimestampType),
        StructField("city", StringType))))
  def caseTable = SparkSession.active.createDataFrame(
    Seq(Row(1, "Open", "Online")),
    StructType(
      Array(
        StructField("id", IntegerType),
        StructField("action", StringType),
        StructField("status", StringType))))
  def vCaseTable = SparkSession.active.createDataFrame(
    Seq(Row(1, "Open", "Online", null, null)),
    StructType(
      Array(
        StructField("id", IntegerType),
        StructField("action", StringType),
        StructField("status", StringType),
        StructField("sortedEventList", ArrayType(StringType)),
        StructField("sortedTSList", ArrayType(TimestampType)))))

  def withDummyCatalog(func: (ModelCatalog) => Unit): Unit = {
    withDatabase("dummyDB") {
      withTable("activity", "case", "vcase") {
        withTempDir { dir =>
          val loc = new File(dir, "dummyDB")
          spark.sql(s"CREATE DATABASE dummyDB LOCATION '$loc'")
          spark.sql(s"USE dummyDB")
          func(dummyModelCatalog())
        }
      }
    }
  }

  def dummyModelCatalog(): ModelCatalog = {
    activityTable.write.saveAsTable("activity")
    caseTable.write.saveAsTable("case")
    vCaseTable.write.saveAsTable("vcase")

    val modelDesc = new ModelDesc(
      CaseTable("dummyDB.`case`", "id", "id"),
      ActiveTable("dummyDB.`activity`", "id", "action", "date", "date", "id"),
      VirtualCaseTable(
        "dummyDB.`vcase`",
        "id",
        "action",
        "status",
        "id",
        "sortedEventList",
        "sortedTSList",
        false),
      Seq(
        Join("dummyDB.`activity`", "dummyDB.`case`", "left", Seq("id"), Seq("id")),
        Join("dummyDB.`activity`", "dummyDB.`vcase`", "left", Seq("id"), Seq("id"))),
      Map("activity" -> "dummyDB.`activity`", "case" -> "dummyDB.`case`"),
      1,
      "activity",
      "case",
      () => "",
      "dummyDB",
      1,
      1,
      null)

    val dummyCatalog = ModelCatalog(modelDesc, SparkSession.active)
    dummyCatalog
  }

}
