package org.apache.spark

import com.sp.proxverse.common.Graph
import com.sp.proxverse.common.model.vo.processExplorer.{EventNode, LineNode}
import org.apache.spark.sql.common.{BaseFunSuite, SharedSparkSession, ValidateFunSuite}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import scala.collection.mutable

/**
 * <AUTHOR>
 * @create 2023-05-10 09:46
 */
@RunWith(classOf[JUnitRunner])
class ProcessFunSuite extends BaseFunSuite with SharedSparkSession with ValidateFunSuite {
  test("dikstras process") {
    val graph = new Graph()
    graph.loadedEventMap += "A" -> EventNode.builder().name("A").number(13).gradient(0).build()
    graph.loadedEventMap += "B" -> EventNode.builder().name("B").number(6).gradient(0).build()
    graph.loadedEventMap += "E" -> EventNode.builder().name("E").number(9).gradient(0).build()
    graph.loadedEventMap += "end" -> EventNode.builder().name("end").number(0).gradient(0).build()
    graph.loadedEventMap += "start" -> EventNode
      .builder()
      .name("start")
      .number(0)
      .gradient(0)
      .build()

    graph.unloadedEventMap += "D" -> EventNode.builder().name("D").number(4).build()
    graph.unloadedEventMap += "C" -> EventNode.builder().name("C").number(3).build()

    val LineNodes = mutable.Set[LineNode]()
    LineNodes += LineNode.builder().source("E").target("end").number(8).build()
    LineNodes += LineNode.builder().source("B").target("end").number(1).build()
    LineNodes += LineNode.builder().source("G").target("end").number(5).build()
    LineNodes += LineNode.builder().source("start").target("C").number(2).build()
    LineNodes += LineNode.builder().source("start").target("A").number(13).build()
    LineNodes += LineNode.builder().source("C").target("end").number(1).build()
    LineNodes += LineNode.builder().source("A").target("B").number(6).build()
    LineNodes += LineNode.builder().source("B").target("C").number(1).build()
    LineNodes += LineNode.builder().source("A").target("D").number(2).build()
    LineNodes += LineNode.builder().source("D").target("E").number(4).build()
    LineNodes += LineNode.builder().source("C").target("D").number(2).build()
    LineNodes += LineNode.builder().source("B").target("E").number(5).build()
    LineNodes += LineNode.builder().source("E").target("B").number(1).build()
    LineNodes += LineNode.builder().source("A").target("F").number(5).build()

    graph.lineGroupBySource ++= LineNodes.toList.groupBy(_.getSource)
    graph.lineGroupByTarget ++= LineNodes.toList.groupBy(_.getTarget)

    graph.loadGradient()
    val gradientMax = graph.getLoadGradientValue()

    assert(gradientMax == 2)
    assert(graph.loadedEventMap.get("D").get.getGradient == 1)
    assert(graph.loadedEventMap.get("C").get.getGradient == 2)
    assert(graph.loadedEventMap.size == 7)
  }
}
