package com.sp.proxverse.test.data.pool;

import com.sp.proxverse.common.model.vo.DataPoolOutputVO;
import com.sp.proxverse.datamerge.service.biz.DataModelBizService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import org.junit.Ignore;
import org.springframework.beans.factory.annotation.Autowired;

public class TestDataPool extends ProxverseTestBase {

  @Autowired private DataModelBizService dataModelBizService;

  @Autowired private TestPoolUtils testPoolUtils;

  @Ignore
  public void testCreatePool() {
    testPoolUtils.createPool("test");
    for (DataPoolOutputVO dataPoolOutputVO : dataModelBizService.getDataPoolList()) {
      if (dataPoolOutputVO.getName().equals("test")) {
        return;
      }
    }
    throw new RuntimeException("Error for save data pool");
  }
}
