package com.sp.proxverse.test.data.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.prx.config.StartupListener;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.enums.job.JobStatusEnum;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelLoadLogPO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.TaskLogPo;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterSubRequest;
import com.sp.proxverse.common.util.DataSourceUtils;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.interfaces.dao.impl.DataModelFileServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TaskLogServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TopicFilterServiceImpl;
import com.sp.proxverse.interfaces.dao.service.DataModelLoadLogService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.service.data.pql.TopicFilterManager;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.common.TestModelManager;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.AnalysisException;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.pql.dict.EncodedColumnDictIndexBuilder;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

/**
 * <AUTHOR>
 * @date 2023/11/7 13:52
 */
@TestPropertySource(
    properties = {"prx.model.buildDimTables=true", "prx.model.build.bucketGroupEnabled=false"})
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class DataModelLoadTest extends ProxverseTestBase {

  @Autowired private TaskLogServiceImpl taskLogService;

  @Autowired StartupListener startupListener;

  @Autowired private transient DataModelLoadLogService dataModelLoadLogService;

  @Autowired TestModelUtils testModelUtils;

  @Autowired DataModelService dataModelService;

  @Autowired DataModelFileServiceImpl dataModelFileService;

  @Autowired ModelDescFactory modelDescFactory;

  @Autowired TopicFilterServiceImpl topicFilterService;

  @Autowired private ProcessTreeWriteBizService processTreeWriteBizService;

  @Autowired TopicFilterManager topicFilterManager;

  @Autowired TestModelManager testModelManager;

  @Test
  public void testDataModelLoadInterrupt() {
    TaskLogPo taskLogPo = new TaskLogPo();
    taskLogPo.setTaskExecutorId(1);
    taskLogPo.setTaskName("test1");
    taskLogPo.setDataTaskId(1);
    taskLogPo.setStatus(JobStatusEnum.OPEN.getValue());
    taskLogService.save(taskLogPo);
    startupListener.checkTaskLoadState();

    Assertions.assertEquals(
        0,
        taskLogService.count(
            new LambdaQueryWrapper<TaskLogPo>()
                .eq(TaskLogPo::getStatus, JobStatusEnum.OPEN.getValue())));

    taskLogService.removeById(taskLogPo.getId());

    DataModelLoadLogPO dataModelLoadLogPO = new DataModelLoadLogPO();
    dataModelLoadLogPO.setStartTime(LocalDateTime.now());

    dataModelLoadLogService.save(dataModelLoadLogPO);
    startupListener.checkDateModelLoadState();
    Assertions.assertEquals(
        0,
        dataModelLoadLogService.count(
            new LambdaQueryWrapper<DataModelLoadLogPO>().isNull(DataModelLoadLogPO::getEndTime)));
    dataModelLoadLogService.removeById(dataModelLoadLogPO.getId());
  }

  // @Test
  @Order(0)
  public void testDataModelVersion() throws AnalysisException {
    int dataModelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME);

    DataModelPO dataModelPO = dataModelService.getById(dataModelId);
    Assertions.assertEquals(1, dataModelPO.getMaxVersion());
    Assertions.assertEquals(1, dataModelPO.getCurrentVersion());
    List<String> tablesName = getSparkSessionTableName(dataModelPO.getPoolId());
    String baseName = DataSourceUtils.makeDatabaseName(dataModelPO.getPoolId());
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                EncodedColumnDictIndexBuilder.sparkTableName(
                    dataModelId, baseName, dataModelPO.getCurrentVersion()))));

    List<DataModelFilePO> fileList = dataModelFileService.getFileList(dataModelId);

    Map<String, String> collect =
        fileList.stream()
            .collect(
                Collectors.toMap(
                    DataModelFilePO::getFileName, po -> po.getSparkTableName().toLowerCase()));
    Assertions.assertTrue(collect.containsKey("case_table"));
    Assertions.assertTrue(collect.containsKey("variant_table"));
    Assertions.assertTrue(collect.containsKey("orderCase"));
    Assertions.assertTrue(collect.containsKey("orderEvent"));

    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("case_table"), 1))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("variant_table"), 1))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderCase"), 1))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderEvent"), 1))));

    testModelUtils.modelBuilderService.buildModel(dataModelId);

    tablesName = getSparkSessionTableName(dataModelPO.getPoolId());
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("case_table"), 1))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("variant_table"), 1))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderCase"), 1))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderEvent"), 1))));
    Assertions.assertTrue(tablesName.contains("encoded_column_dict_1_v_1"));

    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("case_table"), 2))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("variant_table"), 2))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderCase"), 2))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderEvent"), 2))));
    Assertions.assertTrue(tablesName.contains("encoded_column_dict_1_v_2"));

    testModelUtils.modelBuilderService.buildModel(dataModelId);
    tablesName = getSparkSessionTableName(dataModelPO.getPoolId());
    Assertions.assertFalse(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("case_table"), 1))));
    Assertions.assertFalse(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("variant_table"), 1))));
    Assertions.assertFalse(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderCase"), 1))));
    Assertions.assertFalse(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderEvent"), 1))));

    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("case_table"), 2))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("variant_table"), 2))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderCase"), 2))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderEvent"), 2))));

    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("case_table"), 3))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("variant_table"), 3))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderCase"), 3))));
    Assertions.assertTrue(
        tablesName.contains(
            DataSourceUtils.extractTableName(
                DataSourceUtils.makeModelTableName(collect.get("orderEvent"), 3))));
  }

  @Test
  public void testDataModelJoinTable() throws IOException, InterruptedException, AnalysisException {
    TPCHModel tpchModel = new TPCHModel(testModelUtils);
    int modelId = tpchModel.create();
    ModelDesc modelDesc = modelDescFactory.getOrCreate(modelId);

    List<String> tableNames =
        SparkSessionEnv.getSparkSession().catalog().listTables("pool_" + tpchModel.poolId)
            .collectAsList().stream()
            .map(
                po -> {
                  return po.database() + ".`" + po.name() + "`";
                })
            .collect(Collectors.toList());

    HashSet<String> nationTable = new HashSet<>();
    for (Map.Entry<String, String> stringStringEntry :
        modelDesc.tableName2SparkTableName().entrySet()) {
      Assertions.assertTrue(tableNames.contains(stringStringEntry.getValue()));
      if (stringStringEntry.getValue().contains("nation")) {
        nationTable.add(stringStringEntry.getValue());
      }
    }
    Assertions.assertEquals(2, nationTable.size());
    for (String nation : nationTable) {
      //      Assertions.assertTrue(nation.contains("_bucket_v_"));
    }
  }

  private List<String> getSparkSessionTableName(Integer poolId) throws AnalysisException {
    return SparkSessionEnv.getSparkSession().catalog().listTables("pool_" + poolId).collectAsList()
        .stream()
        .map(po -> po.name())
        .collect(Collectors.toList());
  }

  @Test
  @Order(1)
  public void testDataModelCacheCleanup() throws ExecutionException {
    int orCreate = testModelManager.getOrCreate(OrderModel.MODEL_NAME);

    Integer[] sheet = createSheetBase(orCreate, BusinessTopicTypeEnum.BUSINESS_TREE, 11);

    SaveTopicFilterRequest build =
        SaveTopicFilterRequest.builder()
            .topicId(sheet[0])
            .list(
                Lists.newArrayList(
                    SaveTopicFilterSubRequest.builder()
                        .type(2000)
                        .expression("`active_table`.`caseId`='case-10011'")
                        .build()))
            .bizType(0)
            .build();
    processTreeWriteBizService.saveTopicFilterV2(build);

    List<String> allTopicFilters = topicFilterManager.getAllTopicFilters(sheet[0]);
    Assertions.assertEquals(1, allTopicFilters.size());
    testModelUtils.modelBuilderService.buildModel(orCreate);
    topicFilterService.remove(new LambdaQueryWrapper<TopicFilterPO>());
    Assertions.assertEquals(0, topicFilterManager.getAllTopicFilters(sheet[0]).size());
  }
}
