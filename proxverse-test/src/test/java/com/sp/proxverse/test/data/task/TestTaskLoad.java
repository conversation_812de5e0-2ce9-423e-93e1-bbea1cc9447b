package com.sp.proxverse.test.data.task;

import com.sp.proxverse.common.model.dict.TaskRateTypeEnum;
import com.sp.proxverse.common.model.vo.datatask.CheckCronExpressionRes;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.datamerge.service.biz.DataTaskBizService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/9/20 11:02
 */
@Slf4j
public class TestTaskLoad extends ProxverseTestBase {
  private static final Long ONE_HOUR = 3600000L;
  private static final Long ONE_MINUTE = 1000 * 60L;
  private static final Long ONE_DAY = 3600000 * 24L;
  private static final Long ONE_WEEK = 3600000 * 24 * 7L;

  @Autowired DataTaskBizService dataTaskBizService;

  @Test
  public void testCheckCronExpression() {
    // 表示每2秒 执行任务
    CheckCronExpressionRes result = dataTaskBizService.checkCronExpression("0/2 * * * * ? ");
    Assertions.assertFalse(result.isCheckPassed());
    Assertions.assertEquals("Cron表达式最小间隔应大于15分钟", result.getMessage());
    Assertions.assertEquals(0L, result.getRunningInterval());

    // 表示每2分钟 执行任务
    result = dataTaskBizService.checkCronExpression("0 0/2 * * * ?");
    Assertions.assertEquals("Cron表达式最小间隔应大于15分钟", result.getMessage());
    Assertions.assertEquals(2L, result.getRunningInterval());
    Assertions.assertFalse(result.isCheckPassed());

    // 每22分钟 执行任务
    result = dataTaskBizService.checkCronExpression("0 */22 * * * ?");
    Assertions.assertTrue(result.isCheckPassed());
    Assertions.assertTrue(22L >= result.getRunningInterval());

    // error
    result = dataTaskBizService.checkCronExpression("0 0 10,14,16 *  ?");
    Assertions.assertFalse(result.isCheckPassed());
  }

  @Test
  public void testCalNextExecutionTime() {
    Calendar calendar = Calendar.getInstance();
    long currentTime = calendar.getTimeInMillis();

    long difference = currentTime - System.currentTimeMillis();
    assert Math.abs(difference) < 100;

    int nowMinute = calendar.get(Calendar.MINUTE);
    int nowHour = calendar.get(Calendar.HOUR_OF_DAY);
    int nowDayWeeK = calendar.get(Calendar.DAY_OF_WEEK);

    Long hour1 = DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.HOUR, nowMinute - 1, 0, 0);
    Long hour2 = DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.HOUR, nowMinute + 1, 0, 0);

    assert Math.abs(ONE_HOUR - ONE_MINUTE - (hour1 - currentTime)) < 1000 + ONE_MINUTE;
    assert Math.abs(ONE_MINUTE - (hour2 - currentTime)) < 1000 + ONE_MINUTE;

    Long day = DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.DAY, 0, nowHour - 1, 0);
    Long day2 = DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.DAY, 0, nowHour + 1, 0);

    assert Math.abs(ONE_DAY - ONE_HOUR - (day - currentTime) - nowMinute * ONE_MINUTE)
        < 1000 + ONE_MINUTE;
    assert Math.abs(ONE_HOUR - (day2 - currentTime) - nowMinute * ONE_MINUTE) < 1000 + ONE_MINUTE;

    Long WEEK =
        DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.WEEK, 0, nowHour - 1, nowDayWeeK);
    Long WEEK2 =
        DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.DAY, 0, nowHour + 1, nowDayWeeK);
    assert Math.abs(ONE_WEEK - ONE_HOUR - (WEEK - currentTime) - nowMinute * ONE_MINUTE)
        < 1000 + ONE_MINUTE;
    assert Math.abs(ONE_HOUR - (WEEK2 - currentTime) - nowMinute * ONE_MINUTE) < 1000 + ONE_MINUTE;

    Long WEEK3 =
        DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.WEEK, 0, nowHour - 1, nowDayWeeK + 1);
    Long WEEK4 =
        DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.DAY, 0, nowHour + 1, nowDayWeeK + 1);
    assert Math.abs(ONE_DAY - ONE_HOUR - (WEEK3 - currentTime) - nowMinute * ONE_MINUTE)
        < 1000 + ONE_MINUTE;
    assert Math.abs(ONE_HOUR - (WEEK4 - currentTime) - nowMinute * ONE_MINUTE) < 1000 + ONE_MINUTE;

    Long WEEK5 =
        DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.WEEK, 0, nowHour - 1, nowDayWeeK - 1);
    Long WEEK6 =
        DateTimeUtil.calNextExecutionTime(TaskRateTypeEnum.DAY, 0, nowHour + 1, nowDayWeeK - 1);

    assert Math.abs(ONE_WEEK - ONE_DAY - ONE_HOUR - (WEEK5 - currentTime) - nowMinute * ONE_MINUTE)
        < 1000 + ONE_MINUTE;
    assert Math.abs(ONE_HOUR - (WEEK6 - currentTime) - nowMinute * ONE_MINUTE) < 1000 + ONE_MINUTE;
  }

  private static void printlnTime(Long a) {
    Date date = new Date(a);
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String formattedTime = sdf.format(date);
    log.info(formattedTime);
  }
}
