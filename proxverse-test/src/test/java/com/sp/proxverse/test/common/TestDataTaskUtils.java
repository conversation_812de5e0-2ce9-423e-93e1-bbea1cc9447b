package com.sp.proxverse.test.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.dict.DataConnectorBizTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.DataExtractorPO;
import com.sp.proxverse.common.model.po.DataTaskChildPo;
import com.sp.proxverse.common.model.po.TaskLogPo;
import com.sp.proxverse.common.model.vo.api.RunTransformationReqVo;
import com.sp.proxverse.common.model.vo.api.RunTransformationRes;
import com.sp.proxverse.common.model.vo.api.RunTransformationResVo;
import com.sp.proxverse.common.model.vo.api.RunTransformationResult;
import com.sp.proxverse.common.model.vo.api.UpdateTransformationSqlResVo;
import com.sp.proxverse.common.model.vo.dataconnector.request.CheckDataExtractionReq;
import com.sp.proxverse.common.model.vo.dataconnector.request.SelectFieldSubRequest;
import com.sp.proxverse.common.model.vo.datamodel.request.AddFile4ModelRequest;
import com.sp.proxverse.common.model.vo.datatask.DataExtractorByConnectIdReq;
import com.sp.proxverse.common.model.vo.datatask.DataExtractorRes;
import com.sp.proxverse.common.model.vo.datatask.GetDataTaskLogReq;
import com.sp.proxverse.common.model.vo.datatask.RunDataTaskReq;
import com.sp.proxverse.common.model.vo.datatask.SaveDataTaskRequest;
import com.sp.proxverse.common.model.vo.datatask.SaveOrUpdateDataTaskChildReq;
import com.sp.proxverse.common.model.vo.datatask.TaskLogInfo;
import com.sp.proxverse.common.model.vo.request.DataModelSaveRequest;
import com.sp.proxverse.data.service.TransformationService;
import com.sp.proxverse.datamerge.service.biz.DataExtractorBizService;
import com.sp.proxverse.datamerge.service.biz.DataModelBizService;
import com.sp.proxverse.datamerge.service.biz.DataTaskBizService;
import com.sp.proxverse.interfaces.dao.impl.DataTaskChildServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TaskLogServiceImpl;
import com.sp.proxverse.interfaces.dao.service.DataExtractorService;
import com.sp.proxverse.job.schedule.ScheduleTask;
import com.sp.proxverse.job.service.JobSchedule;
import com.sp.proxverse.job.service.ScheduleTaskService;
import com.sp.proxverse.job.service.Task;
import com.sp.proxverse.job.service.impl.JobServiceImpl;
import com.sp.proxverse.test.data.increment.jdbc.JdbcPullDataImpl;
import com.sp.proxverse.test.data.model.TestModelUtils;
import com.sp.proxverse.test.data.pool.TestPoolUtils;
import java.io.FileNotFoundException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/12 17:04
 */
@Slf4j
@Component
public class TestDataTaskUtils {

  public static String dbName = "T_JDBC";
  public static List<String> JOB_ADD_CASE_SCHEMA =
      Arrays.asList("case_id", "time", "updatetime", "department");
  public static List<String> JOB_ADD_SCHEMA =
      Arrays.asList("id", "case_id", "event", "time", "updatetime");
  @Autowired public TestModelUtils testModelUtils;
  @Resource TransformationService transformationService;
  @Autowired TestPoolUtils testPoolUtils;
  @Autowired JobServiceImpl jobService;

  @Autowired ScheduleTaskService scheduleTaskService;

  @Autowired JdbcPullDataImpl jdbcPullDataImpl;
  @Autowired ScheduleTask scheduleTask;
  @Autowired private JobSchedule jobSchedule;
  @Autowired private DataTaskChildServiceImpl dataTaskChildService;
  @Autowired private DataModelBizService dataModelBizService;
  @Autowired private DataExtractorBizService dataExtractorBizService;
  @Autowired private DataExtractorService dataExtractorService;
  @Autowired private DataTaskBizService dataTaskBizService;
  @Autowired private TaskLogServiceImpl taskLogService;

  public static void runMySqlAddOneRowJobStatic(String sql) {
    try (Connection conn = TestJDBCUtils.getOrCreateMysqlConnection();
        Statement state = conn.createStatement()) {
      Class.forName("com.mysql.jdbc.Driver");
      int count = state.executeUpdate(sql);
      if (count > 0) {
        log.info("添加成功");
      } else {
        log.info("添加失败");
      }
    } catch (ClassNotFoundException | SQLException e) {
      e.printStackTrace();
    }
  }

  /**
   * 创建数据链接
   *
   * @param poolId
   * @return
   * @throws Exception
   */
  public Integer createJdbcConnection(Integer poolId) {
    JDBCEnum defaultMysql = TestJDBCUtils.getDefaultMysql();
    // 创建数据链接
    Integer connectorId =
        jdbcPullDataImpl.saveJdbcConnector(
            defaultMysql.name(),
            poolId,
            defaultMysql.getPort(),
            defaultMysql.getUser(),
            defaultMysql.getPassword(),
            defaultMysql.getDbname(),
            defaultMysql.getSchemaName(),
            null,
            defaultMysql.getJdbcType(),
            DataConnectorBizTypeEnum.STANDARD.getValue(),
            defaultMysql.ip);
    return connectorId;
  }

  /**
   * 创建数据提取
   *
   * @param extractName
   * @param connectorId
   */
  public Boolean createDataExtract(
      String extractName,
      Integer connectorId,
      List<SelectFieldSubRequest> fieldList,
      String sourceTableName,
      Integer poolId) {
    CheckDataExtractionReq checkDataExtractionReq = new CheckDataExtractionReq();
    checkDataExtractionReq.setName(extractName);
    checkDataExtractionReq.setDataConnectorId(connectorId);
    checkDataExtractionReq.setPoolId(poolId);
    checkDataExtractionReq.setTableName(sourceTableName);
    checkDataExtractionReq.setUpdateType(1);
    checkDataExtractionReq.setWhereRule("");
    checkDataExtractionReq.setFieldList(fieldList);
    Assertions.assertTrue(
        StringUtils.isEmpty(dataExtractorBizService.checkDataExtraction(checkDataExtractionReq)));

    List<CheckDataExtractionReq> request = new ArrayList<>();
    request.add(checkDataExtractionReq);
    return dataExtractorBizService.batchSaveDataExtraction(request);
  }

  public DataExtractorPO getLatestDataExtractor(Integer poolId) {
    return dataExtractorService.getOne(
        new LambdaQueryWrapper<DataExtractorPO>()
            .eq(DataExtractorPO::getPoolId, poolId)
            .orderByDesc(DataExtractorPO::getId)
            .eq(DataExtractorPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .last("limit 1"));
  }

  /**
   * 保存数据任务
   *
   * @param poolId
   * @param name
   * @return
   */
  public Integer saveDataTaskTest(Integer poolId, String name) {
    SaveDataTaskRequest saveDataTaskRequest = new SaveDataTaskRequest();
    saveDataTaskRequest.setName(name);
    saveDataTaskRequest.setPoolId(poolId);
    saveDataTaskRequest.setRateType(0);

    Integer dataTaskPOId = dataTaskBizService.saveDataTask(saveDataTaskRequest);
    return dataTaskPOId;
  }

  /**
   * 保存数据任务孩子
   *
   * @param poolId
   * @param dataTaskPOId
   * @param state
   * @param dataExtractorId
   * @return
   */
  public List<Integer> saveDataTaskChild(
      Integer poolId, Integer dataTaskPOId, Boolean state, Integer... dataExtractorId) {
    Integer stateInt;
    if (state) {
      stateInt = 0;
    } else {
      stateInt = 1;
    }

    List<Integer> dataTaskChildPoIds = new ArrayList<>();
    for (Integer integer : dataExtractorId) {
      SaveOrUpdateDataTaskChildReq saveOrUpdateDataTaskChildReq =
          new SaveOrUpdateDataTaskChildReq();
      saveOrUpdateDataTaskChildReq.setPoolId(poolId);
      saveOrUpdateDataTaskChildReq.setDataTaskId(dataTaskPOId);
      saveOrUpdateDataTaskChildReq.setChildType(0);
      saveOrUpdateDataTaskChildReq.setState(stateInt);
      saveOrUpdateDataTaskChildReq.setDataExtractorID(integer);

      Integer dataTaskChildPoId =
          dataTaskBizService.saveOrUpdateDataTaskChild(saveOrUpdateDataTaskChildReq);
      dataTaskChildPoIds.add(dataTaskChildPoId);
    }

    return dataTaskChildPoIds;
  }

  /**
   * 获取数据提取
   *
   * @param poolId
   * @param dataConnectorPOId
   * @return
   */
  public List<DataExtractorRes> getDataExtractorRes(Integer poolId, Integer dataConnectorPOId) {
    DataExtractorByConnectIdReq dataExtractorByConnectIdReq = new DataExtractorByConnectIdReq();
    dataExtractorByConnectIdReq.setPool(poolId);
    dataExtractorByConnectIdReq.setConnectId(dataConnectorPOId);
    return dataTaskBizService.getDataExtractorByConnectId(dataExtractorByConnectIdReq);
  }

  /**
   * 手动运行数据任务
   *
   * @param poolId
   * @param dataTaskId
   * @return
   */
  public Boolean runDataTasksManually(Integer poolId, Integer dataTaskId) {
    RunDataTaskReq runDataTaskReq = new RunDataTaskReq();
    runDataTaskReq.setDataTaskId(dataTaskId);
    runDataTaskReq.setPoolId(poolId);
    return dataTaskBizService.runDataTask(runDataTaskReq);
  }

  public List<RunTransformationResVo> runETLManually(Integer poolId, Integer dataTaskChildEtlId) {
    DataTaskChildPo dataTaskChildPo = dataTaskChildService.getById(dataTaskChildEtlId);

    RunTransformationReqVo runTransformationReqVo = new RunTransformationReqVo();
    runTransformationReqVo.setPoolId(poolId);
    runTransformationReqVo.setAsyncFlag(false);
    runTransformationReqVo.setTransformationId(dataTaskChildPo.getBindingId());

    RunTransformationRes runTransformationRes =
        transformationService.runTransformation(runTransformationReqVo);
    return getEtlRunResults(
        runTransformationRes.getSessionId(), runTransformationReqVo.getTransformationId());
  }

  public void waitEtlRun(Integer dataTaskChildEtlId) {
    DataTaskChildPo dataTaskChildPo = dataTaskChildService.getById(dataTaskChildEtlId);
    Integer cyclesNumber = 15;
    do {
      cyclesNumber--;
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        log.error("sleep error", e);
      }
      if (cyclesNumber == 0) {
        return;
      }
      try {
        RunTransformationResult runTransformationResult =
            transformationService.getRunTransformationResult(null, dataTaskChildPo.getBindingId());
        if (runTransformationResult.getFinish()) {
          return;
        }
      } catch (RuntimeException e) {
        return;
      }
    } while (true);
  }

  public List<RunTransformationResVo> getEtlRunResults(String sessionId, Integer transformationId) {
    Integer cyclesNumber = 15;
    RunTransformationResult runTransformationResult;
    do {
      cyclesNumber--;
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        log.error("sleep error", e);
      }
      runTransformationResult =
          transformationService.getRunTransformationResult(sessionId, transformationId);
      if (cyclesNumber == 0) {
        return new ArrayList<>();
      }
    } while (!runTransformationResult.getFinish());
    return runTransformationResult.getData();
  }

  public void waitTaskRun(Integer poolId, Integer dataTaskPOId, Integer runNumbers) {
    try {
      Thread.sleep(2000);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }

    for (Integer integer = 0; integer < runNumbers; integer++) {
      waitTaskRun(dataTaskPOId, poolId);
      List<Task> tasks = jobSchedule.loadTask();
      if (CollectionUtils.isNotEmpty(tasks)) {
        jobSchedule.runTask(tasks);
      }
      waitTaskRun(dataTaskPOId, poolId);
      if (tasks.size() == 0) {
        return;
      }
    }
  }

  public void waitTaskRun(Integer dataTaskPOId, Integer poolId) {
    List<TaskLogInfo> dataTaskLog = this.getDataTaskLog(dataTaskPOId, poolId);
    long count = dataTaskLog.stream().filter(po -> po.getState() == 3).count();

    if (count > 0) {
      try {
        Thread.sleep(2000);
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
      waitTaskRun(dataTaskPOId, poolId);
    }
    return;
  }

  /**
   * 获取数据任务日志
   *
   * @param dataTaskPOId
   * @param poolId
   * @return
   */
  public List<TaskLogInfo> getDataTaskLog(Integer dataTaskPOId, Integer poolId) {
    GetDataTaskLogReq getDataTaskLogReq = new GetDataTaskLogReq();
    getDataTaskLogReq.setPoolId(poolId);
    getDataTaskLogReq.setDataTaskId(dataTaskPOId);
    getDataTaskLogReq.setPageNum(1);
    getDataTaskLogReq.setPageSize(10);

    PageResp dataTaskLog = dataTaskBizService.getDataTaskLog(getDataTaskLogReq);

    return (List<TaskLogInfo>) dataTaskLog.getData().getList();
  }

  public Boolean dataModelAddFile(Integer modelId, String fileName) throws FileNotFoundException {
    Integer fileId = testModelUtils.findFileId(fileName);
    AddFile4ModelRequest request = new AddFile4ModelRequest();
    request.setDataModelId(modelId);
    request.setFileIdList(Lists.newArrayList(fileId));
    return dataModelBizService.addFile4DataModel(request);
  }

  /**
   * 通过日志检查数据提取拉去行数
   *
   * @param wait
   * @return
   * @throws InterruptedException
   */
  public Integer checkDateNumber(Integer taskLogInfoId, int wait) throws InterruptedException {
    TaskLogPo taskLogPo = taskLogService.getById(taskLogInfoId);
    Integer status = taskLogPo.getStatus();
    if (status == 3) {
      if (wait == 0) {
        Assertions.fail();
        return 0;
      }
      Thread.sleep(1000);
      wait = wait - 1;
      return checkDateNumber(taskLogInfoId, wait);
    } else if (status == 2) {
      return getLoadDataNumber(taskLogPo.getLogInfo());
    } else {
      log.error(taskLogPo.getLogInfo());
      Assertions.fail();
      return 0;
    }
  }

  private Integer getLoadDataNumber(String str) {
    String pattern = "Number of rows of pulled data\\s*:\\s*(\\d+)";
    Pattern regex = Pattern.compile(pattern);
    Matcher matcher = regex.matcher(str);

    if (matcher.find()) {
      String numberString = matcher.group(1);
      int number = Integer.parseInt(numberString);
      return number;
    } else {
      Assertions.fail();
    }
    return -1;
  }

  /**
   * 创建数据模型
   *
   * @param poolId
   * @param dataModeName
   * @param caseColumn 案例数据 case列名
   * @param eventColumn 案例数据 event列名
   * @param timeColumn 案例数据 time列名
   * @param fileNames 这个数据模型包含那些表
   * @return
   * @throws FileNotFoundException
   */
  public Integer createDataModer(
      Integer poolId,
      String dataModeName,
      String caseColumn,
      String eventColumn,
      String timeColumn,
      String... fileNames)
      throws FileNotFoundException {
    DataModelSaveRequest dataModelSaveRequest = new DataModelSaveRequest();
    Integer fileId = -1;
    if (fileNames.length < 1) {
      return null;
    }
    for (int i = 0; i < fileNames.length; i++) {
      if (i == 0) {
        fileId = testModelUtils.findFileId(fileNames[i]);
      }
      testModelUtils.findFileId(fileNames[i]);
    }
    dataModelSaveRequest.setCaseId(testModelUtils.findFieldId(fileId, caseColumn));
    dataModelSaveRequest.setEvent(testModelUtils.findFieldId(fileId, eventColumn));
    dataModelSaveRequest.setTime(testModelUtils.findFieldId(fileId, timeColumn));
    dataModelSaveRequest.setActiveFileId(fileId);
    dataModelSaveRequest.setPoolId(poolId);
    dataModelSaveRequest.setName(dataModeName);
    dataModelSaveRequest.setFileIdList(org.assertj.core.util.Lists.newArrayList(fileId));
    return dataModelBizService.saveDataModel(dataModelSaveRequest);
  }

  /**
   * @param poolId
   * @param dataTaskPOId
   * @param etlName
   * @param state 0 运行,1 暂停
   * @return
   */
  public Integer creatingETL(Integer poolId, Integer dataTaskPOId, String etlName, Integer state) {
    SaveOrUpdateDataTaskChildReq saveOrUpdateDataTaskChildReqEtl =
        new SaveOrUpdateDataTaskChildReq();
    saveOrUpdateDataTaskChildReqEtl.setPoolId(poolId);
    saveOrUpdateDataTaskChildReqEtl.setDataTaskId(dataTaskPOId);
    saveOrUpdateDataTaskChildReqEtl.setName(etlName);
    saveOrUpdateDataTaskChildReqEtl.setChildType(1);
    saveOrUpdateDataTaskChildReqEtl.setState(state);
    return dataTaskBizService.saveOrUpdateDataTaskChild(saveOrUpdateDataTaskChildReqEtl);
  }

  public Integer alterTransformation(
      Integer poolId, String etlName, Integer dataTaskChildEtlId, String sql) {
    DataTaskChildPo dataTaskChildPo = dataTaskChildService.getById(dataTaskChildEtlId);

    transformationService.getTableInfoListByPoolId(poolId);
    UpdateTransformationSqlResVo updateTransformationSqlResVo = new UpdateTransformationSqlResVo();
    updateTransformationSqlResVo.setPoolId(poolId);
    updateTransformationSqlResVo.setTransformationId(dataTaskChildPo.getBindingId());
    updateTransformationSqlResVo.setTransformationName(etlName);
    updateTransformationSqlResVo.setContentSql(sql);
    transformationService.getTableInfoListByPoolId(poolId);
    return transformationService.createOrUpdateTransformationSql(updateTransformationSqlResVo);
  }

  public void alterTransformationJob(Integer poolId, Integer dataTaskChildEtlId) {
    DataTaskChildPo dataTaskChildPo = dataTaskChildService.getById(dataTaskChildEtlId);
    RunTransformationReqVo runTransformationReqVo = new RunTransformationReqVo();
    runTransformationReqVo.setAsyncFlag(true);
    runTransformationReqVo.setPoolId(poolId);
    runTransformationReqVo.setTransformationId(dataTaskChildPo.getBindingId());
    runTransformationReqVo.setDataTaskId(dataTaskChildPo.getDataTaskId());
    jobService.runJobTransformation(runTransformationReqVo);
  }
}
