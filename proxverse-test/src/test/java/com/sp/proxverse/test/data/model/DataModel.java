package com.sp.proxverse.test.data.model;

import com.sp.proxverse.test.data.pool.TestPoolUtils;
import com.sp.proxverse.test.data.upload.TestFileUploadUtils;
import java.io.IOException;

public abstract class DataModel {

  TestFileUploadUtils testFileUploadUtils;

  TestModelUtils testModelUtils;

  TestPoolUtils testPoolUtils;

  private String modelName;

  private Integer poolId;

  public void setTestModelUtils(TestModelUtils testModelUtils) {
    this.testFileUploadUtils = testModelUtils.testFileUploadUtils;
    this.testModelUtils = testModelUtils;
    this.testPoolUtils = testModelUtils.testPoolUtils;
  }

  public void setModelName(String modelName) {
    this.modelName = modelName;
  }

  public void setPoolId(Integer poolId) {
    this.poolId = poolId;
  }

  public Integer getPoolId() {
    String name = "upload";
    Integer pool = poolId == null ? testPoolUtils.createPool(name) : poolId;
    return pool;
  }

  public abstract int create() throws IOException, InterruptedException;

  public String getModelName() {
    return this.modelName;
  };
}
