package com.sp.proxverse.test.source.jdbc;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CsvImporter {

  public static final String JDBC_DRIVER = "org.h2.Driver";
  public static final String DB_URL =
      "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false;DB_CLOSE_ON_EXIT=FALSE";
  public static final String USER = "sa";
  public static final String PASSWORD = "";
  private static final String DEFAULT_SCHEMA = "PUBLIC";
  private String schema;

  public CsvImporter(String schemaName) {
    this.schema = schemaName;
  }

  public void importData(String csvFile, String tableName) throws IOException, SQLException {
    List<String> columnNames;
    try (BufferedReader reader =
        new BufferedReader(
            new FileReader(
                Paths.get(
                        Objects.requireNonNull(
                                Thread.currentThread().getContextClassLoader().getResource(csvFile))
                            .toURI())
                    .toFile()))) {
      columnNames = Arrays.asList(reader.readLine().split(","));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }

    try (Connection connection = getConnection()) {
      initSchema(schema);
      createTable(schema, tableName, columnNames);

      String query = buildInsertQuery(schema, tableName, columnNames);
      try (PreparedStatement statement = connection.prepareStatement(query)) {
        try (BufferedReader reader =
            new BufferedReader(
                new InputStreamReader(
                    Objects.requireNonNull(
                        Thread.currentThread()
                            .getContextClassLoader()
                            .getResourceAsStream(csvFile))))) {
          reader
              .lines()
              .skip(1)
              .forEach(
                  line -> {
                    try {
                      for (int i = 0; i < columnNames.size(); i++) {
                        statement.setString(i + 1, line.split(",")[i]);
                      }
                      statement.executeUpdate();
                    } catch (SQLException e) {
                      e.printStackTrace();
                    }
                  });
        }
      }
    }
  }

  private void initSchema(String schema) throws SQLException {
    try (Connection connection = getConnection();
        ResultSet rs = connection.getMetaData().getSchemas()) {
      while (rs.next()) {
        if (schema.equalsIgnoreCase(rs.getString(1))) {
          return;
        }
      }
    }
    try (Connection connection = getConnection();
        PreparedStatement statement =
            connection.prepareStatement("CREATE SCHEMA `" + schema + "`")) {
      statement.executeUpdate();
    }
  }

  private void createTable(String schema, String tableName, List<String> columnNames)
      throws SQLException {
    StringBuilder queryBuilder = new StringBuilder("CREATE TABLE IF NOT EXISTS  `");
    queryBuilder.append(schema).append("`.`").append(tableName).append("`(");
    queryBuilder.append(
        columnNames.stream()
            .map(name -> "`" + name + "` VARCHAR(255)")
            .collect(Collectors.joining(",")));
    queryBuilder.append(")");
    String query = queryBuilder.toString();
    try (Connection connection = getConnection();
        PreparedStatement statement = connection.prepareStatement(query)) {
      statement.executeUpdate();
    }
  }

  private String buildInsertQuery(String schema, String tableName, List<String> columnNames) {
    StringBuilder queryBuilder = new StringBuilder("INSERT INTO `");
    queryBuilder.append(schema).append("`.`").append(tableName).append("`(");
    queryBuilder.append(
        columnNames.stream().map(name -> "`" + name + "`").collect(Collectors.joining(",")));
    queryBuilder.append(") VALUES (");
    queryBuilder.append(columnNames.stream().map(name -> "?").collect(Collectors.joining(",")));
    queryBuilder.append(")");
    return queryBuilder.toString();
  }

  private Connection getConnection() throws SQLException {
    return DriverManager.getConnection(DB_URL, USER, PASSWORD);
  }

  public static void main(String[] args) {
    CsvImporter importer = new CsvImporter("my_schema");

    try {
      importer.importData("data.csv", "my_table");
    } catch (IOException | SQLException e) {
      e.printStackTrace();
    }
  }

  public List<String> getColumns(String csvFile, String tableName)
      throws IOException, SQLException {
    List<String> columnNames;
    try (BufferedReader reader =
        new BufferedReader(
            new InputStreamReader(
                Objects.requireNonNull(
                    Thread.currentThread()
                        .getContextClassLoader()
                        .getResourceAsStream(csvFile))))) {
      return Arrays.asList(reader.readLine().split(","));
    }
  }
}
