package com.sp.proxverse.test.bpmn;

import com.prx.commons.utils.BpmnUtilsPy;
import com.prx.service.conformance.ConformanceService;
import com.sp.proxverse.common.model.dto.conformance.BpmnParam;
import com.sp.proxverse.common.util.FileUtil;
import java.util.Arrays;
import org.apache.spark.sql.catalyst.util.GenericArrayData;
import org.apache.spark.sql.pql.expressions.conformance.PetriNet;
import org.apache.spark.unsafe.types.UTF8String;
import org.junit.Assert;
import org.junit.Test;
import scala.Tuple2;

/**
 * <AUTHOR>
 * @date 2024/5/14 3:42 PM
 */
public class TestBpmn {

  /**
   * P_1 START -> 网关 -> -> 服务台处理 -> P_4 -> 运维人员处理 -> P_5 -> 填报人确认 -> P_6 -> END P_2-> 提交工单 -> P_3
   */
  String bpmnFilePath_one = "test_data/bpmn/1.bpmn";

  String bpmnFilePath_two = "test_data/bpmn/2.bpmn";

  /** START -> 网关 -> P_1-> 提交工单 -> P_2 -> 服务台处理 -> P_3 -> 运维人员处理 -> P_4 -> 填报人确认 -> P_5 -> END */
  String bpmnFilePath_three = "test_data/bpmn/3.bpmn";

  @Test
  public void test1() throws Exception {
    String[] eventList = {"提交工单", "服务台处理", "运维人员处理", "填报人确认"};
    String[] expect = {"Conforms", "Conforms", "Conforms", "Conforms", "Conforms"};

    verify(bpmnFilePath_one, eventList, expect);
  }

  @Test
  public void test2() throws Exception {
    String[] eventList = {"服务台处理", "服务台处理", "运维人员处理", "填报人确认"};
    String[] expect = {
      "服务台处理 executed as start activity",
      "服务台处理 is followed by 服务台处理",
      "Conforms",
      "Conforms",
      "Conforms"
    };

    verify(bpmnFilePath_one, eventList, expect);
  }

  @Test
  public void test4() throws Exception {
    String[] eventList = {"全单", "全单录入一岗", "全单比对", "信息补录"};
    String[] expect = {"Conforms", "Conforms", "Conforms", "Conforms"};

    verify(bpmnFilePath_two, eventList, expect);
  }

  @Test
  public void test5() throws Exception {
    String[] eventList = {"提交工单", "服务台处理", "提交工单", "服务台处理", "运维人员处理", "填报人确认", "运维人员处理", "填报人确认"};
    String[] expect = {
      "Conforms",
      "Conforms",
      "服务台处理 is followed by 提交工单",
      "Conforms",
      "Conforms",
      "Conforms",
      "Conforms",
      "Conforms"
    };

    verify(bpmnFilePath_three, eventList, expect);
  }

  @Test
  public void test6() throws Exception {
    String[] eventList = {"提交工单", "内训师审批", "提交工单"};
    String[] expect = {"Conforms", "Conforms", "内训师审批 is followed by 提交工单"};

    verify("test_data/bpmn/c1.bpmn", eventList, expect);
  }

  private void verify(String bpmnFilePath, String[] eventList, String[] expect) throws Exception {
    try {
      UTF8String[] utf8EventList =
          Arrays.stream(eventList).map(UTF8String::fromString).toArray(UTF8String[]::new);
      GenericArrayData checkGenericArrayData = new GenericArrayData(utf8EventList);
      String bpmnCode = FileUtil.readFileUt(bpmnFilePath);
      BpmnParam bpmnToPetriNetParams = BpmnUtilsPy.getBpmnToPetriNetParams(bpmnCode);
      PetriNet petriNet = ConformanceService.toPetriNet(bpmnToPetriNetParams);

      Tuple2<String, Integer>[] eventListValidWithReadable =
          petriNet.isEventListValidWithReadable(checkGenericArrayData);

      for (int i = 0; i < eventListValidWithReadable.length; i++) {
        Assert.assertEquals(eventListValidWithReadable[i]._1, expect[i]);
      }
    } catch (Exception e) {
    }
  }
}
