package com.sp.proxverse.test.source.jdbc;

import java.io.IOException;
import java.sql.SQLException;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class JDBCConfig {

  @Bean
  public CsvImporter csvImporter() throws SQLException, IOException {
    CsvImporter importer = new CsvImporter("test");
    importer.importData("test_data/source/jdbc/chinese_column.csv", "chinese_column");
    return importer;
  }
}
