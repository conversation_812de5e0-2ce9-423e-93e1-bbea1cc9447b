package com.sp.proxverse.test.data.model;

import com.sp.proxverse.common.model.dict.DataModelRunStatusEnum;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.vo.SetFieldOutputVO;
import com.sp.proxverse.common.model.vo.request.DataModelSaveRequest;
import java.io.IOException;
import java.util.Objects;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;

public class OrderNoJoinModel extends DataModel {

  public static final String MODEL_NAME = "order_no_join";

  public OrderNoJoinModel() {}

  public OrderNoJoinModel(TestModelUtils testModelUtils) {
    setTestModelUtils(testModelUtils);
  }

  // change it with your own path
  Integer poolId;

  @Override
  public int create() throws IOException, InterruptedException {
    poolId = testPoolUtils.createPool(MODEL_NAME);
    SetFieldOutputVO event =
        testFileUploadUtils.uploadFileToPool(
            "orderEvent.csv", "test_data/order/orderEvent.csv", poolId);
    SetFieldOutputVO caseFile =
        testFileUploadUtils.uploadFileToPool(
            "orderCase.csv", "test_data/order/orderCase.csv", poolId);

    DataModelSaveRequest dataModelSaveRequest = new DataModelSaveRequest();
    dataModelSaveRequest.setCaseId(testModelUtils.findFieldId(event.getFileId(), "caseid"));
    dataModelSaveRequest.setEvent(testModelUtils.findFieldId(event.getFileId(), "event"));
    dataModelSaveRequest.setTime(testModelUtils.findFieldId(event.getFileId(), "time"));
    dataModelSaveRequest.setSortingList(
        Lists.newArrayList(testModelUtils.findFieldId(event.getFileId(), "null_column")));
    dataModelSaveRequest.setActiveFileId(event.getFileId());
    dataModelSaveRequest.setPoolId(poolId);
    dataModelSaveRequest.setName(getModelName());
    dataModelSaveRequest.setFileIdList(Lists.newArrayList(event.getFileId(), caseFile.getFileId()));
    int modelId = testModelUtils.dataModelBizService.saveDataModel(dataModelSaveRequest);

    testModelUtils.modelBuilderService.buildModel(modelId);
    DataModelPO byId = testModelUtils.dataModelService.getById(modelId);
    if (!Objects.equals(byId.getStatus(), DataModelRunStatusEnum.LOADED.getValue())) {
      Assertions.fail("Failed to build model");
    }

    return modelId;
  }

  @Override
  public String getModelName() {
    return super.getModelName() == null ? MODEL_NAME : super.getModelName();
  }
}
