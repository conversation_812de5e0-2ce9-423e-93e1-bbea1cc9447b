package com.sp.proxverse.test.data.increment;

import com.prx.commons.utils.ModelUtils;
import com.prx.service.model.IncrementalSparkService;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.test.data.model.BillModel;
import com.sp.proxverse.test.data.model.OrderModel;
import com.sp.proxverse.test.data.model.OrderModelNoCaseTable;
import com.sp.proxverse.test.data.pql.TestPQLBase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class TestIncrementBuild extends TestPQLBase {

  @Autowired ModelUtils modelUtils;

  @Autowired IncrementalSparkService incrementalSparkService;

  @Autowired ModelDescFactory modelDescFactory;

  @Test
  void testAll() {
    testModelManager.getOrCreate(BillModel.MODEL_NAME);
    testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    testModelManager.getOrCreate(OrderModelNoCaseTable.MODEL_NAME);
  }
}
