package com.sp.proxverse.test.data.model;

import com.prx.commons.utils.ModelUtils;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.common.model.dict.DataModelRunStatusEnum;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelLoadLogPO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.vo.SetFieldOutputVO;
import com.sp.proxverse.common.model.vo.datamodel.request.AddForeignKeyRequest;
import com.sp.proxverse.common.model.vo.datamodel.request.AddForeignKeySubRequest;
import com.sp.proxverse.common.model.vo.request.DataModelSaveRequest;
import com.sp.proxverse.engine.service.biz.CopyBizService;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.DataModelLoadLogService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import com.sp.proxverse.test.data.pool.TestPoolUtils;
import com.sp.proxverse.test.data.pql.TestPQLBase;
import com.sp.proxverse.test.data.upload.TestFileUploadUtils;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.spark.sql.pql.PQLBuilder;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

@TestPropertySource(
    properties = {"prx.model.buildDimTables=true", "prx.model.build.bucketGroupEnabled=false"})
class DataModelBuildTest extends TestPQLBase {

  @Autowired ModelDescFactory modelDescFactory;

  @Autowired TestPoolUtils testPoolUtils;

  @Autowired TestFileUploadUtils testFileUploadUtils;

  @Autowired TestModelUtils testModelUtils;

  @Autowired PQLService pqlService;

  @Autowired DataModelService dataModelService;

  @Autowired private DataModelFileService dataModelFileService;

  @Autowired CopyBizService copyBizService;

  @Autowired ModelUtils modelUtils;

  @Autowired DataModelLoadLogService dataModelLoadLogService;

  @Test
  public void testBuildDimTables() throws IOException, InterruptedException {
    TPCHModel tpchModel = new TPCHModel(testModelUtils);
    int modelId = tpchModel.create();
    List<DataModelFilePO> tables = dataModelFileService.getEntityFileList(modelId);
    //    tables.stream()
    //        .map(t -> t.getSparkTableName())
    //        .forEach(name -> Assertions.assertTrue(name.contains("_bucket")));
    PQLBuilder pqlBuilder = newBuilder(modelDescFactory.getOrCreate(modelId));
    pqlBuilder.addColumn("`nation_dup`.`n_name`");
    pqlBuilder.addColumn("region.r_name");
    Assertions.assertNotNull(pqlBuilder.collect());
  }

  public static class BillIncompleteModel extends DataModel {

    public static final String MODEL_NAME = "bill_incomplete";

    public BillIncompleteModel() {}

    public BillIncompleteModel(TestModelUtils testModelUtils) {
      setTestModelUtils(testModelUtils);
    }

    @Override
    public int create() throws IOException, InterruptedException {
      Integer modelIdQuery = testModelUtils.dataModelBizService.getModelIdByName(getModelName());
      if (modelIdQuery != null) {
        return modelIdQuery;
      }
      Integer poolId = testPoolUtils.createPool("bill");
      SetFieldOutputVO activeFile =
          testFileUploadUtils.uploadFileToPool(
              "bill_active.csv", "test_data/bill/bill_active.csv", poolId);
      SetFieldOutputVO caseFile =
          testFileUploadUtils.uploadFileToPool(
              "bill_case_incomplete.csv", "test_data/bill/bill_case_incomplete.csv", poolId);
      DataModelSaveRequest dataModelSaveRequest = new DataModelSaveRequest();
      dataModelSaveRequest.setCaseId(testModelUtils.findFieldId(activeFile.getFileId(), "CaseID"));
      dataModelSaveRequest.setEvent(testModelUtils.findFieldId(activeFile.getFileId(), "Activity"));
      dataModelSaveRequest.setTime(testModelUtils.findFieldId(activeFile.getFileId(), "Eventend"));
      dataModelSaveRequest.setActiveFileId(activeFile.getFileId());
      dataModelSaveRequest.setPoolId(poolId);
      dataModelSaveRequest.setName(getModelName());
      dataModelSaveRequest.setFileIdList(
          Lists.newArrayList(activeFile.getFileId(), caseFile.getFileId()));
      int modelId = testModelUtils.dataModelBizService.saveDataModel(dataModelSaveRequest);
      AddForeignKeyRequest addForeignKeyRequest = new AddForeignKeyRequest();
      addForeignKeyRequest.setDataModelId(modelId);
      addForeignKeyRequest.setFileIdLeft(activeFile.getFileId());
      addForeignKeyRequest.setFileIdRight(caseFile.getFileId());
      AddForeignKeySubRequest addForeignKeySubRequest = new AddForeignKeySubRequest();
      addForeignKeySubRequest.setFileIdLeft(activeFile.getFileId());
      addForeignKeySubRequest.setFieldIdLeft(
          testModelUtils.findFieldId(activeFile.getFileId(), "CaseID"));
      addForeignKeySubRequest.setFileIdRight(caseFile.getFileId());
      addForeignKeySubRequest.setFieldIdRight(
          testModelUtils.findFieldId(caseFile.getFileId(), "CaseID"));
      addForeignKeyRequest.setList(Lists.newArrayList(addForeignKeySubRequest));
      testModelUtils.dataModelBizService.addForeignKey(addForeignKeyRequest);
      testModelUtils.dataModelBizService.setCaseTable(modelId, caseFile.getFileId(), poolId);
      testModelUtils.modelBuilderService.buildModel(modelId);
      DataModelPO byId = testModelUtils.dataModelService.getById(modelId);
      if (!Objects.equals(byId.getStatus(), DataModelRunStatusEnum.LOADED.getValue())) {
        Assertions.fail("Failed to build model");
      }
      return modelId;
    }

    @Override
    public String getModelName() {
      return MODEL_NAME;
    }
  }

  @Test
  public void testConsistencyCheck() throws IOException, InterruptedException {
    BillIncompleteModel model = new BillIncompleteModel(testModelUtils);
    int modelId = model.create();
    Optional<DataModelLoadLogPO> log =
        dataModelLoadLogService.list().stream()
            .filter(m -> m.getDataModelId() == modelId)
            .findFirst();
    Assertions.assertTrue(log.isPresent());
    Assertions.assertEquals("WARNING", log.get().getModelStatus());
  }
}
