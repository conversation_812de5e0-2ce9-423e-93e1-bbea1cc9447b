package com.sp.proxverse.test.data.model;

import com.sp.proxverse.common.model.dict.DataModelRunStatusEnum;
import com.sp.proxverse.common.model.po.DataModelPO;
import java.io.IOException;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;

/**
 * <AUTHOR>
 * @date 2024/2/18 14:50
 */
public class ProcessModel extends DataModel {

  public static final String MODEL_NAME = "process";

  public ProcessModel() {}

  public ProcessModel(TestModelUtils testModelUtils) {
    setTestModelUtils(testModelUtils);
  }

  Integer poolId;

  @Override
  public int create() throws IOException, InterruptedException {
    poolId = testPoolUtils.createPool(MODEL_NAME);
    int modelId =
        testModelUtils.getOrCreateSingleTableModel(
            "/process/process1.csv", "caseId", "event", "Eventend");

    testModelUtils.modelBuilderService.buildModel(modelId);
    DataModelPO byId = testModelUtils.dataModelService.getById(modelId);
    if (!Objects.equals(byId.getStatus(), DataModelRunStatusEnum.LOADED.getValue())) {
      Assertions.fail("Failed to build model");
    }
    return modelId;
  }

  @Override
  public String getModelName() {
    return super.getModelName() == null ? MODEL_NAME : super.getModelName();
  }
}
