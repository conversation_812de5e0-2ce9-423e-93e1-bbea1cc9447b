package com.sp.proxverse.test.data.filter;

import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterSubRequest;
import com.sp.proxverse.common.model.vo.request.SheetCaseViewRequest;
import com.sp.proxverse.engine.service.biz.ProcessTreeReadBizService;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestTopicFilter extends ProxverseTestBase {

  @Autowired private ProcessTreeReadBizService processTreeReadBizService;

  @Autowired private ProcessTreeWriteBizService processTreeWriteBizService;

  @Test
  public void testProcessExplorer() throws IOException, InterruptedException, ExecutionException {
    Integer[] sheet =
        super.createSheetCustomCsv(
            "/process/process.csv",
            "caseId",
            "event",
            "Eventend",
            BusinessTopicTypeEnum.BUSINESS_TREE,
            3);

    SheetCaseViewRequest sheetCaseViewRequest = new SheetCaseViewRequest();
    sheetCaseViewRequest.setTopicSheetId(sheet[1]);
    sheetCaseViewRequest.setSearchList(new ArrayList<>());
    sheetCaseViewRequest.setPageSize(999);
    Result topicSheetCaseView =
        processTreeReadBizService.getTopicSheetCaseView(sheetCaseViewRequest);
    assert (topicSheetCaseView.getData().length == 15);

    // add filter process
    SaveTopicFilterRequest saveTopicFilterRequest = new SaveTopicFilterRequest();
    List<SaveTopicFilterSubRequest> list = new ArrayList<>();
    SaveTopicFilterSubRequest saveTopicFilterSubRequest = new SaveTopicFilterSubRequest();
    List<String> paramValue = Arrays.asList("F,G", "B,E");
    saveTopicFilterSubRequest.setType(500);
    saveTopicFilterSubRequest.setParamValue(paramValue);
    List<String> filterEvent = Arrays.asList("A");
    saveTopicFilterSubRequest.setFilterEvent(filterEvent);
    list.add(saveTopicFilterSubRequest);
    saveTopicFilterRequest.setList(list);
    saveTopicFilterRequest.setTopicId(sheet[0]);
    processTreeWriteBizService.saveTopicFilterV2(saveTopicFilterRequest);

    SheetCaseViewRequest sheetCaseViewRequest1 = new SheetCaseViewRequest();
    sheetCaseViewRequest1.setTopicSheetId(sheet[1]);
    sheetCaseViewRequest1.setSearchList(new ArrayList<>());
    sheetCaseViewRequest1.setPageSize(999);
    Result topicSheetCaseView1 =
        processTreeReadBizService.getTopicSheetCaseView(sheetCaseViewRequest1);
    assert (topicSheetCaseView1.getData().length == 9);
  }
}
