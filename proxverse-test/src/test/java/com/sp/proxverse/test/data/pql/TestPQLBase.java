package com.sp.proxverse.test.data.pql;

import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.common.TestModelManager;
import com.sp.proxverse.test.data.model.BillModel;
import java.io.IOException;
import java.util.List;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.pql.ModelCatalog;
import org.apache.spark.sql.pql.PQLBuilder;
import org.apache.spark.sql.pql.manager.IndexManager;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.springframework.beans.factory.annotation.Autowired;

public class TestPQLBase extends ProxverseTestBase {

  @Autowired public ModelDescFactory modelDescFactory;

  @Autowired IndexManager indexManager;

  @Autowired public TestModelManager testModelManager;

  @Override
  public void afterPropertiesSet() throws Exception {
    super.afterPropertiesSet();
  }

  protected PQLBuilder newBuilder(ModelDesc modelDesc) {
    return new PQLBuilder(
        SparkSessionEnv.getSparkSession(),
        new ModelCatalog(modelDesc, SparkSessionEnv.getSparkSession()));
  }

  public void testAllJoinSideWithColumn(List<String> columns)
      throws IOException, InterruptedException {
    //
    //        // active join  VirtualCaseSide

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));
    PQLBuilder pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();

    // active join  VirtualBothSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // active join case side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_case.CaseID > 108996");
    result = pqlBuilder.collect();

    // active join active side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // active side join both side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    pqlBuilder.addTopicFilter("bill_case.CaseID < 109996");
    result = pqlBuilder.collect();

    // case join  VirtualCaseSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    result = pqlBuilder.collect();

    // case join  VirtualBothSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // case join case side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_case.CaseID > 108996");
    result = pqlBuilder.collect();

    // case join active side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // case side join both side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    pqlBuilder.addTopicFilter("bill_case.CaseID < 109996");
    result = pqlBuilder.collect();

    // both join  VirtualCaseSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    result = pqlBuilder.collect();

    // both join  VirtualBothSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // both join case side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_case.CaseID > 108996");
    result = pqlBuilder.collect();

    // both join active side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // both side join both side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("bill_case.CaseID");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    pqlBuilder.addTopicFilter("bill_case.CaseID < 109996");
    result = pqlBuilder.collect();

    // VirtualCaseSide join  VirtualCaseSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    result = pqlBuilder.collect();

    // VirtualCaseSide join  VirtualBothSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // VirtualCaseSide join case side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_case.CaseID > 108996");
    result = pqlBuilder.collect();

    // VirtualCaseSide join active side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // VirtualCaseSide side join both side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    pqlBuilder.addTopicFilter("bill_case.CaseID < 109996");
    result = pqlBuilder.collect();

    // VirtualBothSide join  VirtualCaseSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    result = pqlBuilder.collect();

    // VirtualBothSide join  VirtualBothSide
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // VirtualBothSide join case side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    pqlBuilder.addTopicFilter("bill_case.CaseID > 108996");
    result = pqlBuilder.collect();

    // VirtualBothSide join active side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    result = pqlBuilder.collect();

    // VirtualBothSide side join both side
    pqlBuilder = newBuilder(orCreate);

    pqlBuilder.addColumn("`case_table`.preCaseId");
    pqlBuilder.addColumn("bill_active.CaseID");
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter("bill_active.CaseID > 108996");
    pqlBuilder.addTopicFilter("bill_case.CaseID < 109996");
    result = pqlBuilder.collect();
  }
}
