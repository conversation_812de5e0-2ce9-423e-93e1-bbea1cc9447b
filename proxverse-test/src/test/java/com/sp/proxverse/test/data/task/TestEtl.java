package com.sp.proxverse.test.data.task;

import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.DataTaskChildPo;
import com.sp.proxverse.common.model.vo.api.RunTransformationReqVo;
import com.sp.proxverse.common.model.vo.api.RunTransformationRes;
import com.sp.proxverse.common.model.vo.api.RunTransformationResVo;
import com.sp.proxverse.common.model.vo.api.RunTransformationResult;
import com.sp.proxverse.common.model.vo.api.TableInfoListByPoolIdResVo;
import com.sp.proxverse.common.util.DataSourceUtils;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.data.service.impl.TransformationServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.DataTaskChildServiceImpl;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.common.TestDataTaskUtils;
import com.sp.proxverse.test.common.TestModelManager;
import com.sp.proxverse.test.data.model.OrderModel;
import com.sp.proxverse.test.data.model.ProcessModel;
import java.util.List;
import org.apache.spark.sql.AnalysisException;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.SparkSessionEnv;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:33
 */
public class TestEtl extends ProxverseTestBase {

  @Autowired public TransformationServiceImpl transformationService;
  @Autowired TestModelManager testModelManager;
  @Autowired TestDataTaskUtils testDataTaskUtils;
  @Autowired DataModelService dataModelService;
  @Autowired DataTaskChildServiceImpl dataTaskChildService;

  @Test
  public void testForLoop() throws AnalysisException {
    int modelId = testModelManager.getOrCreate(ProcessModel.MODEL_NAME);
    SparkSession sparkSession = SparkSessionEnv.getSparkSession();

    sparkSession.catalog().listTables(DataSourceUtils.makeDatabaseName(modelId)).show();
    sparkSession.sql("CREATE TABLE IF NOT EXISTS test (name STRING, age INT)");
    sparkSession.sql("select * from test").show();
    sparkSession
        .sql(
            "WHILE (SELECT COUNT(*) FROM test) < 1000\n"
                + "limit 11\n"
                + "loop_begin \n"
                + "INSERT INTO test VALUES ('name', 001);"
                + "INSERT INTO test VALUES ('name', 001);"
                + "loop_end \n")
        .collectAsList();
    List<Row> rows = sparkSession.sql("select * from test").collectAsList();
    Assertions.assertEquals(22, rows.size());
    Assertions.assertEquals("name", rows.get(0).getString(0));

    sparkSession
        .sql(
            "WHILE (SELECT COUNT(*) FROM test) < 100\n"
                + "loop_begin \n"
                + "INSERT INTO test VALUES ('name', 001);"
                + "loop_end \n")
        .collectAsList();
    rows = sparkSession.sql("select * from test").collectAsList();
    Assertions.assertEquals(25, rows.size());
  }

  @Test
  public void testForLoopInEtl() {
    int modelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    DataModelPO dataModelPO = dataModelService.getById(modelId);
    Integer poolId = dataModelPO.getPoolId();
    Integer dataTaskPOId = testDataTaskUtils.saveDataTaskTest(poolId, "数据任务-testForLoop");
    Integer dataTaskChildEtlId = testDataTaskUtils.creatingETL(poolId, dataTaskPOId, "etlName", 0);
    List<TableInfoListByPoolIdResVo> tableInfoListByPoolId =
        transformationService.getTableInfoListByPoolId(poolId);
    String tableName = tableInfoListByPoolId.get(0).getTableName();

    DataTaskChildPo dataTaskChildPo = dataTaskChildService.getById(dataTaskChildEtlId);
    Integer transformationId = dataTaskChildPo.getBindingId();
    RunTransformationReqVo runTransformationReqVo = new RunTransformationReqVo();
    runTransformationReqVo.setPoolId(poolId);
    runTransformationReqVo.setTransformationId(transformationId);

    runTransformationReqVo.setSqlContent(
        "create table test1 as select * from "
            + tableName
            + "; select count(*) from test1;"
            + "WHILE (select count(*) from `test1` ) < 1000\n"
            + "limit 10\n"
            + "loop_begin insert into  `test1` select * from test1;\n"
            + "loop_end ");

    RunTransformationRes runTransformationRes =
        transformationService.runTransformation(runTransformationReqVo);
    List<RunTransformationResVo> etlRunResults =
        testDataTaskUtils.getEtlRunResults(runTransformationRes.getSessionId(), transformationId);
    Assertions.assertEquals(4, etlRunResults.size());

    List<TableInfoListByPoolIdResVo> tableInfoListByPoolId1 =
        transformationService.getTableInfoListByPoolId(poolId);
    TableInfoListByPoolIdResVo tableInfoListByPoolIdResVo =
        tableInfoListByPoolId1.get(tableInfoListByPoolId1.size() - 1);
    Assertions.assertEquals("test1", tableInfoListByPoolIdResVo.getTableName());
    Assertions.assertEquals("1.93", tableInfoListByPoolIdResVo.getParquetSize());
  }

  @Test
  public void testAsyncRunEtl() {
    int modelId = testModelManager.getOrCreate(ProcessModel.MODEL_NAME);
    DataModelPO dataModelPO = dataModelService.getById(modelId);
    Integer poolId = dataModelPO.getPoolId();

    Integer dataTaskPOId = testDataTaskUtils.saveDataTaskTest(poolId, "数据任务-testAsyncRunEtl");

    Integer dataTaskChildEtlId = testDataTaskUtils.creatingETL(poolId, dataTaskPOId, "etlName", 0);
    DataTaskChildPo dataTaskChildPo = dataTaskChildService.getById(dataTaskChildEtlId);
    Integer transformationId = dataTaskChildPo.getBindingId();

    List<TableInfoListByPoolIdResVo> tableInfoListByPoolId =
        transformationService.getTableInfoListByPoolId(poolId);
    String tableName = tableInfoListByPoolId.get(0).getTableName();
    RunTransformationReqVo runTransformationReqVo = new RunTransformationReqVo();
    runTransformationReqVo.setPoolId(poolId);
    runTransformationReqVo.setSqlContent("select * from " + tableName);
    runTransformationReqVo.setTransformationId(transformationId);

    // Check run results
    RunTransformationRes runTransformationRes =
        transformationService.runTransformation(runTransformationReqVo);
    String sessionId = runTransformationRes.getSessionId();
    RunTransformationResult runTransformationResult =
        transformationService.getRunTransformationResult(sessionId, transformationId);
    Assertions.assertFalse(runTransformationResult.getFinish());

    List<RunTransformationResVo> etlRunResults =
        testDataTaskUtils.getEtlRunResults(sessionId, transformationId);
    Assertions.assertEquals(1, etlRunResults.size());
    Assertions.assertEquals(52, etlRunResults.get(0).getCount());
    Assertions.assertEquals("B", etlRunResults.get(0).getData()[1][1]);
    Assertions.assertEquals("2021-01-02", etlRunResults.get(0).getData()[1][2]);

    runTransformationResult =
        transformationService.getRunTransformationResult(sessionId, transformationId);
    Assertions.assertTrue(runTransformationResult.getData().isEmpty());

    // Check for duplicate execution
    transformationService.runTransformation(runTransformationReqVo);
    try {
      transformationService.runTransformation(runTransformationReqVo);
      Assertions.fail();
    } catch (Exception e) {
      Assertions.assertEquals(e.getMessage(), I18nUtil.getMessage(I18nConst.ETL_IS_RUNNING));
    }

    // check cleanUp
    transformationService.cleanUpEtlResults(sessionId, transformationId);
    try {
      Thread.sleep(3000);
    } catch (InterruptedException e) {
      // do nothing
    }
    Assertions.assertNull(transformationService.getEtlCacheResults(sessionId, transformationId));
    runTransformationResult =
        transformationService.getRunTransformationResult(sessionId, transformationId);
    Assertions.assertEquals(runTransformationResult.getFinish(), true);
    Assertions.assertTrue(runTransformationResult.getData().isEmpty());
  }
}
