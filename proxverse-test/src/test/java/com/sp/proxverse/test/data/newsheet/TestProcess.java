package com.sp.proxverse.test.data.newsheet;

import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.dto.process.ProcessTreeChartOut;
import com.sp.proxverse.common.model.dto.process.ProcessTreeChartRequest;
import com.sp.proxverse.common.model.dto.process.ProcessTreeVariantOut;
import com.sp.proxverse.common.model.dto.process.ProcessTreeVariantRequest;
import com.sp.proxverse.common.model.enums.ProcessPathKpiType;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.vo.processExplorer.EventNode;
import com.sp.proxverse.common.model.vo.processExplorer.LineNode;
import com.sp.proxverse.common.model.vo.processExplorer.ProcessExplorerRes;
import com.sp.proxverse.common.model.vo.processai.TopicProcessKpiResVo;
import com.sp.proxverse.common.model.vo.request.ProcessExplorerRequest;
import com.sp.proxverse.common.model.vo.sheet.SaveComponentRequest;
import com.sp.proxverse.engine.service.ProcessTreeService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.common.TestModelManager;
import com.sp.proxverse.test.data.model.OrderModelNameAction;
import com.sp.proxverse.test.data.model.ProcessCommaModel;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @create 2023-04-24 16:42
 */
public class TestProcess extends ProxverseTestBase {

  @Autowired ProcessTreeService processTree;

  @Autowired TestModelManager testModelManager;

  @Test
  public void testOverallProcessExplorer() throws IOException, InterruptedException {
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7);

    SaveComponentRequest.builder().sheetId(sheet[1]).type(20).name("testComponent").build();
    // init
    ProcessExplorerRequest request = new ProcessExplorerRequest();
    request.setTopicId(sheet[0]);
    ProcessExplorerRes processExplorerRes = processTree.processExplorer(request);

    Assertions.assertEquals("15.5", processExplorerRes.getEventNodes().get(5).getEventRatio());
    Assertions.assertEquals(
        "1.0", processExplorerRes.getEventNodes().get(5).getOccursOnAveragePerCase());
    Assertions.assertEquals(760, processExplorerRes.getEventCount());
    Assertions.assertEquals(121, processExplorerRes.getEventNodes().get(3).getNumber());
    Assertions.assertEquals(false, processExplorerRes.getEventNodes().get(3).getIsEnd());
    Assertions.assertEquals(false, processExplorerRes.getEventNodes().get(5).getIsEnd());
    Assertions.assertEquals(118, processExplorerRes.getEventNodes().get(5).getNumber());

    request.setTimeType(1);
    request.setValueShowType(1);
    ProcessExplorerRes processExplorerRes1 = processTree.processExplorer(request);
    Assertions.assertEquals(760, processExplorerRes1.getEventCount());

    Assertions.assertEquals(46, processExplorerRes1.getLineNodes().size());
    Assertions.assertEquals("0.3天", processExplorerRes1.getLineNodes().get(2).getSecond());

    List<LineNode> lineNodes = processExplorerRes1.getLineNodes();

    List<LineNode> lineNodes1 =
        lineNodes.stream().filter(po -> po.getGradient() == 1).collect(Collectors.toList());
    List<String> lineNodesSources =
        lineNodes1.stream().map(LineNode::getSource).collect(Collectors.toList());
    Assertions.assertTrue(lineNodesSources.contains("T10 Determine necessity to stop indication"));
    Assertions.assertTrue(lineNodesSources.contains("T16 Report reasons to hold request"));
    Assertions.assertTrue(lineNodesSources.contains("T17 Check report Y to stop indication"));
    Assertions.assertTrue(lineNodesSources.contains("T19 Determine report Y to stop indication"));

    List<String> lineNodesTargets =
        lineNodes1.stream().map(LineNode::getTarget).collect(Collectors.toList());
    Assertions.assertTrue(lineNodesTargets.contains("T16 Report reasons to hold request"));
    Assertions.assertTrue(lineNodesTargets.contains("T17 Check report Y to stop indication"));
    Assertions.assertTrue(lineNodesTargets.contains("T19 Determine report Y to stop indication"));
    Assertions.assertTrue(lineNodesTargets.contains("T20 Print report Y to stop indication"));
    Assertions.assertEquals(5, lineNodesTargets.size());

    List<LineNode> lineNodes2 =
        lineNodes.stream().filter(po -> po.getGradient() == 2).collect(Collectors.toList());

    List<String> lineNodesTargets2 =
        lineNodes2.stream().map(LineNode::getTarget).collect(Collectors.toList());
    Assertions.assertTrue(lineNodesTargets2.contains("T11 Create document X request unlicensed"));

    List<String> lineNodesSource2 =
        lineNodes2.stream().map(LineNode::getSource).collect(Collectors.toList());
    Assertions.assertTrue(lineNodesSource2.contains("T10 Determine necessity to stop indication"));
    Assertions.assertTrue(lineNodesSource2.contains("T11 Create document X request unlicensed"));

    List<LineNode> lineNodes3 =
        lineNodes.stream().filter(po -> po.getGradient() == 3).collect(Collectors.toList());

    List<String> lineNodesTargets3 =
        lineNodes3.stream().map(LineNode::getTarget).collect(Collectors.toList());

    Assertions.assertTrue(lineNodesTargets3.contains("T02 Check confirmation of receipt"));
    Assertions.assertTrue(lineNodesTargets3.contains("T03 Adjust confirmation of receipt"));
    Assertions.assertTrue(lineNodesTargets3.contains("T06 Determine necessity of stop advice"));
    List<String> lineNodesSource3 =
        lineNodes3.stream().map(LineNode::getSource).collect(Collectors.toList());

    Assertions.assertTrue(lineNodesSource3.contains("T02 Check confirmation of receipt"));
    Assertions.assertTrue(lineNodesSource3.contains("T03 Adjust confirmation of receipt"));
    Assertions.assertTrue(lineNodesSource3.contains("T06 Determine necessity of stop advice"));
  }

  @Test
  public void testProcessExplorer() throws IOException, InterruptedException {
    Integer[] sheet =
        super.createSheetCustomCsv(
            "/process/process.csv",
            "caseId",
            "event",
            "Eventend",
            BusinessTopicTypeEnum.BUSINESS_TREE,
            7);

    Integer processTreeKpiId = super.createProcessKpi(sheet[0]);
    SaveComponentRequest.builder().sheetId(sheet[1]).type(20).name("testComponent").build();
    // init
    ProcessExplorerRequest request = new ProcessExplorerRequest();
    request.setTopicId(sheet[0]);
    request.setProcessTreeKpiId(processTreeKpiId);
    ProcessExplorerRes processExplorerRes = processTree.processExplorer(request);

    List<String> eventRatios =
        processExplorerRes.getEventNodes().stream()
            .map(EventNode::getEventRatio)
            .collect(Collectors.toList());
    Assertions.assertTrue(eventRatios.contains("20.0"));
    Assertions.assertTrue(eventRatios.contains("28.9"));
    Assertions.assertTrue(eventRatios.contains("13.3"));
    Assertions.assertTrue(eventRatios.contains("13.3"));
    Assertions.assertTrue(eventRatios.contains("8.9"));
    Assertions.assertTrue(eventRatios.contains("6.7"));
    Assertions.assertEquals(9, eventRatios.size());

    List<String> caseEventRatios =
        processExplorerRes.getEventNodes().stream()
            .map(EventNode::getEventCaseRatio)
            .collect(Collectors.toList());
    Assertions.assertTrue(caseEventRatios.contains("86.7"));
    Assertions.assertTrue(caseEventRatios.contains("60.0"));
    Assertions.assertTrue(caseEventRatios.contains("40.0"));
    Assertions.assertTrue(caseEventRatios.contains("20.0"));
    Assertions.assertTrue(caseEventRatios.contains("26.7"));

    List<String> colors =
        processExplorerRes.getEventNodes().stream()
            .map(EventNode::getColor)
            .collect(Collectors.toList());
    Assertions.assertTrue(colors.contains("红"));

    List<String> occursOnAveragePerCases =
        processExplorerRes.getEventNodes().stream()
            .map(EventNode::getOccursOnAveragePerCase)
            .collect(Collectors.toList());
    Assertions.assertTrue(occursOnAveragePerCases.contains("1.0"));

    List<KpiPO> eventKpis = processExplorerRes.getEventKpis();
    Assertions.assertEquals(2, eventKpis.size());
    Assertions.assertEquals("1", eventKpis.get(0).getColumnType());
    Assertions.assertEquals("event_test_format", eventKpis.get(0).getFormat());
    Assertions.assertEquals("event_test_formatting", eventKpis.get(0).getFormatting());
    Assertions.assertEquals("event_test_formatting", eventKpis.get(1).getFormatting());
    Assertions.assertEquals("null", eventKpis.get(1).getExpression());

    Assertions.assertEquals(15, processExplorerRes.getLineNodes().size());
    Assertions.assertEquals(15, processExplorerRes.getLineNodes().size());
    List<LineNode> collect =
        processExplorerRes.getLineNodes().stream()
            .filter(
                po -> Objects.equals("B", po.getSource()) && Objects.equals("E", po.getTarget()))
            .collect(Collectors.toList());
    Assertions.assertEquals(1, collect.size());
    LineNode lineNode = collect.get(0);

    Assertions.assertEquals("33.3", lineNode.getLineCaseRatio());
    Assertions.assertEquals("NULL", lineNode.getKpiValues().get(0));
    Assertions.assertEquals("5", lineNode.getNumber().toString());
    Assertions.assertEquals(true, lineNode.getDefaultLine());

    assert (processExplorerRes.getLineNodes().get(3).getDefaultLine());

    ProcessExplorerRequest request1 = new ProcessExplorerRequest();
    ArrayList<String> filterEvents = new ArrayList<>();
    filterEvents.add("A");
    request1.setTopicId(sheet[0]);
    request1.setFilterEvents(filterEvents);
    ProcessExplorerRes processExplorerFilter = processTree.processExplorer(request1);
    Assertions.assertEquals(8, processExplorerFilter.getEventNodes().size());
    Assertions.assertEquals(14, processExplorerFilter.getLineNodes().size());

    ProcessExplorerRequest request2 = new ProcessExplorerRequest();
    filterEvents.add("A");
    filterEvents.add("B");
    filterEvents.add("C");
    filterEvents.add("D");
    filterEvents.add("E");
    filterEvents.add("F");
    filterEvents.add("G");
    filterEvents.add("H");
    request2.setTopicId(sheet[0]);
    request2.setFilterEvents(filterEvents);
    processExplorerFilter = processTree.processExplorer(request2);
    Assertions.assertEquals(0, processExplorerFilter.getEventNodes().size());
    Assertions.assertEquals(0, processExplorerFilter.getLineNodes().size());
  }

  @Test
  public void testProcessPathKpi() throws IOException, InterruptedException {
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7);

    Integer processTreeKpiId = super.createProcessKpi(sheet[0]);

    List<TopicProcessKpiResVo> results = processTree.getTopicProcessKpiList(sheet[0]);
    Assertions.assertEquals(1, results.size());
    TopicProcessKpiResVo topicProcessKpiResVo = results.get(0);
    Assertions.assertEquals(1, topicProcessKpiResVo.getPathKpi().size());
    Assertions.assertEquals("processKPIName", topicProcessKpiResVo.getName());
    Assertions.assertTrue(topicProcessKpiResVo.getPathKpi().contains("path_kpi"));

    ProcessTreeVariantRequest processTreeVariantRequest = new ProcessTreeVariantRequest();
    processTreeVariantRequest.setTopicId(sheet[0]);
    processTreeVariantRequest.setProcessTreeKpiId(processTreeKpiId);
    ProcessTreeVariantOut processTreeVariantOut =
        processTree.processTreeVariant(processTreeVariantRequest);
    Assertions.assertEquals(9, processTreeVariantOut.getVariantList().size());
    Assertions.assertEquals(
        "Confirmation of receipt,T02 Check confirmation of receipt,T04 Determine confirmation of receipt,T05 Print and send confirmation of receipt,T06 Determine necessity of stop advice,T10 Determine necessity to stop indication",
        processTreeVariantOut.getVariantList().get(0).getVariant());
    Assertions.assertEquals(90, processTreeVariantOut.getVariantList().get(0).getCount());

    processTreeVariantRequest.setShowPathKpiName(topicProcessKpiResVo.getPathKpi().get(0));
    processTreeVariantRequest.setProcessTreeKpiId(topicProcessKpiResVo.getProcessKpiId());
    processTreeVariantRequest.setShowPathKpiType(ProcessPathKpiType.CUSTOMER_KPI);
    ProcessTreeVariantOut processTreeVariantPathKpi =
        processTree.processTreeVariant(processTreeVariantRequest);
    Assertions.assertEquals(
        "190", processTreeVariantPathKpi.getVariantList().get(0).getPathKpiValue());
    Assertions.assertEquals(90, processTreeVariantPathKpi.getVariantList().get(0).getCount());
    Assertions.assertEquals(
        "123", processTreeVariantPathKpi.getVariantList().get(1).getPathKpiValue());
    Assertions.assertFalse(processTreeVariantPathKpi.getPathKpis().isEmpty());
    Assertions.assertEquals("path_kpi", processTreeVariantPathKpi.getPathKpis().get(0).getName());
    Assertions.assertEquals(
        "count(*)+100", processTreeVariantPathKpi.getPathKpis().get(0).getExpression());

    List<KpiPO> pathKpis = processTreeVariantPathKpi.getPathKpis();
    Assertions.assertEquals(1, pathKpis.size());
    Assertions.assertEquals("1", pathKpis.get(0).getColumnType());
    Assertions.assertEquals("path_kpi_format", pathKpis.get(0).getFormat());
  }

  @Test
  public void testEventCommaProcessExplorer() {
    int modelId = testModelManager.getOrCreate(ProcessCommaModel.MODEL_NAME);
    Integer[] sheet = createSheetBase(modelId, BusinessTopicTypeEnum.BUSINESS_TREE, 1);
    ProcessExplorerRequest request = new ProcessExplorerRequest();

    request.setTopicId(sheet[0]);
    ProcessExplorerRes processExplorerRes = processTree.processExplorer(request);

    Assertions.assertEquals("", processExplorerRes.getErrorMsg());
    Assertions.assertEquals(6, processExplorerRes.getEventNodes().size());

    List<EventNode> eventNodes = processExplorerRes.getEventNodes();
    List<String> eventNodeNames =
        eventNodes.stream().map(EventNode::getName).collect(Collectors.toList());
    Assertions.assertTrue(eventNodeNames.contains("B,B"));
    Assertions.assertTrue(eventNodeNames.contains("B"));
    Assertions.assertTrue(eventNodeNames.contains("A"));
    Assertions.assertTrue(eventNodeNames.contains("C"));

    List<LineNode> lineNodes = processExplorerRes.getLineNodes();
    List<String> lineSourceNames =
        lineNodes.stream().map(LineNode::getSource).collect(Collectors.toList());
    Assertions.assertTrue(lineSourceNames.contains("B,B"));
    Assertions.assertTrue(lineSourceNames.contains("B"));
    Assertions.assertTrue(lineSourceNames.contains("A"));
    Assertions.assertTrue(lineSourceNames.contains("C"));

    List<String> lineTargetNames =
        lineNodes.stream().map(LineNode::getTarget).collect(Collectors.toList());
    Assertions.assertTrue(lineTargetNames.contains("B,B"));
    Assertions.assertTrue(lineTargetNames.contains("B"));
    Assertions.assertTrue(lineTargetNames.contains("A"));
    Assertions.assertTrue(lineTargetNames.contains("C"));

    ArrayList<String> filterEvents = new ArrayList<>();
    filterEvents.add("A");
    request.setFilterEvents(filterEvents);
    processExplorerRes = processTree.processExplorer(request);

    Assertions.assertEquals("", processExplorerRes.getErrorMsg());

    Assertions.assertEquals(5, processExplorerRes.getEventNodes().size());

    eventNodes = processExplorerRes.getEventNodes();
    eventNodeNames = eventNodes.stream().map(EventNode::getName).collect(Collectors.toList());
    Assertions.assertTrue(eventNodeNames.contains("B,B"));
    Assertions.assertTrue(eventNodeNames.contains("B"));
    Assertions.assertTrue(eventNodeNames.contains("C"));

    lineNodes = processExplorerRes.getLineNodes();
    lineSourceNames = lineNodes.stream().map(LineNode::getSource).collect(Collectors.toList());
    Assertions.assertTrue(lineSourceNames.contains("B,B"));
    Assertions.assertTrue(lineSourceNames.contains("B"));
    Assertions.assertTrue(lineSourceNames.contains("C"));

    lineTargetNames = lineNodes.stream().map(LineNode::getTarget).collect(Collectors.toList());
    Assertions.assertTrue(lineTargetNames.contains("B,B"));
    Assertions.assertTrue(lineTargetNames.contains("B"));
    Assertions.assertTrue(lineTargetNames.contains("C"));
  }

  // @Test
  public void testDefaultProcessChart() {
    int modelId = testModelManager.getOrCreate(OrderModelNameAction.MODEL_NAME);
    Integer[] sheet = createSheetBase(modelId, BusinessTopicTypeEnum.BUSINESS_TREE, 1);
    ProcessTreeChartRequest processTreeChartRequest = new ProcessTreeChartRequest();
    processTreeChartRequest.setTopicId(sheet[0]);
    processTreeChartRequest.setDefaultChartFlag(true);
    ProcessTreeChartOut processTreeChartOut = processTree.processTreeChart(processTreeChartRequest);
    Assertions.assertEquals(5, processTreeChartOut.getLineNodes().size());
    Assertions.assertEquals(4, processTreeChartOut.getEventNodes().size());
    List<String> lineSourceNames =
        processTreeChartOut.getLineNodes().stream()
            .map(LineNode::getSource)
            .collect(Collectors.toList());
    Assertions.assertTrue(lineSourceNames.contains("<EMAIL>"));
    Assertions.assertTrue(lineSourceNames.contains("<EMAIL>"));
    List<String> lineTargetNames =
        processTreeChartOut.getLineNodes().stream()
            .map(LineNode::getTarget)
            .collect(Collectors.toList());
    Assertions.assertTrue(lineTargetNames.contains("<EMAIL>"));
    Assertions.assertTrue(lineTargetNames.contains("<EMAIL>"));
    List<String> eventNodeNames =
        processTreeChartOut.getEventNodes().stream()
            .map(EventNode::getName)
            .collect(Collectors.toList());
    Assertions.assertTrue(eventNodeNames.contains("<EMAIL>"));
    Assertions.assertTrue(eventNodeNames.contains("<EMAIL>"));
  }
}
