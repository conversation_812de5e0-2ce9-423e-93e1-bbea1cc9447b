package com.sp.proxverse.test.data.topic;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.prx.service.DimensionQueryService;
import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.dto.TopicScriptFilter;
import com.sp.proxverse.common.model.po.BusinessTopicPO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.common.model.vo.TopicSheetKpiOutputVO;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterSubRequest;
import com.sp.proxverse.common.model.vo.request.AddTopicSheetRequest;
import com.sp.proxverse.common.model.vo.request.KpiSaveRequest;
import com.sp.proxverse.common.model.vo.request.SaveBusinessTopicRequest;
import com.sp.proxverse.common.model.vo.request.processAi.SheetReleaseReqVo;
import com.sp.proxverse.datamerge.service.biz.DataModelBizService;
import com.sp.proxverse.engine.service.biz.BusinessTopicBizServiceImpl;
import com.sp.proxverse.engine.service.biz.ComponentBizService;
import com.sp.proxverse.engine.service.biz.CopyBizService;
import com.sp.proxverse.engine.service.biz.NewsheetBizService;
import com.sp.proxverse.engine.service.biz.ProcessTreeReadBizService;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.engine.service.biz.SheetBizService;
import com.sp.proxverse.engine.service.biz.TopicKpiBizService;
import com.sp.proxverse.engine.service.topicexport.ExportService;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicService;
import com.sp.proxverse.interfaces.dao.service.ComponentConfigService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.dao.service.IDataAuthorityService;
import com.sp.proxverse.interfaces.dao.service.SheetService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetNewService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.common.TestModelManager;
import com.sp.proxverse.test.data.model.NormalActiveModel;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Slf4j
public class TestFormattingColumn extends ProxverseTestBase {

  @Autowired public TestModelManager testModelManager;
  @Autowired private TopicKpiBizService topicKpiBizService;
  @Autowired BusinessTopicBizServiceImpl businessTopicBizServiceImpl;
  @Autowired ProcessTreeWriteBizService processTreeWriteBizService;
  @Autowired ProcessTreeReadBizService processTreeReadBizService;
  @Autowired CopyBizService copyBizService;
  @Autowired BusinessTopicService businessTopicService;
  @Autowired private TopicSheetService topicSheetService;
  @Autowired SheetService sheetService;
  @Autowired IDataAuthorityService dataAuthorityService;
  @Autowired TopicSheetNewService topicSheetNewService;
  @Autowired ComponentBizService componentBizService;
  @Autowired NewsheetBizService newsheetBizService;
  @Autowired ExportService exportService;
  @Autowired ComponentConfigService componentConfigService;
  @Autowired DimensionQueryService dimensionQueryService;
  @Autowired DataModelService dataModelService;
  @Autowired DataModelBizService dataModelBizService;
  @Autowired private SheetBizService saveSheetParam;

  @Order(value = 1)
  @Test
  public void testKpiListWithFormatting() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheet[1]);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("active_table.caseId");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    requestKpi.setFormatting("percentage");
    requestKpi.setFormat(",2f%");
    processTreeWriteBizService.saveKpi(requestKpi);

    List<TopicSheetKpiOutputVO> topicSheetKpis =
        processTreeReadBizService.getTopicSheetKpi(sheet[1]);
    TopicSheetKpiOutputVO kpi =
        topicSheetKpis.stream()
            .filter(f -> Objects.equals(f.getName(), "aa"))
            .findFirst()
            .orElse(null);
    Assertions.assertEquals(kpi.getFormatting(), "percentage");
    Assertions.assertEquals(kpi.getFormat(), ",2f%");
  }

  @Order(value = 2)
  @Test
  public void testSheetFilterWithFormatting() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    SaveTopicFilterRequest request = new SaveTopicFilterRequest();
    request.setTopicId(sheet[0]);
    request.setSheetId(sheet[1]);
    request.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(2001)
                .expression("1")
                .formatting("percentage")
                .format(",2f%")
                .build()));
    saveSheetParam.saveSheetFilter(request);

    TopicScriptFilter sheetFilter = saveSheetParam.getSheetFilter(request);
    Assertions.assertEquals(sheetFilter.getFormatting(), "percentage");
    Assertions.assertEquals(sheetFilter.getFormat(), ",2f%");
  }

  @Order(value = 3)
  @Test
  public void testPublishWithFormatting() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheet[1]);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("active_table.caseId");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    requestKpi.setFormatting("percentage");
    requestKpi.setFormat(",2f%");
    processTreeWriteBizService.saveKpi(requestKpi);

    SaveTopicFilterRequest request2 = new SaveTopicFilterRequest();
    request2.setTopicId(sheet[0]);
    request2.setSheetId(sheet[1]);
    request2.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(2001)
                .expression("1")
                .formatting("percentage_decimals")
                .format(",f")
                .build()));
    saveSheetParam.saveSheetFilter(request2);

    SheetReleaseReqVo sheetReleaseReqVo = new SheetReleaseReqVo();
    sheetReleaseReqVo.setTopicId(sheet[0] + "");
    sheetReleaseReqVo.setUserId("1");
    sheetService.sheetRelease(sheetReleaseReqVo);

    BusinessTopicPO snapTopic =
        businessTopicService.getOne(
            new LambdaQueryWrapper<BusinessTopicPO>()
                .eq(BusinessTopicPO::getSnapshotParentId, sheet[0])
                .eq(BusinessTopicPO::getSnapshotFlag, 1));

    List<TopicSheetPO> sheetList =
        topicSheetService.list(
            new LambdaQueryWrapper<TopicSheetPO>().eq(TopicSheetPO::getTopicId, snapTopic.getId()));
    List<TopicSheetKpiOutputVO> topicSheetKpis =
        processTreeReadBizService.getTopicSheetKpi(sheetList.get(0).getId());
    TopicSheetKpiOutputVO kpi =
        topicSheetKpis.stream()
            .filter(f -> Objects.equals(f.getName(), "aa"))
            .findFirst()
            .orElse(null);
    Assertions.assertEquals(kpi.getFormatting(), "percentage");
    Assertions.assertEquals(kpi.getFormat(), ",2f%");

    SaveTopicFilterRequest requestSheet = new SaveTopicFilterRequest();
    requestSheet.setSheetId(sheetList.get(0).getId());
    TopicScriptFilter sheetFilter = saveSheetParam.getSheetFilter(requestSheet);
    Assertions.assertEquals(sheetFilter.getFormatting(), "percentage_decimals");
    Assertions.assertEquals(sheetFilter.getFormat(), ",f");
  }

  @Order(value = 4)
  @Test
  public void testCopyWithFormatting() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    // create parent topic
    SaveBusinessTopicRequest test_topic_parent =
        SaveBusinessTopicRequest.builder()
            .name("test_topic_parent")
            .type(BusinessTopicTypeEnum.BUSINESS_TOPIC.getValue())
            .build();
    Integer parentTopicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic_parent);
    // create sub topic
    SaveBusinessTopicRequest test_topic =
        SaveBusinessTopicRequest.builder()
            .businessTopicId(parentTopicId)
            .name("testCopyWithTopicConfigColor")
            .dataId(Lists.newArrayList(modelId))
            .dataSourceType(1)
            .type(BusinessTopicTypeEnum.BUSINESS_TREE.getValue())
            .build();
    Integer topicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic);
    // create sheet
    AddTopicSheetRequest sheetRequest =
        AddTopicSheetRequest.builder().topicId(topicId).type(1).build();
    Integer sheetId = processTreeWriteBizService.addTopicSheet(sheetRequest);
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheetId);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("active_table.caseId");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    requestKpi.setFormatting("percentage");
    requestKpi.setFormat(",2f%");
    processTreeWriteBizService.saveKpi(requestKpi);

    SaveTopicFilterRequest request2 = new SaveTopicFilterRequest();
    request2.setTopicId(topicId);
    request2.setSheetId(sheetId);
    request2.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(2001)
                .expression("1")
                .formatting("percentage_decimals")
                .format(",f")
                .build()));
    saveSheetParam.saveSheetFilter(request2);

    Integer newTopicId =
        copyBizService.copyTopic(
            topicId, parentTopicId, BusinessTopicTypeEnum.BUSINESS_TREE.getValue());

    List<TopicSheetPO> sheetList =
        topicSheetService.list(
            new LambdaQueryWrapper<TopicSheetPO>().eq(TopicSheetPO::getTopicId, newTopicId));
    List<TopicSheetKpiOutputVO> topicSheetKpis =
        processTreeReadBizService.getTopicSheetKpi(sheetList.get(0).getId());
    TopicSheetKpiOutputVO kpi =
        topicSheetKpis.stream()
            .filter(f -> Objects.equals(f.getName(), "aa"))
            .findFirst()
            .orElse(null);
    Assertions.assertEquals(kpi.getFormatting(), "percentage");
    Assertions.assertEquals(kpi.getFormat(), ",2f%");

    SaveTopicFilterRequest requestSheet = new SaveTopicFilterRequest();
    requestSheet.setSheetId(sheetList.get(0).getId());
    TopicScriptFilter sheetFilter = saveSheetParam.getSheetFilter(requestSheet);
    Assertions.assertEquals(sheetFilter.getFormatting(), "percentage_decimals");
    Assertions.assertEquals(sheetFilter.getFormat(), ",f");
  }

  @Order(value = 5)
  @Test
  public void testExportWithFormatting() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    // create parent topic
    SaveBusinessTopicRequest test_topic_parent =
        SaveBusinessTopicRequest.builder()
            .name("test_topic_parent")
            .type(BusinessTopicTypeEnum.BUSINESS_TOPIC.getValue())
            .build();
    Integer parentTopicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic_parent);
    // create sub topic
    SaveBusinessTopicRequest test_topic =
        SaveBusinessTopicRequest.builder()
            .businessTopicId(parentTopicId)
            .name("testExportWithTopicConfigColor")
            .dataId(Lists.newArrayList(modelId))
            .dataSourceType(1)
            .type(BusinessTopicTypeEnum.BUSINESS_TREE.getValue())
            .build();
    Integer topicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic);
    // create sheet
    AddTopicSheetRequest sheetRequest =
        AddTopicSheetRequest.builder().topicId(topicId).type(1).build();
    Integer sheetId = processTreeWriteBizService.addTopicSheet(sheetRequest);
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheetId);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("active_table.caseId");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    requestKpi.setFormatting("percentage");
    requestKpi.setFormat(",2f%");
    processTreeWriteBizService.saveKpi(requestKpi);

    SaveTopicFilterRequest request2 = new SaveTopicFilterRequest();
    request2.setTopicId(topicId);
    request2.setSheetId(sheetId);
    request2.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(2001)
                .expression("1")
                .formatting("percentage_decimals")
                .format(",f")
                .build()));
    saveSheetParam.saveSheetFilter(request2);

    HttpServletResponse response = new MockHttpServletResponse();
    exportService.topicExprt(response, topicId);
    String contentAsString = ((MockHttpServletResponse) response).getContentAsString();
    SaveBusinessTopicRequest saveBusinessTopicRequest = new SaveBusinessTopicRequest();
    saveBusinessTopicRequest.setBusinessTopicId(parentTopicId);
    saveBusinessTopicRequest.setDataSourceType(1);
    saveBusinessTopicRequest.setDataId(Lists.newArrayList(modelId));
    saveBusinessTopicRequest.setType(201);
    saveBusinessTopicRequest.setTopicJsonData(JSON.parseObject(contentAsString));
    Integer importTopicId = businessTopicBizServiceImpl.saveBusinessTopic(saveBusinessTopicRequest);
    List<TopicSheetPO> sheetList =
        topicSheetService.list(
            new LambdaQueryWrapper<TopicSheetPO>().eq(TopicSheetPO::getTopicId, importTopicId));
    List<TopicSheetKpiOutputVO> topicSheetKpis =
        processTreeReadBizService.getTopicSheetKpi(sheetList.get(0).getId());
    TopicSheetKpiOutputVO kpi =
        topicSheetKpis.stream()
            .filter(f -> Objects.equals(f.getName(), "aa"))
            .findFirst()
            .orElse(null);
    Assertions.assertEquals(kpi.getFormatting(), "percentage");
    Assertions.assertEquals(kpi.getFormat(), ",2f%");

    SaveTopicFilterRequest requestSheet = new SaveTopicFilterRequest();
    requestSheet.setSheetId(sheetList.get(0).getId());
    TopicScriptFilter sheetFilter = saveSheetParam.getSheetFilter(requestSheet);
    Assertions.assertEquals(sheetFilter.getFormatting(), "percentage_decimals");
    Assertions.assertEquals(sheetFilter.getFormat(), ",f");
  }
}
