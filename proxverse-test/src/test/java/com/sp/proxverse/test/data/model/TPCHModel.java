package com.sp.proxverse.test.data.model;

import com.google.common.collect.Streams;
import com.sp.proxverse.common.model.dict.DataModelRunStatusEnum;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.vo.SetFieldOutputVO;
import com.sp.proxverse.common.model.vo.datamodel.request.AddForeignKeyRequest;
import com.sp.proxverse.common.model.vo.datamodel.request.AddForeignKeySubRequest;
import com.sp.proxverse.common.model.vo.request.DataModelSaveRequest;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;

public class TPCHModel extends DataModel {

  public static final String MODEL_NAME = "tpch";

  public TPCHModel() {}

  public TPCHModel(TestModelUtils testModelUtils) {
    setTestModelUtils(testModelUtils);
  }

  // change it with your own path
  private final String dir = "test_data/tpch0001/";
  Integer poolId = -1;
  Integer modelId = -1;

  private SetFieldOutputVO upload(String filename) throws IOException, InterruptedException {
    return testFileUploadUtils.uploadFileToPool(
        filename + ".csv", dir + "/" + filename + ".csv", poolId, ",");
  }

  private void addJoin(
      SetFieldOutputVO left, SetFieldOutputVO right, String[] leftKeys, String[] rightKeys)
      throws FileNotFoundException {
    AddForeignKeyRequest addForeignKeyRequest = new AddForeignKeyRequest();
    addForeignKeyRequest.setDataModelId(modelId);
    addForeignKeyRequest.setFileIdLeft(left.getFileId());
    addForeignKeyRequest.setFileIdRight(right.getFileId());

    addForeignKeyRequest.setList(
        Streams.zip(
                Arrays.stream(leftKeys),
                Arrays.stream(rightKeys),
                (leftKey, rightKey) -> {
                  try {
                    AddForeignKeySubRequest addForeignKeySubRequest = new AddForeignKeySubRequest();
                    addForeignKeySubRequest.setFileIdLeft(left.getFileId());
                    addForeignKeySubRequest.setFileIdRight(right.getFileId());
                    addForeignKeySubRequest.setFieldIdLeft(
                        testModelUtils.findFieldId(left.getFileId(), leftKey));
                    addForeignKeySubRequest.setFieldIdRight(
                        testModelUtils.findFieldId(right.getFileId(), rightKey));
                    return addForeignKeySubRequest;
                  } catch (FileNotFoundException e) {
                    throw new RuntimeException(e);
                  }
                })
            .collect(Collectors.toList()));

    testModelUtils.dataModelBizService.addForeignKey(addForeignKeyRequest);
  }

  private void addJoin(
      SetFieldOutputVO left, SetFieldOutputVO right, String leftKey, String rightKey)
      throws FileNotFoundException {
    addJoin(left, right, new String[] {leftKey}, new String[] {rightKey});
  }

  @Override
  public int create() throws IOException, InterruptedException {
    poolId = testPoolUtils.createPool("tpch");
    SetFieldOutputVO customer = upload("customer");
    SetFieldOutputVO lineitem = upload("lineitem");
    SetFieldOutputVO orders = upload("orders");
    SetFieldOutputVO part = upload("part");
    SetFieldOutputVO partsupp = upload("partsupp");
    SetFieldOutputVO supplier = upload("supplier");
    SetFieldOutputVO nation = upload("nation");
    SetFieldOutputVO region = upload("region");
    SetFieldOutputVO nation_dup = upload("nation_dup");
    SetFieldOutputVO region_dup = upload("region_dup");

    // add tables and save model
    DataModelSaveRequest dataModelSaveRequest = new DataModelSaveRequest();
    dataModelSaveRequest.setCaseId(testModelUtils.findFieldId(lineitem.getFileId(), "l_orderkey"));
    dataModelSaveRequest.setEvent(testModelUtils.findFieldId(lineitem.getFileId(), "l_shipmode"));
    dataModelSaveRequest.setTime(testModelUtils.findFieldId(lineitem.getFileId(), "l_shipdate"));
    dataModelSaveRequest.setActiveFileId(lineitem.getFileId());
    dataModelSaveRequest.setPoolId(poolId);
    dataModelSaveRequest.setName(getModelName());
    dataModelSaveRequest.setFileIdList(
        Lists.newArrayList(
            customer.getFileId(),
            lineitem.getFileId(),
            orders.getFileId(),
            part.getFileId(),
            partsupp.getFileId(),
            supplier.getFileId(),
            nation.getFileId(),
            region.getFileId(),
            nation_dup.getFileId(),
            region_dup.getFileId()));
    modelId = testModelUtils.dataModelBizService.saveDataModel(dataModelSaveRequest);

    // join case table
    addJoin(lineitem, orders, "l_orderkey", "o_orderkey");
    testModelUtils.dataModelBizService.setCaseTable(modelId, orders.getFileId(), poolId);
    addJoin(orders, customer, "o_custkey", "c_custkey");
    addJoin(customer, nation, "c_nationkey", "n_nationkey");
    addJoin(nation, region, "n_regionkey", "r_regionkey");
    addJoin(
        lineitem,
        partsupp,
        new String[] {"l_partkey", "l_suppkey"},
        new String[] {"ps_partkey", "ps_suppkey"});
    addJoin(partsupp, part, "ps_partkey", "p_partkey");
    addJoin(partsupp, supplier, "ps_suppkey", "s_suppkey");
    addJoin(supplier, nation_dup, "s_nationkey", "n_nationkey");
    addJoin(nation_dup, region_dup, "n_regionkey", "r_regionkey");

    // build model
    testModelUtils.modelBuilderService.buildModel(modelId);
    DataModelPO byId = testModelUtils.dataModelService.getById(modelId);
    if (!Objects.equals(byId.getStatus(), DataModelRunStatusEnum.LOADED.getValue())) {
      Assertions.fail("Failed to build model");
    }

    return modelId;
  }

  @Override
  public String getModelName() {
    return super.getModelName() == null ? MODEL_NAME : super.getModelName();
  }
}
