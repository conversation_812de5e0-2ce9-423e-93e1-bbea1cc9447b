package com.sp.proxverse.test.ai;

import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.dto.ai.PromptsDto;
import com.sp.proxverse.common.model.dto.ai.SelectPromptsReq;
import com.sp.proxverse.interfaces.dao.impl.AiPromptsServiceImpl;
import com.sp.proxverse.test.common.ProxverseTestBase;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestAiPrompts extends ProxverseTestBase {

  @Autowired AiPromptsServiceImpl aiPromptsService;

  @Test
  public void testAiPromptsCRUD() {
    PromptsDto promptsDto = new PromptsDto();
    promptsDto.setPromptsType("process");
    promptsDto.setTenantId(0);
    promptsDto.setPromptsValue("promptsValueTemplate");
    promptsDto.setModelConfig("modelConfig");
    promptsDto.setIsTemplate(true);
    promptsDto.check();
    aiPromptsService.insertPrompts(promptsDto);

    promptsDto.setTenantId(1);
    promptsDto.setPromptsValue("promptsValue1");
    aiPromptsService.insertPrompts(promptsDto);

    promptsDto.setPromptsValue("promptsValue2");
    promptsDto.setTenantId(2);
    aiPromptsService.insertPrompts(promptsDto);

    promptsDto.setPromptsType("chat");
    promptsDto.setPromptsValue("promptsValue2");
    aiPromptsService.insertPrompts(promptsDto);
    SelectPromptsReq selectPromptsReq = new SelectPromptsReq();
    PageResp promptsPage = aiPromptsService.selectPrompts(selectPromptsReq);
    List<PromptsDto> promptsList = promptsPage.getData().getList();
    Assertions.assertEquals(4, promptsPage.getData().getTotal());
    Assertions.assertEquals(2, promptsList.get(0).getTenantId());
    Assertions.assertEquals("promptsValue2", promptsList.get(0).getPromptsValue());
    Assertions.assertEquals("chat", promptsList.get(0).getPromptsType());
    Assertions.assertEquals(2, promptsList.get(1).getTenantId());
    Assertions.assertEquals("promptsValue2", promptsList.get(1).getPromptsValue());
    Assertions.assertEquals("process", promptsList.get(1).getPromptsType());
    Assertions.assertEquals("promptsValueTemplate", promptsList.get(3).getPromptsValue());
    Assertions.assertEquals("modelConfig", promptsList.get(3).getModelConfig());

    selectPromptsReq.setPromptsType("chat");
    promptsPage = aiPromptsService.selectPrompts(selectPromptsReq);
    promptsList = promptsPage.getData().getList();
    Assertions.assertEquals(1, promptsList.size());
    Assertions.assertEquals("promptsValue2", promptsList.get(0).getPromptsValue());

    selectPromptsReq.setTenantId(1);
    promptsPage = aiPromptsService.selectPrompts(selectPromptsReq);
    promptsList = promptsPage.getData().getList();
    Assertions.assertEquals(0, promptsList.size());

    aiPromptsService.deletePrompt(1);
    selectPromptsReq = new SelectPromptsReq();
    promptsPage = aiPromptsService.selectPrompts(selectPromptsReq);
    promptsList = promptsPage.getData().getList();
    Assertions.assertEquals(3, promptsList.size());

    promptsDto.setPromptsType("process-v1");
    promptsDto.setTenantId(8);
    promptsDto.setPromptsValue("promptsValueTemplate-v1");
    promptsDto.setModelConfig("modelConfig-v1");
    promptsDto.setId(1);
    promptsDto.setDeleted(0);
    aiPromptsService.updatePrompt(promptsDto);

    promptsPage = aiPromptsService.selectPrompts(selectPromptsReq);
    promptsList = promptsPage.getData().getList();
    Assertions.assertEquals(4, promptsList.size());
    Assertions.assertEquals("promptsValueTemplate-v1", promptsList.get(3).getPromptsValue());
    Assertions.assertEquals("process-v1", promptsList.get(3).getPromptsType());
    Assertions.assertEquals("modelConfig-v1", promptsList.get(3).getModelConfig());

    PromptsDto chat = aiPromptsService.getCurrentPrompt("chat");
    Assertions.assertEquals("promptsValue2", chat.getPromptsValue());
    Assertions.assertEquals("chat", chat.getPromptsType());
    Assertions.assertEquals("modelConfig", chat.getModelConfig());
  }
}
