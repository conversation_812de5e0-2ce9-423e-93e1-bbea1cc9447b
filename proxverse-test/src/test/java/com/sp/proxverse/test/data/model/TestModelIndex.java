package com.sp.proxverse.test.data.model;

import com.prx.commons.utils.ModelUtils;
import com.sp.proxverse.test.data.pool.TestPoolUtils;
import com.sp.proxverse.test.data.pql.TestPQLBase;
import com.sp.proxverse.test.data.upload.TestFileUploadUtils;
import java.util.List;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestModelIndex extends TestPQLBase {
  @Autowired TestModelUtils testModelUtils;

  @Autowired TestPoolUtils testPoolUtils;

  @Autowired TestFileUploadUtils testFileUploadUtils;

  @Autowired ModelUtils modelUtils;

  @Test
  public void testModelIndexWithNullValue() {
    int orCreate = testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    Row[] collect =
        newBuilder(modelDescFactory.getOrCreate(orCreate))
            .addColumn("orderEvent.null_column")
            .addTopicFilter("orderEvent.null_column is not null")
            .collect();
    Assertions.assertEquals(3, collect.length);
    Assertions.assertEquals("1", collect[0].getString(0));
  }

  @Test
  public void testTableAnalyzed() {
    int orCreate = testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    ModelDesc modelDesc = modelDescFactory.getOrCreate(orCreate);
    SparkSessionEnv.getSparkSession()
        .sessionState()
        .catalogManager()
        .setCurrentCatalog("spark_catalog");
    List<Row> rows =
        SparkSessionEnv.getSparkSession()
            .sql("DESC EXTENDED " + modelDesc.baseVirtualCaseTable().tableName() + " precaseid")
            .filter("info_name='distinct_count'")
            .collectAsList();
    Assertions.assertEquals(rows.get(0).getString(1), "33673");
  }

  @Test
  public void testModelIndexWithSortingColumn() {
    int orCreate = testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    Row[] collect =
        newBuilder(modelDescFactory.getOrCreate(orCreate))
            .addColumn("orderEvent.null_column")
            .addColumn("orderEvent.event")
            .addTopicFilter("orderEvent.caseid = 'ABC-20221130-33888' ")
            .collect();

    Assertions.assertEquals("运维人员处理", collect[collect.length - 1].getString(1));

    collect =
        newBuilder(modelDescFactory.getOrCreate(orCreate))
            .addColumn("`case_table`.sortedeventlist")
            .addTopicFilter("orderEvent.caseid = 'ABC-20221130-33888' ")
            .collect();
    String o =
        (String)
            ((GenericRowWithSchema) collect[0])
                .getList(0)
                .get(((GenericRowWithSchema) collect[0]).getList(0).size() - 1);
    Assertions.assertEquals(
        "运维人员处理",
        (String)
            ((GenericRowWithSchema) collect[0])
                .getList(0)
                .get(((GenericRowWithSchema) collect[0]).getList(0).size() - 1));
  }
}
