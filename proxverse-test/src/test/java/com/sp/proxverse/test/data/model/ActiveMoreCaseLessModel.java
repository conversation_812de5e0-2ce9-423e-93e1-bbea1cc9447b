package com.sp.proxverse.test.data.model;

import com.sp.proxverse.common.model.dict.DataModelRunStatusEnum;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.vo.SetFieldOutputVO;
import com.sp.proxverse.common.model.vo.datamodel.request.AddForeignKeyRequest;
import com.sp.proxverse.common.model.vo.datamodel.request.AddForeignKeySubRequest;
import com.sp.proxverse.common.model.vo.request.DataModelSaveRequest;
import java.io.IOException;
import java.util.Objects;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;

/** 主表比案例表的caseId多 virtual_case_table只预处理案例表的case数据 */
public class ActiveMoreCaseLessModel extends DataModel {

  public static final String MODEL_NAME = "active_more_than_case";

  public ActiveMoreCaseLessModel() {}

  public ActiveMoreCaseLessModel(TestModelUtils testModelUtils) {
    setTestModelUtils(testModelUtils);
  }

  // change it with your own path
  Integer poolId;

  @Override
  public int create() throws IOException, InterruptedException {
    poolId = testPoolUtils.createPool("active_more_than_case");
    SetFieldOutputVO event =
        testFileUploadUtils.uploadFileToPool(
            "test_active_more.csv", "test_data/normal_model/test_active_more.csv", poolId);
    SetFieldOutputVO caseFile =
        testFileUploadUtils.uploadFileToPool(
            "test_case_less.csv", "test_data/normal_model/test_case_less.csv", poolId);

    DataModelSaveRequest dataModelSaveRequest = new DataModelSaveRequest();
    dataModelSaveRequest.setCaseId(testModelUtils.findFieldId(event.getFileId(), "caseId"));
    dataModelSaveRequest.setEvent(testModelUtils.findFieldId(event.getFileId(), "event"));
    dataModelSaveRequest.setTime(testModelUtils.findFieldId(event.getFileId(), "time"));
    dataModelSaveRequest.setActiveFileId(event.getFileId());
    dataModelSaveRequest.setPoolId(poolId);
    dataModelSaveRequest.setName(getModelName());
    dataModelSaveRequest.setFileIdList(Lists.newArrayList(event.getFileId(), caseFile.getFileId()));
    int modelId = testModelUtils.dataModelBizService.saveDataModel(dataModelSaveRequest);

    AddForeignKeyRequest addForeignKeyRequest = new AddForeignKeyRequest();
    addForeignKeyRequest.setDataModelId(modelId);
    addForeignKeyRequest.setFileIdLeft(event.getFileId());
    addForeignKeyRequest.setFileIdRight(caseFile.getFileId());
    AddForeignKeySubRequest addForeignKeySubRequest = new AddForeignKeySubRequest();
    addForeignKeySubRequest.setFileIdLeft(event.getFileId());
    addForeignKeySubRequest.setFieldIdLeft(testModelUtils.findFieldId(event.getFileId(), "caseId"));
    addForeignKeySubRequest.setFileIdRight(caseFile.getFileId());
    addForeignKeySubRequest.setFieldIdRight(
        testModelUtils.findFieldId(caseFile.getFileId(), "caseId"));
    addForeignKeyRequest.setList(Lists.newArrayList(addForeignKeySubRequest));
    testModelUtils.dataModelBizService.addForeignKey(addForeignKeyRequest);

    testModelUtils.dataModelBizService.setCaseTable(modelId, caseFile.getFileId(), poolId);
    testModelUtils.modelBuilderService.buildModel(modelId);
    DataModelPO byId = testModelUtils.dataModelService.getById(modelId);
    if (!Objects.equals(byId.getStatus(), DataModelRunStatusEnum.LOADED.getValue())) {
      Assertions.fail("Failed to build model");
    }

    return modelId;
  }

  @Override
  public String getModelName() {
    return super.getModelName() == null ? MODEL_NAME : super.getModelName();
  }
}
