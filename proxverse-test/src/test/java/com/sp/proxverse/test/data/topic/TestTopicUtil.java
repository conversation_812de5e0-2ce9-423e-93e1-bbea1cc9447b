package com.sp.proxverse.test.data.topic;

import static org.junit.Assert.assertTrue;

import com.google.common.collect.Lists;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.dto.TopicFileDTO;
import com.sp.proxverse.common.model.vo.request.AddTopicSheetRequest;
import com.sp.proxverse.common.model.vo.request.SaveBusinessTopicRequest;
import com.sp.proxverse.engine.service.biz.BusinessTopicBizServiceImpl;
import com.sp.proxverse.engine.service.biz.ProcessTreeWriteBizService;
import com.sp.proxverse.interfaces.dao.impl.BusinessTopicDataServiceImpl;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.data.model.TestModelUtils;
import java.io.IOException;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.junit.Ignore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TestTopicUtil extends ProxverseTestBase {

  @Autowired ModelDescFactory modelDescFactory;

  @Autowired TestModelUtils testModelUtils;

  @Autowired private BusinessTopicBizServiceImpl businessTopicBizServiceImpl;

  @Autowired private ProcessTreeWriteBizService processTreeWriteBizService;

  public Integer modelId;

  public Integer topicId;
  @Autowired BusinessTopicDataServiceImpl businessTopicDataService;

  @Ignore
  public void saveSheet() throws IOException, InterruptedException {

    int modelIdLocal = testModelUtils.getOrCreateSingleTableModel();
    this.modelId = modelIdLocal;
    Integer topicIdLocal = this.saveTopic(modelIdLocal);
    // create sheet
    AddTopicSheetRequest sheetRequest =
        AddTopicSheetRequest.builder().topicId(topicIdLocal).type(7).build();

    Integer sheetId = processTreeWriteBizService.addTopicSheet(sheetRequest);
  }

  public Integer saveTopic(Integer modelId) throws IOException, InterruptedException {

    ModelDesc orCreate = modelDescFactory.getOrCreate(modelId);

    // create parent topic
    SaveBusinessTopicRequest test_topic_parent =
        SaveBusinessTopicRequest.builder()
            .name("test_topic_parent")
            .type(BusinessTopicTypeEnum.BUSINESS_TOPIC.getValue())
            .build();

    Integer topicIdLocal = businessTopicBizServiceImpl.saveBusinessTopic(test_topic_parent);

    // create sub topic
    SaveBusinessTopicRequest test_topic =
        SaveBusinessTopicRequest.builder()
            .businessTopicId(topicIdLocal)
            .name("test_topic")
            .dataId(Lists.newArrayList(modelId))
            .dataSourceType(1)
            .type(BusinessTopicTypeEnum.BUSINESS_TREE.getValue())
            .build();

    Integer topicIdSub = businessTopicBizServiceImpl.saveBusinessTopic(test_topic);

    TopicFileDTO topicFileByTopicId = businessTopicDataService.getTopicFileByTopicId(topicIdSub);

    assertTrue(topicFileByTopicId != null && topicFileByTopicId.getFileId() > 0);

    return topicIdSub;
  }

  @Ignore
  public void createProcessExplorationTest() throws IOException, InterruptedException {

    int modelIdLocal = testModelUtils.getOrCreateSingleTableModel();
    this.modelId = modelIdLocal;
    this.createProcessExploration(modelIdLocal);
  }

  public Integer createProcessExploration(Integer modelId)
      throws IOException, InterruptedException {

    Integer topicIdSub = this.saveTopic(modelId);

    this.topicId = topicIdSub;
    assertTrue(topicId > 0);
    // create sheet
    AddTopicSheetRequest sheetRequest =
        AddTopicSheetRequest.builder().topicId(topicId).type(7).build();

    Integer sheetId = processTreeWriteBizService.addTopicSheet(sheetRequest);
    assertTrue(sheetId > 0);
    return sheetId;
  }
}
