package com.sp.proxverse.test.data.model;

import com.sp.proxverse.common.model.vo.request.DataModelSaveRequest;
import com.sp.proxverse.common.model.vo.request.QueryFileDataRequest;
import java.io.IOException;
import org.assertj.core.util.Lists;

public class JdbcIncrementModel extends DataModel {

  public static final String MODEL_NAME = "mysqlIncrement";

  public JdbcIncrementModel() {}

  public JdbcIncrementModel(TestModelUtils testModelUtils) {
    setTestModelUtils(testModelUtils);
  }

  public int create(String name, Integer fileId) throws IOException, InterruptedException {
    Integer modelIdQuery = testModelUtils.dataModelBizService.getModelIdByName(getModelName());
    if (modelIdQuery != null) {
      return modelIdQuery;
    }
    String poolName = "incrementPool";
    Integer poolId = testPoolUtils.createPool(poolName);
    return create(poolId, name, fileId);
  }

  @Override
  public int create() throws IOException, InterruptedException {
    Integer modelIdQuery = testModelUtils.dataModelBizService.getModelIdByName(getModelName());
    if (modelIdQuery != null) {
      return modelIdQuery;
    }
    String name = "incrementPool";
    Integer poolId = testPoolUtils.createPool(name);
    return create(poolId, null, null);
  }

  private int create(int poolId, String name, Integer fileId)
      throws IOException, InterruptedException {
    QueryFileDataRequest queryFileDataRequest = new QueryFileDataRequest();
    queryFileDataRequest.setFileId(fileId);
    DataModelSaveRequest dataModelSaveRequest = new DataModelSaveRequest();
    dataModelSaveRequest.setCaseId(testModelUtils.findFieldId(fileId, "CASEID"));
    dataModelSaveRequest.setEvent(testModelUtils.findFieldId(fileId, "EVENT"));
    dataModelSaveRequest.setTime(testModelUtils.findFieldId(fileId, "TIME"));
    dataModelSaveRequest.setActiveFileId(fileId);
    dataModelSaveRequest.setPoolId(poolId);
    dataModelSaveRequest.setName(name == null ? getModelName() : name);
    dataModelSaveRequest.setFileIdList(Lists.newArrayList(fileId));
    int modelId = testModelUtils.dataModelBizService.saveDataModel(dataModelSaveRequest);
    return modelId;
  }

  @Override
  public String getModelName() {
    return super.getModelName() == null ? MODEL_NAME : super.getModelName();
  }
}
