package com.sp.proxverse.test.data.pql;

import com.google.common.collect.Lists;
import com.sp.proxverse.test.data.model.BillModel;
import java.io.IOException;
import java.util.ArrayList;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.pql.PQLBuilder;
import org.apache.spark.sql.pql.model.ModelDesc;
import org.junit.Assert;
import org.junit.Ignore;

public class TestProcessFunctions extends TestPQLBase {

  @Ignore
  public void testVariant() throws IOException, InterruptedException {
    testAllJoinSideWithColumn(Lists.newArrayList("process_Variant(bill_active.country)"));
  }

  @Ignore
  public void testTargetSource() throws IOException, InterruptedException {
    testAllJoinSideWithColumn(
        Lists.newArrayList(
            "source(bill_active.Activity)",
            "target(bill_active.Activity)",
            "process_variant(bill_active.Activity)",
            "MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend))"));

    testAllJoinSideWithColumn(
        Lists.newArrayList(
            "source(bill_active.Activity, case when bill_active.Activity = '处理发票' then null else bill_active.Activity end)",
            "target(bill_active.Activity)",
            "process_variant(bill_active.Activity)",
            "MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend))"));

    try {
      testAllJoinSideWithColumn(
          Lists.newArrayList(
              "source(bill_active.Activity, case when bill_active.Activity = '处理发票' then null else bill_active.Activity end)",
              "target(bill_active.Activity, case when bill_active.Activity = '最终检查发票' then null else bill_active.Activity end)",
              "process_variant(bill_active.Activity)",
              "MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend))"));
      Assert.fail();
    } catch (Exception e) {

    }

    try {
      testAllJoinSideWithColumn(
          Lists.newArrayList(
              "source(bill_active.Activity, FIRST_OCCURRENCE[] TO ANY_OCCURRENCE_WITH_SELF[])",
              "target(bill_active.Activity, ANY_OCCURRENCE[] to ANY_OCCURRENCE[])",
              "process_variant(bill_active.Activity)",
              "MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend))"));
      Assert.fail();
    } catch (Exception e) {

    }

    testAllJoinSideWithColumn(
        Lists.newArrayList(
            "source(bill_active.Activity, WITH START (['开始了!']))",
            "target(bill_active.Activity)",
            "process_variant(bill_active.Activity)",
            "MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend))"));
  }

  @Ignore
  public void testCrop() throws IOException, InterruptedException {
    ArrayList<String> columns =
        Lists.newArrayList(
            "Process_variant(CALC_CROP_TO_NULL( FIRST_OCCURRENCE [ '接收发票' ] TO LAST_OCCURRENCE [ '处理发票' ], `bill_active`.`Activity`))",
            "count( distinct bill_case.caseId)");

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));
    PQLBuilder pqlBuilder = newBuilder(orCreate);
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();
  }

  @Ignore
  public void testTargetSourceWithCount() throws IOException, InterruptedException {
    ArrayList<String> columns =
        Lists.newArrayList(
            "avg(MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend)))",
            "source(bill_active.Activity)",
            "target(bill_active.Activity, CALC_CROP_TO_NULL( FIRST_OCCURRENCE [ '接收发票' ] TO LAST_OCCURRENCE [ '处理发票' ], `bill_active`.`Activity`))");

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));
    PQLBuilder pqlBuilder = newBuilder(orCreate);
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();
  }

  @Ignore
  public void testCropWithNoEventColumn() throws IOException, InterruptedException {
    ArrayList<String> columns =
        Lists.newArrayList(
            "shortened(Process_variant(CALC_CROP_TO_NULL( FIRST_OCCURRENCE [ '接收' ] TO LAST_OCCURRENCE [ '付款' ], `bill_active`.`Activitytype`)))",
            "count( distinct bill_case.caseId)");

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));

    PQLBuilder pqlBuilder = newBuilder(orCreate);
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();
  }

  @Ignore
  public void testTargetSourceWithCountWithNoEventColumn()
      throws IOException, InterruptedException {
    ArrayList<String> columns =
        Lists.newArrayList(
            "avg(MINUTES_BETWEEN(source(bill_active.Eventend), target(bill_active.Eventend)))",
            "source(bill_active.Activitytype)",
            "target(bill_active.Activity, CALC_CROP_TO_NULL( FIRST_OCCURRENCE [ '接收' ] TO LAST_OCCURRENCE [ '付款' ], `bill_active`.`Activitytype`))");

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));

    PQLBuilder pqlBuilder = newBuilder(orCreate);
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();
  }

  @Ignore
  public void testCalcThroughtPutWithEval() throws IOException, InterruptedException {
    ArrayList<String> columns =
        Lists.newArrayList(
            "`bill_case`.`CaseID`",
            "median(CALC_THROUGHPUT(CASE_START TO CASE_END, REMAP_TIMESTAMPS(`demo_event`.`Eventend`, SECONDS)))");

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));

    PQLBuilder pqlBuilder = newBuilder(orCreate);
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();
  }

  @Ignore
  public void testShoretendVariant() throws IOException, InterruptedException {
    ArrayList<String> columns =
        Lists.newArrayList(
            "`bill_case`.`CaseID`",
            "median(CALC_THROUGHPUT(CASE_START TO CASE_END, REMAP_TIMESTAMPS(`demo_event`.`Eventend`, SECONDS)))");

    ModelDesc orCreate =
        modelDescFactory.getOrCreate(testModelManager.getOrCreate(BillModel.MODEL_NAME));

    PQLBuilder pqlBuilder = newBuilder(orCreate);
    for (String column : columns) {
      pqlBuilder.addColumn(column);
    }
    pqlBuilder.addTopicFilter(
        "process_filter('最终检查发票', '澄清异常发票', 20, `case_table`.sortedEventList)");
    Row[] result = pqlBuilder.collect();
  }
}
