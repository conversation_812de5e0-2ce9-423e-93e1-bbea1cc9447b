package com.sp.proxverse.test.common;

import com.sp.proxverse.common.model.dict.DataConnectorJDBCTypeEnum;
import lombok.Getter;

@Getter
public enum JDBCEnum {
  MYSQL_TEST(
      DataConnectorJDBCTypeEnum.MYSQL.getValue(),
      "mysql_exactor",
      "*************",
      30006,
      "root",
      "rootpass",
      "T_JDBC",
      null),

  MYSQL_LOCAL(
      DataConnectorJDBCTypeEnum.MYSQL.getValue(),
      "mysql_exactor",
      "localhost",
      3306,
      "root",
      "123456",
      "T_JDBC",
      null),

  POSTGRESQL_LOCAL(
      DataConnectorJDBCTypeEnum.POSTGRESQL.getValue(),
      "pg_exactor",
      "localhost",
      5432,
      "postgres",
      "rootpass",
      "postgres",
      "public"),

  POSTGRESQL_TEST(
      DataConnectorJDBCTypeEnum.POSTGRESQL.getValue(),
      "pg_exactor",
      "*************",
      30007,
      "postgres",
      "rootpass",
      "postgres",
      "public"),

  DB2_TEST(
      DataConnectorJDBCTypeEnum.DB2.getValue(),
      "db2_exactor",
      "*************",
      31230,
      "db2inst1",
      "rootpass",
      "foo",
      "DB2INST1"),
  ORACLE_LOCAL(
      DataConnectorJDBCTypeEnum.ORACLE.getValue(),
      "oracle_exactor",
      "localhost",
      1521,
      "system",
      "Th1s1sThe0racle#Pass",
      "xe",
      "SYSTEM"),
  ORACLE_TEST(
      DataConnectorJDBCTypeEnum.ORACLE.getValue(),
      "oracle_exactor",
      "*************",
      30008,
      "system",
      "Th1s1sThe0racle#Pass",
      "xe",
      "SYSTEM");

  public Integer jdbcType;
  public String exactorName;
  public String ip;
  public Integer port;
  public String user;
  public String password;
  public String dbname;
  public String schemaName;

  JDBCEnum(
      Integer jdbcType,
      String exactorName,
      String ip,
      Integer port,
      String user,
      String password,
      String dbname,
      String schemaName) {
    this.jdbcType = jdbcType;
    this.exactorName = exactorName;
    this.ip = ip;
    this.port = port;
    this.user = user;
    this.password = password;
    this.dbname = dbname;
    this.schemaName = schemaName;
  }
}
