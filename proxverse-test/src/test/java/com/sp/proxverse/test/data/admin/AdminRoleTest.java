package com.sp.proxverse.test.data.admin;

import com.google.common.collect.Lists;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.model.dto.domain.Admin;
import com.sp.proxverse.common.model.enums.AdminStatusEnum;
import com.sp.proxverse.common.model.oauth2.UserDetailsToken;
import com.sp.proxverse.common.model.vo.authority.SaveUserGroupReqDTO;
import com.sp.proxverse.common.model.vo.commandModule.GetTopicModuleReqVo;
import com.sp.proxverse.datamerge.controller.DataModelController;
import com.sp.proxverse.engine.controller.CommandModuleController;
import com.sp.proxverse.handler.RequirePermissionInterceptor;
import com.sp.proxverse.interfaces.dao.service.Oauth2AdminService;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.CreateUserDataAuthorityReqVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.CreateUserReqVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.LoginReqVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.SelectUserDataAuthorityReqVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UserDataAuthorityInfoResVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UserDataAuthorityResVo;
import com.sp.proxverse.oauth2.model.vo.AddRoleRequest;
import com.sp.proxverse.oauth2.service.AdminServiceImpl;
import com.sp.proxverse.oauth2.service.biz.RolePermissionBizService;
import com.sp.proxverse.oauth2.service.biz.UserDataAuthBizService;
import com.sp.proxverse.oauth2.service.biz.UserGroupBizService;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.data.pool.TestPoolUtils;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.method.HandlerMethod;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Slf4j
public class AdminRoleTest extends ProxverseTestBase {

  @Autowired private RolePermissionBizService rolePermissionBizService;

  @Autowired private CommandModuleController commandModuleController;

  @Autowired private DataModelController dataModelController;

  @Autowired AdminServiceImpl adminService;

  @Autowired Oauth2AdminService oauth2AdminService;

  @Autowired UserInfoUtil userInfoUtil;

  @Autowired TestPoolUtils testPoolUtils;

  @Autowired private UserDataAuthBizService userDataAuthBizService;

  @Autowired private UserGroupBizService userGroupBizService;

  @Autowired RequirePermissionInterceptor requirePermissionInterceptor;

  @BeforeAll
  public static void beforeRole() throws Exception {
    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1000);
              userDetailsToken.setUserName("test_role1");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
  }

  @AfterAll
  public static void afterRole() throws Exception {
    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1);
              userDetailsToken.setUserName("<EMAIL>");
              userDetailsToken.setLevel("ROOT");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
  }

  @Order(value = 1)
  @Test
  public void testHasAndNoPermission() throws UnsupportedEncodingException, NoSuchMethodException {
    AddRoleRequest addRoleRequest = new AddRoleRequest();
    addRoleRequest.setHasAllPermission(false);
    addRoleRequest.setName("test_role3");
    addRoleRequest.setPermissionList(Lists.newArrayList(1, 2, 3, 4, 5, 6));
    Integer roleId = rolePermissionBizService.addRole(addRoleRequest);
    CreateUserReqVo createUserReqVo = new CreateUserReqVo();
    createUserReqVo.setRoleList(Lists.newArrayList(roleId));
    createUserReqVo.setUserName("test_role3");
    createUserReqVo.setPassword("********************************");
    createUserReqVo.setId(1002);
    Admin admin = adminService.create(createUserReqVo);
    admin.setStatus(AdminStatusEnum.NORMAL.getCode());
    adminService.updateById(admin);

    LoginReqVo loginReqVo = new LoginReqVo();
    loginReqVo.setUsername("test_role1");
    loginReqVo.setPassword("********************************");
    oauth2AdminService.login(loginReqVo);

    Admin testAdmin = adminService.getAdmin("test_role3");
    GetTopicModuleReqVo getTopicModuleReqVo = new GetTopicModuleReqVo();
    getTopicModuleReqVo.setUserId(testAdmin.getId());
    getTopicModuleReqVo.setSnapshotFlag(false);

    HttpServletRequest request =
        new MockHttpServletRequest("getTopicModuleList", "/sp-engine/getTopicModuleList");
    HttpServletResponse response = new MockHttpServletResponse();
    HandlerMethod handlerMethod =
        new HandlerMethod(
            commandModuleController,
            commandModuleController
                .getClass()
                .getMethod("getTopicModuleList", GetTopicModuleReqVo.class));
    requirePermissionInterceptor.preHandle(request, response, handlerMethod);
    String contentAsString = ((MockHttpServletResponse) response).getContentAsString();
    // assert !contentAsString.contains("No permission");

    HttpServletRequest request2 =
        new MockHttpServletRequest("getDataPoolList", "/sp-engine/getBusinessTopicList");
    HttpServletResponse response2 = new MockHttpServletResponse();
    HandlerMethod handlerMethod2 =
        new HandlerMethod(
            dataModelController, dataModelController.getClass().getMethod("getDataPoolList"));
    requirePermissionInterceptor.preHandle(request2, response2, handlerMethod2);
    String contentAsString2 = ((MockHttpServletResponse) response2).getContentAsString();
    assert contentAsString2.contains("No permission");
  }

  @Order(value = 2)
  @Test
  public void testUserLevelIsRootAndQuerySelectAuthorityList() {
    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1000);
              userDetailsToken.setUserName("test_role1");
              userDetailsToken.setGroupId(1);
              userDetailsToken.setLevel("ROOT");
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
    Integer poolId = testPoolUtils.createPool("testUserLevelIsRootAndQuerySelectAuthorityList");
    SelectUserDataAuthorityReqVo selectDataAuthorityReqVo =
        SelectUserDataAuthorityReqVo.builder()
            .dataId(poolId)
            .dataIdType(26)
            .queryType(1)
            .showGroupPermission(false)
            .build();
    UserDataAuthorityResVo select = userDataAuthBizService.select(selectDataAuthorityReqVo);
    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1000);
              userDetailsToken.setUserName("test_role1");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
    Assertions.assertEquals(select.getData().get(0).getAuthorityValue(), 0);
    // Assertions.assertEquals(select.getData().get(1).getAuthorityValue(), 14);
    // Assertions.assertTrue(StringUtils.isEmpty(select.getData().get(0).getFilter()));
    // Assertions.assertTrue(StringUtils.isEmpty(select.getData().get(1).getFilter()));
  }

  @Order(value = 3)
  @Test
  public void testUserAuthPage() {
    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1);
              userDetailsToken.setUserName("<EMAIL>");
              userDetailsToken.setLevel("ROOT");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
    for (int i = 0; i < 10; i++) {
      SaveUserGroupReqDTO saveUserGroupReq = new SaveUserGroupReqDTO();
      saveUserGroupReq.setName("g" + (i + 1));
      userGroupBizService.saveUserGroup(saveUserGroupReq);
    }
    Integer poolId = testPoolUtils.createPool("testUserAuthPage");
    SelectUserDataAuthorityReqVo selectDataAuthorityReqVo =
        SelectUserDataAuthorityReqVo.builder()
            .dataId(poolId)
            .dataIdType(24)
            .search("g1")
            .showGroupPermission(false)
            .build();
    selectDataAuthorityReqVo.setPageNum(1);
    selectDataAuthorityReqVo.setPageSize(10);
    UserDataAuthorityResVo select = userDataAuthBizService.select(selectDataAuthorityReqVo);
    List<String> groupName = Lists.newArrayList("g1", "g10");
    List<String> collect =
        select.getData().stream()
            .map(UserDataAuthorityInfoResVo::getUserName)
            .collect(Collectors.toList());
    // all in group
    Assertions.assertEquals(collect, groupName);
    selectDataAuthorityReqVo.setSearch(null);
    selectDataAuthorityReqVo.setPageNum(2);
    UserDataAuthorityResVo selectPage2 = userDataAuthBizService.select(selectDataAuthorityReqVo);
    List<String> collect2 =
        selectPage2.getData().stream()
            .map(UserDataAuthorityInfoResVo::getUserName)
            .collect(Collectors.toList());
    collect2.contains("<EMAIL>");

    selectDataAuthorityReqVo.setPageNum(1);
    selectDataAuthorityReqVo.setPageSize(12);

    UserDataAuthorityResVo selectPage3 = userDataAuthBizService.select(selectDataAuthorityReqVo);
    List<String> collect3 =
        selectPage3.getData().stream()
            .map(UserDataAuthorityInfoResVo::getUserName)
            .collect(Collectors.toList());
    List<String> groupName3 =
        Lists.newArrayList(
            "g1",
            "g2",
            "g3",
            "g4",
            "g5",
            "g6",
            "g7",
            "g8",
            "g9",
            "g10",
            "<EMAIL>",
            "test_role1");
    // group and user
    // Assertions.assertEquals(collect3, groupName3);

    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1000);
              userDetailsToken.setUserName("test_role1");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
  }

  @Order(value = 4)
  @Test
  public void testUserAuthPageWithSort() {
    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1);
              userDetailsToken.setUserName("<EMAIL>");
              userDetailsToken.setLevel("ROOT");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
    Integer poolId = testPoolUtils.createPool("testUserAuthPage");
    // 设置用户组权限
    CreateUserDataAuthorityReqVo createUserDataAuthorityReqVo = new CreateUserDataAuthorityReqVo();
    createUserDataAuthorityReqVo.setDataId(poolId);
    createUserDataAuthorityReqVo.setDataType(20);
    createUserDataAuthorityReqVo.setAuthorityValue(6);
    createUserDataAuthorityReqVo.setUserType(20);
    createUserDataAuthorityReqVo.setUserId(2);
    CreateUserDataAuthorityReqVo createUserDataAuthorityReqVo2 = new CreateUserDataAuthorityReqVo();
    createUserDataAuthorityReqVo2.setDataId(poolId);
    createUserDataAuthorityReqVo2.setDataType(21);
    createUserDataAuthorityReqVo2.setAuthorityValue(14);
    createUserDataAuthorityReqVo2.setUserType(20);
    createUserDataAuthorityReqVo2.setUserId(4);
    List<CreateUserDataAuthorityReqVo> createUserDataAuthorityReqVos =
        Lists.newArrayList(createUserDataAuthorityReqVo, createUserDataAuthorityReqVo2);
    userDataAuthBizService.createOrUpdateBatch(createUserDataAuthorityReqVos);

    SelectUserDataAuthorityReqVo selectDataAuthorityReqVo =
        SelectUserDataAuthorityReqVo.builder()
            .dataId(poolId)
            .dataIdType(25)
            .showGroupPermission(false)
            .build();
    selectDataAuthorityReqVo.setPageNum(1);
    selectDataAuthorityReqVo.setPageSize(10);
    UserDataAuthorityResVo select = userDataAuthBizService.select(selectDataAuthorityReqVo);
    List<UserDataAuthorityInfoResVo> data = select.getData();
    // all in group
    // Assertions.assertEquals(data.get(3).getAuthorityValue(), 14);
    // Assertions.assertEquals(data.get(1).getAuthorityValue(), 6);
    // Assertions.assertEquals(data.get(0).getAuthorityValue(), 0);
    // Assertions.assertEquals(data.get(3).getUserName(), "g4");
    // Assertions.assertEquals(data.get(1).getUserName(), "g2");

    // 设置用户权限
    CreateUserDataAuthorityReqVo createUserDataAuthorityReqVo3 = new CreateUserDataAuthorityReqVo();
    createUserDataAuthorityReqVo3.setDataId(poolId);
    createUserDataAuthorityReqVo3.setDataType(22);
    createUserDataAuthorityReqVo3.setAuthorityValue(2);
    createUserDataAuthorityReqVo3.setUserType(10);
    createUserDataAuthorityReqVo3.setUserId(1000);
    CreateUserDataAuthorityReqVo createUserDataAuthorityReqVo4 = new CreateUserDataAuthorityReqVo();
    createUserDataAuthorityReqVo4.setDataId(poolId);
    createUserDataAuthorityReqVo4.setDataType(23);
    createUserDataAuthorityReqVo4.setAuthorityValue(14);
    createUserDataAuthorityReqVo4.setUserType(10);
    createUserDataAuthorityReqVo4.setUserId(2);
    List<CreateUserDataAuthorityReqVo> createUserDataAuthorityReqVos2 =
        Lists.newArrayList(createUserDataAuthorityReqVo3, createUserDataAuthorityReqVo4);
    userDataAuthBizService.createOrUpdateBatch(createUserDataAuthorityReqVos2);

    selectDataAuthorityReqVo.setSearch(null);
    selectDataAuthorityReqVo.setPageNum(2);
    UserDataAuthorityResVo selectPage2 = userDataAuthBizService.select(selectDataAuthorityReqVo);
    List<UserDataAuthorityInfoResVo> dataUser = selectPage2.getData();
    // all in user
    //    Assertions.assertEquals(dataUser.get(0).getAuthorityValue(), 14);
    //    Assertions.assertEquals(dataUser.get(1).getAuthorityValue(), 2);
    //    Assertions.assertEquals(dataUser.get(0).getUserName(), "<EMAIL>");
    //    Assertions.assertEquals(dataUser.get(1).getUserName(), "test_role1");

    selectDataAuthorityReqVo.setPageNum(1);
    selectDataAuthorityReqVo.setPageSize(12);

    UserDataAuthorityResVo selectPage3 = userDataAuthBizService.select(selectDataAuthorityReqVo);
    List<String> collect3 =
        selectPage3.getData().stream()
            .map(UserDataAuthorityInfoResVo::getUserName)
            .collect(Collectors.toList());
    List<String> groupName3 =
        Lists.newArrayList(
            "g1",
            "g2",
            "g3",
            "g4",
            "g5",
            "g6",
            "g7",
            "g8",
            "g9",
            "g10",
            "<EMAIL>",
            "test_role1");
    // group and user
    //    Assertions.assertEquals(collect3, groupName3);

    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1000);
              userDetailsToken.setUserName("test_role1");
              userDetailsToken.setGroupId(1);
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };
  }

  @Order(value = 20)
  @Test
  public void testNoPermissionButRoot() throws UnsupportedEncodingException, NoSuchMethodException {
    Admin admin = new Admin();
    admin.setId(1000);
    admin.setLevel("ROOT");
    adminService.updateById(admin);

    UserInfoUtil.authInfo =
        new ThreadLocal() {
          @Override
          protected Object initialValue() {
            if (System.getProperty("isTest", "false").equals("true")) {
              UserDetailsToken userDetailsToken = new UserDetailsToken();
              userDetailsToken.setUserId(1000);
              userDetailsToken.setUserName("test_role1");
              userDetailsToken.setGroupId(1);
              userDetailsToken.setLevel("ROOT");
              return userDetailsToken;
            } else {
              return null;
            }
          }
        };

    HttpServletRequest request2 =
        new MockHttpServletRequest("getDataPoolList", "/sp-engine/getBusinessTopicList");
    HttpServletResponse response2 = new MockHttpServletResponse();
    HandlerMethod handlerMethod2 =
        new HandlerMethod(
            dataModelController, dataModelController.getClass().getMethod("getDataPoolList"));
    requirePermissionInterceptor.preHandle(request2, response2, handlerMethod2);
    String contentAsString2 = ((MockHttpServletResponse) response2).getContentAsString();
    assert !contentAsString2.contains("No permission");
  }
}
