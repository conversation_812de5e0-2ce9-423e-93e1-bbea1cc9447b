package com.sp.proxverse.test.data.topic;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.prx.service.model.ModelBuilderService;
import com.prx.service.page.DeviationPageServiceImpl;
import com.prx.service.page.entity.Deviation;
import com.prx.service.page.entity.DeviationOutput;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.dict.BusinessTopicTypeEnum;
import com.sp.proxverse.common.model.dict.OccurrenceTypeEnum;
import com.sp.proxverse.common.model.dict.TimeUnitEnum;
import com.sp.proxverse.common.model.dict.TopicFilterTypeEnum;
import com.sp.proxverse.common.model.dto.AggCalcTimexyDTO;
import com.sp.proxverse.common.model.dto.CaseDetailActiveDTO;
import com.sp.proxverse.common.model.dto.CaseDetailDTO;
import com.sp.proxverse.common.model.dto.DeviationKipDTO;
import com.sp.proxverse.common.model.dto.SheetKpiDTO;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.ComponentWorkTimePO;
import com.sp.proxverse.common.model.po.KpiParamPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.common.model.vo.KnowledgeDataOutputVO;
import com.sp.proxverse.common.model.vo.TopicDeviationOutputVO;
import com.sp.proxverse.common.model.vo.TopicFilterOutputVO;
import com.sp.proxverse.common.model.vo.TopicSheetKpiOutputVO;
import com.sp.proxverse.common.model.vo.TopicSheetOutputVO;
import com.sp.proxverse.common.model.vo.processai.request.QueryEventListRequest;
import com.sp.proxverse.common.model.vo.processai.request.QueryEventRate4VariableRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterRequest;
import com.sp.proxverse.common.model.vo.processai.request.SaveTopicFilterSubRequest;
import com.sp.proxverse.common.model.vo.request.AddTopicSheetRequest;
import com.sp.proxverse.common.model.vo.request.CaseDataActiveRequest;
import com.sp.proxverse.common.model.vo.request.CaseDataRequest;
import com.sp.proxverse.common.model.vo.request.CaseTotalOutputVO;
import com.sp.proxverse.common.model.vo.request.DeleteTopicSheetRequest;
import com.sp.proxverse.common.model.vo.request.EditBusinessTopicDataOutputVO;
import com.sp.proxverse.common.model.vo.request.KpiSaveRequest;
import com.sp.proxverse.common.model.vo.request.QueryCaseTotalRequest;
import com.sp.proxverse.common.model.vo.request.QueryEditTopicRequest;
import com.sp.proxverse.common.model.vo.request.QueryKnowledgeDataRequest;
import com.sp.proxverse.common.model.vo.request.QueryTopicFilterRequest;
import com.sp.proxverse.common.model.vo.request.SaveBusinessTopicRequest;
import com.sp.proxverse.common.model.vo.request.TopicSheetDataRequest;
import com.sp.proxverse.common.model.vo.request.UpdateTopicDataRequest;
import com.sp.proxverse.common.model.vo.request.processAi.SheetReleaseReqVo;
import com.sp.proxverse.common.model.vo.request.topic.TopicKpiSaveRequest;
import com.sp.proxverse.common.model.vo.sheet.SaveComponentRequest;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.data.service.biz.ThroughputCalculator;
import com.sp.proxverse.engine.service.biz.*;
import com.sp.proxverse.engine.service.topicexport.ExportService;
import com.sp.proxverse.interfaces.dao.service.SheetService;
import com.sp.proxverse.interfaces.dao.service.TopicFilterService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetKpiService;
import com.sp.proxverse.interfaces.service.remote.model.data.QueryDurationValueByTypeDTO;
import com.sp.proxverse.test.common.ProxverseTestBase;
import com.sp.proxverse.test.common.TestModelManager;
import com.sp.proxverse.test.data.model.NormalActiveModel;
import com.sp.proxverse.test.data.model.OrderModel;
import com.sp.proxverse.test.data.model.TimeNoSorttingModel;
import com.sp.proxverse.test.data.pool.TestPoolUtils;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;

@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class TestTopic extends ProxverseTestBase {

  @Autowired SheetService sheetService;

  @Autowired private ProcessTreeReadBizService processTreeReadBizService;

  @Autowired private DeviationPageServiceImpl devicationPageServiceImpl;

  @Autowired private ModelBuilderService modelBuilderService;

  @Autowired public TestModelManager testModelManager;

  @Autowired public TestPoolUtils testPoolUtils;

  @Autowired private BusinessTopicBizServiceImpl businessTopicBizServiceImpl;

  @Autowired private ExportService exportService;

  @Autowired private TopicKpiBizService topicKpiBizService;

  @Autowired private ComponentBizService componentBizService;

  @Autowired private SheetBizService sheetBizService;

  @Autowired private ProcessTreeWriteBizService processTreeWriteBizService;

  @Autowired private DurationBizService durationBizService;

  @Autowired private TopicFilterService topicFilterService;

  @Autowired private ProcessSearchBizService processSearchBizService;

  @Autowired private TopicSheetKpiService topicSheetKpiService;

  @Order(4)
  @Test
  public void testAggCalcTimeView() throws Exception {
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7);

    QueryEventRate4VariableRequest request =
        QueryEventRate4VariableRequest.builder()
            .topicId(sheet[0])
            .type(TopicFilterTypeEnum.CALC_TIME.value)
            .type1(10)
            .type2(20)
            .unit(TimeUnitEnum.SECOND.getValue())
            .num1(1)
            .event1("")
            .event2("T02 Check confirmation of receipt")
            .build();
    List<AggCalcTimexyDTO> resp = processSearchBizService.getAggCalcTimeView(request);
    // 吞吐时间柱状图从最小值开始而不是从0开始
    assert resp.get(0).getXdata1() == 60 && resp.get(0).getXdata2() == 340;
  }

  @Test
  public void testAggCalcTimeViewWithSheetFilter() throws Exception {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7, modelId);

    sheetBizService.saveSheetFilter(
        SaveTopicFilterRequest.builder()
            .topicId(sheet[0])
            .sheetId(sheet[1])
            .list(
                Lists.newArrayList(
                    SaveTopicFilterSubRequest.builder()
                        .type(2001)
                        .expression("active_table.caseId='case-10011'")
                        .build()))
            .build());

    QueryEventRate4VariableRequest request =
        QueryEventRate4VariableRequest.builder()
            .topicId(sheet[0])
            .sheetId(sheet[1])
            .type(TopicFilterTypeEnum.CALC_TIME.value)
            .type1(10)
            .type2(20)
            .unit(TimeUnitEnum.SECOND.getValue())
            .num1(1)
            .event1("")
            .event2("T02 Check confirmation of receipt")
            .build();
    List<AggCalcTimexyDTO> resp = processSearchBizService.getAggCalcTimeView(request);
    // 吞吐时间柱状图从最小值开始而不是从0开始
    // assert resp.get(9).getXdata1() == 3741060;

    sheetBizService.saveSheetFilter(
        SaveTopicFilterRequest.builder()
            .topicId(sheet[0])
            .sheetId(sheet[1])
            .list(
                Lists.newArrayList(
                    SaveTopicFilterSubRequest.builder().type(2001).expression("true").build()))
            .build());
  }

  @Order(5)
  @Test
  public void testTopicReleaseCheckFilter()
      throws IOException, InterruptedException, ExecutionException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    // create component
    SaveComponentRequest test =
        SaveComponentRequest.builder().sheetId(sheet[1]).type(30).name("testComponent").build();

    Integer componentId = componentBizService.saveSheetComponent(test);

    sheetBizService.saveSheetFilter(
        SaveTopicFilterRequest.builder()
            .topicId(sheet[0])
            .sheetId(sheet[1])
            .list(
                Lists.newArrayList(
                    SaveTopicFilterSubRequest.builder()
                        .type(2001)
                        .expression("active_table.caseId='case-10011'")
                        .build()))
            .build());

    SaveTopicFilterRequest v2Req = new SaveTopicFilterRequest();
    v2Req.setTopicId(sheet[0]);
    v2Req.setBizType(1);
    v2Req.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(TopicFilterTypeEnum.EXPRESSION.getValue())
                .expression("active_table.caseId in ('case-10011','case-10017')")
                .build()));
    processTreeWriteBizService.saveTopicFilterV2(v2Req);

    SheetReleaseReqVo req = new SheetReleaseReqVo();
    req.setTopicId(sheet[0] + "");
    req.setUserId("1");

    sheetService.sheetRelease(req);

    List<TopicFilterPO> sheetFilterList =
        topicFilterService.getSheetFilterBySheetIdList(Lists.newArrayList(sheet[1] + 1));
    assert sheetFilterList.size() == 1;
    assert sheetFilterList.get(0).getUserId() == null;

    TopicFilterPO init = topicFilterService.getInitFilter(sheet[0] + 1);
    assert init != null && init.getUserId() == null;

    QueryCaseTotalRequest queryCaseTotalRequest = new QueryCaseTotalRequest();
    queryCaseTotalRequest.setTopicId(sheet[0] + 1);
    queryCaseTotalRequest.setSheetId(sheet[1] + 1);
    CaseTotalOutputVO caseTotalOutputVO =
        processTreeReadBizService.getCaseTotalCurrent(queryCaseTotalRequest);
    assert (Objects.equals(caseTotalOutputVO.getCurrent(), "1"));
    assert (Objects.equals(caseTotalOutputVO.getTotal(), "2"));
  }

  @Order(6)
  @Test
  public void testEventListSortting() throws Exception {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7, modelId);
    QueryEventListRequest request = new QueryEventListRequest();
    request.setTopicId(sheet[0]);
    request.setSkipFilter(0);
    List<String> eventList = processTreeWriteBizService.getEventList(request);
    assert eventList.get(0).equals("Confirmation of receipt");
    assert eventList.get(1).equals("T02 Check confirmation of receipt");
  }

  @Order(1)
  @Test
  public void testTopicDeviation()
      throws IOException, InterruptedException, NoSuchMethodException, NoSuchFieldException,
          InstantiationException, IllegalAccessException, InvocationTargetException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    List<SheetKpiDTO> sheetKpiList = topicSheetKpiService.getTopicSheetKpiList(sheet[1]);
    Deviation request = new Deviation();
    request.setTopicId(sheet[0]);
    request.setTopicSheetId(sheet[1]);
    request.setSortKpiId(sheetKpiList.get(0).getId());
    request.setPageNum(1);
    request.setPageSize(10);
    DeviationOutput pageResp = devicationPageServiceImpl.page(request);
    List<TopicDeviationOutputVO> topicDeviation = pageResp.getList();
    ArrayList<Integer> integers = Lists.newArrayList(144, 119, 118, 1, 1, 113, 3, 3, 3, 3);
    List<Integer> collect =
        topicDeviation.stream()
            .sorted(Comparator.comparing(TopicDeviationOutputVO::getEventName))
            .map(TopicDeviationOutputVO::getValue)
            .collect(Collectors.toList());
    Assertions.assertEquals(collect, integers);

    request.setPageNum(2);
    DeviationOutput pageResp2 = devicationPageServiceImpl.page(request);
    List<TopicDeviationOutputVO> topicDeviation2 = pageResp2.getList();
    ArrayList<Integer> integers2 = Lists.newArrayList(121, 2, 118, 2, 3, 2, 2, 2);
    List<Integer> collect2 =
        topicDeviation2.stream()
            .sorted(Comparator.comparing(TopicDeviationOutputVO::getEventName))
            .map(TopicDeviationOutputVO::getValue)
            .collect(Collectors.toList());
    Assertions.assertEquals(collect2, integers2);
  }

  @Order(2)
  @Test
  public void testTopicDeviationNoAggregationError()
      throws IOException, InterruptedException, NoSuchMethodException, NoSuchFieldException,
          InstantiationException, IllegalAccessException, InvocationTargetException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    // add a kpi and no aggregation function
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheet[1]);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("active_table.caseId");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    processTreeWriteBizService.saveKpi(requestKpi);
    List<SheetKpiDTO> sheetKpiList = topicSheetKpiService.getTopicSheetKpiList(sheet[1]);
    Deviation request = new Deviation();
    request.setTopicId(sheet[0]);
    request.setTopicSheetId(sheet[1]);
    request.setSortKpiId(sheetKpiList.get(0).getId());
    try {
      PageResp topicDeviation = devicationPageServiceImpl.page(request);
    } catch (Exception e) {
      Assert.fail(I18nUtil.getMessage(I18nConst.DEVIATION_KPI_CONTAIN_AGGREGATION));
    }
  }

  @Order(3)
  @Test
  public void testCaseViewDetail()
      throws IOException, InterruptedException, NoSuchMethodException, NoSuchFieldException,
          InstantiationException, IllegalAccessException, InvocationTargetException,
          ExecutionException {
    TimeNoSorttingModel model = new TimeNoSorttingModel(testModelUtils);
    int modelId = model.create();
    modelBuilderService.buildModel(modelId);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 3, modelId);

    // add virtual_case filter
    int fileId = testModelUtils.findFileId("time_nosort");
    int caseId = testModelUtils.findFieldId(fileId, "caseId");
    SaveTopicFilterRequest requestFilter = new SaveTopicFilterRequest();
    requestFilter.setTopicId(sheet[0]);
    requestFilter.setHasInvert(0);
    requestFilter.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(700)
                .fileId(fileId)
                .fieldId(caseId)
                .paramValue(Lists.newArrayList("case-10017"))
                .build()));
    processTreeWriteBizService.saveTopicFilterV2(requestFilter);

    CaseDataRequest request = new CaseDataRequest();
    request.setTopicSheetId(sheet[1]);
    request.setCaseId("case-10017");
    request.setIsTest(false);
    List<CaseDetailDTO> caseViewDetail = processTreeReadBizService.getCaseViewDetail(request);
    Assertions.assertEquals("26天23时59分", caseViewDetail.get(1).getTimeSpace());
    Assertions.assertEquals("2天", caseViewDetail.get(2).getTimeSpace());
    Assertions.assertEquals("23时59分", caseViewDetail.get(3).getTimeSpace());
    Assertions.assertEquals("1分", caseViewDetail.get(4).getTimeSpace());
    Assertions.assertEquals("0秒", caseViewDetail.get(5).getTimeSpace());

    CaseDataActiveRequest caseDataActiveRequest = new CaseDataActiveRequest();
    caseDataActiveRequest.setCaseId("case-10017");
    caseDataActiveRequest.setActiveName("T03 Adjust confirmation of receipt");
    caseDataActiveRequest.setActiveTime("2011-10-15 13:47:00");
    caseDataActiveRequest.setTopicSheetId(sheet[1]);

    CaseDetailActiveDTO caseViewActiveDetail =
        processTreeReadBizService.getCaseViewActiveDetail(caseDataActiveRequest);
    Assertions.assertFalse(caseViewActiveDetail.getActive().isEmpty());
    Assertions.assertEquals("8", caseViewActiveDetail.getActive().get("Id"));
    Assertions.assertEquals("case-10017", caseViewActiveDetail.getActive().get("caseId"));
    Assertions.assertEquals(null, caseViewActiveDetail.getActive().get("encoded_case_id"));
  }

  @Order(7)
  @Test
  public void testTopicDeviationHasAggregationKpi() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);

    TopicKpiSaveRequest kpiSaveRequest =
        TopicKpiSaveRequest.builder()
            .topicId(sheet[0])
            .name("KPI1")
            .expression("count(`active_table`.`event`)+${p1}+${p2}")
            .paramList(
                Lists.newArrayList(
                    KpiParamPO.builder().levelName("p1").name("p1").type(1).build(),
                    KpiParamPO.builder().levelName("p2").name("p2").type(1).build()))
            .build();
    topicKpiBizService.saveTopicKpi(kpiSaveRequest);

    // add a kpi and has aggregation function
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheet[1]);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("KPI('KPI1',10,20)");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    processTreeWriteBizService.saveKpi(requestKpi);
    List<SheetKpiDTO> sheetKpiList = topicSheetKpiService.getTopicSheetKpiList(sheet[1]);
    Deviation request = new Deviation();
    request.setTopicId(sheet[0]);
    request.setTopicSheetId(sheet[1]);
    request.setSortKpiId(sheetKpiList.get(0).getId());
    try {
      PageResp topicDeviation = devicationPageServiceImpl.page(request);
    } catch (Exception e) {
      log.error("testTopicDeviationHasAggregationKpi error=>", e);
      Assert.fail("topicDeviation 的KPI()函数解析错误");
    }
  }

  @Order(8)
  @Test
  public void testTopicFilterDescDisplay()
      throws IOException, InterruptedException, ExecutionException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    int fileId = testModelUtils.findFileId("active_table");
    int fieldId = testModelUtils.findFieldId(fileId, "event");
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    SaveTopicFilterRequest request = new SaveTopicFilterRequest();
    request.setTopicId(sheet[0]);
    List<SaveTopicFilterSubRequest> list = new ArrayList<>();
    SaveTopicFilterSubRequest build =
        SaveTopicFilterSubRequest.builder()
            .type(700)
            .fileId(fileId)
            .fieldId(fieldId)
            .paramValue(
                Lists.newArrayList(
                    "T02 Check confirmation of receipt", "T03 Adjust confirmation of receipt"))
            .build();
    list.add(build);
    request.setList(list);
    processTreeWriteBizService.saveTopicFilterV2(request);

    QueryTopicFilterRequest queryTopicFilterRequest = new QueryTopicFilterRequest();
    queryTopicFilterRequest.setTopicId(sheet[0]);
    List<TopicFilterOutputVO> topicFilterList =
        processTreeReadBizService.getTopicFilterList(queryTopicFilterRequest);
    assert topicFilterList
        .get(0)
        .getDesc()
        .equals("event=T02 Check confirmation of receipt,T03 Adjust confirmation of receipt");
  }

  @Order(9)
  @Test
  public void testTopicDurationValueByType() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 9, modelId);
    QueryDurationValueByTypeDTO request = new QueryDurationValueByTypeDTO();
    request.setEvent1("服务台处理");
    request.setEvent2("系统内训师处理");
    request.setType(3);
    request.setType1(10);
    request.setType2(10);
    request.setSheetId(sheet[1]);
    String value = durationBizService.queryDurationValueByType(request);
    Assertions.assertEquals(value, "NULL");
  }

  @Order(10)
  @Test
  public void testTopicFilterListCalcTimeEqualsThan()
      throws IOException, InterruptedException, ExecutionException {
    int modelId = testModelManager.getOrCreate(OrderModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 9, modelId);
    SaveTopicFilterRequest request = new SaveTopicFilterRequest();
    request.setTopicId(sheet[0]);
    request.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .event1("后评估下发环节")
                .event2("后评估确认环节")
                .num1(0)
                .type(900)
                .type1(10)
                .type2(20)
                .unit(4)
                .build()));
    Boolean aBoolean = processTreeWriteBizService.saveTopicFilterV2(request);

    QueryTopicFilterRequest queryTopicFilterRequest = new QueryTopicFilterRequest();
    queryTopicFilterRequest.setTopicId(sheet[0]);
    List<TopicFilterOutputVO> topicFilterList =
        processTreeReadBizService.getTopicFilterList(queryTopicFilterRequest);
    Assertions.assertEquals(topicFilterList.get(0).getDesc(), ">=0天");
  }

  @Order(11)
  @Test
  public void testKnowledgeModelModifyDataModel()
      throws IOException, InterruptedException, InstantiationException, IllegalAccessException,
          NoSuchMethodException, InvocationTargetException {
    Integer poolId1 = testPoolUtils.createPool("testKnowledgeModelModifyDataModelPool1");
    int modelId1 =
        testModelManager.createModelByClass(
            poolId1, "testKnowledgeModelModifyDataModel1", NormalActiveModel.class);

    Integer poolId2 = testPoolUtils.createPool("testKnowledgeModelModifyDataModelPool2");
    int modelId2 =
        testModelManager.createModelByClass(
            poolId2, "testKnowledgeModelModifyDataModel2", NormalActiveModel.class);

    // create parent topic
    SaveBusinessTopicRequest test_topic_parent =
        SaveBusinessTopicRequest.builder()
            .name("test_topic_parent" + modelId1)
            .type(BusinessTopicTypeEnum.BUSINESS_TOPIC.getValue())
            .build();
    Integer topicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic_parent);

    SaveBusinessTopicRequest request = new SaveBusinessTopicRequest();
    request.setName("know1");
    request.setType(202);
    request.setDataSourceType(1);
    request.setBusinessTopicId(topicId);
    request.setDataId(Lists.newArrayList(modelId1));
    Integer knowledgeId = businessTopicBizServiceImpl.saveBusinessTopic(request);

    QueryKnowledgeDataRequest queryKnowledgeDataRequest = new QueryKnowledgeDataRequest();
    queryKnowledgeDataRequest.setTopicId(knowledgeId);
    List<KnowledgeDataOutputVO> knowledgeDataList =
        processTreeReadBizService.getKnowledgeDataList(queryKnowledgeDataRequest);
    Assertions.assertEquals(knowledgeDataList.size(), 2);
    Assertions.assertEquals(knowledgeDataList.get(0).getVariableNum(), 4);

    UpdateTopicDataRequest updateTopicDataRequest = new UpdateTopicDataRequest();
    updateTopicDataRequest.setTopicId(knowledgeId);
    updateTopicDataRequest.setDataId(modelId2);
    updateTopicDataRequest.setDataSourceType(1);
    businessTopicBizServiceImpl.updateTopicData(updateTopicDataRequest);

    QueryKnowledgeDataRequest queryKnowledgeDataRequest2 = new QueryKnowledgeDataRequest();
    queryKnowledgeDataRequest2.setTopicId(knowledgeId);
    List<KnowledgeDataOutputVO> knowledgeDataList2 =
        processTreeReadBizService.getKnowledgeDataList(queryKnowledgeDataRequest2);
    Assertions.assertEquals(knowledgeDataList2.size(), 2);
    Assertions.assertEquals(knowledgeDataList2.get(0).getVariableNum(), 4);

    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId1);
    TopicSheetDataRequest topicSheetDataRequest = new TopicSheetDataRequest();
    topicSheetDataRequest.setTopicId(sheet[0]);
    topicSheetDataRequest.setTopicSheetId(sheet[1]);
    List<TopicSheetKpiOutputVO> topicSheetKpi =
        processTreeReadBizService.getTopicSheetKpi(topicSheetDataRequest.getTopicSheetId());
    Assertions.assertEquals(topicSheetKpi.get(0).getValue(), "1.84");

    UpdateTopicDataRequest updateTopicDataRequest2 = new UpdateTopicDataRequest();
    updateTopicDataRequest2.setTopicId(sheet[0]);
    updateTopicDataRequest2.setDataId(knowledgeId);
    updateTopicDataRequest2.setDataSourceType(2);
    businessTopicBizServiceImpl.updateTopicData(updateTopicDataRequest2);

    List<TopicSheetKpiOutputVO> topicSheetKpi2 =
        processTreeReadBizService.getTopicSheetKpi(topicSheetDataRequest.getTopicSheetId());
    Assertions.assertEquals(topicSheetKpi2.get(0).getValue(), "1.84");

    QueryEditTopicRequest queryEditTopicRequest = new QueryEditTopicRequest();
    queryEditTopicRequest.setTopicId(topicId);
    EditBusinessTopicDataOutputVO editBusinessTopicData =
        businessTopicBizServiceImpl.getEditBusinessTopicData(queryEditTopicRequest);
    Assertions.assertEquals(
        "testKnowledgeModelModifyDataModelPool2", editBusinessTopicData.getPoolName());
    Assertions.assertEquals(
        "testKnowledgeModelModifyDataModel2", editBusinessTopicData.getDataSourceName());
    Assertions.assertNull(editBusinessTopicData.getKnowledgeModelId());

    queryEditTopicRequest.setTopicId(sheet[0]);
    editBusinessTopicData =
        businessTopicBizServiceImpl.getEditBusinessTopicData(queryEditTopicRequest);

    Assertions.assertEquals("", editBusinessTopicData.getPoolName());
    Assertions.assertEquals("know1", editBusinessTopicData.getDataSourceName());
    Assertions.assertNotNull(editBusinessTopicData.getKnowledgeModelId());
    Assertions.assertEquals("test_topic5", editBusinessTopicData.getName());
  }

  @Order(12)
  @Test
  public void testTopicExportAndImport()
      throws IOException, InterruptedException, ExecutionException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    // create parent topic
    SaveBusinessTopicRequest test_topic_parent =
        SaveBusinessTopicRequest.builder()
            .name("test_topic_parent")
            .type(BusinessTopicTypeEnum.BUSINESS_TOPIC.getValue())
            .build();
    Integer parentTopicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic_parent);
    // create sub topic
    SaveBusinessTopicRequest test_topic =
        SaveBusinessTopicRequest.builder()
            .businessTopicId(parentTopicId)
            .name("test_topic")
            .dataId(Lists.newArrayList(modelId))
            .dataSourceType(1)
            .type(BusinessTopicTypeEnum.BUSINESS_TREE.getValue())
            .build();
    Integer topicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic);

    TopicKpiSaveRequest kpiSaveRequest =
        TopicKpiSaveRequest.builder()
            .topicId(topicId)
            .name("KPI1")
            .expression("max(`active_table`.`event`)")
            .build();
    topicKpiBizService.saveTopicKpi(kpiSaveRequest);

    SaveTopicFilterRequest saveTopicFilterRequest = new SaveTopicFilterRequest();
    saveTopicFilterRequest.setTopicId(topicId);
    saveTopicFilterRequest.setBizType(1);
    saveTopicFilterRequest.setList(
        Lists.newArrayList(
            SaveTopicFilterSubRequest.builder()
                .type(2000)
                .expression("`active_table`.`caseId` in ('case-10011','case-10017')")
                .build()));
    processTreeWriteBizService.saveTopicFilterV2(saveTopicFilterRequest);

    // create sheet
    AddTopicSheetRequest sheetRequest =
        AddTopicSheetRequest.builder().topicId(topicId).type(1).build();
    Integer sheetId = processTreeWriteBizService.addTopicSheet(sheetRequest);

    KpiSaveRequest saveRequest =
        KpiSaveRequest.builder()
            .businessDataId(sheetId)
            .expression("count(`active_table`.`caseId`)")
            .businessType(1)
            .kpiType(0)
            .name("d1")
            .saveType(2)
            .build();
    processTreeWriteBizService.saveKpi(saveRequest);

    KpiSaveRequest saveRequest2 =
        KpiSaveRequest.builder()
            .businessDataId(sheetId)
            .expression("KPI('KPI1')")
            .businessType(1)
            .kpiType(0)
            .name("d2")
            .saveType(2)
            .build();
    processTreeWriteBizService.saveKpi(saveRequest2);
    HttpServletResponse response = new MockHttpServletResponse();
    exportService.topicExprt(response, topicId);
    String contentAsString = ((MockHttpServletResponse) response).getContentAsString();

    SaveBusinessTopicRequest saveBusinessTopicRequest = new SaveBusinessTopicRequest();
    saveBusinessTopicRequest.setBusinessTopicId(parentTopicId);
    saveBusinessTopicRequest.setDataSourceType(1);
    saveBusinessTopicRequest.setDataId(Lists.newArrayList(modelId));
    saveBusinessTopicRequest.setType(201);
    saveBusinessTopicRequest.setTopicJsonData(JSON.parseObject(contentAsString));
    Integer importTopicId = businessTopicBizServiceImpl.saveBusinessTopic(saveBusinessTopicRequest);
    List<TopicSheetOutputVO> topicSheetData =
        processTreeReadBizService.getTopicSheetData(importTopicId);
    List<TopicSheetKpiOutputVO> topicSheetKpi =
        processTreeReadBizService.getTopicSheetKpi(topicSheetData.get(0).getDataId());
    Assertions.assertEquals(topicSheetKpi.get(0).getValue(), "21.65");
    Assertions.assertEquals(topicSheetKpi.get(1).getValue(), "12");
    Assertions.assertEquals(
        topicSheetKpi.get(2).getValue(), "T10 Determine necessity to stop indication");
  }

  @Order(13)
  @Test
  public void testDeviationIncludeMaxVariant() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    List<SheetKpiDTO> sheetKpiList = topicSheetKpiService.getTopicSheetKpiList(sheet[1]);
    Deviation request = new Deviation();
    request.setTopicId(sheet[0]);
    request.setTopicSheetId(sheet[1]);
    request.setSortKpiId(sheetKpiList.get(0).getId());
    request.setHasMost(0);
    request.setPageNum(1);
    request.setPageSize(50);
    DeviationOutput pageResp = devicationPageServiceImpl.page(request);
    List<TopicDeviationOutputVO> topicDeviation = pageResp.getList();
    //    Assertions.assertEquals(
    //        "T20 Print report Y to stop indication", topicDeviation.get(0).getEventName());
    //    Assertions.assertEquals(
    //        "T16 Report reasons to hold request", topicDeviation.get(1).getEventName());
    //    Assertions.assertEquals(
    //        "T17 Check report Y to stop indication", topicDeviation.get(2).getEventName());
    //    Assertions.assertEquals(
    //        "T19 Determine report Y to stop indication", topicDeviation.get(3).getEventName());
    Assertions.assertEquals(
        "T08 Draft and send request for advice", topicDeviation.get(4).getEventName());
    Assertions.assertEquals("2.100", topicDeviation.get(3).getRate().toString());
    Assertions.assertEquals("0.700", topicDeviation.get(4).getRate().toString());
    Assertions.assertEquals("1.400", topicDeviation.get(10).getRate().toString());
    Assertions.assertEquals(
        "T11 Create document X request unlicensed", topicDeviation.get(8).getEventName());
    Assertions.assertEquals(
        "T14 Determine document X request unlicensed", topicDeviation.get(10).getEventName());
    request.setHasMost(1);

    DeviationOutput pageResp2 = devicationPageServiceImpl.page(request);
    List<TopicDeviationOutputVO> topicDeviation2 = pageResp2.getList();

    //    Assertions.assertEquals(
    //        "T17 Check report Y to stop indication", topicDeviation2.get(0).getEventName());
    //    Assertions.assertEquals(
    //        "T19 Determine report Y to stop indication", topicDeviation2.get(1).getEventName());
    //    Assertions.assertEquals(
    //        "T20 Print report Y to stop indication", topicDeviation2.get(2).getEventName());
    //    Assertions.assertEquals(
    //        "T16 Report reasons to hold request", topicDeviation2.get(3).getEventName());
    Assertions.assertEquals(
        "T08 Draft and send request for advice", topicDeviation2.get(4).getEventName());
    Assertions.assertEquals("2.100", topicDeviation2.get(3).getRate().toString());
    Assertions.assertEquals("0.700", topicDeviation2.get(4).getRate().toString());
    Assertions.assertEquals("81.400", topicDeviation2.get(10).getRate().toString());
    Assertions.assertEquals("Confirmation of receipt", topicDeviation2.get(8).getEventName());
    Assertions.assertEquals(
        "T06 Determine necessity of stop advice", topicDeviation2.get(10).getEventName());
  }

  @Order(14)
  @Test
  public void testDeviationKpiAddFirstFunc() throws IOException, InterruptedException {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 1, modelId);
    KpiSaveRequest requestKpi = new KpiSaveRequest();
    requestKpi.setName("aa");
    requestKpi.setBaseLine("1");
    requestKpi.setBusinessDataId(sheet[1]);
    requestKpi.setBusinessType(1);
    requestKpi.setExpression("active_table.caseId");
    requestKpi.setKpiType(0);
    requestKpi.setSaveType(2);
    Integer kpiId = processTreeWriteBizService.saveKpi(requestKpi);
    Deviation request = new Deviation();
    request.setTopicId(sheet[0]);
    request.setTopicSheetId(sheet[1]);
    request.setSortKpiId(kpiId);
    request.setHasMost(0);
    request.setPageNum(1);
    request.setPageSize(50);
    DeviationOutput pageResp = devicationPageServiceImpl.page(request);
    List<TopicDeviationOutputVO> topicDeviation = pageResp.getList();
    List<String> kpiValues =
        topicDeviation.stream()
            .flatMap(
                m ->
                    m.getDeviationKipDTOList().stream()
                        .filter(f -> Objects.equals(f.getKpiName(), "aa"))
                        .map(DeviationKipDTO::getValue))
            .collect(Collectors.toList());
    Assertions.assertEquals(
        StringUtils.join(kpiValues, ","),
        "case-10011,case-10011,case-10011,case-10011,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10028,case-10071,case-10071,case-10071,case-10071,case-10071,case-10071,case-10071,case-10071,case-10071,case-10073,case-10073,case-10073,case-10073");
  }

  @Test
  public void testGetTopicSheet() {
    int modelId = testModelManager.getOrCreate(NormalActiveModel.MODEL_NAME);
    SaveBusinessTopicRequest test_topic_parent =
        SaveBusinessTopicRequest.builder()
            .name("test_topic_parent")
            .type(BusinessTopicTypeEnum.BUSINESS_TOPIC.getValue())
            .build();

    Integer topicId = businessTopicBizServiceImpl.saveBusinessTopic(test_topic_parent);

    // create sub topic
    SaveBusinessTopicRequest test_topic =
        SaveBusinessTopicRequest.builder()
            .businessTopicId(topicId)
            .name("test_topic2")
            .dataId(Lists.newArrayList(modelId))
            .dataSourceType(1)
            .type(BusinessTopicTypeEnum.BUSINESS_TREE.getValue())
            .build();

    Integer topicIdSub = businessTopicBizServiceImpl.saveBusinessTopic(test_topic);

    AddTopicSheetRequest sheetRequest =
        AddTopicSheetRequest.builder().topicId(topicIdSub).type(7).build();

    Integer sheetId = processTreeWriteBizService.addTopicSheet(sheetRequest);
    TopicSheetPO topicSheet = processTreeWriteBizService.getTopicSheet(sheetId);
    Assertions.assertTrue(topicSheet != null);
    DeleteTopicSheetRequest request = new DeleteTopicSheetRequest();

    request.setTopicSheetId(sheetId);
    processTreeWriteBizService.deleteTopicSheet(request);
    topicSheet = processTreeWriteBizService.getTopicSheet(sheetId);
    Assertions.assertTrue(topicSheet == null);
  }

  @Order(15)
  @Test
  public void testBuildCalcExpression() throws Exception {
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7);

    QueryEventRate4VariableRequest request =
        QueryEventRate4VariableRequest.builder()
            .topicId(sheet[0])
            .type(TopicFilterTypeEnum.CALC_TIME.value)
            .type1(OccurrenceTypeEnum.FIRST_OCCURRENCE.getCode())
            .type2(OccurrenceTypeEnum.LAST_OCCURRENCE.getCode())
            .unit(TimeUnitEnum.DAY.getValue())
            .num1(1)
            .event1("first_event")
            .event2("last_event")
            .build();
    String sql = ThroughputCalculator.buildCalcExpression(request);
    Assertions.assertEquals(
        sql,
        "floor( CALC_CASETIME (FIRST_OCCURRENCE [ 'first_event' ] TO LAST_OCCURRENCE [ 'last_event' ], DAYS))");
  }

  @Order(16)
  @Test
  public void testBuildCalcExpressionWithWorkTime() throws Exception {
    Integer[] sheet = super.createSheet(BusinessTopicTypeEnum.BUSINESS_TREE, 7);

    ComponentWorkTimePO workTimePO =
        ComponentWorkTimePO.builder()
            .tableName("table1")
            .ident("BAY")
            .startTime("09:00")
            .endTime("18:00")
            .build();

    QueryEventRate4VariableRequest request =
        QueryEventRate4VariableRequest.builder()
            .topicId(sheet[0])
            .type(TopicFilterTypeEnum.CALC_TIME.value)
            .type1(OccurrenceTypeEnum.FIRST_OCCURRENCE.getCode())
            .type2(OccurrenceTypeEnum.LAST_OCCURRENCE.getCode())
            .unit(TimeUnitEnum.DAY.getValue())
            .num1(1)
            .event1("first_event")
            .event2("last_event")
            .workTimeList(Lists.newArrayList(workTimePO))
            .build();
    String sql = ThroughputCalculator.buildCalcExpression(request);

    log.info(sql);
    //    Assertions.assertEquals(
    //        sql,
    //        "CALC_THROUGHPUT ( FIRST_OCCURRENCE [ 'first_event' ] TO LAST_OCCURRENCE [
    // 'last_event' ] , REMAP_TIMESTAMPS ( TIMESTAMP_COLUMN() , DAYS, INTERSECT ( WORKDAY_CALENDAR (
    // 'table1' , 'BAY'), WEEKDAY_CALENDAR( MONDAY 09:00-18:00 TUESDAY 09:00-18:00 Wednesday
    // 09:00-18:00 THURSDAY 09:00-18:00 FRIDAY 09:00-18:00 SATURDAY 09:00-18:00 SUNDAY 09:00-18:00 )
    // ) ) )");
  }
}
