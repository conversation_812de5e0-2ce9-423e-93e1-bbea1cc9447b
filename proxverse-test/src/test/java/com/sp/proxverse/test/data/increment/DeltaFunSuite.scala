package com.sp.proxverse.test.data.increment

import io.delta.tables.DeltaTable
import org.apache.spark.sql.{SparkSession, SparkSessionEnv}

object DeltaFunSuite {

    def run(spark: SparkSession): Unit = {
        spark.read.option("header", "true")
                .csv("/Users/<USER>/work_space/pm-project/proxverse-test/src/test/resources/test_data/increment/1.csv")
                .write
                .mode("overwrite")
                .format("delta")
                .saveAsTable("delta1")

        val target = spark.read.option("header", "true")
                .csv("/Users/<USER>/work_space/pm-project/proxverse-test/src/test/resources/test_data/increment/2.csv")

        DeltaTable.forName(SparkSessionEnv.getSparkSession, "delta1").as("old_data")
                .merge(target.as("new_data"), "old_data.id = new_data.id and old_data.`case:concept:name` = new_data.`case:concept:name`")
                .whenMatched()
                .updateAll()
                .whenNotMatched()
                .insertAll()
                .execute()

        val rows = spark.table("delta1").collect()
        rows
    }
}
