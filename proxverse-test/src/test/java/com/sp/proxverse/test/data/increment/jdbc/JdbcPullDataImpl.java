package com.sp.proxverse.test.data.increment.jdbc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.prx.service.model.IncrementalSparkService;
import com.prx.service.page.JdbcTablePageServiceImpl;
import com.prx.service.page.entity.JdbcTable;
import com.sp.proxverse.common.model.dict.DataConnectorJDBCTypeEnum;
import com.sp.proxverse.common.model.dto.TableDataDTO;
import com.sp.proxverse.common.model.dto.TableFieldDTO;
import com.sp.proxverse.common.model.dto.connector.JdbcUrlDriverDTO;
import com.sp.proxverse.common.model.job.SaveJdbcConnectorDTO;
import com.sp.proxverse.common.model.po.DataConnectorJdbcPO;
import com.sp.proxverse.common.model.po.DataConnectorPO;
import com.sp.proxverse.common.model.po.DataExtractorPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.model.vo.dataconnector.request.QueryFieldListRequest;
import com.sp.proxverse.common.model.vo.dataconnector.request.SaveJdbcConnectorRequest;
import com.sp.proxverse.common.model.vo.dataconnector.request.SelectFieldRequest;
import com.sp.proxverse.common.model.vo.dataconnector.request.SelectFieldSubRequest;
import com.sp.proxverse.common.util.JdbcUtil;
import com.sp.proxverse.datamerge.service.biz.ConnectorCommonService;
import com.sp.proxverse.datamerge.service.biz.DataConnectorBizService;
import com.sp.proxverse.datamerge.service.biz.DataExtractorBizService;
import com.sp.proxverse.interfaces.dao.service.DataConnectorJdbcService;
import com.sp.proxverse.interfaces.dao.service.DataConnectorService;
import com.sp.proxverse.interfaces.dao.service.DataExtractorService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.apache.spark.common.model.DataTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JdbcPullDataImpl {

  @Autowired ConnectorCommonService connectorCommonService;

  @Autowired DataConnectorBizService dataConnectorBizService;

  @Autowired DataConnectorService dataConnectorService;

  @Autowired DataConnectorJdbcService dataConnectorJdbcService;

  @Autowired DataExtractorBizService dataExtractorBizService;

  @Autowired JdbcTablePageServiceImpl jdbcTablePageService;

  @Autowired FileService fileService;

  @Autowired DataExtractorService dataExtractorService;

  @Autowired IncrementalSparkService incrementalSparkService;

  String whereRule = "CASEID >= 1 and TIME > '2020-10-10'";

  String whereRule4Oracle = "CASEID >= 1 and TIME > TO_DATE('2023-08-01', 'YYYY-MM-DD')";

  public String whereRule(Integer jdbcType) {
    if (jdbcType == DataConnectorJDBCTypeEnum.ORACLE.getValue()) {
      return whereRule4Oracle;
    }
    return whereRule;
  }

  public Integer saveJdbcConnector(
      String ip,
      String name,
      Integer poolId,
      Integer port,
      String user,
      String password,
      String dbname,
      String schemaName,
      String link,
      Integer jdbcType,
      Integer bizType) {
    SaveJdbcConnectorRequest request = new SaveJdbcConnectorRequest();
    request.setName(name);
    request.setPoolId(poolId);
    request.setIp(ip);
    request.setPort(port);
    request.setUsername(user);
    request.setPassword(password);
    request.setConnectLink(link);
    request.setJdbcType(jdbcType);
    request.setBizType(bizType);
    request.setDbname(dbname);
    request.setSchemaName(schemaName);
    return dataConnectorBizService.saveJdbcConnector(request);
  }

  public Integer saveJdbcConnector(
      String name,
      Integer poolId,
      Integer port,
      String user,
      String password,
      String dbname,
      String schemaName,
      String link,
      Integer jdbcType,
      Integer bizType,
      String ip) {
    SaveJdbcConnectorRequest request = new SaveJdbcConnectorRequest();
    request.setName(name);
    request.setPoolId(poolId);
    request.setIp(ip);
    request.setPort(port);
    request.setUsername(user);
    request.setPassword(password);
    request.setConnectLink(link);
    request.setJdbcType(jdbcType);
    request.setBizType(bizType);
    request.setDbname(dbname);
    request.setSchemaName(schemaName);
    return dataConnectorBizService.saveJdbcConnector(request);
  }

  public List<SelectFieldSubRequest> builderFieldList() {
    return Lists.newArrayList(
        SelectFieldSubRequest.builder()
            .field("ID")
            .fieldType(DataTypeEnum.INT.getValue())
            .hasPriField(1)
            .build(),
        SelectFieldSubRequest.builder()
            .field("CASEID")
            .fieldType(DataTypeEnum.INT.getValue())
            .build(),
        SelectFieldSubRequest.builder()
            .field("EVENT")
            .fieldType(DataTypeEnum.STR.getValue())
            .build(),
        SelectFieldSubRequest.builder()
            .field("TIME")
            .fieldType(DataTypeEnum.DATE_TIME.getValue())
            .hasUpdateTimeField(1)
            .build());
  }

  public List<String> getTableListByDataConnector(Integer dataConnectorId) {
    JdbcTable jdbcTable = new JdbcTable();
    jdbcTable.setDataConnectorId(dataConnectorId);
    jdbcTable.setPageNum(1);
    jdbcTable.setPageSize(10000);
    return jdbcTablePageService.page(jdbcTable).getList();
  }

  public List<TableFieldDTO> getJdbcTableFieldList(Integer connectorId, String tableName) {
    QueryFieldListRequest request = new QueryFieldListRequest();
    request.setDataConnectorId(connectorId);
    request.setTableName(tableName);
    return dataExtractorBizService.getJdbcTableFieldList(request);
  }

  public List<TableDataDTO> getPreviewData(
      Integer extractorId,
      Integer jdbcType,
      String tableName,
      String whereRule,
      List<String> columnList) {
    SelectFieldRequest request = new SelectFieldRequest();
    request.setDataExtractorId(extractorId);
    request.setTableName(tableName);
    request.setWhereRule(whereRule(jdbcType));
    request.setFieldList(
        columnList.stream()
            .map(m -> SelectFieldSubRequest.builder().field(m).build())
            .collect(Collectors.toList()));
    DataExtractorPO byId = dataExtractorService.getById(extractorId);
    request.setDataConnectorId(byId.getConnectorId());
    return dataExtractorBizService.getPreviewData(request);
  }

  public long countPullData(
      Integer poolId,
      Integer jdbcType,
      Integer updateType,
      Integer extractorId,
      String fileName,
      String whereRule) {
    FilePO filePO =
        fileService.getOne(new LambdaQueryWrapper<FilePO>().eq(FilePO::getFilename, fileName));
    DataExtractorPO extractorPO = dataExtractorService.getById(extractorId);
    SaveJdbcConnectorDTO req = new SaveJdbcConnectorDTO();
    req.setPoolId(poolId);
    req.setFileId(filePO.getId());
    req.setTableName(extractorPO.getSparkTableName());
    req.setUpdateType(updateType);
    req.setWhereRule(whereRule(jdbcType));
    req.setTempTableName("`temp_" + extractorPO.getSparkTableName().replace("`", "") + "`");
    return incrementalSparkService.pullJdbcData(req).getRowsCount();
  }

  public void clearMysqlTempData(Integer connectorId, String user, String pass) throws Exception {
    DataConnectorPO connectorPO = dataConnectorService.getById(connectorId);
    DataConnectorJdbcPO jdbcPO = dataConnectorJdbcService.getByConnectorId(connectorId);
    JdbcUrlDriverDTO jdbcUrlDriverDTO = JdbcUtil.buildUrl(connectorPO, jdbcPO);
    String url = jdbcUrlDriverDTO.getUrl();
    String username = user;
    String password = pass;
    try (Connection connection = DriverManager.getConnection(url, username, password)) {
      String insertQuery = "TRUNCATE TABLE T_JDBC.T_ORDER;";
      try (Statement statement = connection.createStatement()) {
        statement.executeUpdate(insertQuery);
      }
    }
  }

  public void insertMysqlTempData(Integer connectorId, String user, String pass) throws Exception {
    DataConnectorPO connectorPO = dataConnectorService.getById(connectorId);
    DataConnectorJdbcPO jdbcPO = dataConnectorJdbcService.getByConnectorId(connectorId);
    JdbcUrlDriverDTO jdbcUrlDriverDTO = JdbcUtil.buildUrl(connectorPO, jdbcPO);
    String url = jdbcUrlDriverDTO.getUrl();
    String username = user;
    String password = pass;
    try (Connection connection = DriverManager.getConnection(url, username, password)) {
      String insertQuery = "INSERT INTO T_ORDER VALUES (?, ?, ?,?)";
      if (connectorPO.getJdbcType().equals(DataConnectorJDBCTypeEnum.DB2.getValue())) {
        insertQuery = "INSERT INTO T_JDBC.T_ORDER VALUES (?, ?, ?,?)";
      }
      try (PreparedStatement statement = connection.prepareStatement(insertQuery)) {
        statement.setInt(1, (int) new java.util.Date().getTime());
        statement.setInt(2, 100);
        statement.setString(3, "insert_event");
        statement.setTimestamp(
            4,
            new Timestamp(new java.util.Date().getTime() + TimeZone.getDefault().getRawOffset()));
        int rowsInserted = statement.executeUpdate();
        if (rowsInserted < 1) {
          throw new RuntimeException(" ");
        }
      }
    }
  }

  public void insertMysqlSignalTempData(Integer connectorId, String user, String pass)
      throws Exception {
    DataConnectorPO connectorPO = dataConnectorService.getById(connectorId);
    DataConnectorJdbcPO jdbcPO = dataConnectorJdbcService.getByConnectorId(connectorId);
    JdbcUrlDriverDTO jdbcUrlDriverDTO = JdbcUtil.buildUrl(connectorPO, jdbcPO);
    String url = jdbcUrlDriverDTO.getUrl();
    String username = user;
    String password = pass;
    try (Connection connection = DriverManager.getConnection(url, username, password);
        PreparedStatement statement =
            connection.prepareStatement("INSERT INTO T_SIGNAL2 VALUES (?, ?, ?,?,?)")) {
      statement.setInt(1, (int) new java.util.Date().getTime());
      statement.setInt(2, 100);
      statement.setString(3, "<EMAIL>");
      statement.setString(4, "18d489181d25f2e38c7e943db24595e6");
      statement.setTimestamp(
          5, new Timestamp(new java.util.Date().getTime() + TimeZone.getDefault().getRawOffset()));
      int rowsInserted = statement.executeUpdate();
      if (rowsInserted < 1) {
        throw new RuntimeException(" ");
      }
    }
  }
}
