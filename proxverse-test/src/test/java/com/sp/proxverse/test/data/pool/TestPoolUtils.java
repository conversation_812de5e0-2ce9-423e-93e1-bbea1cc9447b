package com.sp.proxverse.test.data.pool;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.DataPoolPO;
import com.sp.proxverse.common.model.vo.request.DataPoolSaveRequest;
import com.sp.proxverse.datamerge.service.biz.DataModelBizService;
import com.sp.proxverse.interfaces.dao.service.DataPoolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TestPoolUtils {

  @Autowired private DataModelBizService dataModelBizService;

  @Autowired private DataPoolService dataPoolService;

  public DataPoolPO getDataPool(String name) {
    DataPoolSaveRequest dataPoolSaveRequest = new DataPoolSaveRequest();
    dataPoolSaveRequest.setName(name);

    DataPoolPO pool =
        dataPoolService.getOne(
            new LambdaQueryWrapper<DataPoolPO>()
                .eq(DataPoolPO::getPoolName, name)
                .eq(DataPoolPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last("LIMIT 1"));
    return pool;
  }

  public Integer createPool(String name) {

    DataPoolPO dataPool = getDataPool(name);
    if (dataPool != null) {
      return dataPool.getId();
    }
    DataPoolSaveRequest dataPoolSaveRequest = new DataPoolSaveRequest();
    dataPoolSaveRequest.setName(name);
    return dataModelBizService.saveDataPool(dataPoolSaveRequest);
  }
}
