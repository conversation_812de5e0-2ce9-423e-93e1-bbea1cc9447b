package com.sp.proxverse.test.data.source

import java.nio.charset.StandardCharsets.UTF_8
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.spark.sql.kafka010.KafkaSinkSuiteBase
import org.apache.spark.sql.source.{KafkaSource, KafkaSourceOptions}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row}


class KafkaSourceSuite extends KafkaSinkSuiteBase {

  import testImplicits._

  override protected def sparkConf = {
    val conf = super.sparkConf
    conf.set("spark.plugins", "io.glutenproject.GlutenPlugin")
    conf.set("spark.gluten.sql.columnar.backend.lib", "velox")
    conf.set("spark.gluten.sql.columnar.libpath", "/Users/<USER>/native_engine")
    conf.set("spark.gluten.enabled", "true")
    conf.set("spark.gluten.sql.columnar.broadcastexchange", "true")
    conf.set("spark.gluten.sql.columnar.broadcastJoin", "true")
    conf.set("spark.gluten.sql.columnar.filescan", "false")
    conf.set(
      "spark.gluten.sql.columnar.extended.expressions.transformer",
      "org.apache.spark.sql.pql.transform.PQLTransform")
    conf.set("spark.sql.inMemoryColumnarStorage.partitionPruning", "false")
    conf.set("spark.memory.offHeap.enabled", "true")
    conf.set("spark.sql.inMemoryColumnarStorage.partitionPruning", "false")
    conf.set("spark.sql.inMemoryColumnarStorage.enableVectorizedReader", "true")
    conf.set("spark.sql.codegen.maxFields", "1000")
    conf.set("spark.memory.offHeap.size", "4G")
  }

  test("batch - read to kafka") {
    val topic = newTopic()
    testUtils.createTopic(topic)
    val data = Seq(
      Row(topic, "1", Seq(Row("a", "b".getBytes(UTF_8)))),
      Row(topic, "2", Seq(Row("c", "d".getBytes(UTF_8)), Row("e", "f".getBytes(UTF_8)))),
      Row(topic, "3", Seq(Row("g", "h".getBytes(UTF_8)), Row("g", "i".getBytes(UTF_8)))),
      Row(topic, "4", null),
      Row(
        topic,
        "5",
        Seq(
          Row("j", "k".getBytes(UTF_8)),
          Row("j", "l".getBytes(UTF_8)),
          Row("m", "n".getBytes(UTF_8)))))

    val df = spark.createDataFrame(
      spark.sparkContext.parallelize(data),
      StructType(
        Seq(
          StructField("topic", StringType),
          StructField("value", StringType),
          StructField("headers", KafkaRecordToRowConverter.headersType))))

    df.write
      .format("kafka")
      .option("kafka.bootstrap.servers", testUtils.brokerAddress)
      .option("topic", topic)
      .mode("append")
      .save()

    val options = new KafkaSourceOptions(testUtils.brokerAddress, topic)
    val source = new KafkaSource(options)
    assert("string" == source.source().source.schema.toList.head.dataType.typeName)
    checkAnswer(
      source.source().source.selectExpr("CAST(value as STRING) value", "headers"),
      Row("1", "[{a, b}]") ::
        Row("2", "[{c, d}, {e, f}]") ::
        Row("3", "[{g, h}, {g, i}]") ::
        Row("4", null) ::
        Row(
          "5", "[{j, k}, {j, l}, {m, n}]"
        ) :: Nil
    )
  }
  test("batch - write to kafka") {
    val topic = newTopic()
    testUtils.createTopic(topic)
    val data = Seq(
      Row(topic, "1", Seq(Row("a", "b".getBytes(UTF_8)))),
      Row(topic, "2", Seq(Row("c", "d".getBytes(UTF_8)), Row("e", "f".getBytes(UTF_8)))),
      Row(topic, "3", Seq(Row("g", "h".getBytes(UTF_8)), Row("g", "i".getBytes(UTF_8)))),
      Row(topic, "4", null),
      Row(
        topic,
        "5",
        Seq(
          Row("j", "k".getBytes(UTF_8)),
          Row("j", "l".getBytes(UTF_8)),
          Row("m", "n".getBytes(UTF_8)))))

    val df = spark.createDataFrame(
      spark.sparkContext.parallelize(data),
      StructType(
        Seq(
          StructField("topic", StringType),
          StructField("value", StringType),
          StructField("headers", KafkaRecordToRowConverter.headersType))))

    df.write
      .format("kafka")
      .option("kafka.bootstrap.servers", testUtils.brokerAddress)
      .option("topic", topic)
      .mode("append")
      .save()
    checkAnswer(
      createKafkaReader(topic, includeHeaders = true)
        .selectExpr("CAST(value as STRING) value", "headers"),
      Row("1", Seq(Row("a", "b".getBytes(UTF_8)))) ::
        Row("2", Seq(Row("c", "d".getBytes(UTF_8)), Row("e", "f".getBytes(UTF_8)))) ::
        Row("3", Seq(Row("g", "h".getBytes(UTF_8)), Row("g", "i".getBytes(UTF_8)))) ::
        Row("4", null) ::
        Row(
        "5",
        Seq(
          Row("j", "k".getBytes(UTF_8)),
          Row("j", "l".getBytes(UTF_8)),
          Row("m", "n".getBytes(UTF_8)))) ::
        Nil)
  }

  def writeToKafka(
      df: DataFrame,
      topic: String,
      options: Map[String, String] = Map.empty): Unit = {
    df.write
      .format("kafka")
      .option("kafka.bootstrap.servers", testUtils.brokerAddress)
      .option("topic", topic)
      .options(options)
      .mode("append")
      .save()
  }

  def partitionsInTopic(topic: String): Set[Int] = {
    createKafkaReader(topic)
      .select("partition")
      .map(_.getInt(0))
      .collect()
      .toSet
  }

  object KafkaRecordToRowConverter {
    type Record = ConsumerRecord[Array[Byte], Array[Byte]]

    val headersType = ArrayType(
      StructType(Array(StructField("key", StringType), StructField("value", BinaryType))))

    private val schemaWithoutHeaders = new StructType(
      Array(
        StructField("key", BinaryType),
        StructField("value", BinaryType),
        StructField("topic", StringType),
        StructField("partition", IntegerType),
        StructField("offset", LongType),
        StructField("timestamp", TimestampType),
        StructField("timestampType", IntegerType)))

    private val schemaWithHeaders =
      new StructType(schemaWithoutHeaders.fields :+ StructField("headers", headersType))

    def kafkaSchema(includeHeaders: Boolean): StructType = {
      if (includeHeaders) schemaWithHeaders else schemaWithoutHeaders
    }
  }
}
