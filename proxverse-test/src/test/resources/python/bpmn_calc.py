import sys

from pm4py.objects.bpmn.importer import importer as bpmn_importer
from pm4py.objects.conversion.bpmn.variants import to_petri_net
import io

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

if __name__ == "__main__":
    path = sys.argv[1]
    bpmn_graph = bpmn_importer.apply(path)

    net, im, fm = to_petri_net.apply(bpmn_graph)

    places = net.places
    transitions = net.transitions
    arcs = net.arcs

    places_list = [p.name for p in places]

    transitions_list = []

    edges_list = []

    for t in transitions:
        if t.label == None:
            transitions_list.append([t.name, "null"])
        else :
            transitions_list.append([t.name, t.label])


    for edge in arcs:
        edges_list.append([edge.source.name, edge.target.name])

    bpmn_dict = {"places": places_list, "transitions": transitions_list, "edges": edges_list}
    print(bpmn_dict)
