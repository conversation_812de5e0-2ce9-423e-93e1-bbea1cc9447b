import sys

import pandas as pd
import pm4py

if __name__ == "__main__":
  path = sys.argv[1]
  file_name = sys.argv[2]
  log = pd.read_csv(path)
  #format_dataframe在3.0.0版本后将会被remove掉
  log = pm4py.format_dataframe(log, case_id='case:concept:name', activity_key='concept:name', timestamp_key='time:timestamp')
  log = pm4py.convert_to_event_log(log)
  tree = pm4py.discover_process_tree_inductive(log)
  bpmn_graph = pm4py.convert_to_bpmn(tree)
  pm4py.write_bpmn(bpmn_graph, file_name)
