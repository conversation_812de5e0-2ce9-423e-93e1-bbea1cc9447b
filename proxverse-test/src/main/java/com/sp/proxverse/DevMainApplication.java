package com.sp.proxverse;

import org.apache.spark.SparkConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@MapperScan(
    basePackages = {
      "com.sp.proxverse.common.mapper",
      "com.sp.proxverse.interfaces.dao",
      "com.sp.proxverse.oauth2",
      "com.sp.proxverse.mapper"
    })
@ComponentScan(
    basePackages = {
      "com.sp.proxverse",
      "com.sp.proxverse.oauth2.service",
      "com.sp.proxverse.datamerge.service",
      "com.sp.proxverse.oauth2.service",
      "com.prx",
      "com.sp.proxverse.process"
    })
@EnableTransactionManagement
@EnableScheduling
public class DevMainApplication {

  public DevMainApplication() {
    SparkConfiguration.withProfile("test-inmem").initSparkSession();
  }

  public static void main(String[] args) {
    SpringApplication.run(DevMainApplication.class, args);
  }
}
