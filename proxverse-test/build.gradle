/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
  id 'scala'
}
repositories {
  mavenLocal()
  maven { url 'http://121.37.155.119:8081/repository/maven-public/' }
  maven { url 'https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/' }
}
sourceSets {
  main {
    scala {
      srcDirs = ['src/main/scala', 'src/main/java']
    }
    java {
      srcDirs = []
    }
  }

  test {
    scala {
      srcDirs = ['src/test/scala', 'src/test/java']
    }
    java {
      srcDirs = []
    }
  }
}
dependencies {
  implementation project(':proxverse-main')
  implementation project(':proxverse-data-core')
  implementation project(':proxverse-engine')

  implementation 'org.codehaus.janino:commons-compiler:3.0.16'
  implementation 'org.codehaus.janino:janino:3.0.16'
  implementation 'com.fasterxml.jackson.core:jackson-core:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4'
  implementation 'com.fasterxml.jackson.module:jackson-module-scala_2.12:2.13.0'
  implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.13.3'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'com.h2database:h2:2.1.214'
  implementation 'org.apache.kafka:kafka-clients:2.8.1'
  implementation 'org.xerial.snappy:snappy-java:1.1.8.4'
  implementation 'org.apache.commons:commons-pool2:2.11.1'
  implementation 'org.apache.spark:spark-sql-kafka-0-10_2.12:3.3.1-prx-0.0.2'
  implementation 'com.sp:starry-core:0.0.9'
  testImplementation 'org.springframework.boot:spring-boot-starter-test:2.3.2.RELEASE'
  testImplementation 'org.scalatestplus:junit-4-13_2.12:3.2.16.0'
  testImplementation 'org.apache.kafka:kafka_2.12:2.8.1'
  testImplementation 'com.sp:proxverse-spark-common:1.0.0'
  testImplementation 'org.mockito:mockito-scala_2.12:1.16.3'
  testImplementation 'org.mockito:mockito-scala-scalatest_2.12:1.16.3'
  testImplementation 'org.mockito:mockito-scala-specs2_2.12:1.16.3'
  testImplementation 'org.mockito:mockito-scala-cats_2.12:1.16.3'
  testImplementation 'org.mockito:mockito-scala-scalaz_2.12:1.16.3'
  implementation 'org.apache.tomcat.embed:tomcat-embed-core:9.0.37'
  implementation 'org.springframework.security:spring-security-config:5.3.3.RELEASE'
  implementation 'org.springframework:spring-context:5.2.8.RELEASE'
  implementation 'org.springframework:spring-tx:5.2.8.RELEASE'
  implementation 'org.mybatis:mybatis-spring:2.1.2'
  testImplementation 'org.springframework.boot:spring-boot-starter-test:2.3.2.RELEASE'
}

description = 'proxverse-test'
