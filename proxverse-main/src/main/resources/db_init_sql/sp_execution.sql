drop schema IF EXISTS "sp_execution" cascade ;
create schema IF NOT EXISTS sp_execution;
set schema sp_execution;

-- ----------------------------
-- Table structure for t_action_log
-- ----------------------------
DROP TABLE IF EXISTS `t_action_log`;
CREATE TABLE `t_action_log`  (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `action_id` int(11) NULL DEFAULT NULL,
                                 `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                 `deleted` tinyint(2) NULL DEFAULT 0,
                                 `user_id` int(11) NULL DEFAULT NULL,
                                 `tenant_id` int(11) NULL DEFAULT NULL,
                                 `status` tinyint(2) NULL DEFAULT 1 COMMENT '1:成功，2:失败',
                                 `task_id` int(11) NULL DEFAULT NULL,
                                 `type` tinyint(2) NULL DEFAULT NULL,
                                 PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 4712 CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_action_parameter
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_action_parameter`;
CREATE TABLE `t_optimization_action_parameter`  (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
                                                    `execute_action_id` int(11) NULL DEFAULT NULL COMMENT '执行动作id',
                                                    `parameter` varchar(50) NULL DEFAULT NULL COMMENT '参数',
                                                    `value` varchar(50) NULL DEFAULT NULL COMMENT '参数值',
                                                    `create_id` int(11) NULL DEFAULT NULL COMMENT '创建人Id',
                                                    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                    `deleted` int(11) NULL DEFAULT NULL COMMENT '是否逻辑删除',
                                                    `order_no` int(11) NULL DEFAULT NULL COMMENT '排序号',
                                                    `type` varchar(4000) NULL DEFAULT NULL COMMENT '类型',
                                                    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 300 CHARACTER SET = utf8  COMMENT = '执行动作参数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_execute_action
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_execute_action`;
CREATE TABLE `t_optimization_execute_action`  (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
                                                  `name` varchar(50)  NULL DEFAULT NULL COMMENT '执行动作名称',
                                                  `topic_id` int(11) NULL DEFAULT NULL COMMENT '主题id',
                                                  `optimization_object_id` int(20) NULL DEFAULT NULL COMMENT '优化对象id',
                                                  `action_flow_id` int(11) NULL DEFAULT NULL COMMENT '动作流程id',
                                                  `type` int(11) NULL DEFAULT NULL COMMENT '类型（0手动；1自动）',
                                                  `create_id` int(11) NULL DEFAULT NULL COMMENT '创建人Id',
                                                  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                  `deleted` int(11) NULL DEFAULT NULL COMMENT '是否逻辑删除',
                                                  `order_no` int(11) NULL DEFAULT NULL COMMENT '排序号',
                                                  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 125 CHARACTER SET = utf8  COMMENT = '优化执行动作' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_execution_management_rules
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_execution_management_rules`;
CREATE TABLE `t_optimization_execution_management_rules`  (
                                                              `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                                              `optimization_execution_id` bigint(20) NULL DEFAULT NULL COMMENT '执行规则id',
                                                              `data_model_id` bigint(20) NULL DEFAULT NULL COMMENT '数据模型id',
                                                              `file_id` bigint(20) NULL DEFAULT NULL COMMENT '表id',
                                                              `column_id` bigint(20) NULL DEFAULT NULL COMMENT '列id',
                                                              `field_type_rule` bigint(20) NULL DEFAULT NULL COMMENT '字段类型规则',
                                                              `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
                                                              `match_value` varchar(50)  NULL DEFAULT NULL COMMENT '匹配值',
                                                              `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                              `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                              `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                                              `sort` bigint(20) NULL DEFAULT NULL COMMENT '排序',
                                                              `tenant_id` int(11) NULL DEFAULT NULL,
                                                              PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 84 CHARACTER SET = utf8  COMMENT = '执行管理规则' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_execution_rules
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_execution_rules`;
CREATE TABLE `t_optimization_execution_rules`  (
                                                   `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                                   `optimization_object_id` bigint(20) NULL DEFAULT NULL COMMENT '优化对象id',
                                                   `type` int(11) NULL DEFAULT NULL COMMENT '类型',
                                                   `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                                   `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                   `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                   `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                                   `tenant_id` int(11) NULL DEFAULT NULL,
                                                   PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8  COMMENT = '执行规则' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_object
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_object`;
CREATE TABLE `t_optimization_object`  (
                                          `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                          `name` varchar(50) NULL DEFAULT NULL COMMENT '名称',
                                          `status` bigint(20) NULL DEFAULT NULL COMMENT '任务状态（0暂停/1运行中）',
                                          `description` varchar(4000) NULL DEFAULT NULL COMMENT '描述',
                                          `optimization_project_id` bigint(20) NULL DEFAULT NULL COMMENT '优化项目id',
                                          `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                          `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                          `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                          `tenant_id` int(11) NULL DEFAULT NULL,
                                          PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 67 CHARACTER SET = utf8  COMMENT = '优化对象' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_project
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_project`;
CREATE TABLE `t_optimization_project`  (
                                           `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                           `name` varchar(50) NULL DEFAULT NULL COMMENT '名称',
                                           `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                           `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                           `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                           `tenant_id` int(11) NULL DEFAULT NULL,
                                           PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 53 CHARACTER SET = utf8  COMMENT = '优化项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_signal
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_signal`;
CREATE TABLE `t_optimization_signal`  (
                                          `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                          `optimization_object_id` bigint(20) NULL DEFAULT NULL COMMENT '优化对象id',
                                          `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                          `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                          `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                          `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                          `tenant_id` int(11) NULL DEFAULT NULL,
                                          PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8  COMMENT = '优化信号' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_signal_condition
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_signal_condition`;
CREATE TABLE `t_optimization_signal_condition`  (
                                                    `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                                    `name` varchar(50)  NULL DEFAULT NULL COMMENT '条件名称',
                                                    `optimization_signal_id` bigint(20) NULL DEFAULT NULL COMMENT '优化信号id',
                                                    `column_id` bigint(20) NULL DEFAULT NULL COMMENT '信号数据模型表列id',
                                                    `field_type_rule` bigint(20) NULL DEFAULT NULL COMMENT '字段类型规则',
                                                    `match_value` varchar(1000)  NULL DEFAULT NULL COMMENT '匹配值',
                                                    `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                                    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                    `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                                    `tenant_id` int(11) NULL DEFAULT NULL,
                                                    PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 59 CHARACTER SET = utf8  COMMENT = '优化信号条件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_signal_data
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_signal_data`;
CREATE TABLE `t_optimization_signal_data`  (
                                               `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                               `optimization_task_id` bigint(20) NULL DEFAULT NULL COMMENT '优化任务id',
                                               `signal_id` bigint(20) NULL DEFAULT NULL COMMENT '信号id',
                                               `column_id` bigint(20) NULL DEFAULT NULL COMMENT '列id',
                                               `row_id` int(11) NULL DEFAULT NULL COMMENT '行id',
                                               `value` varchar(1000)  NULL DEFAULT NULL COMMENT '值',
                                               `creater_id` varchar(50)  NULL DEFAULT NULL COMMENT '创建人id',
                                               `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                               `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                               `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                               `tenant_id` int(11) NULL DEFAULT NULL,
                                               PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 90149 CHARACTER SET = utf8  COMMENT = '优化信号数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_signal_data_model_table_column
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_signal_data_model_table_column`;
CREATE TABLE `t_optimization_signal_data_model_table_column`  (
                                                                  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                                                  `optimization_signal_id` bigint(20) NULL DEFAULT NULL COMMENT '优化信号id',
                                                                  `data_model_id` bigint(20) NULL DEFAULT NULL COMMENT '数据模型id',
                                                                  `file_id` bigint(20) NULL DEFAULT NULL COMMENT '文件id（表）',
                                                                  `column_id` bigint(20) NULL DEFAULT NULL COMMENT '列id',
                                                                  `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                                                  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                                  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                                  `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                                                  `tenant_id` int(11) NULL DEFAULT NULL,
                                                                  PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 293 CHARACTER SET = utf8   ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_task
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_task`;
CREATE TABLE `t_optimization_task`  (
                                        `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `status` bigint(20) NULL DEFAULT NULL COMMENT '任务状态（0未完成/1已完成）',
                                        `optimization_object_id` bigint(20) NULL DEFAULT NULL COMMENT '优化对象id',
                                        `optimization_signal_id` bigint(20) NULL DEFAULT NULL COMMENT '优化信号id',
                                        `assigned_user_id` bigint(20) NULL DEFAULT NULL COMMENT '被分配的用户id',
                                        `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                        `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                        `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                        `tenant_id` int(11) NULL DEFAULT NULL,
                                        `execute_action_ids` varchar(2000)   NULL DEFAULT NULL COMMENT '执行动作id集',
                                        PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2143578250 CHARACTER SET = utf8  COMMENT = '优化任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_task_comment
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_task_comment`;
CREATE TABLE `t_optimization_task_comment`  (
                                                `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                                `comment_content` varchar(2000)   NULL DEFAULT NULL COMMENT '评论内容',
                                                `comment_user_id` bigint(20) NULL DEFAULT NULL COMMENT '评论的用户id',
                                                `optimization_task_id` bigint(20) NULL DEFAULT NULL COMMENT '优化任务id',
                                                `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                                `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                                `tenant_id` int(11) NULL DEFAULT NULL,
                                                PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8  COMMENT = '优化任务评论' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_task_log
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_task_log`;
CREATE TABLE `t_optimization_task_log`  (
                                            `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                            `type` bigint(20) NULL DEFAULT NULL COMMENT '类型（0任务分配/1状态）',
                                            `optimization_task_id` bigint(20) NULL DEFAULT NULL COMMENT '优化任务id',
                                            `distribution_user_id` bigint(20) NULL DEFAULT NULL COMMENT '分配的用户id',
                                            `assigned_user_id` varchar(2000)  NULL DEFAULT NULL COMMENT '被分配的用户id',
                                            `status` bigint(20) NULL DEFAULT NULL COMMENT '任务状态（0完成任务/1重启任务）',
                                            `creater_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人id',
                                            `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                            `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                            `deleted` bigint(20) NULL DEFAULT NULL COMMENT '是否删除',
                                            `tenant_id` int(11) NULL DEFAULT NULL,
                                            PRIMARY KEY (`Id`)
) ENGINE = InnoDB AUTO_INCREMENT = 11187 CHARACTER SET = utf8  COMMENT = '优化任务日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_optimization_task_user
-- ----------------------------
DROP TABLE IF EXISTS `t_optimization_task_user`;
CREATE TABLE `t_optimization_task_user`  (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `task_id` int(11) NULL DEFAULT NULL,
                                             `user_id` int(11) NULL DEFAULT NULL,
                                             `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                             `deleted` varchar(255)  NULL DEFAULT '0',
                                             PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 17148 CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

