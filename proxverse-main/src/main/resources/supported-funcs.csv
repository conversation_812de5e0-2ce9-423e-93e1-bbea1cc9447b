PU_AVG,"PU_AVG ( target_table, source_table.column [, filter_expression] )",PU_AVG计算给定目标表中每个元素的指定源列的平均值。,PQL/PU-Aggregation/PU_AVG.md
PU_COUNT,"PU_COUNT ( target_table, source_table.column [, filter_expression] )",PU_COUNT计算给定目标表中每个元素的指定源列中的元素数。,PQL/PU-Aggregation/PU_COUNT.md
PU_COUNT_DISTINCT,"PU_COUNT_DISTINCT ( target_table, source_table.column [, filter_expression] )",PU_COUNT_DISTINCT计算给定目标表中每个元素的指定源列中不同元素的数量。,PQL/PU-Aggregation/PU_COUNT_DISTINCT.md
PU_FIRST,"PU_FIRST ( target_table, source_table.column [, filter_expression] [, ORDER BY source_table.column [ASC|DESC] ] )",PU_FIRST返回给定目标表中每个元素的指定源列的第一个元素。,PQL/PU-Aggregation/PU_FIRST.md
PU_LAST,"PU_LAST ( target_table, source_table.column [, filter_expression] [, ORDER BY source_table.column [ASC|DESC] ] )",PU_LAST返回给定目标表的每个元素的指定源列的最后一个元素。,PQL/PU-Aggregation/PU_LAST.md
PU_MAX,"PU_MAX ( target_table, source_table.column [, filter_expression] )",PU_MAX计算给定目标表中每个元素的指定源列的最大值。,PQL/PU-Aggregation/PU_MAX.md
PU_MEDIAN,"PU_MEDIAN ( target_table, source_table.column [, filter_expression] )",PU_MEDIAN计算给定目标表中每个元素的指定源列的中值。,PQL/PU-Aggregation/PU_MEDIAN.md
PU_MIN,"PU_MIN ( target_table, source_table.column [, filter_expression] )",PU_MIN计算给定目标表中每个元素的指定源列的最小值。,PQL/PU-Aggregation/PU_MIN.md
PU_QUANTILE,"PU_QUANTILE ( target_table, source_table.column, quantile [, filter_expression] )",PU_QUANTILE计算给定目标表中每个元素的指定源列的分位数。,PQL/PU-Aggregation/PU_QUANTILE.md
PU_STDEV,"PU_STDEV ( target_table, source_table.column [, filter_expression] )",PU_STDEV计算给定目标表中每组每组样本的指定源列的标准偏差。,PQL/PU-Aggregation/PU_STDEV.md
PU_SUM,"PU_SUM ( target_table, source_table.column [, filter_expression] )",PU_SUM计算给定目标表中每个元素的指定源列的总和。,PQL/PU-Aggregation/PU_SUM.md
AVG,AVG ( table.column ),AVG此聚合函数计算一组值的平均值。,PQL/Stand-Aggregation/AVG.md
COUNT,COUNT ( table.column ),COUNT此函数用于计算每组列的行数。,PQL/Stand-Aggregation/COUNT.md
COUNT DISTINCT,COUNT ( DISTINCT table.column ),COUNT DISTINCT函数计算每组不同元素的数量。,PQL/Stand-Aggregation/COUNT_DISTINCT.md
COUNT_TABLE,COUNT_TABLE ( table ),COUNT_TABLE此聚合函数计算指定表的行数。,PQL/Stand-Aggregation/COUNT_TABLE.md
FIRST,"FIRST ( table.input_column [, ORDER BY table.column [ASC|DESC] ] )",FIRST返回组中每个元素的指定源列的第一个元素。,PQL/Stand-Aggregation/FIRST.md
GLOBAL,GLOBAL ( aggregation ),GLOBAL 为聚合计算聚合函数，将所有值聚合到一个组中。,PQL/Stand-Aggregation/GLOBAL.md
LAST,"LAST ( table.input_column [, ORDER BY table.column [ASC|DESC] ] )",LAST返回组中每个元素的指定源列的最后一个元素。,PQL/Stand-Aggregation/LAST.md
MAX,MAX ( table.column ),MAX函数计算每组的最大值。,PQL/Stand-Aggregation/MAX.md
MEDIAN,MEDIAN ( table.column ),MEDIAN函数计算每个组的中值。,PQL/Stand-Aggregation/MEDIAN.md
MIN,MIN ( table.column ),MIN函数计算每组的最小值。,PQL/Stand-Aggregation/MIN.md
PRODUCT,PRODUCT ( table.column ),PRODUCT计算INT或FLOAT列上的乘积。,PQL/Stand-Aggregation/PRODUCT.md
QUANTILE,"QUANTILE ( table.column, quantile )",QUANTILE函数计算每组的给定分位数。,PQL/Stand-Aggregation/QUANTILE.md
STDEV,STDEV ( table.column ),STDEV函数计算每组样本的标准偏差。,PQL/Stand-Aggregation/STDEV.md
STRING_AGG,"STRING_AGG ( source_table.column, delimiter [, ORDER BY source_table.column [ASC|DESC] ] )",STRING_AGG 运算符为组中的每个元素返回来自指定源列的字符串连接。,PQL/Stand-Aggregation/STRING_AGG.md
SUM,SUM ( table.column ),SUM计算INT或FLOAT列上的总和。,PQL/Stand-Aggregation/SUM.md
TOPN,"TOPN ( n, table(table.column1, table.column2), table.column1, order_expression [ASC|DESC])",TOPN按照规定的排序规则统计某一列的前N行。,PQL/Stand-Aggregation/TOPN.md
TRIMMED_MEAN,"TRIMMED_MEAN ( table.column [, lower_cutoff [, upper_cutoff ] ] )",TRIMMED_MEAN函数计算 INT 或 FLOAT 列的修剪平均值。,PQL/Stand-Aggregation/TRIMMED_MEAN.md
CASE WHEN,CASE WHEN condition THEN result_expression [ WHEN condition THEN result_expression ]* [ ELSE result_expression ] END,CASE WHEN计算条件列表并基于这些条件返回结果表达式。,PQL/Data-Flow/CASEWHEN.md
COALESCE,"COALESCE (table.column1, ..., table.columnN )",COALESCE函数返回一组值中第一个不为NULL的元素。,PQL/Data-Flow/COALESCE.md
GREATEST,"GREATEST ( table.column1 , ... , table.columnN )",GREATEST函数从一组值中返回非NULL的最大元素。,PQL/Data-Flow/GREATEST.md
LEAST,LEAST（table.column1，...，table.columnN）,LEAST 函数返回一组值中不为 NULL 的最小元素。,PQL/Data-Flow/LEAST.md
TO_FLOAT,TO_FLOAT ( table.column ),TO_FLOAT 将 STRING 输入转换为 FLOAT 输出。,PQL/Data-Type-Conversion/TO_FLOAT.md
TO_INT,TO_INT ( table.column ),TO_INT 将 STRING 输入转换为 INT 输出。,PQL/Data-Type-Conversion/TO_INT.md
TO_STRING,"TO_STRING ( table.column [, FORMAT ( format ) ] )",TO_STRING 将 INT 或 DATE 输入转换为 STRING 输出。,PQL/Data-Type-Conversion/TO_STRING.md
DATE_BETWEEN,"DATE_BETWEEN ( table.column1, table.column2, table.column3 )",DATE_BETWEEN 确定第一个日期是否介于第二个日期和第三个日期之间。,PQL/Date-Time/DateTime-Difference/DATE_BETWEEN.md
DATEDIFF,"DATEDIFF ( time_unit, table.date_column1, table.date_column2 )",DATEDIFF以指定的时间单位计算两个日期之间的差值。,PQL/Date-Time/DateTime-Difference/DATEDIFF.md
DAYS_BETWEEN,"DAYS_BETWEEN ( table.column1, table.column2 )",DAYS_BETWEEN 以天为单位计算两个日期之间的差异。,PQL/Date-Time/DateTime-Difference/DAYS_BETWEEN.md
HOURS_BETWEEN,"HOURS_BETWEEN ( start_column, end_column [, calendar_specification [, calendar_id_column]] )",HOURS_BETWEEN 以小时为单位计算两个时间戳之间的差异。,PQL/Date-Time/DateTime-Difference/HOURS_BETWEEN.md
MINUTES_BETWEEN,"MINUTES_BETWEEN ( start_column, end_column [, calendar_specification [, calendar_id_column]] )",MINUTES_BETWEEN以分钟为单位计算两个时间戳之间的差异。,PQL/Date-Time/DateTime-Difference/MINUTES_BETWEEN.md
MONTHS_BETWEEN,"MONTHS_BETWEEN ( table.column1, table.column2 )",MONTHS_BETWEEN 计算两个日期之间的月差。,PQL/Date-Time/DateTime-Difference/MONTHS_BETWEEN.md
SECONDS_BETWEEN,"SECONDS_BETWEEN ( start_column, end_column [, calendar_specification [, calendar_id_column]] )",SECONDS_BETWEEN 以秒为单位计算两个时间戳之间的差异。,PQL/Date-Time/DateTime-Difference/SECONDS_BETWEEN.md
ADD_DAYS,"ADD_DAYS ( table.base_column, table.days_column )",ADD_DAYS将给定天数添加到给定日期。,PQL/Date-Time/DateTime-Modification/ADD_DAYS.md
ADD_MONTHS,"ADD_MONTHS ( table.base_column, table.months_column )",ADD_MONTHS 将给定的月数添加到给定的日期。,PQL/Date-Time/DateTime-Modification/ADD_MONTHS.md
TO_TIMESTAMP,"TO_TIMESTAMP ( table.column, format )",TO_TIMESTAMP将字符串转换为DATE。,PQL/Date-Time/DateTime-Projection/TO_TIMESTAMP.md
ROUND_DAY,ROUND_DAY ( table.date_column ),ROUND_DAY函数通过将DATE输入的HOUR、MINUTE、SECOND和MILLISECOND值舍弃，将其舍入为一天。,PQL/Date-Time/DateTime-Rounding/ROUND_DAY.md
ROUND_MONTH,ROUND_MONTH ( table.date_column ),ROUND_MONTH函数通过将DATE输入的DAY、HOUR、MINUTE、SECOND和MILLISECOND值舍弃，将其舍入为一年。,PQL/Date-Time/DateTime-Rounding/ROUND_MONTH.md
ROUND_QUARTER,ROUND_QUARTER(column),ROUND_QUARTER将日期四舍五入到季度，即保持年份不变，将月和日设置为季度开始值,PQL/Date-Time/DateTime-Rounding/ROUND_QUARTER.md
ROUND_WEEK,ROUND_WEEK ( table.date_column ),ROUND_WEEK函数通过将DATE输入的HOUR、MINUTE、SECOND和MILLISECOND值舍弃，将其向下舍入到周。,PQL/Date-Time/DateTime-Rounding/ROUND_WEEK.md
ROUND_YEAR,ROUND_YEAR ( table.date_column ),ROUND_YEAR函数通过将DATE输入的MONTH、DAY、HOUR、MINUTE、SECOND和MILLISECOND值舍弃，将其舍入为一年。,PQL/Date-Time/DateTime-Rounding/ROUND_YEAR.md
AND,logical expression AND logical expression,left logical expression AND right logical expression 当左侧和右侧的逻辑表达式为真时，计算结果为真。 否则评估为假。,PQL/Logical/AND.md
NOT,NOT (logical expression),NOT 否定一个逻辑表达式。,PQL/Logical/NOT.md
OR,logical expression OR logical expression,left logical expression OR right logical expression当左侧或右侧逻辑表达式为真时，计算结果为真。 否则评估为假。,PQL/Logical/OR.md
ABS,ABS ( table.column ),ABS 是一个数学函数，它返回指定数值表达式的绝对（正）值。,PQL/Math/ABS.md
CEIL,CEIL ( table.column ),Ceiling 函数返回大于或等于指定数值表达式的最小整数。,PQL/Math/CEIL.md
DIV,"DIV ( table.column1 , table.column2 )",DIV除法运算符计算两个值的商。,PQL/Math/DIV.md
FLOOR,FLOOR ( table.column ),FLOOR 函数返回小于或等于指定数值表达式的最大整数。,PQL/Math/FLOOR.md
INVERSE,INVERSE ( table.column ),INVERSE 否定输入数字。,PQL/Math/INVERSE.md
LOG,"LOG ( table.column [, base] )",LOG 函数返回指定浮点表达式的对数。,PQL/Math/LOG.md
MODULO,"MODULO ( dividend, divisor )",MODULO模运算符返回两个参数相除的余数。,PQL/Math/MODULO.md
POWER,"POWER ( table.column, exponent )",POWER 函数将指定表达式的值返回到指定的幂。,PQL/Math/POWER.md
ROUND,"ROUND ( column [, precision ] )",ROUND 函数将数值四舍五入到最接近的数字。,PQL/Math/ROUND.md
SQRT,SQRT ( table.column ),SQRT 函数计算指定值的平方根。,PQL/Math/SQRT.md
SQUARE,SQUARE ( table.column ),SQUARE 函数计算每个输入值的平方。,PQL/Math/SQUARE.md
IN,"value/column IN ( value1/column1, ... )",IN 对于与匹配列表的任何值匹配的列或值的所有情况返回 true。,PQL/Predicate/IN.md
ISNULL,ISNULL ( table.column ),ISNULL 函数返回一个 INT，指示输入值是否为 NULL。,PQL/Predicate/ISNULL.md
LIKE,table.column LIKE pattern,LIKE 谓词确定右表达式字符串是否与左表达式字符串匹配。,PQL/Predicate/LIKE.md
BETWEEN,table.column BETWEEN inclusive start of interval AND inclusive end of interval,BETWEEN … AND … 对于在给定闭合区间内的列的所有情况返回 true。,PQL/Predicate/BETWEEN.md
CALC_REWORK,CALC_REWORK (),CALC_REWORK 计算每个案例的活动数量。,PQL/Process/CALC_REWORK.md
CONCAT,"CONCAT ( table.column1, ..., table.columnN )",CONCAT 和 || 返回两个或多个字符串的连接。,PQL/String-Modification/CONCAT.md
LEFT,"LEFT ( table.column, n )",LEFT 返回字符串的前 n 个字符。,PQL/String-Modification/LEFT.md
LEN,LEN ( table.column ),LEN 返回字符串的大小，即字符数。,PQL/String-Modification/LEN.md
LOWER,LOWER ( table.column ),LOWER 返回字符串的小写版本。,PQL/String-Modification/LOWER.md
PATINDEX,"PATINDEX ( table.column, pattern [, occurrence ] )",PATINDEX 搜索模式子字符串，如果找到，则在输入中返回其索引（从 1 开始）（否则为零）。,PQL/String-Modification/PATINDEX.md
REPLACE,"REPLACE ( table.column, pattern, replace )",REPLACE 返回由字符串替换替换的模式子字符串的字符串。,PQL/String-Modification/REPLACE.md
REVERSE,REVERSE ( table.column ),REVERSE 返回反转后的字符串。,PQL/String-Modification/REVERSE.md
RIGHT,"RIGHT ( table.column, n )",RIGHT 返回字符串的最后 n 个字符。 如果字符串小于 n 个字符，则返回整个字符串。,PQL/String-Modification/RIGHT.md
RTRIM,"RTRIM ( table.column [, characters ] )",RTRIM 返回删除了尾随空格的字符串。,PQL/String-Modification/RTRIM.md
STR_TO_INT,STR_TO_INT ( table.column ),STR_TO_INT将输入字符串转换为整数。,PQL/String-Modification/STR_TO_INT.md
STRING_SPLIT,"STRING_SPLIT ( table.column, pattern, split-index )",STRING_SPLIT 根据模式将字符串拆分为子字符串，并返回第 split-index-th 拆分。,PQL/String-Modification/STRING_SPLIT.md
SUBSTRING,"SUBSTRING ( table.column, start, size )",SUBSTRING 返回从索引从零开始的字符开始的大小大小的子字符串。,PQL/String-Modification/SUBSTRING.md
UPPER,UPPER ( table.column ),UPPER 返回字符串的大写版本。,PQL/String-Modification/UPPER.md
LTRIM,"LTRIM ( table.column [, characters ])",LTRIM 返回删除了前导空格的字符串。,PQL/String-Modification/LTRIM.md
MOVING_AVG,"MOVING_AVG ( table.column, start, end )",MOVING_AVG函数计算每个窗口的平均值。,PQL/Window-Aggregation/MOVING_AVG.md
MOVING_COUNT,"MOVING_COUNT ( table.column, start, end )",MOVING_COUNT函数计算每个窗口的值。,PQL/Window-Aggregation/MOVING_COUNT.md
MOVING_MAX,"MOVING_MAX ( table.column, start, end )",MOVING_COUNT计算每个窗口的最大值。,PQL/Window-Aggregation/MOVING_MAX.md
MOVING_MEDIAN,"MOVING_MEDIAN ( table.column, start, end )",MOVING_MEDIAN函数计算每个窗口的中值。,PQL/Window-Aggregation/MOVING_MEDIAN.md
MOVING_MIN,"MOVING_MIN ( table.column, start, end )",MOVING_MIN计算每个窗口的最小值。,PQL/Window-Aggregation/MOVING_MIN.md
MOVING_STDEV,"MOVING_STDEV ( table.column, start, end )",MOVING_STDEV函数计算每个窗口的标准偏差。,PQL/Window-Aggregation/MOVING_STDEV.md
MOVING_SUM,"MOVING_SUM ( table.column, start, end )",MOVING_SUM函数计算每个窗口的标准偏差。,PQL/Window-Aggregation/MOVING_SUM.md
MOVING_VAR,"MOVING_VAR ( table.column, start, end )",MOVING_VAR计算每个窗口的方差。,PQL/Window-Aggregation/MOVING_VAR.md
RUNNING_TOTAL,RUNNING_TOTAL ( table.column ),RUNNING_TOTAL 对给定列的所有条目求和并返回所有中间总和。,PQL/Window-Aggregation/RUNNING_TOTAL.md
DOMAIN_TABLE,"DOMAIN_TABLE ( table.column, ... )",DOMAIN_TABLE 函数可用于从各种列创建临时表，该表可用作所有 PU 函数内的目标表。,PQL/PU-Aggregation/DOMAIN_TABLE.md
PU_STRING_AGG,"PU_STRING_AGG ( target_table, source_table.column, delimiter [, filter_expression ] [, ORDER BY source_table.column [ASC|DESC] ] )",PU_STRING_AGG 运算符为给定目标表中的每个元素返回来自指定源列的字符串串联。,PQL/PU-Aggregation/PU_STRING_AGG.md
VAR,VAR ( table.column ),VAR 计算每组的方差。 方差可以应用于 INT 或 FLOAT 列。,PQL/Stand-Aggregation/VAR.md
REMAP_VALUES,REMAP_VALUES,REMAP_VALUES 允许您映射类型为 STRING 的列的值。,PQL/Data-Flow/REMAP_VALUES.md
INTERSECT,"INTERSECT ( lhs_calendar, rhs_calendar )",对 INTERSECT 的调用将两个任意日历作为输入并生成一个新的相交日历（其类型取决于输入日历）。,PQL/Date-Time/DateTime-Calendars/INTERSECT.md
WEEKDAY_CALENDAR,WEEKDAY_CALENDAR ( day [ day_time ] ... ),WEEKDAY_CALENDAR 使用给定的配置生成一个新的工作日日历。,PQL/Date-Time/DateTime-Calendars/WEEKDAY_CALENDAR.md
WORKDAY_CALENDAR,"WORKDAY_CALENDAR ( TFACS_table, TFACS_entry_identifier )",WORKDAY_CALENDAR 使用传递给它的 TFACS 表生成一个新的工作日日历（即 SAP TFACS 样式的日历或有时称为 SAP 工厂日历）。,PQL/Date-Time/DateTime-Calendars/WORKDAY_CALENDAR.md
HOUR_NOW,HOUR_NOW ( [time_zone_id] ),HOUR_NOW 函数返回指定时区中应用服务器当前时间的小时数。,PQL/Date-Time/DateTime-Difference/HOUR_NOW.md
MINUTE_NOW,MINUTE_NOW ( [time_zone_id] ),MINUTE_NOW函数返回指定时区中应用服务器当前时间的分钟数。,PQL/Date-Time/DateTime-Projection/MINUTE_NOW.md
ADD_MINUTES,"ADD_MINUTES ( start_column, minutes_column )",ADD_MINUTES 将给定的分钟数添加到给定的时间戳。,PQL/Date-Time/DateTime-Modification/ADD_MINUTES.md
ADD_SECONDS,"ADD_SECONDS ( start_column, seconds_column )",ADD_SECONDS 将给定秒数添加到给定时间戳。,PQL/Date-Time/DateTime-Modification/ADD_SECONDS.md
ADD_YEARS,"ADD_YEARS ( table.base_column, table.years_column )",ADD_YEARS 将给定的年数添加到给定的日期。,PQL/Date-Time/DateTime-Modification/ADD_YEARS.md
ADD_HOURS,"ADD_HOURS ( start_column, hours_column )",ADD_HOURS 将给定的小时数添加到给定的时间戳。,(PQL/Date-Time/DateTime-Modification/ADD_HOURS.md
ADD_MILLIS,"ADD_MILLIS ( start_column, milliseconds_column )",ADD_MILLIS 将给定的毫秒数添加到给定的时间戳。,PQL/Date-Time/DateTime-Modification/ADD_MILLIS.md
CALENDAR_WEEK,CALENDAR_WEEK ( table.column ),CALENDAR_WEEK 返回给定日期的日历周。,PQL/Date-Time/DateTime-Projection/CALENDAR_WEEK.md
DAY,DAY( table.column ),DAY返回给定日期的日期。,PQL/Date-Time/DateTime-Projection/DAY.md
DAY_OF_WEEK,DAY_OF_WEEK ( table.column ),"DAY_OF_WEEK
以数字形式返回星期几。",PQL/Date-Time/DateTime-Projection/DAY_OF_WEEK.md
MONTH,MONTH ( table.column ),MONTH返回给定日期的月份。,PQL/Date-Time/DateTime-Projection/MONTH.md
QUARTER,QUARTER ( table.column ),QUARTER返回给定日期的季度。,PQL/Date-Time/DateTime-Projection/QUARTER.md
YEAR,YEAR ( table.column ),YEAR返回给定日期的年份。,PQL/Date-Time/DateTime-Projection/YEAR.md
ROUND_MINUTE,ROUND_MINUTE ( table.date_column ),ROUND_MINUTE函数通过将其 SECOND 和 MILLISECOND 值设置为其基态，将 DATE 输入舍入到分钟。,PQL/Date-Time/DateTime-Rounding/ROUND_MINUTE.md
ROUND_SECOND,ROUND_SECOND ( table.date_column ),ROUND_SECOND函数通过将其 MILLISECOND 值设置为其基态，将 DATE 输入向下舍入到秒。,PQL/Date-Time/DateTime-Rounding/ROUND_SECOND.md
MATCH_ACTIVITIES,"MATCH_ACTIVITIES([ activity_table.string_column, ] [STARTING activity_list ] [, ] [NODE activity_list ] [, ] [NODE_ANY activity_list ] [, ] [ENDING activity_list ] [, ] [EXCLUDING activity_list ] [, ] [EXCLUDING_ALL activity_list ])",MATCH_ACTIVITIES 标记具有某些活动的案例，而不考虑活动的确切顺序。,PQL/Process/MATCH_ACTIVITIES.md
SOURCE - TARGET,"SOURCE ( activity_table.column [, activity_table.filter_column ] [, edge_configuration [ WITH START( [ start_value ] ) ] ] )",SOURCE和TARGET函数提供了一种将活动表中两个不同行的值合并到同一行的方法，例如，用于计算案例中连续事件之间的吞吐量时间。,PQL/Process/SOURCE-TARGET.md
DELETE_CHARACTERS,"DELETE_CHARACTERS ( table.column, match_string )",DELETE_CHARACTERS 返回一个字符串，其中删除了匹配字符串中定义的字符。,PQL/String-Modification/DELETE_CHARACTERS.md
MAP_CHARACTERS,"MAP_CHARACTERS ( table.column, match_string, replace_string )",MAP_CHARACTERS 返回一个字符串，其中匹配字符串中定义的字符替换为相应的字符。,PQL/String-Modification/MAP_CHARACTERS.md
STRINGHASH,STRINGHASH ( table.column ),STRINGHASH 计算字符串的加密散列，使用 base64 编码进行编码。 返回的哈希值将来可能会发生变化。,PQL/String-Modification/STRINGHASH.md
LAG,"LAG ( column [, ORDER BY ( sort_column [sorting], ... )] [, PARTITION BY ( partition_column, ... )] [, offset ] )",LAG 返回当前行之前偏移行数的行。 可以指定基于列的排序和分区。,PQL/Window/LAG.md
LEAD,"LEAD ( table.column [, ORDER BY ( order_column [sorting], ... )] [, PARTITION BY ( partition_column, ... )] [, offset ] )",LEAD 返回当前行之后偏移行数的行。 可以指定基于列的排序和分区。,PQL/Window/LEAD.md
WINDOW_AVG,"RUNNING_SUM ( column [, ORDER BY ( sort_column [sorting], ... )] [, PARTITION BY ( partition_column, ... )] )","WINDOW_AVG ( column, start, end [, ORDER BY ( sort_column [sorting], ... )] [, PARTITION BY ( partition_column, ... )] )",PQL/Window/WINDOW_AVG.md
ACTIVITY_LAG,"ACTIVITY_LAG ( activity_table.column [, offset ] )",ACTIVITY_LAG 返回活动表中在当前行之前的行偏移个案中的行数。,PQL/Process/ACTIVITY_LAG.md
ACTIVITY_LEAD,"ACTIVITY_LEAD ( activity_table.column [, offset ] )",ACTIVITY_LEAD 返回活动表中当前行之后的行（个案中的偏移行数）。,PQL/Process/ACTIVITY_LEAD.md
CALC_CROP,"CALC_CROP ( begin_range_specifier TO end_range_specifier, activity_table.string_column )",CALC_CROP该运算符将案例裁剪为一系列活动。,PQL/Process/CALC_CROP.md
CALC_CROP_TO_NULL,"CALC_CROP_TO_NULL ( begin_range_specifier TO end_range_specifier, activity_table.string_column )",CALC_CROP_TO_NULL该运算符将案例裁剪为一系列活动。,PQL/Process/CALC_CROP_TO_NULL.md
ACTIVITY_COLUMN,ACTIVITY_COLUMN (),ACTIVITY_COLUMN 允许在不使用确切列名的情况下引用数据模型中活动表的活动列。,PQL/Process/ACTIVITY_COLUMN.md
CALC_THROUGHPUT,"CALC_THROUGHPUT ( begin_range_specifier TO end_range_specifier, timestamps [, activity_table.string_column ] )",CALC_THROUGHPUT吞吐量用于计算每种情况下两个活动之间的时间。,PQL/Process/CALC_THROUGHPUT.md
SHORTENED,"SHORTENED ( PROCESS_VARIANT ( activity_table.column ) [, max_cycle_length ] )",PROCESS_VARIANT将案例的所有事件聚合到一个字符串中，该字符串表示案例的变体。SHORTENED可用于将自循环缩短到规定的出现次数。,PQL/Process/PROCESS_VARIANT.md
ROUND_HOUR,ROUND_HOUR ( table.date_column ),此函数通过将 DATE 输入的 MINUTE、SECOND 和 MILLISECOND 值设置为其基态，将 DATE 输入向下舍入为小时。,PQL/Date-Time/DateTime-Rounding/ROUND_HOUR.md
IS NULL,input IS NULL,＜input＞当输入为NULL时，IS NULL的计算结果为true，否则为false。,PQL/Predicate/IS_NULL.md
PROCESS_VARIANT,PROCESS_VARIANT ( activity_table.string_column ),PROCESS_VARIANT 将案例的所有活动聚合成一个字符串，该字符串表示流程的一个变体。,PQL/Process/PROCESS_VARIANT.md
CONFORMANCE,"CONFORMANCE ( activity_table.string_column, [ places ], [ transitions ], [ edges ], [ mapping ], [ start_places ], [ end_places ] )",CONFORMANCE 标记活动是否符合给定模型。,PQL/Process/CONFORMANCE.md
PROCESS EQUALS,PROCESS [ ON activity_table.string_column ] [ NOT ] equals [ start ] activity ( to activity )* [ end ],PROCESS EQUALS 根据简单表达式匹配流程的变体。,PQL/Process/PROCESS_EQUALS.md
PU_PRODUCT,"PU_PRODUCT ( target_table, source_table.column [, filter_expression] )",PU_PRODUCT计算给定目标表中每个元素的指定源列的乘积。,/PQL/PU-Aggregation/PU_PRODUCT.md
MILLIS_BETWEEN,"MILLIS_BETWEEN ( start_column, end_column [, calendar_specification [, calendar_id_column]] )",MILLIS_BETWEEN 计算两个时间戳之间的差异（以毫秒为单位）。,PQL/Date-Time/DateTime-Difference/MILLIS_BETWEEN.md
REMAP_TIMESTAMPS,"REMAP_TIMESTAMPS ( table.column, time_unit [, calendar_specification [, calendar_id_column ] ] )",REMAP_TIMESTAMPS 函数计算自纪元年 (1970-01-01 00:00:00.000) 以来给定日期经过的时间单位数。,PQL/Date-Time/DateTime-Projection/REMAP_TIMESTAMPS.md
INDEX_ACTIVITY_LOOP,INDEX_ACTIVITY_LOOP ( activity_table.column ),INDEX_ACTIVITY_LOOP 运算符返回每种情况下每个活动直接连续发生的次数。,PQL/Process/Process-Index/INDEX_ACTIVITY_LOOP.md
INDEX_ACTIVITY_LOOP_REVERSE,INDEX_ACTIVITY_LOOP_REVERSE ( activity_table.column ),INDEX_ACTIVITY_LOOP_REVERSE 运算符以相反的顺序返回每种情况下每个活动直接连续发生的次数。,PQL/Process/Process-Index/INDEX_ACTIVITY_LOOP_REVERSE.md
INDEX_ACTIVITY_ORDER,INDEX_ACTIVITY_ORDER ( activity_table.column ),INDEX_ACTIVITY_ORDER 返回案例中每个活动的位置。,PQL/Process/Process-Index/INDEX_ACTIVITY_ORDER.md
INDEX_ACTIVITY_ORDER_REVERSE,INDEX_ACTIVITY_ORDER_REVERSE ( activity_table.column ),INDEX_ACTIVITY_ORDER_REVERSE 返回案例中每个活动的相反位置。,PQL/Process/Process-Index/INDEX_ACTIVITY_ORDER_REVERSE.md
INDEX_ACTIVITY_TYPE,INDEX_ACTIVITY_TYPE ( activity_table.column ),INDEX_ACTIVITY_TYPE 运算符针对每种情况下的每个活动返回该活动在流程中的给定点已发生的次数。,PQL/Process/Process-Index/INDEX_ACTIVITY_TYPE.md
INDEX_ACTIVITY_TYPE_REVERSE,INDEX_ACTIVITY_TYPE_REVERSE ( activity_table.column ),INDEX_ACTIVITY_TYPE_REVERSE 运算符针对每种情况下的每个活动返回在流程中的给定点该活动已经以相反顺序发生了多少次。,PQL/Process/Process-Index/INDEX_ACTIVITY_TYPE_REVERSE.md
INDEX_ORDER,"INDEX_ORDER ( column [, ORDER BY ( sort_column [sorting], ... )] [, PARTITION BY ( partition_column, ... )] )",INDEX_ORDER返回一个具有整数索引的列，从1开始。索引指示行的顺序。,PQL/Window/INDEX_ORDER.md
TO_DATE,"TO_DATE ( table.column, FORMAT ( format ) )",TO_DATE 将 STRING 输入转换为 DATE 输出。,PQL/Data-Type-Conversion/TO_DATE.md
TODAY,TODAY(),TODAY函数以UTC格式返回应用服务器的当前日期。,PQL/Date-Time/DateTime-Projection/TODAY.md
YEARS_BETWEEN,"YEARS_BETWEEN ( table.column1, table.column2 )",YEARS_BETWEEN 计算两个日期之间的年差。,PQL/Date-Time/DateTime-Difference/YEARS_BETWEEN.md
DAYS_IN_MONTH,DAYS_IN_MONTH( table.column ),DAYS_IN_MONTH返回给定时间戳的给定月份中的天数。,PQL/Date-Time/DateTime-Projection/DAYS_IN_MONTH.md
HOURS,HOURS ( table.column ),HOUR返回给定日期的小时。,PQL/Date-Time/DateTime-Projection/HOURS.md
MILLIS,MILLIS ( table.column ),MILLIS返回给定日期的毫秒数。,PQL/Date-Time/DateTime-Projection/MILLIS.md
MINUTES,MINUTES ( table.column ),MINUTES返回给定日期的分钟数。,PQL/Date-Time/DateTime-Projection/MINUTES.md
SECONDS,SECONDS ( table.column ),SECONDS返回给定日期的秒数。,PQL/Date-Time/DateTime-Projection/SECONDS.md
ACTIVITY_TABLE,ACTIVITY_TABLE(),ACTIVITY_TABLE 允许在不使用其确切表名的情况下引用数据模型中的活动表。,PQL/Process/ACTIVITY_TABLE.md
CASE_TABLE,CASE_TABLE(),CASE_TABLE允许引用数据模型中的事例表，而不使用其确切的表名。,PQL/Process/CASE_TABLE.md
CASE_ID_COLUMN,CASE_ID_COLUMN(),允许在数据模型中引用活动表的CASEID列，而无需使用确切的列名称。,	PQL/Process/CASE_ID_COLUMN.md
TIMESTAMP_COLUMN,TIMESTAMP_COLUMN(),TIMESTAMP_COLUMN 允许在数据模型中引用活动表的时间戳列，而无需使用确切的列名称。,PQL/Process/TIMESTAMP_COLUMN.md
USER_NAME,USER_NAME(),返回当前用户的 celonis 用户名。,PQL/Custom/USER_NAME.md
IN_CALENDAR,"IN_CALENDAR ( timestamp_column, calendar_specification [, calendar_id_column])",IN_CALENDAR 检查给定日期是否在日历内。,PQL/Date-Time/DateTime-Projection/IN_CALENDAR.md
WORKDAYS_BETWEEN,"WORKDAYS_BETWEEN ( start_date, end_date, calendar_specification [, calendar_id_column])",WORKDAYS_BETWEEN 函数确定两个给定日期之间的工作日数量。,PQL/Date-Time/DateTime-Difference/WORKDAYS_BETWEEN.md
CALC_CASETIME,"CALC_CASETIME ( begin_range_specifier TO end_range_specifier, time_unit)",吞吐量用于计算每种情况下两个活动之间的时间。 可以通过范围说明符配置计算的吞吐量时间应该从哪个活动开始以及应该在哪个活动结束。 结果列被临时添加到案例表中，并返回每个案例的两个指定活动之间的吞吐量时间。该函数主要是以秒为单位换算的，所以误差较小。,PQL/Process/CALC_CASETIME.md
