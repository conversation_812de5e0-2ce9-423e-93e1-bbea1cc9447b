spring:
    # flyway
  flyway:
    enabled: true
    # sql编码
    encoding: utf-8
    # 版本文件路径
    locations: classpath:ddl/mysql/migration
    validate-on-migrate: true
    # 是否以当前数据为基线
    baselineOnMigrate: true
    # 当前数据为基线的时候版本号
    baselineVersion: 1.0.0
    # 是否允许执行比当前版本号低的版本sql
    outOfOrder: true
  application:
    name: pm-project
  messages:
    basename: i18n/messages
    encoding: utf8
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

  h2:
    console:
      enabled=true:
  datasource:
    dynamic:
      # 设置默认的数据源或数据源组，默认值即为master
      primary: engine
      # 严格匹配数据源，默认false，true未匹配到指定数据源时抛异常，false使用默认数据源
      strict: false
      datasource:
        oauth2:
          username: root
          password: 123456
          url: ******************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        engine:
          username: root
          password: 123456
          url: *******************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        execution:
          username: root
          password: 123456
          url: *********************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        meta:
          username: root
          password: 123456
          url: *************************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
  servlet:
    multipart:
      enabled: true
      max-file-size: 2000MB
      max-request-size: 2000MB


server:
  port: 8888
  sleuth:
    log:
      slf4j:
        enabled: true
    sampler:
      # 抽样率，默认0.1
      probability: 1.0



oauth2.license.path: /data/sp/file/license
logging:
  level:
    com.sp.spengine.mapper: debug
    root: info
    org.springframework.web.servlet.DispatcherServlet: DEBUG
    org.springframework.cloud.sleuth: DEBUG

csv.file.save.path: ${java.io.tmpdir}/upload/
csv.child.file.save.path: ${java.io.tmpdir}/upload//child/
del.file.path: ${java.io.tmpdir}
business.file.path: ${java.io.tmpdir}/export/
# csv.file.save.path: /data/sp/file/csv/

spring.cache.enabled: false
spark.store.path: ${java.io.tmpdir}/warehouse/
spark.metastore.jdbc.url: *************************************************************************************************************************************************************************
spark.metastore.jdbc.user:  root
spark.metastore.jdbc.password:  123456
spark.master: yarn-client


redis.env: dev

spring.redis.host: *************
spring.redis.port: 30389
#连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active: 50
#连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait: 3000
#连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle: 20
#连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle: 2
#连接超时时间（毫秒）
spring.redis.timeout: 5000

security:
  oauth2:
    client:
      client-id: client
      client-secret: secret
      access-token-uri: http://localhost:8888/oauth/token
      user-authorization-uri: http://localhost:8888/oauth/authorize
    resource:
      token-info-uri: http://localhost:8888/oauth/check_token
mybatis-plus:
  mapper-locations: classpath:mapper/**.xml

spark.conf: "{\"spark.sql.pql.planCacheEnabled\":\"false\", \"spark.sql.pql.preBucketNumberRows\": \"2500000\", \"spark.sql.files.openCostInBytes\":\"512M\",\"spark.sql.files.maxPartitionBytes\":\"512M\",\"spark.sql.pql.encodedColDict.encodeWithModelBuild\":\"true\", \"spark.master\":\"local[8]\"}"

spark.sql.columnar.enabled: true




############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效 (三个月)
  timeout: 7776000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结 (7天)
  active-timeout: 4233600
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 每次清理过期数据间隔的时间 （单位: 秒） ，默认值30秒，设置为-1代表不启动定时清理
  data-refresh-period: 3600
  enable-mysql-memory: true


ai:
  server:
    url: "localhost:12345"


prx:
  tableCache:
    cacheAllModelEnabled: true


#默认密码
prx.admin.local.initPassword: proxverse
#快速开始的csv文件默认发票文件
prx.dataModel.file.quickStartDefaultFile: proxverse_demo
# 上传文件的限制大小默认1GB
prx.dataModel.file.maxUploadSize: 1g
#快速开始的限制大小默认50MB
prx.dataModel.file.quickStartMaxUploadSize: 50m
#流程图显示的最大event默认500
prx.workbench.processTree.maxQueryEvents: 500
#流程图显示的最大connect默认500
prx.workbench.processTree.maxQueryConnects: 500
#日志保存的默认天数默认14天
prx.gateway.log.keepDays: 14
#echarts查询top数量
prx.workbench.component.echartsQueryTopCount: 1000
prx.admin.license.enabled: false

prx.pql.queryCache.enabled: false
prx.model.build.bucketGroupEnabled: true

spark.sql.starry.expressions.rewriteCountDistinctAsBitmap: true

prx.model.build.bucketGroupFailBack: true

prx.login.sso.tokenUrl: http://localhost:8888/decision/zsdl/token/validate
prx.login.loginToken: token
