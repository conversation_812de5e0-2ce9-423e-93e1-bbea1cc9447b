# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# UI templates initialized file includes the default template when the SkyWalking OAP starts up at the first time.
#
# Also, SkyWalking would detect the existing templates in the database, once they are missing, all templates in this file
# could be added automatically.

templates:
  - name: "APM"
    # The type includes DASHBOARD, TOPOLOGY_INSTANCE, TOPOLOGY_ENDPOINT.
    # DASHBOARD type templates could have multiple definitions, by using different names.
    # TOPOLOGY_INSTANCE, TOPOLOGY_ENDPOINT type templates should be defined once, as they are used in the topology page only.
    type: "DASHBOARD"
    # Configuration could be defined through UI, and use `export` to format in the standard JSON.
    configuration: |-
      [
        {
          "name": "APM",
          "type": "service",
          "children": [
            {
              "name": "Global",
              "children": [
                {
                  "width": 3,
                  "title": "Services Load",
                  "height": "300",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_cpm",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": false,
                  "unit": "CPM - calls per minute"
                },
                {
                  "width": 3,
                  "title": "Slow Services",
                  "height": "300",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_resp_time",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": false,
                  "unit": "ms"
                },
                {
                  "width": 3,
                  "title": "Un-Health Services (Apdex)",
                  "height": "300",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_apdex",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": false,
                  "aggregation": "/",
                  "aggregationNum": "10000",
                  "sortOrder": "ASC"
                },
                {
                  "width": 3,
                  "title": "Slow Endpoints",
                  "height": "300",
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "endpoint_avg",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": false,
                  "unit": "ms"
                },
                {
                  "width": "6",
                  "title": "Global Response Latency",
                  "height": "280",
                  "entityType": "All",
                  "independentSelector": false,
                  "metricType": "LABELED_VALUE",
                  "metricName": "all_percentile",
                  "queryMetricType": "readLabeledMetricsValues",
                  "chartType": "ChartLine",
                  "metricLabels": "P50, P75, P90, P95, P99",
                  "labelsIndex": "0, 1, 2, 3, 4",
                  "unit": "percentile in ms"
                },
                {
                  "width": "6",
                  "title": "Global Heatmap",
                  "height": "280",
                  "entityType": "All",
                  "independentSelector": false,
                  "metricType": "HEATMAP",
                  "unit": "ms",
                  "queryMetricType": "readHeatMap",
                  "chartType": "ChartHeatmap",
                  "metricName": "all_heatmap"
                }
              ]
            },
            {
              "name": "Service",
              "children": [
                {
                  "width": 3,
                  "title": "Service Apdex",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_apdex",
                  "queryMetricType": "readMetricsValue",
                  "chartType": "ChartNum",
                  "aggregation": "/",
                  "aggregationNum": "10000"
                },
                {
                  "width": 3,
                  "title": "Service Avg Response Time",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_resp_time",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "ms"
                },
                {
                  "width": 3,
                  "title": "Successful Rate",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_sla",
                  "queryMetricType": "readMetricsValue",
                  "chartType": "ChartNum",
                  "unit": "%",
                  "aggregation": "/",
                  "aggregationNum": "100"
                },
                {
                  "width": 3,
                  "title": "Service Load",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_cpm",
                  "queryMetricType": "readMetricsValue",
                  "chartType": "ChartNum",
                  "unit": "CPM - calls per minute"
                },
                {
                  "width": 3,
                  "title": "Service Apdex",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_apdex",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "aggregation": "/",
                  "aggregationNum": "10000"
                },
                {
                  "width": 3,
                  "title": "Service Response Time Percentile",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "LABELED_VALUE",
                  "metricName": "service_percentile",
                  "queryMetricType": "readLabeledMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "ms",
                  "metricLabels": "P50, P75, P90, P95, P99",
                  "labelsIndex": "0, 1, 2, 3, 4"
                },
                {
                  "width": 3,
                  "title": "Successful Rate",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_sla",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "%",
                  "aggregation": "/",
                  "aggregationNum": "100"
                },
                {
                  "width": 3,
                  "title": "Service Load",
                  "height": "200",
                  "entityType": "Service",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_cpm",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "CPM - calls per minute"
                },
                {
                  "width": "4",
                  "title": "Service Instances Load",
                  "height": "280",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_instance_cpm",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": true,
                  "unit": "CPM - calls per minute"
                },
                {
                  "width": "4",
                  "title": "Slow Service Instance",
                  "height": "280",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_instance_resp_time",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": true,
                  "unit": "ms"
                },
                {
                  "width": "4",
                  "title": "Service Instance Successful Rate",
                  "height": "280",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_instance_sla",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": true,
                  "unit": "%",
                  "aggregation": "/",
                  "aggregationNum": "100",
                  "sortOrder": "ASC"
                }
              ]
            },
            {
              "name": "Instance",
              "children": [
                {
                  "width": "4",
                  "title": "Service Instance Load",
                  "height": "150",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_instance_cpm",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "CPM - calls per minute"
                },
                {
                  "width": "4",
                  "title": "Service Instance Successful Rate",
                  "height": "150",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_instance_sla",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "%",
                  "aggregation": "/",
                  "aggregationNum": "100"
                },
                {
                  "width": "4",
                  "title": "Service Instance Latency",
                  "height": "150",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "service_instance_resp_time",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "ms"
                },
                {
                  "width": 3,
                  "title": "JVM CPU (Java Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "instance_jvm_cpu",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "%",
                  "aggregation": "+",
                  "aggregationNum": ""
                },
                {
                  "width": 3,
                  "title": "JVM Memory (Java Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "instance_jvm_memory_heap, instance_jvm_memory_heap_max,instance_jvm_memory_noheap, instance_jvm_memory_noheap_max",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "MB",
                  "aggregation": "/",
                  "aggregationNum": "1048576"
                },
                {
                  "width": 3,
                  "title": "JVM GC Time",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "instance_jvm_young_gc_time, instance_jvm_old_gc_time",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "ms"
                },
                {
                  "width": 3,
                  "title": "JVM GC Count",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartBar",
                  "metricName": "instance_jvm_young_gc_count, instance_jvm_old_gc_count"
                },
                {
                  "width": 3,
                  "title": "JVM Thread Count (Java Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "metricName": "instance_jvm_thread_live_count, instance_jvm_thread_daemon_count, instance_jvm_thread_peak_count"
                },
                {
                  "width": 3,
                  "title": "CLR CPU  (.NET Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "instance_clr_cpu",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "%"
                },
                {
                  "width": 3,
                  "title": "CLR GC (.NET Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "instance_clr_gen0_collect_count, instance_clr_gen1_collect_count, instance_clr_gen2_collect_count",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartBar"
                },
                {
                  "width": 3,
                  "title": "CLR Heap Memory (.NET Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "instance_clr_heap_memory",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "MB",
                  "aggregation": "/",
                  "aggregationNum": "1048576"
                },
                {
                  "width": 3,
                  "title": "CLR Thread (.NET Service)",
                  "height": "250",
                  "entityType": "ServiceInstance",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "metricName": "instance_clr_available_completion_port_threads,instance_clr_available_worker_threads,instance_clr_max_completion_port_threads,instance_clr_max_worker_threads"
                }
              ]
            },
            {
              "name": "Endpoint",
              "children": [
                {
                  "width": "4",
                  "title": "Endpoint Load in Current Service",
                  "height": "280",
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "endpoint_cpm",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "parentService": true,
                  "unit": "CPM - calls per minute"
                },
                {
                  "width": "4",
                  "title": "Slow Endpoints in Current Service",
                  "height": "280",
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "metricName": "endpoint_avg",
                  "unit": "ms",
                  "parentService": true
                },
                {
                  "width": "4",
                  "title": "Successful Rate in Current Service",
                  "height": "280",
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "endpoint_sla",
                  "queryMetricType": "sortMetrics",
                  "chartType": "ChartSlow",
                  "aggregation": "/",
                  "aggregationNum": "100",
                  "parentService": true,
                  "unit": "%",
                  "sortOrder": "ASC"
                },
                {
                  "width": 3,
                  "title": "Endpoint Load",
                  "height": 350,
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "endpoint_cpm",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine"
                },
                {
                  "width": 3,
                  "title": "Endpoint Avg Response Time",
                  "height": 350,
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "endpoint_avg",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "ms"
                },
                {
                  "width": 3,
                  "title": "Endpoint Response Time Percentile",
                  "height": 350,
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "LABELED_VALUE",
                  "metricName": "endpoint_percentile",
                  "queryMetricType": "readLabeledMetricsValues",
                  "chartType": "ChartLine",
                  "metricLabels": "P50, P75, P90, P95, P99",
                  "labelsIndex": "0, 1, 2, 3, 4",
                  "unit": "ms"
                },
                {
                  "width": 3,
                  "title": "Endpoint Successful Rate",
                  "height": 350,
                  "entityType": "Endpoint",
                  "independentSelector": false,
                  "metricType": "REGULAR_VALUE",
                  "metricName": "endpoint_sla",
                  "queryMetricType": "readMetricsValues",
                  "chartType": "ChartLine",
                  "unit": "%",
                  "aggregation": "/",
                  "aggregationNum": "100"
                }
              ]
            }
          ]
        }
      ]
    # Activated as the DASHBOARD type, makes this templates added into the UI page automatically.
    # False means providing a basic template, user needs to add it manually.
    activated: true
    # True means wouldn't show up on the dashboard. Only keeps the definition in the storage.
    disabled: false