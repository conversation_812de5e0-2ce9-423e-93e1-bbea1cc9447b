/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// CLR instance metrics
instance_clr_cpu = from(ServiceInstanceCLRCPU.usePercent).doubleAvg();
instance_clr_gen0_collect_count = from(ServiceInstanceCLRGC.gen0CollectCount).sum();
instance_clr_gen1_collect_count = from(ServiceInstanceCLRGC.gen1CollectCount).sum();
instance_clr_gen2_collect_count = from(ServiceInstanceCLRGC.gen2CollectCount).sum();
instance_clr_heap_memory = from(ServiceInstanceCLRGC.heapMemory).longAvg();
instance_clr_available_completion_port_threads = from(ServiceInstanceCLRThread.availableCompletionPortThreads).max();
instance_clr_available_worker_threads = from(ServiceInstanceCLRThread.availableWorkerThreads).max();
instance_clr_max_completion_port_threads = from(ServiceInstanceCLRThread.maxCompletionPortThreads).max();
instance_clr_max_worker_threads = from(ServiceInstanceCLRThread.maxWorkerThreads).max();