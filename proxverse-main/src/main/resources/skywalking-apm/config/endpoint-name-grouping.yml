# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Endpoint name grouping rules.
# In most cased, endpoint name should be detected by agents or service mesh automatically, and aggregate the metrics based
# on the name.
# But, in some cases, application puts the parameter in the endpoint name, such as putting order id in the URI, like
# /prod/ORDER123, /prod/ORDER456.
# This grouping file provides the regex based definition capability to merge those endpoints into a group by better and
# more meaningful aggregation metrics.

#grouping:
#  # Endpoint of the service would follow the following rules
#  - service-name: serviceA
#    rules:
#      - endpoint-name: /prod/{id}
#        regex: \/prod\/.+