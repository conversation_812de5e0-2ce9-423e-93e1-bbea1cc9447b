<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.TopicSheetMapper">

    <select id="getTopicSheetFileBySheetId" parameterType="integer" resultType="com.sp.proxverse.common.model.dto.DataModelFileDTO">
        select

        mf.file_id fileId,
        bt.id topicId
        from t_topic_sheet ts
        left join t_business_topic bt
        on ts.topic_id = bt.id
        left join t_business_topic_data td
        on bt.id = td.topic_id
        left join t_data_model_file mf
        on td.model_id = mf.data_model_id
        where ts.id = #{sheetId}
        and ts.deleted = 0
        and td.data_source_type=1
        limit 1
    </select>

    <select id="getTopicSheetFileBySheetIdList" resultType="com.sp.proxverse.common.model.dto.SheetFileDTO">
        select

        mf.file_id fileId,
        ts.id sheetId

        from t_topic_sheet ts
        left join t_business_topic bt
        on ts.topic_id = bt.id
        left join t_business_topic_data td
        on bt.id = td.topic_id
        left join t_data_model_file mf
        on td.model_id = mf.data_model_id
        where  ts.deleted = 0
        and td.data_source_type=1
        <if test="ids!=null and ids.size>0">
            AND ts.id IN
            <foreach collection="ids" index="index" item="c" open="(" separator="," close=")">
                #{c}
            </foreach>
        </if>
    </select>

</mapper>