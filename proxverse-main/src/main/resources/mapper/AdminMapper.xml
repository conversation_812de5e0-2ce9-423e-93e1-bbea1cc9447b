<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.AdminMapper">


    <select id="getAdmin" resultType="com.sp.proxverse.common.model.dto.domain.Admin">
        select *
        from t_admin
        where user_name = #{userName}
          and deleted = 0 limit 1
    </select>

    <select id="getSapAdminList" resultType="com.sp.proxverse.common.model.dto.domain.Admin">
        select id ,user_name userName, name,user_source userSource
        from t_admin
        where deleted = 0
    </select>

    <select id="getSapAdminAllList" resultType="com.sp.proxverse.common.model.dto.domain.Admin">
        select id ,user_name userName, name,user_source userSource
        from t_admin
    </select>

    <select id="getAdminById" resultType="com.sp.proxverse.common.model.dto.domain.Admin">
        select *
        from t_admin
        where id = #{userId}
          and deleted = 0 limit 1
    </select>

    <select id="getAdminSso" resultType="com.sp.proxverse.common.model.dto.domain.Admin">
        select *
        from t_admin
        where user_name = #{userName}
          and deleted = 0
          and sso_id is not null limit 1
    </select>

    <select id="getAdminCount" resultType="java.lang.Integer">
        select count(*)
        from t_admin
        where `status` = 1
          and deleted = 0
    </select>

    <select id="getAdminIdList" resultType="java.lang.Integer">
        select id
        from t_admin
        where `status` = 1
          and deleted = 0
        order by id limit #{limit}
    </select>

    <select id="getAdminIdListAll" resultType="java.lang.Integer">
        select id
        from t_admin
        where `level` != 'SUPER' or `level` is null
    </select>



</mapper>
