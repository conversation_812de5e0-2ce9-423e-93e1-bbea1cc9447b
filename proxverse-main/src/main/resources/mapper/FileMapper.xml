<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.FileMapper">
    <select id="getList" resultType="com.sp.proxverse.common.model.po.FilePO">
        select parquet_length parquetLength
        from t_file
        where deleted = 0 AND type IS NOT NULL AND type &lt; '40'
    </select>

    <select id="getList4Tenant" resultType="com.sp.proxverse.common.model.po.FilePO">
        select parquet_length parquetLength
        from t_file
        where deleted = 0 and tenant_id = #{tenantId} AND type IS NOT NULL AND type &lt; '40'
    </select>
</mapper>