<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.CompanyPropertyMapper">

    <select id="getCompanyProperty" resultType="com.sp.proxverse.common.model.po.CompanyPropertyPo">
        select * from  t_company_property
        where `tenant_id` = #{companyId} AND `property_key` = #{propertyKey}   AND `deleted` = 0
    </select>

    <update id="removeCompanyProperty" parameterType="Boolean">
        update `t_company_property` set `deleted`= 1
        where `tenant_id` = #{companyId} AND `property_key` = #{propertyKey}
    </update>
</mapper>