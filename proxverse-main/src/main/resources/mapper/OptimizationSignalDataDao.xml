<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationSignalDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationSignalDataPO" id="optimizationSignalDataMap">
        <result property="id" column="Id"/>
        <result property="optimizationTaskId" column="optimization_task_id"/>
        <result property="signalId" column="signal_id"/>
        <result property="dataModelId" column="data_model_id"/>
        <result property="tableId" column="table_id"/>
        <result property="columnId" column="column_id"/>
        <result property="value" column="value"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


    <insert id="save4TenantId" parameterType="com.sp.proxverse.common.model.po.OptimizationSignalDataPO">
        insert  into t_optimization_signal_data(optimization_task_id,signal_id,column_id,row_id,value,creater_id,create_time,deleted,tenant_id)
        values (#{optimizationTaskId},#{signalId},#{columnId},#{rowId},#{value},#{createrId},#{createTime},0,#{tenantId})
    </insert>

</mapper>