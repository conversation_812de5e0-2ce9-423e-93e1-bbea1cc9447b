<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationTaskDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationTaskPO" id="optimizationTaskMap">
        <result property="id" column="Id"/>
        <result property="status" column="status"/>
        <result property="optimizationSignalId" column="optimization_signal_id"/>
        <result property="assignedUserId" column="assigned_user_id"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


    <insert id="save4TenantId" parameterType="com.sp.proxverse.common.model.po.OptimizationTaskPO">
        insert  into t_optimization_task(status,optimization_object_id,optimization_signal_id,assigned_user_id,creater_id,create_time,update_time,deleted,tenant_id)
        values (#{status},#{optimizationObjectId},#{optimizationSignalId},#{assignedUserId},#{createrId},#{createTime},#{updateTime},0,#{tenantId})
    </insert>

</mapper>