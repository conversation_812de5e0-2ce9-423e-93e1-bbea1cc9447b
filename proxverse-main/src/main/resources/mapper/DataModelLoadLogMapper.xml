<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.DataModelLoadLogMapper">


    <select id="getDataModeUpdateTime" resultType="com.sp.proxverse.common.model.po.DataModelLoadLogPO">
        select dm.id as dataModelId, MAX(tdmll.end_time) as endTime
        from t_data_model dm
                 JOIN t_data_model_load_log tdmll on tdmll.data_model_id = dm.id
        where dm.pool_id =#{poolId} and dm.deleted = 0
        GROUP BY dm.id;

    </select>



</mapper>