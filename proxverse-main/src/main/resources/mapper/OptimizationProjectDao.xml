<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationProjectDao">

    <select id="getAllColumnIds" resultType="integer">
        SELECT
            sc.`column_id`
        FROM
            `t_optimization_project` AS p
                LEFT JOIN `t_optimization_object` AS o
                          ON p.`Id` = o.`optimization_project_id`
                              AND p.`deleted` = 0
                              AND o.`deleted` = 0
                LEFT JOIN `t_optimization_signal` AS s
                          ON o.`Id` = s.`optimization_object_id`
                              AND s.`deleted` = 0
                LEFT JOIN `t_optimization_signal_data_model_table_column` AS sc
                          ON s.`Id` = sc.`optimization_signal_id`
                              AND sc.`deleted` = 0
        WHERE sc.`column_id` IS NOT NULL and p.tenant_id=#{tenantId}
    </select>

    <select id="getTenantIdByProjectId" resultType="integer">
        select tenant_id from t_optimization_project where id=#{projectId}
    </select>

	<!-- 可根据自己的需求，是否要使用 -->
    <select id="getAllOptimizedProjects" resultMap="projectMap">
        SELECT `id`,`name` from t_optimization_project  where `deleted` = 0 ORDER BY create_time DESC
    </select>

    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationProjectBO" id="projectMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <collection property="objectList" ofType="com.sp.proxverse.common.model.bo.OptimizationObjectBO" select="getObjectsByProjectId" column="id" />
    </resultMap>

    <select id="getObjectsByProjectId" resultMap="objectMap" >
        SELECT `id`,`name` from t_optimization_object  where `deleted` = 0 AND `optimization_project_id` = #{id} ORDER BY create_time DESC
    </select>

    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationObjectBO" id="objectMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <association property="signal" column="id" select="signalById"/>
        <association property="rules" column="id" select="rulesById"/>
    </resultMap>

    <select id="signalById" resultMap="signalMap">
        SELECT `id` from  t_optimization_signal where `deleted` = 0 and optimization_object_id = #{id} ORDER BY create_time DESC
    </select>


    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationSignalBO" id="signalMap">
        <result property="id" column="id"/>
        <collection property="tableColumnList" ofType="com.sp.proxverse.common.model.bo.SignalDataModelTableColumnBO" select="getColumnBysignalId" column="id" />
    </resultMap>

    <select id="getColumnBysignalId" resultMap="signalTableColumnMap">
        SELECT `id`,`column_id`,`data_model_id` from  t_optimization_signal_data_model_table_column where `deleted` = 0 and optimization_signal_id = #{id} ORDER BY create_time DESC
    </select>

    <resultMap type="com.sp.proxverse.common.model.bo.SignalDataModelTableColumnBO" id="signalTableColumnMap">
        <result property="id" column="id"/>
        <result property="dataModelId" column="data_model_id"/>
        <result property="columnId" column="column_id"/>
    </resultMap>

    <select id="rulesById" resultMap="rulesMap">
        SELECT `id`,`type` from  t_optimization_execution_rules where `deleted` = 0 and optimization_object_id = #{id} ORDER BY create_time DESC
    </select>


    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationExecutionRulesBO" id="rulesMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
    </resultMap>








</mapper>