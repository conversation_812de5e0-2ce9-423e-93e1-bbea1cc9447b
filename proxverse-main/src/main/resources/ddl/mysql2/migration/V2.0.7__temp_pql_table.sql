
CREATE TABLE `t_temp_pql_table`
(
  `id`              int(11) NOT NULL AUTO_INCREMENT,
  `topic_id`        int(11) NOT NULL,
  `name`            varchar(256) NOT NULL,
  `table_def`       text NOT NULL,
  `deleted`         boolean not null default 0,
  `created_at`      timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at`      timestamp DEFAULT CURRENT_TIMESTAMP,
  `tenant_id`       int DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

