use sp_process;

CREATE TABLE `t_topic_multi_process` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `topic_id` int(11) NOT NULL,
                                         `process_name` varchar(255),
                                         `event_name_column` varchar(255),
                                         `start_time_column` varchar(255),
                                         `end_time_column` varchar(255),
                                         `event_index` int COMMENT '事件列下标（如第一个事件列，则为 1）',
                                         `deleted` int COMMENT '0：未删除，1：已删除',
                                         `create_time`     datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
                                         `update_time`     datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                         `tenant_id`       int(11)      NULL DEFAULT NULL COMMENT '租户ID',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='topic多流程图';