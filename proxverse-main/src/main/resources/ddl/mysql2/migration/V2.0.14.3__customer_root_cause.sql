CREATE TABLE t_customer_root_cause
(
  `id`              int(11)      NOT NULL AUTO_INCREMENT,
  `topic_sheet_id`  int(11)      NULL DEFAULT NULL,
  `deleted`         int(11)      NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
  `create_time`     datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time`     datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  `expression_name` varchar(255) NOT NULL,
  `expression`      text         NOT NULL,
  `tenant_id`       int(11)      NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
);
