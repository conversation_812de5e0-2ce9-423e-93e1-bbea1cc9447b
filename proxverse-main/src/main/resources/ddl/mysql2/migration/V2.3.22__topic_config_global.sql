use sp_process;

CREATE TABLE `t_topic_config_global` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `topic_id` int(11) NOT NULL,
                                         `description` varchar(255),
                                         `event_column` varchar(255),
                                         `format` varchar(64),
                                         `formatting` varchar(64),
                                         `deleted` int(11) COMMENT '0：未删除，1：已删除',
                                         `create_time`     datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
                                         `update_time`     datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                         `tenant_id`       int(11)      NULL DEFAULT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='topic全局过滤设置';