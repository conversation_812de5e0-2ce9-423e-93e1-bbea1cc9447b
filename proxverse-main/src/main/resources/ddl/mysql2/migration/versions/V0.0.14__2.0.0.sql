use sp_process;

alter table t_kpi ADD COLUMN formatting varchar(64) null;
alter table t_kpi ADD COLUMN format varchar(64) null;
alter table t_kpi ADD COLUMN column_type varchar(64) null;

alter table t_topic_filter ADD COLUMN formatting varchar(64) null;
alter table t_topic_filter ADD COLUMN format varchar(64) null;

alter table t_signal_column ADD COLUMN formatting varchar(64) null;
alter table t_signal_column ADD COLUMN format varchar(64) null;
alter table t_signal_condition ADD COLUMN formatting varchar(64) null;
alter table t_signal_condition ADD COLUMN format varchar(64) null;


alter table t_simulation
    add data_model_id int null;



alter table t_simulation_event_attr
    add run_cost_expression varchar(4000) null;

alter table t_simulation_event_attr
    add run_cost_formatting varchar(500) null;

alter table t_simulation_event_attr
    add run_cost_format varchar(500) null;


alter table t_simulation_event_attr
    add run_time_expression varchar(4000) null;

alter table t_simulation_event_attr
    add run_time_formatting varchar(500) null;

alter table t_simulation_event_attr
    add run_time_format varchar(500) null;


alter table t_simulation_run_frequency
    add expression varchar(5000) null;

alter table t_simulation_run_frequency
    add formatting varchar(5000) null;

alter table t_simulation_run_frequency
    add format varchar(5000) null;


alter table t_simulation_resource
    add number_expression varchar(2000) null;

alter table t_simulation_resource
    add number_formatting varchar(500) null;

alter table t_simulation_resource
    add number_format varchar(500) null;


alter table t_simulation_resource
    add resource_cost_expression varchar(2000) null;

alter table t_simulation_resource
    add resource_cost_formatting varchar(500) null;

alter table t_simulation_resource
    add resource_cost_format varchar(500) null;

