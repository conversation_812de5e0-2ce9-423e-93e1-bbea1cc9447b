use sp_process;

CREATE TABLE `t_newsheet_template` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `sheet_name` varchar(255) DEFAULT NULL,
  `info` longtext ,
  `introduction` text,
  `deleted` tinyint DEFAULT '0',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE `t_newsheet_template_parameter` (
  `id` int NOT NULL AUTO_INCREMENT,
  `template_id` int DEFAULT NULL,
  `origin_refer` text  DEFAULT NULL,
  `origin_refer_expression` text  DEFAULT NULL,
  `origin_expression` text  DEFAULT NULL,
  `parameter` varchar(255) DEFAULT NULL,
  `introduction` varchar(500) DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `type` tinyint DEFAULT '1',
  `kpi_param` varchar(255) DEFAULT NULL,
  `column_type` varchar(255) DEFAULT NULL,
  `format` varchar(255) DEFAULT NULL,
  `formatting` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

use sp_oauth2;
INSERT INTO `sp_oauth2`.`t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`, `i18n_no`) VALUES (66, '发布模板', 1, '/sp-engine/saveNewSheetTemplate', 2006, NULL, NULL, 1, '2023-09-28 14:13:41', 0, 1, 4, 0, 0, null,'300510');
INSERT INTO `sp_oauth2`.`t_role_permission`( `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES ( 1, 66, '2023-09-28 14:20:31', 1, 0, 1);
