
-- auto-generated definition
create table t_sub_process_manage
(
    id                        int auto_increment
        primary key,
    element_id                varchar(255)                         null,
    process_manage_id         int        default 0                 null comment '当前流程管理Id',
    sub_process_manage_id int        default 0                 null comment '绑定的流程管理Id',
    create_time               datetime   default CURRENT_TIMESTAMP null,
    tenant_id                 int                                  null
);


use sp_oauth2;
alter table t_user_group
    modify sso_id varchar(5000) null;



