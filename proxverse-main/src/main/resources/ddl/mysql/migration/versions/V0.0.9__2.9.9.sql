use sp_process;
CREATE TABLE `t_topic_config_color` (
  `id` int NOT NULL AUTO_INCREMENT,
  `topic_id` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` tinyint DEFAULT '0',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `color_template` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

ALTER TABLE t_topic_config ADD COLUMN `active_color_template` varchar(128) DEFAULT NULL;



use sp_oauth2;
ALTER TABLE t_user_group_detail ADD INDEX user_group_id (user_group_id);