use sp_process;
ALTER TABLE t_topic_filter MODIFY COLUMN expression longtext DEFAULT NULL;
ALTER TABLE t_topic_filter ADD COLUMN description VARCHAR(1000) DEFAULT NULL;

use sp_process;
create table t_audit_log
(
    id          int auto_increment
        primary key,
    path        varchar(256)                       null comment '接口地址',
    user_name   varchar(256)                       null comment '用户名称',
    user_id     int                                null comment '用户Id',
    ip          varchar(64)                        null comment 'IP地址',
    create_time datetime default CURRENT_TIMESTAMP null,
    tenant_id   int                                null comment '租户ID',
    parameter   varchar(5000)                      null comment '请求参数'
)
    comment '用户请求路径';



use sp_oauth2;

INSERT INTO `sp_oauth2`.`t_admin`( `user_name`, `password`, `level`, `deleted`, `user_source`, `create_time`, `update_time`, `status`, `tenant_id`, `sso_id`, `authorization_code`, `authorization_date`, `role_expire`) VALUES ( 'ROOT@1', 'cc7a301a615349f08524e5ef15f663e8', 'SUPER', 0, 'LOCAL', '2023-11-02 15:40:45', '2023-11-02 15:41:52', 1, 1, NULL, NULL, NULL, 0);

ALTER TABLE t_permission ADD COLUMN `i18n_no` varchar(64) DEFAULT NULL;

UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300400 WHERE `id` = 1;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300401 WHERE `id` = 2;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300402 WHERE `id` = 3;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300500 WHERE `id` = 4;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300501 WHERE `id` = 5;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300502 WHERE `id` = 7;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300503 WHERE `id` = 8;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300504 WHERE `id` = 9;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300505 WHERE `id` = 12;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300506 WHERE `id` = 15;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300700 WHERE `id` = 18;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300600 WHERE `id` = 24;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300601 WHERE `id` = 25;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300602 WHERE `id` = 26;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300603 WHERE `id` = 27;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300604 WHERE `id` = 28;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300605 WHERE `id` = 29;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300800 WHERE `id` = 33;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300801 WHERE `id` = 34;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300802 WHERE `id` = 35;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300803 WHERE `id` = 37;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300507 WHERE `id` = 62;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300804 WHERE `id` = 63;
UPDATE `sp_oauth2`.`t_permission` SET  `i18n_no` = 300508 WHERE `id` = 64;

use sp_oauth2;
alter table t_admin
    add name varchar(32) null comment '姓名';

alter table t_admin
    drop key t_admin_user_name_uindex;


use sp_process;

alter table t_process_manage
    drop column responsible_person_id;

alter table t_process_manage
    add column responsible_person_ids varchar(5000) null after description;
