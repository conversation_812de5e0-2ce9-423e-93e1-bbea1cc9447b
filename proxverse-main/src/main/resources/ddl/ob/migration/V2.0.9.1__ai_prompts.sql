create table t_ai_prompts
(
  id            int auto_increment
    primary key,
  prompts_type  varchar(128)                       null,
  prompts_value text                               null,
  model_config  text                               null,
  deleted       tinyint  default 0                 null comment '0：未删除，1：已删除',
  create_time   datetime default CURRENT_TIMESTAMP null,
  update_time   datetime                           null on update CURRENT_TIMESTAMP,
  tenant_id     int                                null,
  is_template   tinyint(1)                         null
)
  ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (1, 'dot-chart', '请你根据我提供的点状图数据，总结出该点状图中数据的特点，字数不要超过100字。然后分析出该点状图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。
\'\'\'
数据:{0}
\'\'\'


{0}:data2.json

', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (2, 'olap-chart', '请你根据我提供的OLAP表数据，总结出该OLAP表中数据的特点，字数不要超过200字。然后分析出该OLAP表中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。
,,,
数据:{0}
\'\'\'


{0}:data3.json

', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (3, 'pie-chart', '请你根据我提供的饼图数据，总结出该饼图中数据的特点，字数不要超过100字。然后分析出该饼图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。
\'\'\'
数据:{0}
\'\'\'


{0}:data2.json

', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (4, 'bar-chart', '请你根据我提供的柱状图数据，总结出该柱状图中数据的特点，字数不要超过100字。然后分析出该柱状图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。
\'\'\'
数据:{0}
\'\'\'


{0}:data2.json

', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (5, 'line-chart ', '请你根据我提供的折线图数据，总结出该折线图中数据的特点，字数不要超过100字。然后分析出该折线图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定文字尽量简洁精炼。
\'\'\'
数据:{0}
\'\'\'


{0}:data2.json

', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (6, 'process-chart', '请你根据我提供的流程路径信息，提炼出该流程路径的特点，字数不要超过100字。然后分析出该流程路径中的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。
\'\'\'
流程节点:{0}
\'\'\'


{0}:node1.json
', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (7, 'path-chart', '请你根据我提供的流程节点和流程图信息，提炼出该流程路径的特点，可以参考我提供的统计结论样式，也可以自由发挥，字数不要超过100字。然后分析出该流程路径中的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定，但是请遵守输出格式，文字尽量简洁精炼。
\'\'\'
流程节点:{1}
---
流程图:{0}
---
统计结论:{2}
---
输出格式:{3}
\'\'\'





{0}:data.json
{1}:node.json
{2}:statistic_model
{3}:output_format1
', null, 0, '2024-07-22 17:57:29', '2024-07-23 10:34:33', 0, 1);
INSERT INTO t_ai_prompts (id, prompts_type, prompts_value, model_config, deleted, create_time, update_time, tenant_id, is_template) VALUES (8, 'calc-time-chart', '请你根据我提供的配置信息和数据信息，提炼出该配置下流程吞吐时间的特点，字数不要超过100字。然后分析出该流程吞吐时间的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。
\'\'\'
配置信息:{0}
---
数据信息:{1}
\'\'\'


{0}:componentConfig.json
{1}:data1.json
', null, 0, '2024-07-22 17:58:16', '2024-07-23 10:34:33', 0, 1);

