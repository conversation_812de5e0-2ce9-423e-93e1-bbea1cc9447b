use sp_oauth2;

create table t_company_property
(
    id          int auto_increment
        primary key,
    tenant_id   int                                null,
    property_key        varchar(128)                       null,
    property_value       varchar(5000)                      null,
    deleted     tinyint  default 0                 null comment '0：未删除，1：已删除',
    create_time datetime default CURRENT_TIMESTAMP null,
    update_time datetime                           null on update CURRENT_TIMESTAMP
)
    comment '公司配置';

ALTER TABLE t_allow_process add COLUMN reason_type TINYINT DEFAULT 1;

ALTER TABLE t_allow_process add COLUMN condition_name VARCHAR(255) DEFAULT NULL;

alter table t_data_model_load_log
    add version_number int null;

alter table t_data_model
    add max_version int  default 0 null comment '最大版本号';

alter table t_data_model
    add current_version int  default 0 null comment '当前版本号';
