
CREATE TABLE `t_user_group_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_group_id` int DEFAULT NULL,
  `permission` blob,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `create_user` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_group_id` (`user_group_id`)
);




alter table t_file
    add parquet_length bigint unsigned null comment 'parquet占用空间大小';

create table t_sap_log
(
    id          int auto_increment
        primary key,
    info   varchar(5000)                      null comment '信息',
    run_sql   varchar(5000)                      null comment 'sql',
    load_type   varchar(64)                        null comment '加载类型',
    create_time datetime default CURRENT_TIMESTAMP null,
    tenant_id   int                                null comment '租户ID'
);
