FROM *************:5000/base/starry:8
ADD proxverse-project-exec.jar proxverse-project-exec.jar
ENV LANG C.UTF-8
ADD libs libs
RUN echo "Asia/shanghai" > /etc/timezone
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
ADD spark /opt/spark
ADD run.sh /run.sh
ENV SPARK_HOME /opt/spark
ENV JAVA_HOME=/jdk8
RUN mv /opt/spark/jars /opt/spark/ext_jars
RUN cp -r -p /velox/libs /opt/spark/jars
RUN apt-get install zip -y
RUN unzip proxverse-project-exec.jar
RUN cp -p BOOT-INF/lib/proxverse-* /opt/spark/jars
RUN cp -p BOOT-INF/lib/backend-* /opt/spark/jars
RUN rm -r BOOT-INF
ENTRYPOINT ["/jdk8/bin/java","-Dnacos.logging.config=logback-proxverse.xml", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005","-noverify" ,"-Djdk.tls.client.protocols=TLSv1.2" ,"-Dlogging.config=classpath:logback-proxverse.xml","-Dfile.encoding=UTF-8", "-XX:ErrorFile=/data/hs_err_pid%p.log", "-Djava.ext.dirs=/jdk8/jre/lib/ext:libs","-XX:+UseG1GC" ,"-XX:ParallelGCThreads=20", "-XX:G1HeapRegionSize=4m", "-XX:ConcGCThreads=10", "-Xmx16384M","-jar","proxverse-project-exec.jar","--spring.profiles.active=${ACTIVE_PROFILE}"]
