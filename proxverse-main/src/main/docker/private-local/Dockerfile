FROM 192.168.1.121:5000/base/prx-vector-engine:1
ADD proxverse-project-exec.jar proxverse-project-exec.jar
ENV LANG C.UTF-8
ADD libs libs


ENTRYPOINT ["/jdk8/bin/java","-Dnacos.logging.config=logback-proxverse.xml" , "-Dlogging.config=classpath:logback-proxverse.xml","-Djava.ext.dirs=/jdk8/jre/lib/ext:libs","-Xmx${JMX}M","-Xms${JMS}M","-jar","proxverse-project-exec.jar","--spring.profiles.active=private","--spring.config.location=/data/proxverse/conf/bootstrap-private.yaml", "-classpath", "/data/proxverse/conf"]
