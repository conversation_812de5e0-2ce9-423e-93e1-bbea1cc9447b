package com.sp.web.main;

import java.util.*;

class Solution {
  public ArrayList<ArrayList<Integer>> levelOrder(TreeNode root) {

    collectTreeNode(root, 0);
    return new ArrayList<>();
  }

  Map<Integer, ArrayList<Integer>> list = new HashMap<>(10);

  public void collectTreeNode(TreeNode root, int dep) {
    if (root == null) {
      return;
    }
    if (!list.containsKey(dep)) {
      list.put(dep, new ArrayList<>());
    }
    list.get(dep).add(root.val);
    collectTreeNode(root.left, dep + 1);
    collectTreeNode(root.right, dep + 1);
  }

  static class TreeNode {
    int val;
    TreeNode left;
    TreeNode right;

    TreeNode() {}

    public TreeNode(int val) {
      this.val = val;
    }

    TreeNode(int val, TreeNode left, TreeNode right) {
      this.val = val;
      this.left = left;
      this.right = right;
    }
  }

  //    1
  // 2    3
  // 7 6  5  4
  public static void main(String[] args) {

    TreeNode node1 = new TreeNode(1);
    TreeNode node2 = new TreeNode(2);
    TreeNode node3 = new TreeNode(3);
    TreeNode node4 = new TreeNode(4);
    TreeNode node5 = new TreeNode(5);
    TreeNode node6 = new TreeNode(6);
    TreeNode node7 = new TreeNode(7);
    node1.left = node2;
    node1.right = node3;
    node3.right = node4;
    node3.left = node5;
    node2.right = node6;
    node2.left = node7;
    new Solution().levelOrder(node1);
  }
}
