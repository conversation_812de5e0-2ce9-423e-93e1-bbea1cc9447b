package com.sp.web.main;

import org.apache.catalina.webresources.TomcatURLStreamHandlerFactory;
import org.apache.spark.SparkConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@MapperScan(
    basePackages = {
      "com.sp.proxverse.common.mapper",
      "com.sp.proxverse.interfaces.dao",
      "com.sp.proxverse.oauth2",
      "com.sp.proxverse.mapper"
    })
@ComponentScan(
    basePackages = {
      "com.sp.proxverse",
      "com.sp.proxverse.oauth2.service",
      "com.sp.proxverse.process",
      "com.sp.proxverse.datamerge.service",
      "com.sp.proxverse.oauth2.service",
      "com.prx"
    })
@EnableTransactionManagement
@EnableScheduling
@EnableWebSecurity
public class MainApplication {
  private static final Logger logger = LoggerFactory.getLogger(MainApplication.class);

  public static void main(String[] args) {
    try {
      // make sure TomcatURLStreamHandlerFactory is registered first
      // otherwise Spark will register the factory and Tomcat will fail
      TomcatURLStreamHandlerFactory.register();
    } catch (Exception e) {
      logger.info("run error:{}", e.getMessage());
    }
    Thread thread = new Thread(() -> SparkConfiguration.initSparkWithArgs(args));
    thread.setDaemon(true);
    thread.start();
    SpringApplication.run(MainApplication.class, args);
  }
}
