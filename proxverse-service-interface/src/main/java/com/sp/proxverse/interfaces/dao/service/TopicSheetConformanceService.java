package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.dto.SheetConformance.SheetConformanceDto;
import com.sp.proxverse.common.model.po.TopicSheetConformancePO;
import java.util.List;

public interface TopicSheetConformanceService extends IService<TopicSheetConformancePO> {

  List<SheetConformanceDto> getSheetConformanceByTopicSheetId(Integer topicSheetId);
}
