package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.TopicSheetKpiMapper;
import com.sp.proxverse.common.model.dto.SheetKpiDTO;
import com.sp.proxverse.common.model.po.TopicSheetKpiPO;
import com.sp.proxverse.interfaces.dao.service.TopicSheetKpiService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class TopicSheetKpiServiceImpl extends ServiceImpl<TopicSheetKpiMapper, TopicSheetKpiPO>
    implements TopicSheetKpiService {
  @Override
  public List<SheetKpiDTO> getTopicSheetKpiList(Integer topicSheetId) {
    return baseMapper.getTopicSheetKpiList(topicSheetId);
  }
}
