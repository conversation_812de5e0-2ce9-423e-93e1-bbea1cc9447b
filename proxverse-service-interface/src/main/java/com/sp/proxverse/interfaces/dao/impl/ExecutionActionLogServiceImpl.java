package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.ExecutionActionLogMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.ExecutionActionLogPO;
import com.sp.proxverse.interfaces.dao.service.ExecutionActionLogService;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class ExecutionActionLogServiceImpl
    extends ServiceImpl<ExecutionActionLogMapper, ExecutionActionLogPO>
    implements ExecutionActionLogService {
  @Override
  public List<ExecutionActionLogPO> getList() {
    return this.list(
        new LambdaQueryWrapper<ExecutionActionLogPO>()
            .eq(ExecutionActionLogPO::getDeleted, DeletedEnum.NO_DELETED));
  }

  @Override
  public List<ExecutionActionLogPO> getListBySignalRuleIds(List<Integer> signalRuleIds) {
    if (CollectionUtils.isEmpty(signalRuleIds)) {
      return new ArrayList<>();
    }
    return this.list(
        new LambdaQueryWrapper<ExecutionActionLogPO>()
            .in(ExecutionActionLogPO::getSignalRuleId, signalRuleIds)
            .eq(ExecutionActionLogPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<ExecutionActionLogPO> getList(Integer opmitizationId) {
    return this.list(
        new LambdaQueryWrapper<ExecutionActionLogPO>()
            .eq(ExecutionActionLogPO::getOpmitizationId, opmitizationId)
            .eq(ExecutionActionLogPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
