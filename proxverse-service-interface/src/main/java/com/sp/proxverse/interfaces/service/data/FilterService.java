package com.sp.proxverse.interfaces.service.data;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.common.LocalParameter;
import com.sp.proxverse.common.model.dict.TopicFilterTypeEnum;
import com.sp.proxverse.common.model.oauth2.UserDetailsToken;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.interfaces.dao.service.IUserDataAuthorityService;
import com.sp.proxverse.interfaces.dao.service.TopicFilterService;
import com.sp.proxverse.interfaces.service.data.pql.TopicFilterManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FilterService {

  @Autowired private IUserDataAuthorityService userDataAuthorityService;

  @Autowired private TopicFilterService topicFilterService;

  @Autowired private UserInfoUtil userInfoUtil;

  @Autowired TopicFilterManager topicFilterManager;

  public static Map<String, String> userTopicFilterCache = new ConcurrentHashMap<>();

  private final LoadingCache<Integer, String> sheetFilterCache =
      CacheBuilder.newBuilder()
          .maximumSize(100)
          .expireAfterAccess(6, TimeUnit.HOURS)
          .build(
              new CacheLoader<Integer, String>() {
                @Override
                public String load(@NotNull Integer sheetId) throws Exception {
                  return doGetSheetFilter(sheetId);
                }
              });

  public void invalidate(Integer sheetId) {
    sheetFilterCache.invalidate(sheetId);
  }

  public List<TopicFilterPO> getTopicInitFilter(Integer topicId) {
    TopicFilterPO filterInit = topicFilterService.getInitFilter(topicId);

    List<TopicFilterPO> initList = new ArrayList<>();
    if (filterInit != null) {
      filterInit.setType(TopicFilterTypeEnum.EXPRESSION.getValue());
      initList.add(filterInit);
    }

    this.getRestrictionFilterForCurrentUser(topicId)
        .ifPresent(
            filter -> {
              TopicFilterPO userFilter =
                  TopicFilterPO.builder()
                      .topicId(topicId)
                      .type(TopicFilterTypeEnum.EXPRESSION.getValue())
                      .expression(filter)
                      .build();
              initList.add(userFilter);
            });

    return initList;
  }

  public Optional<String> getRestrictionFilterForCurrentUser(Integer topicId) {
    UserDetailsToken userInfo = userInfoUtil.getUserInfo();
    String cacheKey = userInfo.getUserId() + "-" + topicId;
    String cache = userTopicFilterCache.get(cacheKey);
    if (StringUtils.isBlank(cache)) {
      cache = userDataAuthorityService.getUserTopicFilter(userInfo.getUserId(), topicId);
      if (cache == null) {
        cache = "";
      } else {
        userTopicFilterCache.put(cacheKey, cache);
      }
    }
    return StringUtils.isBlank(cache) ? Optional.empty() : Optional.of(cache);
  }

  public Optional<String> getSheetFilter(Integer sheetId) {
    if (sheetId == null) {
      return Optional.empty();
    }
    Boolean disableFilter = LocalParameter.disableFilterThreadLocal.get();
    if (Objects.equals(true, disableFilter)) {
      return Optional.empty();
    }

    String filter = sheetFilterCache.getUnchecked(sheetId);
    return StringUtils.isBlank(filter) ? Optional.empty() : Optional.of(filter);
  }

  public String doGetSheetFilter(Integer sheetId) {
    if (sheetId == null) {
      return "";
    }
    List<TopicFilterPO> sheetFilterList =
        topicFilterService.getSheetFilterBySheetIdList(Lists.newArrayList(sheetId));
    if (CollectionUtils.isNotEmpty(sheetFilterList)) {
      return sheetFilterList.get(0).getExpression();
    }
    return "";
  }

  public void remove(Integer userId, Integer topicId) {
    String key = userId + "-" + topicId;
    userTopicFilterCache.remove(key);
  }
}
