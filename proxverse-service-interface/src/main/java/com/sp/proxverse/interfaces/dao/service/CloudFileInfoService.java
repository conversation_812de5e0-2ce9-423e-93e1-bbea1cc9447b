package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.CloudFileInfoPO;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface CloudFileInfoService extends IService<CloudFileInfoPO> {

  String getCodeBySheetId(Integer sheetId);

  CloudFileInfoPO getBySheetId(Integer sheetId);

  CloudFileInfoPO getFileInfo(Integer fileId);
}
