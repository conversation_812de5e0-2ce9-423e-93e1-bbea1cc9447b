package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.SimulationRunFrequencyMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.SimulationRunFrequencyPo;
import com.sp.proxverse.interfaces.dao.service.ISimulationRunFrequencyService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 流程仿真运行频率 服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Primary
@Service
@DS("sp_process")
public class SimulationRunFrequencyServiceImpl
    extends ServiceImpl<SimulationRunFrequencyMapper, SimulationRunFrequencyPo>
    implements ISimulationRunFrequencyService {

  public List<SimulationRunFrequencyPo> listByProgrammeId(Integer programmeId) {
    List<SimulationRunFrequencyPo> list =
        this.list(
            new LambdaQueryWrapper<SimulationRunFrequencyPo>()
                .eq(SimulationRunFrequencyPo::getProgrammeId, programmeId)
                .eq(SimulationRunFrequencyPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(SimulationRunFrequencyPo::getId));
    return list;
  }

  @Override
  public List<SimulationRunFrequencyPo> getList(Integer simulationId) {
    return baseMapper.selectList(
        new LambdaQueryWrapper<SimulationRunFrequencyPo>()
            .eq(SimulationRunFrequencyPo::getSimulationId, simulationId)
            .eq(SimulationRunFrequencyPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
