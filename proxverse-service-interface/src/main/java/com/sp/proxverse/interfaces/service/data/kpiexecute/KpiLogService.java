package com.sp.proxverse.interfaces.service.data.kpiexecute;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.KpiLogPO;
import com.sp.proxverse.common.model.vo.kpi.response.KpiStatusResponse;
import java.util.List;

/**
 * kpi日志 kpi日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 11:03:15
 */
public interface KpiLogService extends IService<KpiLogPO> {

  List<KpiStatusResponse> getKpiStatusById(Integer kpiRelationId);

  Integer deleteKpiStatusById(Integer id);
}
