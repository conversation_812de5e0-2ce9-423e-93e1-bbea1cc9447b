package com.sp.proxverse.interfaces.common.common;

import com.google.common.collect.Maps;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.interfaces.service.oauth2.Oauth2ApiService;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.SelectAllResVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UserInfoPageResVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UserInfoResVo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2022-05-19 5:28 下午
 */
@Data
@Component
public class CommonBean {

  @Autowired Oauth2ApiService oauth2ApiService;

  /** key 动作流Id key runCode */
  public Map<Integer, Map<Integer, Object>> actionRunResult;

  /**
   * 获取用户
   *
   * @param userIds
   * @return
   */
  public Map<Integer, String> getUserName(List<Integer> userIds) {
    SelectAllResVo selectAllResVo = new SelectAllResVo();
    selectAllResVo.setUserIds(userIds);

    Response<UserInfoPageResVo> userInfoPageResVoResponse =
        oauth2ApiService.selectUserInfoAll(selectAllResVo);

    UserInfoPageResVo data = userInfoPageResVoResponse.getData();
    Map<Integer, String> userInfosMap = new HashMap<>();
    if (data.getList() != null) {
      List<UserInfoResVo> userInfos = data.getList();
      for (UserInfoResVo userInfo : userInfos) {
        userInfosMap.put(userInfo.getUserId(), userInfo.getUserName());
      }
    }
    return userInfosMap;
  }

  public CommonBean() {
    this.actionRunResult = Maps.newConcurrentMap();
  }

  /**
   * 获取动作结果
   *
   * @param actionFlowId
   * @param runCode
   * @return
   */
  public Object getActionResult(Integer actionFlowId, Integer runCode) {
    if (!this.actionRunResult.containsKey(actionFlowId)) {
      return null;
    }
    if (!this.actionRunResult.get(actionFlowId).containsKey(runCode)) {
      return null;
    }
    return this.actionRunResult.get(actionFlowId).get(runCode);
  }

  /**
   * 移除动作结果
   *
   * @param actionFlowId
   * @param runCode
   * @return
   */
  public Object removeActionResult(Integer actionFlowId, Integer runCode) {
    if (this.actionRunResult.containsKey(actionFlowId)) {
      if (this.actionRunResult.get(actionFlowId).containsKey(runCode)) {
        return this.actionRunResult.get(actionFlowId).remove(runCode);
      }
    }
    return null;
  }

  /**
   * 添加动作结果
   *
   * @param actionFlowId
   * @param runCode
   * @return
   */
  public void addActionResult(Integer actionFlowId, Integer runCode, Object result) {
    if (!this.actionRunResult.containsKey(actionFlowId)) {
      this.actionRunResult.put(actionFlowId, new ConcurrentHashMap<>());
    }
    this.actionRunResult.get(actionFlowId).put(runCode, result);
    return;
  }
}
