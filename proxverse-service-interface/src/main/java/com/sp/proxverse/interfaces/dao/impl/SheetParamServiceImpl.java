package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.SheetParamMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.SheetParamPO;
import com.sp.proxverse.interfaces.dao.service.SheetParamService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class SheetParamServiceImpl extends ServiceImpl<SheetParamMapper, SheetParamPO>
    implements SheetParamService {
  @Override
  public List<SheetParamPO> listBySheetId(Integer sheetId) {
    return this.list(
        new LambdaQueryWrapper<SheetParamPO>()
            .eq(SheetParamPO::getSheetId, sheetId)
            .eq(SheetParamPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
