package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.OptimizationObjectMapper;
import com.sp.proxverse.common.model.dto.api.QueryTenantIdDTO;
import com.sp.proxverse.common.model.po.OptimizationObjectPO;
import com.sp.proxverse.interfaces.dao.service.OptimizationObjectService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@DS("execution")
@Service
@Primary
public class OptimizationObjectServiceImpl
    extends ServiceImpl<OptimizationObjectMapper, OptimizationObjectPO>
    implements OptimizationObjectService {

  @Override
  public Integer queryTenantId(QueryTenantIdDTO dto) {
    return baseMapper.queryTenantId(
        dto.getTable(), dto.getField(), dto.getValue(), dto.getTenantId());
  }
}
