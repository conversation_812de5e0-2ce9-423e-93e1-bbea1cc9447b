package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.DataTaskMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.TaskRateTypeEnum;
import com.sp.proxverse.common.model.po.DataTaskPO;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.interfaces.dao.service.DataTaskService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class DataTaskServiceImpl extends ServiceImpl<DataTaskMapper, DataTaskPO>
    implements DataTaskService {
  @Override
  public void updateNextExecutionTime(List<DataTaskPO> dataTaskPOs) {
    if (dataTaskPOs.size() == 0) {
      return;
    }
    for (DataTaskPO dataTaskPO : dataTaskPOs) {
      dataTaskPO.setNextExecutionTime(
          DateTimeUtil.calNextExecutionTime(
              TaskRateTypeEnum.getEnumByCode(dataTaskPO.getRateType()),
              dataTaskPO.getMinute(),
              dataTaskPO.getHour(),
              dataTaskPO.getWeek()));
    }
    this.updateBatchById(dataTaskPOs);
  }

  @Override
  public IPage<DataTaskPO> listPage(Integer poolId, Integer pageNum, Integer pageSize) {
    LambdaQueryWrapper<DataTaskPO> dataTaskLambdaWrapper =
        new LambdaQueryWrapper<DataTaskPO>()
            .eq(poolId != null, DataTaskPO::getPoolId, poolId)
            .eq(DataTaskPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .orderByDesc(DataTaskPO::getId);

    IPage<DataTaskPO> page = new Page<>(pageNum, pageSize);
    return this.page(page, dataTaskLambdaWrapper);
  }

  @Override
  public void updateNextExecutionTime(
      Integer id, TaskRateTypeEnum rateType, Integer minute, Integer hour, Integer week) {
    Long aLong = DateTimeUtil.calNextExecutionTime(rateType, minute, hour, week);
    this.update(
        new LambdaUpdateWrapper<DataTaskPO>()
            .eq(DataTaskPO::getId, id)
            .set(DataTaskPO::getNextExecutionTime, aLong));
  }
}
