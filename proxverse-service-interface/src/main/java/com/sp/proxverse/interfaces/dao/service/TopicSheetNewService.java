package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.TopicSheetNewPo;
import com.sp.proxverse.common.model.vo.request.UpdateNewSheetReq;

/**
 * new sheet信息 服务类
 *
 * <AUTHOR>
 * @since 2022-03-07
 */
public interface TopicSheetNewService extends IService<TopicSheetNewPo> {

  /**
   * 更新或添加newSheet信息
   *
   * @param updateNewSheetReq
   * @return
   */
  String updateNewSheetInfo(UpdateNewSheetReq updateNewSheetReq);

  /**
   * 查找newSheet信息
   *
   * @param topicSheetId
   * @return
   */
  String selectNewSheetInfo(Integer topicSheetId);

  TopicSheetNewPo getTopicSheetNewByTopicSheetId(Integer sheetId);
}
