package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.SignalConditionPO;
import java.util.List;

public interface SignalConditionService extends IService<SignalConditionPO> {
  List<SignalConditionPO> getListByOptimizationId(Integer optimizationId);

  List<SignalConditionPO> getListByOptimizationIds(List<Integer> optimizationIds);

  void removeByOptimizationId(Integer optimizationId);
}
