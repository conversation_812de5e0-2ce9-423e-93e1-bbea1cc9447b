package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.SheetMenuMapper;
import com.sp.proxverse.common.model.po.SheetMenuPO;
import com.sp.proxverse.interfaces.dao.service.SheetMenuService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class SheetMenuServiceImpl extends ServiceImpl<SheetMenuMapper, SheetMenuPO>
    implements SheetMenuService {
  @Override
  public List<SheetMenuPO> listBySheetIds(List<Integer> sheetIds) {
    return this.list(
        new LambdaQueryWrapper<SheetMenuPO>()
            .in(SheetMenuPO::getSheetId, sheetIds)
            .eq(SheetMenuPO::getDeleted, 0));
  }
}
