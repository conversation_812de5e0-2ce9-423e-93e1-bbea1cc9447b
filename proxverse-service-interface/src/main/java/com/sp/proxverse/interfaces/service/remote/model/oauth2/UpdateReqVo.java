package com.sp.proxverse.interfaces.service.remote.model.oauth2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-04-02 12:53 下午
 */
@Data
@ApiModel("创建用户")
public class UpdateReqVo {

  @ApiModelProperty(value = "用户Id", required = true)
  @NotNull(message = "用户Id不能为空")
  private Integer adminId;

  @ApiModelProperty(value = "用户名", required = true)
  @NotNull(message = "用户名不能为空")
  private String userName;

  @ApiModelProperty(value = "姓名")
  private String name;

  @ApiModelProperty(value = "密码", required = true)
  private String password;

  @ApiModelProperty(value = "用户角色,USER,ANALYST,ROOT", required = true)
  private String level;

  private List<Integer> roleList;

  private List<Integer> userGroupList;

  public List<Integer> getRoleList() {
    return roleList == null ? new ArrayList<>() : roleList;
  }

  public List<Integer> getUserGroupList() {
    return userGroupList == null ? new ArrayList<>() : userGroupList;
  }
}
