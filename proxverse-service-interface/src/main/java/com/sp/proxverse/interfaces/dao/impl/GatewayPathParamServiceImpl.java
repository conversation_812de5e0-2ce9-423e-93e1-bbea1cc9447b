package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.GatewayPathParamMapper;
import com.sp.proxverse.common.model.po.GatewayPathParamPO;
import com.sp.proxverse.interfaces.dao.service.GatewayPathParamService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@DS("oauth2")
@Service
@Primary
public class GatewayPathParamServiceImpl
    extends ServiceImpl<GatewayPathParamMapper, GatewayPathParamPO>
    implements GatewayPathParamService {
  @Override
  public GatewayPathParamPO queryPathParam(String path) {
    return baseMapper.selectOne(
        new LambdaQueryWrapper<GatewayPathParamPO>()
            .eq(GatewayPathParamPO::getPath, path)
            .eq(GatewayPathParamPO::getDeleted, 0)
            .last("LIMIT 1"));
  }
}
