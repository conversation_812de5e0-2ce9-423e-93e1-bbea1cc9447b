package com.sp.proxverse.interfaces.service.remote.model.oauth2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-15 6:37 下午
 */
@ApiModel
@Getter
@Setter
public class UserDataAuthorityResVo {
  @ApiModelProperty(value = "数据信息", required = true)
  List<UserDataAuthorityInfoResVo> data;

  @ApiModelProperty(value = "页大小", required = true)
  private Integer pageSize;

  @ApiModelProperty(value = "一共多少页", required = true)
  private Integer pageSum;

  @ApiModelProperty(value = "多少行", required = true)
  private Long totalCount;
}
