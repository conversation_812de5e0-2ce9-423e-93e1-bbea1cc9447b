package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.BusinessTopicDataMapper;
import com.sp.proxverse.common.model.dict.DataSourceTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.dict.TopicSheetTypeEnum;
import com.sp.proxverse.common.model.dto.DataModelDTO;
import com.sp.proxverse.common.model.dto.TopicFileDTO;
import com.sp.proxverse.common.model.po.BusinessTopicDataPO;
import com.sp.proxverse.common.model.po.BusinessTopicPO;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicService;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Primary
public class BusinessTopicDataServiceImpl
    extends ServiceImpl<BusinessTopicDataMapper, BusinessTopicDataPO>
    implements BusinessTopicDataService {

  @Autowired private DataModelFileService dataModelFileService;

  @Autowired private DataModelService dataModelService;

  @Autowired private BusinessTopicService businessTopicService;

  @Autowired private FileService fileService;

  @Autowired private TopicSheetService topicSheetService;
  private static final String LIMIT_1 = "LIMIT 1";

  @Override
  public List<DataModelDTO> getDataModelListByTopicId(Integer topicId) {

    return baseMapper.getDataModelListByTopicId(topicId);
  }

  @Override
  public TopicFileDTO getTopicFileByTopicId(Integer topicId) {

    TopicFileDTO topicFileByTopicId = baseMapper.getTopicFileByTopicId(topicId);
    if (Objects.isNull(topicFileByTopicId)) {
      // 如果查数据模型没查到，要再查一下知识模型
      BusinessTopicDataPO list =
          baseMapper.selectOne(
              new LambdaQueryWrapper<BusinessTopicDataPO>()
                  .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                  .eq(
                      BusinessTopicDataPO::getDataSourceType,
                      DataSourceTypeEnum.KNOWNLEADE_MODEL.getValue())
                  .eq(BusinessTopicDataPO::getTopicId, topicId)
                  .last(LIMIT_1));
      if (Objects.isNull(list)) {
        log.info("getTopicFileByTopicId 查知识模型为空 topicId={}", topicId);
        return null;
      } else {
        // 这里要再对知识模型查一下数据模型
        list =
            baseMapper.selectOne(
                new LambdaQueryWrapper<BusinessTopicDataPO>()
                    .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                    .eq(
                        BusinessTopicDataPO::getDataSourceType,
                        DataSourceTypeEnum.DATA_MODEL.getValue())
                    .eq(BusinessTopicDataPO::getTopicId, list.getModelId())
                    .last(LIMIT_1));
      }

      DataModelFilePO one =
          dataModelFileService.getOne(
              new LambdaQueryWrapper<DataModelFilePO>()
                  .eq(DataModelFilePO::getDataModelId, list.getModelId())
                  .eq(DataModelFilePO::getActive, FileActiveTypeEnum.ACTIVE.getValue())
                  .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                  .last(LIMIT_1));
      if (Objects.isNull(one)) {
        log.info("getTopicFileByTopicId 查数据模型文件为空 topicId={}", topicId);
        return null;
      }

      FilePO byId = fileService.getById(one.getFileId());

      return TopicFileDTO.builder()
          .fileId(byId.getId())
          .topicId(topicId)
          .dataModelId(one.getDataModelId())
          .filename(byId.getFilename())
          .attributes(byId.getAttributes())
          .uniqFileName(byId.getUniqFilename())
          .build();
    } else {
      return topicFileByTopicId;
    }
  }

  @Override
  public BusinessTopicDataPO queryKnowledge(Integer topicId) {

    return baseMapper.selectOne(
        new LambdaQueryWrapper<BusinessTopicDataPO>()
            .eq(BusinessTopicDataPO::getTopicId, topicId)
            .eq(
                BusinessTopicDataPO::getDataSourceType,
                DataSourceTypeEnum.KNOWNLEADE_MODEL.getValue())
            .eq(BusinessTopicDataPO::getDeleted, 0)
            .last(LIMIT_1));
  }

  @Override
  public List<BusinessTopicDataPO> queryKnowledgeTopicList(Integer knowledgeTopicId) {

    return baseMapper.selectList(
        new LambdaQueryWrapper<BusinessTopicDataPO>()
            .eq(BusinessTopicDataPO::getModelId, knowledgeTopicId)
            .eq(
                BusinessTopicDataPO::getDataSourceType,
                DataSourceTypeEnum.KNOWNLEADE_MODEL.getValue())
            .eq(BusinessTopicDataPO::getDeleted, 0));
  }

  @Override
  public List<Integer> queryConformanceSheetIdByModelId(Integer modelId) {
    List<BusinessTopicDataPO> topicDataList =
        this.list(
            new LambdaQueryWrapper<BusinessTopicDataPO>()
                .eq(BusinessTopicDataPO::getModelId, modelId)
                .eq(
                    BusinessTopicDataPO::getDataSourceType,
                    DataSourceTypeEnum.DATA_MODEL.getValue())
                .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (CollectionUtils.isNotEmpty(topicDataList)) {
      List<Integer> topicIdList =
          topicDataList.stream().map(BusinessTopicDataPO::getTopicId).collect(Collectors.toList());
      List<BusinessTopicDataPO> topicLedgeModelList =
          this.list(
              new LambdaQueryWrapper<BusinessTopicDataPO>()
                  .in(BusinessTopicDataPO::getModelId, topicIdList)
                  .eq(
                      BusinessTopicDataPO::getDataSourceType,
                      DataSourceTypeEnum.DATA_MODEL.getValue())
                  .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      topicDataList.addAll(topicLedgeModelList);
    }

    if (CollectionUtils.isNotEmpty(topicDataList)) {
      List<Integer> topicIdList =
          topicDataList.stream()
              .map(BusinessTopicDataPO::getTopicId)
              .distinct()
              .collect(Collectors.toList());
      List<TopicSheetPO> sheetList =
          topicSheetService.list(
              new LambdaQueryWrapper<TopicSheetPO>()
                  .in(TopicSheetPO::getTopicId, topicIdList)
                  .eq(TopicSheetPO::getType, TopicSheetTypeEnum.PROCESS_CONFORMANCE.getValue())
                  .eq(TopicSheetPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
      return sheetList.stream().map(TopicSheetPO::getId).collect(Collectors.toList());
    }

    return new ArrayList<>();
  }

  @Override
  public BusinessTopicDataPO getOne(Integer topicId) {
    return baseMapper.selectOne(
        new LambdaQueryWrapper<BusinessTopicDataPO>()
            .eq(BusinessTopicDataPO::getTopicId, topicId)
            .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .last(LIMIT_1));
  }

  @Override
  public Map<Integer, Integer> getDataModelIdList(List<Integer> topicIdList) {
    if (CollectionUtils.isEmpty(topicIdList)) {
      return new HashMap<>();
    }
    List<BusinessTopicDataPO> dataList =
        this.baseMapper.selectList(
            new LambdaQueryWrapper<BusinessTopicDataPO>()
                .in(BusinessTopicDataPO::getTopicId, topicIdList)
                .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

    List<BusinessTopicDataPO> knowledgeList =
        dataList.stream()
            .filter(
                f ->
                    Objects.equals(
                        f.getDataSourceType(), DataSourceTypeEnum.KNOWNLEADE_MODEL.getValue()))
            .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(knowledgeList)) {
      List<Integer> knowledgeDataIdList =
          knowledgeList.stream().map(BusinessTopicDataPO::getModelId).collect(Collectors.toList());

      List<BusinessTopicDataPO> knowledgeDataList =
          this.baseMapper.selectList(
              new LambdaQueryWrapper<BusinessTopicDataPO>()
                  .in(BusinessTopicDataPO::getTopicId, knowledgeDataIdList)
                  .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));

      dataList.addAll(knowledgeDataList);
    }

    Map<Integer, Integer> map =
        dataList.stream()
            .filter(
                f ->
                    Objects.equals(f.getDataSourceType(), DataSourceTypeEnum.DATA_MODEL.getValue()))
            .collect(
                Collectors.toMap(
                    BusinessTopicDataPO::getTopicId,
                    BusinessTopicDataPO::getModelId,
                    (k1, k2) -> k1));

    return map;
  }

  @Override
  public List<Integer> getKnowledgeListByModelIds(List<Integer> modelIds) {
    if (CollectionUtils.isEmpty(modelIds)) {
      return new ArrayList<>();
    }
    return baseMapper.getKnowledgeIdListByModelIds(modelIds);
  }

  @Override
  public String getDataSourceName(BusinessTopicDataPO data) {
    if (Objects.equals(data.getDataSourceType(), DataSourceTypeEnum.DATA_MODEL.getValue())) {
      DataModelPO model = dataModelService.getById(data.getModelId());
      return model.getName();
    } else {
      BusinessTopicPO model = businessTopicService.getById(data.getModelId());
      return model.getName();
    }
  }
}
