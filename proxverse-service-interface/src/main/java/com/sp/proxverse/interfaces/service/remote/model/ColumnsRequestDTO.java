package com.sp.proxverse.interfaces.service.remote.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Builder
@Data
@ApiModel("列的请求对象数据层")
public class ColumnsRequestDTO implements Serializable {

  @Tolerate
  public ColumnsRequestDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "列id集合", required = true)
  @NotNull(message = "列id集合不能为空")
  private List<Integer> columnIds;
}
