package com.sp.proxverse.interfaces.service.remote.model.oauth2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-04-06 12:30 下午
 */
@Data
@ApiModel("停(启)用用户请求对象")
public class PauseUserReqVo {

  @ApiModelProperty(value = "用户ID", required = true)
  @NotNull(message = "用户ID不能为空")
  private Integer userId;
}
