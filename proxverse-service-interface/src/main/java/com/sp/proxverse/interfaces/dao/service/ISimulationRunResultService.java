package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.bo.SimulationRunResult;
import com.sp.proxverse.common.model.po.SimulationRunResultPo;

/** <AUTHOR> */
public interface ISimulationRunResultService extends IService<SimulationRunResultPo> {

  /**
   * 保存结果
   *
   * @param simulationRunResult
   * @return
   */
  Integer saveSimulationResult(SimulationRunResult simulationRunResult);
}
