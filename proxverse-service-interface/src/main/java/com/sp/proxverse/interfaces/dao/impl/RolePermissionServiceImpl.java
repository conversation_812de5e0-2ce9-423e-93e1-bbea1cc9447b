package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.RolePermissionMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.RolePermissionPO;
import com.sp.proxverse.interfaces.dao.service.RolePermissionService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@DS("oauth2")
@Primary
public class RolePermissionServiceImpl extends ServiceImpl<RolePermissionMapper, RolePermissionPO>
    implements RolePermissionService {
  @Override
  public List<Integer> listPermissionIdsByRoleIds(List<Integer> roleIds) {
    if (CollectionUtils.isEmpty(roleIds)) {
      return new ArrayList<>();
    }
    List<RolePermissionPO> list =
        list(
            new LambdaQueryWrapper<RolePermissionPO>()
                .in(RolePermissionPO::getRoleId, roleIds)
                .eq(RolePermissionPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    return list.stream().map(RolePermissionPO::getPermissionId).collect(Collectors.toList());
  }
}
