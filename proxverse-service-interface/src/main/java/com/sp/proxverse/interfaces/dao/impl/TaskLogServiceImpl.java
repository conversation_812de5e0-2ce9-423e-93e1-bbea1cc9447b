package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.TaskLogMapper;
import com.sp.proxverse.common.model.dto.task.TaskLogCountDto;
import com.sp.proxverse.common.model.po.TaskLogPo;
import com.sp.proxverse.interfaces.dao.service.ITaskLogService;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 任务日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-05
 */
@Service
public class TaskLogServiceImpl extends ServiceImpl<TaskLogMapper, TaskLogPo>
    implements ITaskLogService {

  @Autowired TaskLogMapper taskLogMapper;

  @Override
  public List<TaskLogCountDto> getTaskLogCountByTaskId(
      Collection<Integer> taskIds, Integer status) {
    return taskLogMapper.getTaskLogCountByTaskId(taskIds, status);
  }

  @Override
  public Long checkTaskStatus(Collection<Integer> taskIds) {
    return taskLogMapper.checkTaskStatus(taskIds);
  }

  @Override
  public void clearTaskLog(Integer taskLogCleanBeforeDay) {
    if (taskLogCleanBeforeDay == null) {
      return;
    }
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    calendar.add(Calendar.DAY_OF_YEAR, -taskLogCleanBeforeDay);
    this.remove(
        new LambdaQueryWrapper<TaskLogPo>().lt(TaskLogPo::getCreateTime, calendar.getTime()));
  }
}
