package com.sp.proxverse.interfaces.service.remote.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询kpi值请求DTO")
public class QueryKpiDTO {

  @Tolerate
  public QueryKpiDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "kpi关联Id")
  private Integer kpiRelationId;

  @ApiModelProperty(value = "数据模型ID", required = true)
  private Integer dataModelId;

  @ApiModelProperty(value = "kpiID", required = true)
  private Integer kpiId;

  @ApiModelProperty(value = "kpi公式")
  private String expression;

  @ApiModelProperty(value = "时间维度列ID 1.6.3", required = true)
  public Integer timeColumnId;
}
