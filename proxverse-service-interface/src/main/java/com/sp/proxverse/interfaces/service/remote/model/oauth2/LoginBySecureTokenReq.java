package com.sp.proxverse.interfaces.service.remote.model.oauth2;

import com.sp.proxverse.common.model.enums.ShareEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-02-02 09:59
 */
@Data
@ApiModel("授权令牌登录请求对象")
public class LoginBySecureTokenReq {

  @ApiModelProperty(value = "授权令牌", required = true)
  @NotNull(message = "授权令牌不能为空")
  private String encryptToken;

  private String userName;

  @ApiModelProperty(value = "分享检查")
  private String checkShareData;

  /** @see ShareEnum */
  private ShareEnum shareEnum;
}
