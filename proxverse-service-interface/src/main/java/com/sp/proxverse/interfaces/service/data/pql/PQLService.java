package com.sp.proxverse.interfaces.service.data.pql;

import com.google.common.collect.Lists;
import com.sp.proxverse.common.model.dto.KpiParserParamDTO;
import com.sp.proxverse.common.model.dto.ParseDataModelKpiDTO;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.request.ParseKpiDTO;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.spark.sql.catalyst.expressions.Expression;
import org.apache.spark.sql.pql.PQLBuilder;
import org.apache.spark.sql.types.StructType;

public interface PQLService {

  String validatePQL(int topicId, String rawStatement);

  String evalPQL(int topicId, String rawStatement);

  Expression parsePQLExpr(int topicId, String expression);

  String evalPQLWithModel(int modelId, String rawStatement);

  String calcExpression(int topicId, Integer sheetId, String expression);

  List<String> calcExpression(
      int topicId,
      Integer sheetId,
      String expression,
      List<TopicFilterPO> addFilterList,
      int limit);

  List<String> calcExpressionWithFilter(
      int topicId,
      Integer sheetId,
      String expression,
      List<TopicFilterPO> filters,
      boolean skipFilter);

  String calcExpressionWithFilter(int topicId, Integer sheetId, String expression, String filter);

  String calcExpression(KpiParserParamDTO kpiParserParamDTO);

  Result calcExpression(ParseDataModelKpiDTO parseDataModelKpiDTO);

  Result calcExpression(ParseKpiDTO parseKpiDTO);

  Result resolveResultTypes(
      int topicId,
      List<String> columns,
      int limit,
      List<String> filters,
      List<Pair<String, String>> orders,
      boolean withFilter);

  default PQLBuilder newPQLBuilder(Integer topicId, Integer sheetId) {
    return newPQLBuilder(topicId, sheetId, Lists.newArrayList());
  }

  PQLBuilder newPQLBuilder(Integer topicId, Integer sheetId, List<String> filters);

  PQLBuilder newPQLBuilder(Integer topicId, Integer sheetId, boolean withTopicFilters);

  PQLBuilder newPQLBuilderWithOutCalcFilter(
      Integer topicId, Integer sheetId, List<String> customFilters);

  PQLBuilder newPQLBuilderWithScriptFilterOnly(
      Integer topicId, Integer sheetId, List<String> customFilters);

  PQLBuilder newPQLBuilderExcludePrikey(
      Integer topicId, Integer sheetId, List<String> customFilters, Integer prikey);

  PQLBuilder newPQLBuilder(Integer modelId);

  StructType export(ParseKpiDTO parseKpiDTO, String filePath);

  PQLBuilder newPQLBuilderWithTopic(Integer topicId);

  PQLBuilder newPQLBuilder(
      Integer topicId, Integer sheetId, List<String> customFilters, boolean withTopicFilters);

  PQLBuilder newPQLBuilderWithOptionalCalcFilter(
      Integer topicId,
      Integer sheetId,
      List<String> customFilters,
      boolean withTopicCalcFilters,
      boolean withCustomCalcFilters,
      boolean withUserCalcFilters);
}
