package com.sp.proxverse.interfaces.service.biz;

import com.google.common.collect.Lists;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.model.dict.CaseEventTimeFixEnum;
import com.sp.proxverse.common.model.dict.Consts;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.dict.prehandler.PreHandlerVariableEnum;
import com.sp.proxverse.common.model.dict.prehandler.PreTableEnum;
import com.sp.proxverse.common.model.dict.prehandler.PreTimeStepVariableEnum;
import com.sp.proxverse.common.model.dto.Lable;
import com.sp.proxverse.common.model.dto.TableDataDTO;
import com.sp.proxverse.common.model.dto.ValueDTO;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.oauth2.AsyncLoginInfo;
import com.sp.proxverse.common.model.po.DataModelFileMasterPO;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.FileFieldPO;
import com.sp.proxverse.common.model.po.FilePO;
import com.sp.proxverse.common.util.ChineseUtils;
import com.sp.proxverse.common.util.DataSourceUtils;
import com.sp.proxverse.common.util.DataUtil;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.DataModelFileMasterService;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.dao.service.FileFieldService;
import com.sp.proxverse.interfaces.dao.service.FileService;
import java.text.SimpleDateFormat;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.common.model.DataTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

// 保留

@Slf4j
@Service
public class CommonBizService {

  DataUtil dataUtil = new DataUtil();

  @Autowired private DataModelService dataModelService;

  @Autowired private FileFieldService fileFieldService;

  @Autowired private FileService fileService;

  @Autowired private DataModelFileService dataModelFileService;

  @Autowired private DataModelFileMasterService dataModelFileMasterService;

  @Autowired UserInfoUtil userInfoUtil;

  public static final String UTF8_BOM = "\uFEFF";

  public List<TableDataDTO> getTableDataDTOList(
      List<FileFieldPO> fileFieldPOList, List<Map<String, String>> fileDataList) {
    List<TableDataDTO> retList = new ArrayList<>();
    for (FileFieldPO po : fileFieldPOList) {
      List<ValueDTO> valueList =
          fileDataList.stream()
              .map(
                  m -> {
                    ValueDTO valueDTO = new ValueDTO();

                    String field =
                        StringUtils.isBlank(po.getFieldOrigin())
                            ? po.getField()
                            : po.getFieldOrigin();
                    if (field.startsWith(UTF8_BOM)) {
                      field = field.substring(1);
                    }
                    String o = m.get(field);
                    if (Objects.isNull(o) || StringUtils.isBlank(o)) {
                      valueDTO.setValue(Consts.EMPYT);
                    } else {
                      valueDTO.setValue(o);
                    }
                    return valueDTO;
                  })
              .collect(Collectors.toList());
      TableDataDTO tableDataDTO =
          TableDataDTO.builder()
              .propName(po.getFieldOrigin())
              .fieldId(po.getId())
              .field(po.getField())
              .fieldType(po.getFieldType())
              .dateFormat(po.getDateFormat())
              .list(valueList)
              .originalColumnName(po.getOriginalColumnName())
              .build();
      retList.add(tableDataDTO);
    }
    return retList;
  }

  public Map.Entry<Integer, String> transFieldDataType(String data) {
    if (DataUtil.isInteger(data)) {
      return new AbstractMap.SimpleEntry<>(DataTypeEnum.INT.getValue(), "");
    }

    if (DataUtil.isDouble(data)) {
      return new AbstractMap.SimpleEntry<>(DataTypeEnum.FLOAT.getValue(), "");
    }
    if (dataUtil.isBool(data)) {
      return new AbstractMap.SimpleEntry<>(DataTypeEnum.BOOL.getValue(), "");
    }
    String format = DateTimeUtil.getDateFormat(data);
    if (StringUtils.isNotBlank(format) && format.contains("T")) {
      format = format.replace("T", "'T'");
    }
    try {
      if (StringUtils.isNotBlank(format)) {
        new SimpleDateFormat(dataUtil.turnDataFormat(format), Locale.US);
      }
    } catch (Exception e) {
      return new AbstractMap.SimpleEntry<>(DataTypeEnum.STR.getValue(), "");
    }
    if (dataUtil.isDate(format)) {
      return new AbstractMap.SimpleEntry<>(
          DataTypeEnum.DATE.getValue(), dataUtil.turnDataFormat(format));
    }
    if (dataUtil.isTime(format)) {
      return new AbstractMap.SimpleEntry<>(DataTypeEnum.DATE_TIME.getValue(), "H:m:s");
    }
    if (dataUtil.isDateTime(format)) {
      return new AbstractMap.SimpleEntry<>(
          DataTypeEnum.DATE_TIME.getValue(), dataUtil.turnDataFormat(format));
    }
    return new AbstractMap.SimpleEntry<>(DataTypeEnum.STR.getValue(), "");
  }

  public Integer transFieldDataType(String data, FileFieldPO fieldPO) {
    if (DataUtil.isInteger(data)) {
      return DataTypeEnum.INT.getValue();
    }
    if (DataUtil.isDouble(data)) {
      return DataTypeEnum.FLOAT.getValue();
    }
    if (dataUtil.isBool(data)) {
      return DataTypeEnum.BOOL.getValue();
    }
    String format = DateTimeUtil.getDateFormat(data);
    if (StringUtils.isNotBlank(format) && format.contains("T")) {
      format = format.replace("T", "'T'");
    }
    try {
      if (StringUtils.isNotBlank(format)) {
        new SimpleDateFormat(dataUtil.turnDataFormat(format), Locale.US);
      }
    } catch (Exception e) {
      return DataTypeEnum.STR.getValue();
    }
    if (dataUtil.isDate(format)) {
      fieldPO.setDateFormat(dataUtil.turnDataFormat(format));
      return DataTypeEnum.DATE.getValue();
    }
    if (dataUtil.isTime(format)) {
      fieldPO.setDateFormat("H:m:s");
      return DataTypeEnum.DATE_TIME.getValue();
    }
    if (dataUtil.isDateTime(format)) {
      fieldPO.setDateFormat(dataUtil.turnDataFormat(format));
      return DataTypeEnum.DATE_TIME.getValue();
    }
    return DataTypeEnum.STR.getValue();
  }

  public AsyncLoginInfo getAuthContext() {
    AsyncLoginInfo asyncLoginInfo = new AsyncLoginInfo(userInfoUtil.getUserInfo());
    return asyncLoginInfo;
  }

  public void invokeAuthContext(AsyncLoginInfo auth) {
    UserInfoUtil.authInfo.set(auth.getUserInfo());
  }

  public List<Lable> getLableList(List<String> fieldList, Map<String, String> caseEventMap) {
    return fieldList.stream()
        .map(m -> Lable.builder().lable(m).prop(m).build())
        .collect(Collectors.toList());
  }

  public List<Map<String, String>> getTableDataList4map(
      List<Map<String, String>> fileDataList, List<String> fieldList) {
    return fileDataList.stream()
        .map(
            d -> {
              Map<String, String> tableDataMap = new HashMap<>();
              for (String param : fieldList) {
                String o = d.get(param);
                if (StringUtils.isBlank(o) || Objects.equals(o, "null")) {
                  o = com.sp.proxverse.common.model.dict.Consts.EMPYT;
                }
                tableDataMap.put(param, o);
              }
              return tableDataMap;
            })
        .collect(Collectors.toList());
  }

  public Map<String, String> getCaseEventTimeField(Integer dataModelId) {
    DataModelPO byId = dataModelService.getById(dataModelId);
    if (Objects.isNull(byId)) {
      return new HashMap<>();
    }
    List<Integer> fieldIdList =
        Lists.newArrayList(byId.getCaseid(), byId.getEvent(), byId.getTime());

    List<FileFieldPO> fieldPOS = fileFieldService.listByIds(fieldIdList);
    Map<Integer, FileFieldPO> fieldPOMap =
        fieldPOS.stream()
            .collect(Collectors.toMap(FileFieldPO::getId, Function.identity(), (k1, k2) -> k1));

    Map<String, String> map = new HashMap<>();
    FileFieldPO caseId = fieldPOMap.get(byId.getCaseid());
    if (Objects.nonNull(caseId)) {

      map.put(CaseEventTimeFixEnum.CASEID.getValue(), caseId.getFieldOrigin());
    }
    FileFieldPO event = fieldPOMap.get(byId.getEvent());
    if (Objects.nonNull(event)) {

      map.put(CaseEventTimeFixEnum.EVENT.getValue(), event.getFieldOrigin());
    }
    FileFieldPO time = fieldPOMap.get(byId.getTime());
    if (Objects.nonNull(time)) {

      map.put(CaseEventTimeFixEnum.TIME.getValue(), time.getFieldOrigin());
    }

    return map;
  }

  public String makeBucketTableName(FilePO filePO, Integer dataModelId, String databaseName) {
    return DataSourceUtils.makeFullTableName(
        DataSourceUtils.makeBucketTableName(
            DataSourceUtils.makeTableName(
                dataModelId,
                ChineseUtils.transformChineseToPinyinIfNeed(filePO.getFilename()),
                filePO.getId().toString())),
        databaseName);
  }

  public String makeNonBucketTableName(FilePO filePO, String databaseName) {
    return DataSourceUtils.makeFullTableName(
        filePO.getType() == 6
            ? "`" + filePO.getFilename() + "`"
            : DataSourceUtils.makeFileTableName(
                ChineseUtils.transformChineseToPinyinIfNeed(filePO.getFilename()),
                filePO.getId().toString()),
        databaseName);
  }

  public void saveDataModelVirtualFileData(
      Integer activeFileId, Integer poolId, Integer dataModelId, List<Integer> fileIdList) {

    String databaseName = DataSourceUtils.makeDatabaseName(poolId);
    List<FilePO> filePOList = fileService.listByIds(fileIdList);

    Map<Integer, FilePO> filePOMap =
        filePOList.stream()
            .collect(Collectors.toMap(FilePO::getId, Function.identity(), (k1, k2) -> k1));
    String activeBucketTableName =
        makeBucketTableName(filePOMap.get(activeFileId), dataModelId, databaseName);
    List<DataModelFilePO> dataModelFilePOList =
        fileIdList.stream()
            .map(
                fileId -> {
                  FilePO fileIdPO = filePOMap.get(fileId);
                  DataModelFilePO dataModelFilePO = new DataModelFilePO();
                  dataModelFilePO.setDataModelId(dataModelId);
                  dataModelFilePO.setFileId(fileId);
                  dataModelFilePO.setFileName(
                      Objects.isNull(fileIdPO) ? null : fileIdPO.getFilename());

                  if (Objects.equals(fileId, activeFileId)) {
                    dataModelFilePO.setActive(FileActiveTypeEnum.ACTIVE.getValue());
                  }
                  return dataModelFilePO;
                })
            .collect(Collectors.toList());
    boolean modelFileSave = dataModelFileService.saveBatch(dataModelFilePOList);
    if (!modelFileSave) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.SAVE_DATA_MODEL_ERROR));
    }

    // 在file表中插入type=虚拟case表的数据，在datamodel_file表中插入datamodel-虚拟casefile的关联数据
    FilePO caseTableFile =
        FilePO.builder()
            .uploadStatus(0)
            .loadStartTime(new Date())
            .filename(PreTableEnum.CASE_TABLE.getValue())
            .parentId(activeFileId)
            .build();
    fileService.save(caseTableFile);
    FilePO timeGapTableFile =
        FilePO.builder()
            .uploadStatus(0)
            .loadStartTime(new Date())
            .filename(PreTableEnum.CASE_TIME_GAP.getValue())
            .parentId(activeFileId)
            .build();
    fileService.save(timeGapTableFile);
    FilePO variantFile =
        FilePO.builder()
            .uploadStatus(0)
            .loadStartTime(new Date())
            .filename(PreTableEnum.VARIANT_TABLE.getValue())
            .parentId(activeFileId)
            .build();
    fileService.save(variantFile);

    FilePO activeBucketFile =
        FilePO.builder()
            .uploadStatus(0)
            .loadStartTime(new Date())
            .filename(PreTableEnum.ACTIVE_BUCKET.getValue())
            .parentId(activeFileId)
            .build();
    fileService.save(activeBucketFile);

    Stream<FileFieldPO> caseTableFieldsStream =
        Arrays.stream(PreHandlerVariableEnum.values())
            .map(value -> FileFieldPO.fromEnum(value, caseTableFile.getId()));
    Stream<FileFieldPO> timeGapFieldsSteam =
        Arrays.stream(PreTimeStepVariableEnum.values())
            .map(value -> FileFieldPO.fromEnum(value, timeGapTableFile.getId()));
    List<FileFieldPO> fields =
        Stream.concat(caseTableFieldsStream, timeGapFieldsSteam).collect(Collectors.toList());
    fileFieldService.saveBatch(fields);

    DataModelFilePO virtualCaseTable =
        DataModelFilePO.builder()
            .fileId(caseTableFile.getId())
            .dataModelId(dataModelId)
            .active(FileActiveTypeEnum.VIRTUAL_CASE.getValue())
            .fileName(caseTableFile.getFilename())
            .sparkTableName(
                DataSourceUtils.makeFullTableName(
                    DataSourceUtils.makeTableName(
                        dataModelId, PreTableEnum.CASE_TABLE.getName(), activeFileId),
                    databaseName))
            .build();
    dataModelFileService.save(virtualCaseTable);

    dataModelFileService.save(
        DataModelFilePO.builder()
            .fileId(variantFile.getId())
            .dataModelId(dataModelId)
            .fileName(variantFile.getFilename())
            .active(FileActiveTypeEnum.VARIANT_TABLE.getValue())
            .sparkTableName(
                DataSourceUtils.makeFullTableName(
                    DataSourceUtils.makeTableName(
                        dataModelId, PreTableEnum.VARIANT_TABLE.getName(), activeFileId),
                    databaseName))
            .build());

    dataModelFileMasterService.save(
        DataModelFileMasterPO.builder()
            .dataModelId(dataModelId)
            .originFileId(activeFileId)
            .mergeSql(activeBucketTableName)
            .caseMergeSql(virtualCaseTable.getSparkTableName())
            .build());
  }
}
