package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.EventStartEndPOMapper;
import com.sp.proxverse.common.model.po.EventStartEndPO;
import com.sp.proxverse.interfaces.dao.service.EventStartEndPOService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class EventStartEndPOServiceImpl extends ServiceImpl<EventStartEndPOMapper, EventStartEndPO>
    implements EventStartEndPOService {}
