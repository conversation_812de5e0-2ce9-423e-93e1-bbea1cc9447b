package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.GatewayPathPermissionMapper;
import com.sp.proxverse.common.model.po.GatewayPathPermissionPO;
import com.sp.proxverse.interfaces.dao.service.GatewayPathPermissionService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@DS("oauth2")
@Service
@Primary
public class GatewayPathPermissionServiceImpl
    extends ServiceImpl<GatewayPathPermissionMapper, GatewayPathPermissionPO>
    implements GatewayPathPermissionService {
  @Override
  public GatewayPathPermissionPO queryPathParam(String path) {
    return baseMapper.selectOne(
        new LambdaQueryWrapper<GatewayPathPermissionPO>()
            .eq(GatewayPathPermissionPO::getPath, path)
            .eq(GatewayPathPermissionPO::getDeleted, 0)
            .last("LIMIT 1"));
  }
}
