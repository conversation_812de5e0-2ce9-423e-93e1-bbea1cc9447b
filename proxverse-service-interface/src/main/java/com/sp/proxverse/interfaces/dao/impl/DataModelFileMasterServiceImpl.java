package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.DataModelFileMasterMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.DataModelFileMasterPO;
import com.sp.proxverse.interfaces.dao.service.DataModelFileMasterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class DataModelFileMasterServiceImpl
    extends ServiceImpl<DataModelFileMasterMapper, DataModelFileMasterPO>
    implements DataModelFileMasterService {

  @Override
  public void updateInfo(Integer dataModelId, String mergeSql, String caseMergeSql) {
    this.update(
        new LambdaUpdateWrapper<DataModelFileMasterPO>()
            .eq(DataModelFileMasterPO::getDataModelId, dataModelId)
            .set(DataModelFileMasterPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .set(StringUtils.isNotBlank(mergeSql), DataModelFileMasterPO::getMergeSql, mergeSql)
            .set(
                StringUtils.isNotBlank(caseMergeSql),
                DataModelFileMasterPO::getCaseMergeSql,
                caseMergeSql));
  }
}
