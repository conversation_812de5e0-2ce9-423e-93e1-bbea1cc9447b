package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.SsoGroupMapperMapper;
import com.sp.proxverse.common.model.po.SsoGroupMapperPo;
import com.sp.proxverse.interfaces.dao.service.ISsoGroupMapperService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-27
 */
@Service
@DS("oauth2")
public class SsoGroupMapperServiceImpl extends ServiceImpl<SsoGroupMapperMapper, SsoGroupMapperPo>
    implements ISsoGroupMapperService {}
