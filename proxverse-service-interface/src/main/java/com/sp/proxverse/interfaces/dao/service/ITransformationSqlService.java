package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.TransformationSqlPo;
import java.util.List;

/**
 * 数据转换SQL 服务类
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
public interface ITransformationSqlService extends IService<TransformationSqlPo> {
  /**
   * 根据数据池Id获取sql
   *
   * @param poolId
   * @return
   */
  List<TransformationSqlPo> getListByPoolId(Integer poolId);
}
