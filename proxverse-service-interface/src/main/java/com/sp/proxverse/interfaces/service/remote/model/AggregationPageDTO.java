package com.sp.proxverse.interfaces.service.remote.model;

import com.sp.proxverse.common.model.dto.DataModelFileDTO;
import com.sp.proxverse.common.model.vo.request.FieldSearchRequest;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class AggregationPageDTO implements Serializable {
  private static final long serialVersionUID = 2090307504616884308L;

  @Tolerate
  public AggregationPageDTO() {
    // comment empty
  }

  private Integer pageNum;

  private Integer pageSize;

  private String param;

  private Integer paramFileId;

  private DataModelFileDTO dataModelFileDTO;

  private List<FieldSearchRequest> searchList;
}
