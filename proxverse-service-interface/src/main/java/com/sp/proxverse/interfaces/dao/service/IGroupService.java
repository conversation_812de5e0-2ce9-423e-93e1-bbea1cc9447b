package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.dto.domain.Group;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.CompanyConfigInfoRes;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.CompanyInfoVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.GetGroupIconReq;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.GroupIconInfoRes;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UpdateGroupPropertyReq;

/**
 * 用户信息 服务类
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@DS("oauth2")
public interface IGroupService extends IService<Group> {

  Group getGroup();

  Group getByEmail(String email);

  String getUsernameByTenantId(Integer tenantId);

  /**
   * 获取公司信息
   *
   * @param companyId
   * @return
   */
  CompanyInfoVo getCompanyInfo(String companyId);

  String getGroupColor(String companyId);

  Boolean updateGroupProperty(UpdateGroupPropertyReq updateGroupColorReq);

  GroupIconInfoRes getGroupIcon(GetGroupIconReq getGroupIconReq);

  Boolean removeCompanyProperty(UpdateGroupPropertyReq updateCompanyPropertyReq);

  CompanyConfigInfoRes getCompanyConfigInfo();
}
