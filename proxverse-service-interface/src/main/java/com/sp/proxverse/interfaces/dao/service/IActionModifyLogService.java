package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.bo.actionLog.CreatActionModifyLogBo;
import com.sp.proxverse.common.model.po.ActionModifyLogPo;
import com.sp.proxverse.common.model.vo.action.GetActionModifyLogReqVo;

/**
 * 执行动作修改日志 服务类
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
public interface IActionModifyLogService extends IService<ActionModifyLogPo> {

  /**
   * 分页获取日志信息
   *
   * @param getActionModifyLogReqVo
   * @return
   */
  PageResp getActionModifyLog(GetActionModifyLogReqVo getActionModifyLogReqVo);

  /**
   * 创建一个action修改日志
   *
   * @param creatActionModifyLogBo
   * @return
   */
  Integer creatActionModifyLog(CreatActionModifyLogBo creatActionModifyLogBo);
}
