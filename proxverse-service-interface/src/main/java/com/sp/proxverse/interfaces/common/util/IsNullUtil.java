package com.sp.proxverse.interfaces.common.util;

import com.google.common.collect.Sets;
import com.sp.proxverse.common.exception.BizException;
import java.lang.reflect.Field;
import java.util.HashSet;
import org.apache.commons.lang3.StringUtils;

/**
 * 空值校验
 *
 * <AUTHOR>
 * @create 2022-04-02 1:21 下午
 */
public class IsNullUtil {

  /**
   * 判断对象是否为空-String-is
   *
   * @param obj
   * @return 包含空属性时返回false，均有值时返回true
   */
  public static Boolean isNotEmptyBeanString(Object obj, String... ignoreFieldNames) {
    HashSet<String> fieldNameSet = Sets.newHashSet(ignoreFieldNames);
    try {
      if (null != obj) {

        // 得到类对象
        Class<?> c = (Class<?>) obj.getClass();
        // 得到属性集合
        Field[] fs = c.getDeclaredFields();
        // 遍历属性
        for (Field f : fs) {

          // 设置属性是可以访问的(私有的也可以)
          f.setAccessible(true);

          if (fieldNameSet.contains(f.getName())) {
            continue;
          }
          // 得到此属性的值
          Object val = f.get(obj);

          if (val instanceof String) {
            String valString = (String) val;
            if (StringUtils.isEmpty(valString)) {
              return false;
            }
          }
        }
      }
    } catch (Exception e) {
      throw new BizException(5000, "校验实体是否包含空值！");
    }
    return true;
  }
}
