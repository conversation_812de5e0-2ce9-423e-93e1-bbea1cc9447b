package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.po.KpiRelationPO;
import com.sp.proxverse.common.model.vo.kpi.request.DataModelRequest;
import com.sp.proxverse.common.model.vo.kpi.request.KpiDataModelColumnRequest;
import com.sp.proxverse.common.model.vo.kpi.response.TableResponse;
import java.util.List;

/**
 * kpi关联表 kpi关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 11:03:15
 */
public interface KpiRelationService extends IService<KpiRelationPO> {

  Integer saveDataModelColumn(KpiDataModelColumnRequest responseDTO);

  Response<List<TableResponse>> getDataModelField(DataModelRequest responseDTO);
}
