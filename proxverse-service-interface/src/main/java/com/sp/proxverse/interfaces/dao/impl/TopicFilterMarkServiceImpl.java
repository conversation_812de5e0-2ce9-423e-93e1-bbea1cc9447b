package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.TopicFilterMarkMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.TopicFilterMarkPO;
import com.sp.proxverse.interfaces.dao.service.TopicFilterMarkService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class TopicFilterMarkServiceImpl
    extends ServiceImpl<TopicFilterMarkMapper, TopicFilterMarkPO>
    implements TopicFilterMarkService {
  @Override
  public List<TopicFilterMarkPO> getList(Integer topicId) {
    return this.list(
        new LambdaQueryWrapper<TopicFilterMarkPO>()
            .eq(TopicFilterMarkPO::getTopicId, topicId)
            .isNotNull(TopicFilterMarkPO::getIndexId)
            .eq(TopicFilterMarkPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public TopicFilterMarkPO getOneByName(Integer topicId, String name) {
    return this.getOne(
        new LambdaQueryWrapper<TopicFilterMarkPO>()
            .eq(TopicFilterMarkPO::getTopicId, topicId)
            .eq(TopicFilterMarkPO::getDeleted, 0)
            .eq(TopicFilterMarkPO::getName, name));
  }

  @Override
  public TopicFilterMarkPO getByIndexId(Integer topicId, Integer indexId) {
    return this.getOne(
        new LambdaQueryWrapper<TopicFilterMarkPO>()
            .eq(TopicFilterMarkPO::getTopicId, topicId)
            .eq(TopicFilterMarkPO::getDeleted, 0)
            .eq(TopicFilterMarkPO::getIndexId, indexId));
  }

  @Override
  public Integer getMaxIndexId(Integer topicId) {
    Integer maxIndexId = baseMapper.getMaxIndexId(topicId);
    return maxIndexId == null ? 0 : maxIndexId;
  }

  @Override
  public Boolean removeByIndexId(Integer topicId, Integer indexId) {
    return this.remove(
        new LambdaQueryWrapper<TopicFilterMarkPO>()
            .eq(TopicFilterMarkPO::getTopicId, topicId)
            .eq(TopicFilterMarkPO::getIndexId, indexId));
  }
}
