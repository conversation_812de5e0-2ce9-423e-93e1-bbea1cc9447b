package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.ProcessTreeKpiMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.ProcessTreeKpiPo;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 任务 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-20
 */
@Primary
@Service
public class ProcessTreeKpiServiceImpl extends ServiceImpl<ProcessTreeKpiMapper, ProcessTreeKpiPo>
    implements IProcessTreeKpiService {

  @Override
  public List<ProcessTreeKpiPo> getList(Integer topicId) {
    return baseMapper.selectList(
        new LambdaQueryWrapper<ProcessTreeKpiPo>()
            .eq(ProcessTreeKpiPo::getTopicId, topicId)
            .eq(ProcessTreeKpiPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
