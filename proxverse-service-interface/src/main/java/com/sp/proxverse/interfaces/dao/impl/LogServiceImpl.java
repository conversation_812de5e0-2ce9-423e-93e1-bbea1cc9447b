package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sp.proxverse.common.model.base.PageResp;
import com.sp.proxverse.common.model.dto.domain.Admin;
import com.sp.proxverse.common.model.page.PageRespList;
import com.sp.proxverse.common.model.po.AuditLogPo;
import com.sp.proxverse.common.model.vo.AuditLogReq;
import com.sp.proxverse.common.model.vo.AuditLogRes;
import com.sp.proxverse.common.model.vo.CreateAuditLog;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.interfaces.dao.service.IAdminService;
import com.sp.proxverse.interfaces.dao.service.LogService;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/11/6 14:04
 */
@Slf4j
@Service
public class LogServiceImpl implements LogService {

  @Autowired AuditLogServiceImpl auditLogService;

  @Autowired IAdminService adminService;

  @Value("#{'${prx.admin.auditLog.hideRoles:SUPER}'.split(',')}")
  private List<String> auditLogHideRoles;

  @Override
  public void saveAuditLog(CreateAuditLog createAuditLog) {
    try {
      AuditLogPo auditLogPo = new AuditLogPo();
      auditLogPo.setPath(createAuditLog.getPath());
      auditLogPo.setUserName(createAuditLog.getUserName());
      auditLogPo.setUserId(createAuditLog.getUserId());
      auditLogPo.setIp(createAuditLog.getIp());
      auditLogPo.setTenantId(createAuditLog.getTenantId());
      auditLogPo.setParameter(createAuditLog.getParameterStr());
      auditLogService.save(auditLogPo);
    } catch (Exception e) {
      log.error("saveAuditLog save error path{}", createAuditLog.getPath(), e);
    }
  }

  @Override
  public void saveAuditLog(List<CreateAuditLog> createAuditLogs) {
    if (CollectionUtils.isEmpty(createAuditLogs)) {
      return;
    }
    try {
      ArrayList<AuditLogPo> auditLogPos = new ArrayList<>();
      for (CreateAuditLog createAuditLog : createAuditLogs) {
        AuditLogPo auditLogPo = new AuditLogPo();
        auditLogPo.setPath(createAuditLog.getPath());
        auditLogPo.setUserName(createAuditLog.getUserName());
        auditLogPo.setUserId(createAuditLog.getUserId());
        auditLogPo.setIp(createAuditLog.getIp());
        auditLogPo.setTenantId(createAuditLog.getTenantId());
        auditLogPo.setParameter(createAuditLog.getParameterStr());
        auditLogPo.setCreateTime(new Date());
        auditLogPos.add(auditLogPo);
      }
      auditLogService.saveBatch(auditLogPos);
    } catch (Exception e) {
      log.error("saveAuditLog save error", e);
    }
  }

  @Override
  public Boolean clearAuditLog(Integer days) {
    // 获取当前日期
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());

    if (days != null && days != 0) {
      calendar.add(Calendar.DAY_OF_YEAR, -days);
    }
    Date tenDaysAgo = calendar.getTime();
    return auditLogService.remove(
        new LambdaQueryWrapper<AuditLogPo>().lt(AuditLogPo::getCreateTime, tenDaysAgo));
  }

  @Override
  public PageResp getAuditLog(AuditLogReq logReq) {

    Page<AuditLogPo> page = new Page<>(logReq.getPageNum(), logReq.getPageSize());
    LambdaQueryWrapper<AuditLogPo> wrapper = new LambdaQueryWrapper<AuditLogPo>();

    Set<Integer> userIds = getHideRoleUser(logReq);
    if (!userIds.isEmpty()) {
      wrapper.notIn(AuditLogPo::getUserId, userIds);
    }
    wrapper.like(
        StringUtils.isNotBlank(logReq.getInterfacePath()),
        AuditLogPo::getPath,
        logReq.getInterfacePath());
    wrapper.like(
        StringUtils.isNotBlank(logReq.getParam()), AuditLogPo::getParameter, logReq.getParam());
    wrapper.like(
        StringUtils.isNotBlank(logReq.getUserName()),
        AuditLogPo::getUserName,
        logReq.getUserName());
    wrapper.eq(
        logReq.getUserId() != null && logReq.getUserId() > 0,
        AuditLogPo::getUserId,
        logReq.getUserId());

    if (logReq.getStartTimeDate() != null) {
      Calendar startCalendar = Calendar.getInstance();
      startCalendar.setTime(logReq.getStartTimeDate());
      startCalendar.set(Calendar.HOUR_OF_DAY, 0);
      startCalendar.set(Calendar.MINUTE, 0);
      startCalendar.set(Calendar.SECOND, 0);
      wrapper.ge(AuditLogPo::getCreateTime, startCalendar.getTime());
    }
    if (logReq.getEndTimeDate() != null) {
      Calendar endCalendar = Calendar.getInstance();
      endCalendar.setTime(logReq.getEndTimeDate());
      endCalendar.set(Calendar.HOUR_OF_DAY, 23);
      endCalendar.set(Calendar.MINUTE, 59);
      endCalendar.set(Calendar.SECOND, 59);
      wrapper.le(AuditLogPo::getCreateTime, endCalendar.getTime());
    }
    long count = auditLogService.count(wrapper);
    wrapper.orderByDesc(AuditLogPo::getId);

    Page<AuditLogPo> paged = auditLogService.page(page, wrapper);
    Map<Integer, Admin> userNameMap =
        adminService.getUserInfoByIds(
            paged.getRecords().stream()
                .map(AuditLogPo::getUserId)
                .distinct()
                .collect(Collectors.toList()));
    List<AuditLogRes> auditLogList = new ArrayList<>();
    for (AuditLogPo record : paged.getRecords()) {
      AuditLogRes auditLogRes = new AuditLogRes();
      BeanUtils.copyProperties(record, auditLogRes);
      auditLogRes.setCreateTime(DateTimeUtil.date2String(record.getCreateTime()));
      auditLogRes.setName(userNameMap.get(record.getUserId()).getName());
      auditLogList.add(auditLogRes);
    }

    PageRespList<AuditLogRes> pageRespList = new PageRespList<>();
    pageRespList.setTotal(count);
    pageRespList.setList(auditLogList);
    pageRespList.setPageNum(logReq.getPageNum());
    pageRespList.setPageSize(logReq.getPageSize());
    return PageResp.success(pageRespList);
  }

  private Set<Integer> getHideRoleUser(AuditLogReq logReq) {
    Set<String> roleNames = new HashSet<>();
    if (CollectionUtils.isNotEmpty(this.auditLogHideRoles)) {
      roleNames.addAll(this.auditLogHideRoles);
    }
    if (CollectionUtils.isNotEmpty(logReq.getAuditLogHideRoles())) {
      roleNames.addAll(logReq.getAuditLogHideRoles());
    }

    if (roleNames.isEmpty()) {
      return new HashSet<>();
    }
    List<Admin> superAdmins =
        adminService.list(new LambdaQueryWrapper<Admin>().in(Admin::getLevel, roleNames));
    return superAdmins.stream().map(Admin::getId).collect(Collectors.toSet());
  }
}
