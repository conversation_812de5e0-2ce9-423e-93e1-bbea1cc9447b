package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.ActionFlowPo;
import com.sp.proxverse.common.model.vo.action.ActionFlowListResVo;
import com.sp.proxverse.common.model.vo.action.ActionFlowParamResVo;
import com.sp.proxverse.common.model.vo.action.ActionStartParamResVo;
import com.sp.proxverse.common.model.vo.action.CheckReleaseActionFlowResVo;
import com.sp.proxverse.common.model.vo.action.CreateActionFlowReqVo;
import com.sp.proxverse.common.model.vo.action.FilterFunctionResVo;
import com.sp.proxverse.common.model.vo.action.GetActionFlowListReqVo;
import com.sp.proxverse.common.model.vo.action.GetRunFlowActionReqVo;
import com.sp.proxverse.common.model.vo.action.ReleaseActionFlowReqVo;
import com.sp.proxverse.common.model.vo.action.RunFlowActionResVo;
import com.sp.proxverse.common.model.vo.action.SaveActionFlowListReqVo;
import com.sp.proxverse.common.model.vo.action.SaveActionNodeVo;
import com.sp.proxverse.common.model.vo.action.SupportActionResVo;
import com.sp.proxverse.common.model.vo.action.UpdateActionFlowNameReqVo;
import com.sp.proxverse.common.model.vo.action.UpdateActionStartParamReqVo;
import java.util.List;

/**
 * 动作流程 服务类
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IActionFlowService extends IService<ActionFlowPo> {

  /**
   * 创建一个动作流程
   *
   * @param runActionReqVo
   * @return
   */
  Integer createActionFlow(CreateActionFlowReqVo runActionReqVo);

  /**
   * 更新执行动作名称
   *
   * @param updateActionFlowNameReqVo
   * @return
   */
  Boolean updateActionFlowName(UpdateActionFlowNameReqVo updateActionFlowNameReqVo);

  /**
   * 删除
   *
   * @param topicId
   * @return
   */
  Boolean removeActionFlow(Integer topicId);

  /**
   * 获取支持的Action
   *
   * @return
   */
  List<SupportActionResVo> getSupportAction();

  /**
   * 获取启动参数
   *
   * @param topicId
   * @return
   */
  List<ActionStartParamResVo> getActionStartParam(Integer topicId);

  /**
   * 更新动作初始化参数
   *
   * @param updateActionStartParamReqVo
   * @return
   */
  Boolean updateActionStartParam(UpdateActionStartParamReqVo updateActionStartParamReqVo);

  /**
   * 创建动作流列表
   *
   * @param saveActionFlowListReqVo
   * @return
   */
  Integer saveActionFlowList(SaveActionFlowListReqVo saveActionFlowListReqVo);

  /**
   * 获取动作流列表信息
   *
   * @param getActionFlowListReqVo
   * @return
   */
  List<ActionFlowListResVo> getActionFlowList(GetActionFlowListReqVo getActionFlowListReqVo);

  /**
   * 根据topicId获取流信息
   *
   * @param topicId
   * @return
   */
  ActionFlowPo getActionFlowByTopicId(Integer topicId);

  List<ActionFlowPo> getActionFlowByTopicIds(List<Integer> topicIds);

  /**
   * 获取当前动作流的code
   *
   * @param topicId
   * @return
   */
  Integer getCurrentActionCode(Integer topicId);

  /**
   * 保存node节点信息
   *
   * @param saveActionNodeVo
   * @return
   */
  Integer saveActionNode(SaveActionNodeVo saveActionNodeVo);

  /**
   * 获取node节点信息
   *
   * @param topicId
   * @return
   */
  SaveActionNodeVo getActionNode(String topicId);

  /**
   * 获取动作流参数
   *
   * @param topicId
   * @return
   */
  List<ActionFlowParamResVo> getActionFlowParam(Integer topicId);

  /**
   * 获取获取动作流的节点
   *
   * @param getRunFlowActionReqVo
   * @return
   */
  RunFlowActionResVo getRunFlowAction(GetRunFlowActionReqVo getRunFlowActionReqVo);

  /**
   * 发布执行动作
   *
   * @param getRunFlowActionReqVo
   * @return
   */
  boolean releaseActionFlow(GetRunFlowActionReqVo getRunFlowActionReqVo);

  /**
   * 获取过滤函数类型
   *
   * @return
   */
  FilterFunctionResVo getFilterFunction();

  /**
   * 检查发布
   *
   * @param releaseActionFlowReqVo
   * @return
   */
  CheckReleaseActionFlowResVo checkReleaseActionFlow(ReleaseActionFlowReqVo releaseActionFlowReqVo);
}
