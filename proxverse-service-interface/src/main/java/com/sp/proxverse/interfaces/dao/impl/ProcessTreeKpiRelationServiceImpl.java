package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.ProcessTreeKpiRelationMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.ProcessTreeKpiRelationPo;
import com.sp.proxverse.interfaces.dao.service.IProcessTreeKpiRelationService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 流程图Kpi关系 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-20
 */
@Primary
@Service
public class ProcessTreeKpiRelationServiceImpl
    extends ServiceImpl<ProcessTreeKpiRelationMapper, ProcessTreeKpiRelationPo>
    implements IProcessTreeKpiRelationService {

  @Override
  public List<ProcessTreeKpiRelationPo> getList(List<Integer> idList) {
    return baseMapper.selectList(
        new LambdaQueryWrapper<ProcessTreeKpiRelationPo>()
            .in(ProcessTreeKpiRelationPo::getProcessTreeKpiId, idList)
            .eq(ProcessTreeKpiRelationPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
