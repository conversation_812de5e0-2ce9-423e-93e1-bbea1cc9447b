package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.BusinessTopicMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.SnapshotTypeEnum;
import com.sp.proxverse.common.model.dto.TopicDataDTO;
import com.sp.proxverse.common.model.po.BusinessTopicPO;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicService;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class BusinessTopicServiceImpl extends ServiceImpl<BusinessTopicMapper, BusinessTopicPO>
    implements BusinessTopicService {
  @Override
  public List<TopicDataDTO> getTopicDataDtoByModelId(Integer modelId, List<Integer> topicIdList) {

    return baseMapper.getTopicDataDtoByModelId(modelId, topicIdList);
  }

  @Override
  public BusinessTopicPO getOneById(Integer id) {
    return this.getOne(
        new LambdaQueryWrapper<BusinessTopicPO>()
            .eq(BusinessTopicPO::getId, id)
            .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<BusinessTopicPO> listSubTopic(Integer topicId) {
    return baseMapper.selectList(
        new LambdaQueryWrapper<BusinessTopicPO>()
            .eq(BusinessTopicPO::getParentId, topicId)
            .eq(BusinessTopicPO::getSnapshotFlag, SnapshotTypeEnum.NOT.getValue())
            .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<BusinessTopicPO> listSubTopicByParnetIdList(List<Integer> topicIds) {
    if (CollectionUtils.isEmpty(topicIds)) {
      return new ArrayList<>();
    }
    return baseMapper.selectList(
        new LambdaQueryWrapper<BusinessTopicPO>()
            .in(BusinessTopicPO::getParentId, topicIds)
            .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<BusinessTopicPO> listByIdList(List<Integer> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return new ArrayList<>();
    }
    return this.list(
        new LambdaQueryWrapper<BusinessTopicPO>()
            .in(BusinessTopicPO::getId, ids)
            .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<BusinessTopicPO> listSnapshotDataByTopicIds(List<Integer> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return new ArrayList<>();
    }
    return this.list(
        new LambdaQueryWrapper<BusinessTopicPO>()
            .eq(BusinessTopicPO::getSnapshotFlag, SnapshotTypeEnum.SNAP.getValue())
            .in(BusinessTopicPO::getSnapshotParentId, ids)
            .eq(BusinessTopicPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
