package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.TransformationRunLogPo;
import com.sp.proxverse.common.model.vo.api.RunTransformationReqVo;

/**
 * 运行日志 服务类
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
public interface ITransformationRunLogService extends IService<TransformationRunLogPo> {

  /**
   * 创建一个数据转换记录
   *
   * @param runTransformationReqVo
   * @param successFlag 1：运行中，2：完成，3：失败
   * @param sqlContent
   * @param transformationName
   * @param tenantId
   * @return
   */
  Integer creationOneLog(
      RunTransformationReqVo runTransformationReqVo,
      Integer successFlag,
      String sqlContent,
      String transformationName,
      Integer tenantId);

  /**
   * 更新
   *
   * @param logId
   * @param timeScope
   * @param successFlag
   * @param errorMessage
   */
  public void updateOneLog(Integer logId, Long timeScope, Integer successFlag, String errorMessage);
}
