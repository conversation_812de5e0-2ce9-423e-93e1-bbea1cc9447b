package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.SignalConditionMapper;
import com.sp.proxverse.common.model.po.SignalConditionPO;
import com.sp.proxverse.interfaces.dao.service.SignalConditionService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class SignalConditionServiceImpl
    extends ServiceImpl<SignalConditionMapper, SignalConditionPO>
    implements SignalConditionService {
  @Override
  public List<SignalConditionPO> getListByOptimizationId(Integer optimizationId) {
    return this.list(
        new LambdaQueryWrapper<SignalConditionPO>()
            .eq(SignalConditionPO::getOptimizationSignalId, optimizationId)
            .eq(SignalConditionPO::getDeleted, 0));
  }

  @Override
  public List<SignalConditionPO> getListByOptimizationIds(List<Integer> optimizationIds) {
    return this.list(
        new LambdaQueryWrapper<SignalConditionPO>()
            .in(SignalConditionPO::getOptimizationSignalId, optimizationIds)
            .eq(SignalConditionPO::getDeleted, 0));
  }

  @Override
  public void removeByOptimizationId(Integer optimizationId) {
    this.remove(
        new LambdaQueryWrapper<SignalConditionPO>()
            .eq(SignalConditionPO::getOptimizationSignalId, optimizationId));
  }
}
