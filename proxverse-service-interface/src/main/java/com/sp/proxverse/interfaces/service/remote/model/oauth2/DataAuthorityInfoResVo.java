package com.sp.proxverse.interfaces.service.remote.model.oauth2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-04-04 5:47 下午
 */
@Data
@ApiModel("数据权限信息")
public class DataAuthorityInfoResVo {

  @ApiModelProperty(value = "ID", required = true)
  private Integer id;

  @ApiModelProperty(value = "数据源Id", required = true)
  private String dataId;

  @ApiModelProperty(value = "数据类型，1：topic", required = true)
  private Integer dataType;

  @ApiModelProperty(value = "创建时间")
  private LocalDateTime createTime;
}
