package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.ProcessTreeKpiRelationPo;
import java.util.List;

/**
 * 流程图Kpi关系 服务类
 *
 * <AUTHOR>
 * @since 2022-12-20
 */
public interface IProcessTreeKpiRelationService extends IService<ProcessTreeKpiRelationPo> {

  List<ProcessTreeKpiRelationPo> getList(List<Integer> idList);
}
