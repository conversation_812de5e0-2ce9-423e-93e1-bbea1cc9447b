package com.sp.proxverse.interfaces.service.remote.model.oauth2;

import com.sp.proxverse.common.model.enums.AuthorityClientEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-04-02 12:30 下午
 */
@ApiModel("创建用户")
@Data
public class CreateUserReqVo {

  @ApiModelProperty(value = "用户名", required = true)
  @NotNull(message = "用户名不能为空")
  private String userName;

  @ApiModelProperty(value = "姓名")
  private String name;

  @ApiModelProperty(value = "test use")
  private Integer id;

  @ApiModelProperty(value = "密码", required = true)
  @NotNull(message = "用户密码不能为空")
  private String password;

  @ApiModelProperty(value = "用户角色,USER,ANALYST,ROOT", required = true)
  private String level;

  private AuthorityClientEnum userType;

  private String ssoId;

  public AuthorityClientEnum getUserType() {
    if (this.userType == null) {
      return AuthorityClientEnum.LOCAL;
    }
    return userType;
  }

  private List<Integer> roleList;

  private List<Integer> userGroupList;

  public List<Integer> getRoleList() {
    return roleList == null ? new ArrayList<>() : roleList;
  }

  public List<Integer> getUserGroupList() {
    return userGroupList == null ? new ArrayList<>() : userGroupList;
  }
}
