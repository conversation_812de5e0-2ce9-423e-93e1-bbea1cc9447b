package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.SimulationEventAttrMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.SimulationEventAttrPo;
import com.sp.proxverse.interfaces.dao.service.ISimulationEventAttrService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 流程仿真事件属性 服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
@Primary
@Service
@DS("sp_process")
public class SimulationEventAttrServiceImpl
    extends ServiceImpl<SimulationEventAttrMapper, SimulationEventAttrPo>
    implements ISimulationEventAttrService {

  public List<SimulationEventAttrPo> selectBySimulationId(Integer programmeId) {
    return this.list(
        new LambdaQueryWrapper<SimulationEventAttrPo>()
            .eq(SimulationEventAttrPo::getProgrammeId, programmeId)
            .eq(SimulationEventAttrPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<SimulationEventAttrPo> getList(Integer simulationId) {
    return baseMapper.selectList(
        new LambdaQueryWrapper<SimulationEventAttrPo>()
            .eq(SimulationEventAttrPo::getSimulationId, simulationId)
            .eq(SimulationEventAttrPo::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
