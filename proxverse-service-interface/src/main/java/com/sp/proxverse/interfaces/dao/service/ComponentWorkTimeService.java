package com.sp.proxverse.interfaces.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sp.proxverse.common.model.po.ComponentWorkTimePO;
import java.util.List;

public interface ComponentWorkTimeService extends IService<ComponentWorkTimePO> {

  List<ComponentWorkTimePO> queryWorkTimeList(Integer componentId);

  List<ComponentWorkTimePO> queryWorkTimeList(List<Integer> componentIdList);
}
