package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.RoleMapper;
import com.sp.proxverse.common.model.po.RolePO;
import com.sp.proxverse.interfaces.dao.service.RoleService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@DS("oauth2")
@Primary
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RolePO> implements RoleService {}
