package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.AuditLogMapper;
import com.sp.proxverse.common.model.po.AuditLogPo;
import com.sp.proxverse.interfaces.dao.service.IAuditLogService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 用户请求路径 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
@Primary
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLogPo>
    implements IAuditLogService {}
