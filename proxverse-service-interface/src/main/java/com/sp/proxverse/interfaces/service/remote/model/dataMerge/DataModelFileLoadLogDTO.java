package com.sp.proxverse.interfaces.service.remote.model.dataMerge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
@ApiModel
public class DataModelFileLoadLogDTO {

  @Tolerate
  public DataModelFileLoadLogDTO() {
    // comment empty
  }

  @ApiModelProperty(value = "文件Id")
  private Integer fileId;

  @ApiModelProperty(value = "数据表名称")
  private String fileName;

  @ApiModelProperty(value = "数据类型：10：文件导入，20：数据提取")
  private Integer fileType;

  @ApiModelProperty(value = "更新时间")
  private String time;

  @ApiModelProperty(value = "数据数量")
  private Integer dataCount;
}
