package com.sp.proxverse.interfaces.common.util;

import java.io.UnsupportedEncodingException;
import java.util.UUID;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @create 2021-12-17 1:40 下午
 */
public class CodeUtils {

  /**
   * 获取UUID
   *
   * @return
   */
  public static String createUUID() {
    String uuid = UUID.randomUUID().toString().replace("-", "");
    return uuid;
  }

  public static String encrypt3ToMD5(String str) {
    String md5 = "  ";
    try {
      md5 = DigestUtils.md5DigestAsHex(str.getBytes("utf-8"));
    } catch (UnsupportedEncodingException e) {
      e.printStackTrace();
    }
    return md5;
  }
}
