package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.DataTaskChildMapper;
import com.sp.proxverse.common.model.po.DataTaskChildPo;
import com.sp.proxverse.interfaces.dao.service.IDataTaskChildService;
import org.springframework.stereotype.Service;

/**
 * 数据任务的子信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Service
@DS("sp_process")
public class DataTaskChildServiceImpl extends ServiceImpl<DataTaskChildMapper, DataTaskChildPo>
    implements IDataTaskChildService {}
