package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.mapper.PermissionMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.UserLevelEnum;
import com.sp.proxverse.common.model.oauth2.UserDetailsToken;
import com.sp.proxverse.common.model.po.PermissionPO;
import com.sp.proxverse.interfaces.dao.service.PermissionService;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@DS("oauth2")
@Primary
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, PermissionPO>
    implements PermissionService {

  @Autowired private UserInfoUtil userInfoUtil;

  @Value(
      "${prx.permission.globalConfig:1000,1002,2000,2001,2002,2004,2005,2100,2300,3000,4000,4001,4002,4003,4004,4005,5000,5001,5002,5100,6000,6001,6004,6003,6002,6005,2200,5200,2003,2006}")
  private String globalPermissionConfig;

  @Override
  public List<PermissionPO> getListNoHidden() {
    List<Integer> indexTagConfig = this.getIndexTagConfig();
    return baseMapper.selectList(
        new LambdaQueryWrapper<PermissionPO>()
            .eq(PermissionPO::getHidden, 0)
            .in(
                CollectionUtils.isNotEmpty(indexTagConfig),
                PermissionPO::getIndexTag,
                indexTagConfig)
            .eq(PermissionPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<PermissionPO> listAll() {
    return list(
        new LambdaQueryWrapper<PermissionPO>()
            .eq(PermissionPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .orderByAsc(PermissionPO::getId));
  }

  @Override
  public List<Integer> getIndexTagConfig() {
    UserDetailsToken userInfo = userInfoUtil.getUserInfo();
    if (UserLevelEnum.isSuper(userInfo.getLevel())) {
      return Collections.emptyList();
    }
    String[] split = globalPermissionConfig.split(",");
    return Stream.of(split).map(Integer::new).collect(Collectors.toList());
  }

  @Override
  public List<PermissionPO> list() {
    List<Integer> indexTagConfig = this.getIndexTagConfig();
    return baseMapper.selectList(
        new LambdaQueryWrapper<PermissionPO>()
            .in(
                CollectionUtils.isNotEmpty(indexTagConfig),
                PermissionPO::getIndexTag,
                indexTagConfig));
  }

  @Override
  public List<PermissionPO> listByIds(Collection<? extends Serializable> idList) {
    if (CollectionUtils.isEmpty(idList)) {
      return new ArrayList<>();
    }
    List<Integer> indexTagConfig = this.getIndexTagConfig();
    return baseMapper.selectList(
        new LambdaQueryWrapper<PermissionPO>()
            .in(
                CollectionUtils.isNotEmpty(indexTagConfig),
                PermissionPO::getIndexTag,
                indexTagConfig)
            .in(PermissionPO::getId, idList));
  }
}
