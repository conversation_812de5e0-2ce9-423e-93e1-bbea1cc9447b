package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.NewsheetTemplateMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.po.NewsheetTemplatePO;
import com.sp.proxverse.interfaces.dao.service.NewsheetTemplateService;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class NewsheetTemplateServiceImpl
    extends ServiceImpl<NewsheetTemplateMapper, NewsheetTemplatePO>
    implements NewsheetTemplateService {

  @Override
  public NewsheetTemplatePO getByName(String name) {
    List<NewsheetTemplatePO> list =
        list(
            new LambdaQueryWrapper<NewsheetTemplatePO>()
                .eq(NewsheetTemplatePO::getName, name)
                .eq(NewsheetTemplatePO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    return list.isEmpty() ? null : list.get(0);
  }

  @Override
  public boolean checkNameDuplication(Integer templateId, String name) {
    return count(
            new LambdaQueryWrapper<NewsheetTemplatePO>()
                .ne(templateId != null, NewsheetTemplatePO::getId, templateId)
                .eq(NewsheetTemplatePO::getName, name))
        > 0;
  }

  @Override
  public boolean removeTemplate(Integer templateId) {
    return remove(
        new LambdaQueryWrapper<NewsheetTemplatePO>().eq(NewsheetTemplatePO::getId, templateId));
  }
}
