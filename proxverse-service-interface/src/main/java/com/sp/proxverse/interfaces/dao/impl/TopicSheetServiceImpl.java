package com.sp.proxverse.interfaces.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.TopicSheetMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dto.DataModelFileDTO;
import com.sp.proxverse.common.model.dto.SheetFileDTO;
import com.sp.proxverse.common.model.dto.TopicFileDTO;
import com.sp.proxverse.common.model.po.TopicSheetPO;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.TopicSheetService;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class TopicSheetServiceImpl extends ServiceImpl<TopicSheetMapper, TopicSheetPO>
    implements TopicSheetService {

  @Autowired private BusinessTopicDataService businessTopicDataService;

  @Override
  public DataModelFileDTO getTopicSheetFileBySheetId(Integer sheetId) {
    TopicSheetPO sheetPO = baseMapper.selectById(sheetId);
    if (Objects.isNull(sheetPO)) {
      return null;
    }

    TopicFileDTO topicFileByTopicId =
        businessTopicDataService.getTopicFileByTopicId(sheetPO.getTopicId());

    if (Objects.isNull(topicFileByTopicId)) {
      return null;
    }

    return DataModelFileDTO.builder()
        .dataModelId(topicFileByTopicId.getDataModelId())
        .fileId(topicFileByTopicId.getFileId())
        .topicId(sheetPO.getTopicId())
        .fileName(topicFileByTopicId.getFilename())
        .build();
  }

  @Override
  public List<SheetFileDTO> getTopicSheetFileBySheetIdList(List<Integer> ids) {
    return baseMapper.getTopicSheetFileBySheetIdList(ids);
  }

  @Override
  public List<TopicSheetPO> getList(Integer topicId) {
    return baseMapper.selectList(
        new LambdaQueryWrapper<TopicSheetPO>()
            .eq(TopicSheetPO::getTopicId, topicId)
            .eq(TopicSheetPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }
}
