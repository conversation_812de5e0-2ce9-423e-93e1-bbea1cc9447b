FROM openjdk:8
ADD sp-data-exec.jar sp-data-exec.jar
ADD libs libs

RUN echo "Asia/shanghai" > /etc/timezone

ENTRYPOINT ["java","-Dnacos.logging.config=sp-logback.xml" , "-Dlogging.config=classpath:sp-logback.xml","-Djava.ext.dirs=libs","-Xmx16384M","-Xms16384M","-XX:MaxMetaspaceSize=512M","-XX:MetaspaceSize=512M","-XX:+PrintGCDetails","-XX:+PrintGCTimeStamps","-XX:+PrintHeapAtGC","-Xloggc://opt/GC.log","-XX:+HeapDumpOnOutOfMemoryError","-XX:HeapDumpPath=/opt/heapDump.hprof","-jar","sp-data-exec.jar","--spring.profiles.active=prod"]