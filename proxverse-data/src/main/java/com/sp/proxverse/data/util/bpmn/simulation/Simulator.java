package com.sp.proxverse.data.util.bpmn.simulation;

import static org.apache.spark.sql.catalyst.util.DateTimeConstants.HOURS_PER_DAY;
import static org.apache.spark.sql.catalyst.util.DateTimeConstants.MINUTES_PER_HOUR;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.util.HadoopUtil;
import com.sp.proxverse.data.util.bpmn.model.BpmnRoot;
import com.sp.proxverse.data.util.bpmn.simulation.listener.ProcessListenerBus;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

@Slf4j
public class Simulator {

  public BpmnRoot bpmnRoot;
  List<ResourcePool> resourcePools;
  public Map<String, ResourcePool> resourcePoolsMapping;
  public List<CaseInterval> caseIntervals;

  public ConcurrentHashMap<String, ProcessRun> runningProcessMap = new ConcurrentHashMap<>();

  public SimulatorMetrics simulatorMetrics;

  public ProcessListenerBus listenerBus;

  long deadLine;
  public String simulationId;

  public String simulationDataDir;

  public static ExecutorService SIMULATION_RUN_THREAD_POOL = Executors.newFixedThreadPool(1);

  public void init(
      String id,
      BpmnRoot bpmnRoot,
      List<ResourcePool> resourcePools,
      List<CaseInterval> caseIntervals,
      int runningDays,
      ProcessListenerBus listenerBus,
      String simulationDataDir) {

    HadoopUtil.createDir(simulationDataDir);
    deadLine = MINUTES_PER_HOUR * HOURS_PER_DAY * runningDays;
    Map<String, ResourcePool> nameToResourcePool =
        resourcePools.stream()
            .collect(Collectors.toMap(ResourcePool::getName, resourcePool -> resourcePool));

    this.resourcePools = resourcePools;
    this.resourcePoolsMapping = nameToResourcePool;
    this.caseIntervals = caseIntervals;
    this.simulatorMetrics = new SimulatorMetrics();
    this.bpmnRoot = bpmnRoot;

    this.simulationId = id;
    listenerBus.init(id, simulationDataDir);
    this.listenerBus = listenerBus;
    this.simulationDataDir = simulationDataDir;
  }

  /** 模拟每一分钟的运行 */
  public void run() {
    long index = 0L;

    for (long currentTime = 0; currentTime < deadLine; currentTime++) {
      // TODO feng.pan 2024/6/8 想办法清除前置的无效时间消耗
      StopWatch stopWatch = new StopWatch("simulator 性能监控");
      index += generateCases(currentTime, index);
      if (runningProcessMap.isEmpty()) {
        continue;
      }

      generateResources(currentTime);

      stopWatch.start("runProcesses() ");
      final long finalCurrentTime = currentTime;
      List<CompletableFuture<SimulatorResult>> completableFutureList =
          runningProcessMap.entrySet().stream()
              .map(entry -> runProcessAsync(entry.getKey(), entry.getValue(), finalCurrentTime))
              .collect(Collectors.toList());
      stopWatch.stop();

      stopWatch.start("waitForAllFuturesAndProcessResults() ");
      waitForAllFuturesAndProcessResults(completableFutureList);
      stopWatch.stop();
      log.info("run() cost time, detail:{} ", stopWatch.prettyPrint());
    }

    simulatorMetrics.submitCase = index;
    simulatorMetrics.summary();
    listenerBus.close();
  }

  private int generateCases(long currentTime, long startIndex) {
    int newCase = 0;
    for (CaseInterval caseInterval : caseIntervals) {
      newCase += caseInterval.genCaseIfNeed(currentTime);
    }
    for (int i = 0; i < newCase; i++) {
      String caseId = "case_" + (startIndex + i);
      runningProcessMap.put(
          caseId, new ProcessRun(caseId, bpmnRoot, deadLine, listenerBus, currentTime));
    }
    return newCase;
  }

  private void generateResources(long currentTime) {
    for (ResourcePool resourcePool : resourcePools) {
      resourcePool.genResourceIfNeed(currentTime);
    }
  }

  private CompletableFuture<SimulatorResult> runProcessAsync(
      String caseId, ProcessRun processRun, long currentTime) {
    return CompletableFuture.supplyAsync(
            () -> {
              try {
                processRun.runProcess(caseId, currentTime);
                return new SimulatorResult(processRun.isEndFlag(), processRun.getCaseId());
              } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new CompletionException(e);
              }
            },
            SIMULATION_RUN_THREAD_POOL)
        .exceptionally(
            throwable -> {
              // Handle exception and return a default result
              handleExecutionException(throwable);
              return new SimulatorResult(false, processRun.getCaseId());
            });
  }

  private void waitForAllFuturesAndProcessResults(
      List<CompletableFuture<SimulatorResult>> futures) {
    try {
      StopWatch stopWatch = new StopWatch("waitForAllFuturesAndProcessResults 性能监控");

      CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

      stopWatch.start("for() ");
      Set<String> caseIdSimulatorResultSet =
          futures
              .parallelStream()
              .map(CompletableFuture::join)
              .filter(SimulatorResult::isFinish)
              .map(SimulatorResult::getCaseId)
              .collect(Collectors.toSet());
      stopWatch.stop();

      stopWatch.start("processResults ");
      processResults(caseIdSimulatorResultSet);
      stopWatch.stop();

      log.info(
          "waitForAllFuturesAndProcessResults() cost time, detail:{} ", stopWatch.prettyPrint());
    } catch (Exception e) {
      handleExecutionException(e);
    }
  }

  private void processResults(Set<String> caseIdSimulatorResultSet) {
    List<String> caseIdSet = new ArrayList<>(caseIdSimulatorResultSet);
    for (String caseId : caseIdSet) {
      runningProcessMap.remove(caseId);
      simulatorMetrics.getFinishCase().incrementAndGet();
    }
  }

  private void handleExecutionException(Throwable e) {
    Throwable cause = e.getCause();
    if (cause instanceof RuntimeException) {
      throw (RuntimeException) cause;
    } else {
      log.info("handleExecutionException error:", cause);
      throw new BizException(5000, cause.getMessage());
    }
  }
}
