package com.sp.proxverse.data.util.bpmn.simulation;

import com.sp.proxverse.data.util.bpmn.Event;

public interface ProcessListener {

  default void init(String simulationId, String baseDir) {}

  default void close() {}

  default void onCaseStart(String caseId, Event element, long currentTime) {}

  default void onCaseFinish(String caseId, Event element, long currentTime) {}

  default void onEventStart(String caseId, Event element, long currentTime) {}

  default void onCaseFailed(String caseId, Event element, long currentTime) {}

  default void onCasePending(String caseId, Event element, long currentTime) {}

  default void onEventFinish(String caseId, Event element, long currentTime) {}

  default void onEventPending(String caseId, Event element, long currentTime) {}
}
