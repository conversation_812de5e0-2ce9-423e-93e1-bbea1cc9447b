package com.sp.proxverse.data.util.bpmn.simulation.listener;

import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.data.util.bpmn.simulation.ProcessListener;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ProcessListenerFactory {

  @Value(
      "${proxverse.conf.simulation.listeners:com.sp.proxverse.data.util.bpmn.simulation.listener.DebugProcessListener,com.sp.proxverse.data.util.bpmn.simulation.FileWriterProcessListener}")
  private String listeners;

  public ProcessListenerBus createListener() {
    String[] split = listeners.split(",");
    ProcessListenerBus processListenerBus = new ProcessListenerBus();
    Arrays.stream(split)
        .forEach(
            clazz -> {
              try {
                processListenerBus.register((ProcessListener) (Class.forName(clazz).newInstance()));
              } catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
                log.info("createListener error:", e);
                throw new BizException(5000, e.getMessage());
              }
            });
    return processListenerBus;
  }
}
