package com.sp.proxverse.data.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.model.dict.Consts;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.DimensionEnum;
import com.sp.proxverse.common.model.dict.FileActiveTypeEnum;
import com.sp.proxverse.common.model.dict.RuleEnum;
import com.sp.proxverse.common.model.dto.Lable;
import com.sp.proxverse.common.model.dto.QueryColumnDataByColumnIdDTO;
import com.sp.proxverse.common.model.dto.QueryColumnDataByColumnRespDTO;
import com.sp.proxverse.common.model.dto.QueryColumnDataRuleDTO;
import com.sp.proxverse.common.model.dto.QueryColumnsDimensionDTO;
import com.sp.proxverse.common.model.dto.QueryDataByColumnsDTO;
import com.sp.proxverse.common.model.dto.QueryDataByColumnsRespDTO;
import com.sp.proxverse.common.model.dto.QueryDataRuleDTO;
import com.sp.proxverse.common.model.dto.QueryFileDimensionDTO;
import com.sp.proxverse.common.model.dto.QueryTableDataCountDTO;
import com.sp.proxverse.common.model.dto.VariantTableDataDTO;
import com.sp.proxverse.common.model.po.DataModelFileMasterPO;
import com.sp.proxverse.common.model.po.DataModelFilePO;
import com.sp.proxverse.common.model.po.FileFieldPO;
import com.sp.proxverse.data.service.cache.CacheService;
import com.sp.proxverse.data.service.datahouse.DataHouseService;
import com.sp.proxverse.data.service.pql.PQLCatalogManager;
import com.sp.proxverse.data.util.DatahouseUtil;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.DataModelFileMasterService;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.interfaces.dao.service.FileFieldService;
import com.sp.proxverse.interfaces.service.biz.CommonBizService;
import com.sp.proxverse.interfaces.service.datahouse.TimelyModelingService;
import com.sp.proxverse.interfaces.service.remote.model.QueryKnowledgeDataDTO;
import com.sp.proxverse.interfaces.service.remote.model.QueryKnowledgeDataRespDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ApiService {

  @Autowired private DataHouseService dataHouseService;

  @Autowired private BusinessTopicDataService businessTopicDataService;

  @Autowired private FileFieldService fileFieldService;

  @Autowired private DataModelFileMasterService dataModelFileMasterService;

  @Autowired private TimelyModelingService timelyModelingService;

  @Autowired private CommonService commonService;

  @Autowired private CommonBizService commonBizService;

  @Autowired private DataModelCalService dataModelCalService;

  @Autowired private DataModelFileService dataModelFileService;

  @Autowired private CacheService cacheService;

  @Autowired private DatahouseUtil datahouseUtil;

  @Autowired PQLCatalogManager pqlCatalogManager;
  private static final String SQL_SELECT = "select count(*) count from ";
  private static final String COUNT = "count";
  private static final String AND = " and `";
  private static final String LIMIT_1 = "LIMIT 1";
  private static final String AND_INFO = " and ";

  public QueryKnowledgeDataRespDTO queryKnowledgeData(QueryKnowledgeDataDTO req) {
    String tableName = dataHouseService.makeTableNameFromFileID(req.getModelId(), req.getFileId());
    String sql = "select * from " + tableName + " where 1 = 1 ";

    List<Map<String, String>> fileDataList =
        cacheService.getCachePage(sql, req.getPageNum(), req.getPageSize());

    String countSql = SQL_SELECT + tableName + " where 1 = 1";

    List<Map<String, String>> countData = dataHouseService.selectField(countSql);

    Long count = countData.stream().map(m -> Long.valueOf(m.get(COUNT))).findFirst().orElse(0L);
    count = count > 1000 ? 1000 : count;
    return QueryKnowledgeDataRespDTO.builder().total(count).list(fileDataList).build();
  }

  public QueryColumnDataByColumnRespDTO getColumnDataByColumnId(QueryColumnDataByColumnIdDTO req) {
    FileFieldPO fieldPO = fileFieldService.getById(req.getColumnId());

    if (Objects.isNull(fieldPO)) {
      return null;
    }

    String tableName = dataHouseService.makeTableNameFromFileID(fieldPO.getFileId());
    StringBuilder sql =
        new StringBuilder(
            " select distinct `"
                + fieldPO.getFieldOrigin()
                + "` from "
                + tableName
                + " where 1=1 ");

    StringBuilder countSql =
        new StringBuilder(
            " select count(distinct `"
                + fieldPO.getFieldOrigin()
                + "`) count from "
                + tableName
                + " where 1=1");

    StringBuilder where = new StringBuilder();
    if (CollectionUtils.isNotEmpty(req.getRuleList())) {
      for (QueryColumnDataRuleDTO rule : req.getRuleList()) {
        if (Objects.equals(rule.getRule(), RuleEnum.GTEQ.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(
                AND + fieldPO.getFieldOrigin() + "`" + RuleEnum.GTEQ.getName() + matchValue.get(0));
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.GT.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(
                AND + fieldPO.getFieldOrigin() + "`" + RuleEnum.GT.getName() + matchValue.get(0));
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.LTEQ.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(
                AND + fieldPO.getFieldOrigin() + "`" + RuleEnum.LTEQ.getName() + matchValue.get(0));
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.LT.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(
                AND + fieldPO.getFieldOrigin() + "`" + RuleEnum.LT.getName() + matchValue.get(0));
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.EQ.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(AND + fieldPO.getFieldOrigin() + "` in (" + buildRefArr(matchValue) + ")");
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.NQ.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(
                AND + fieldPO.getFieldOrigin() + "` not in (" + buildRefArr(matchValue) + ")");
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.TIME.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue) && matchValue.size() == 2) {
            where.append(
                AND
                    + fieldPO.getFieldOrigin()
                    + "`>='"
                    + matchValue.get(0)
                    + "' and `"
                    + fieldPO.getFieldOrigin()
                    + "`<='"
                    + matchValue.get(1)
                    + "'");
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.TIME_START.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(AND + fieldPO.getFieldOrigin() + "`>='" + matchValue.get(0) + "'");
          }
        }

        if (Objects.equals(rule.getRule(), RuleEnum.TIME_END.getValue())) {
          List<String> matchValue = rule.getMatchValue();
          if (CollectionUtils.isNotEmpty(matchValue)) {
            where.append(AND + fieldPO.getFieldOrigin() + "`<='" + matchValue.get(0) + "'");
          }
        }
      }
    }

    countSql.append(where);

    where.append(" group by `" + fieldPO.getFieldOrigin() + "` ");

    sql.append(where);

    List<Map<String, String>> maps =
        cacheService.getCachePage(sql.toString(), req.getPageNum(), req.getPageSize());

    List<String> collect =
        maps.stream()
            .map(
                m -> {
                  String value = m.get(fieldPO.getFieldOrigin());
                  if (StringUtils.isBlank(value)) {
                    value = m.get(fieldPO.getFieldOrigin());
                  }
                  return value;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    Integer count = dataHouseService.selectGroupCount(countSql.toString());

    return QueryColumnDataByColumnRespDTO.builder()
        .count(count > 1000 ? 1000 : count)
        .data(collect)
        .pageNum(req.getPageNum())
        .pageSize(req.getPageSize())
        .build();
  }

  private String buildRefArr(List<String> matchValue) {
    List<String> collect = matchValue.stream().map(m -> "'" + m + "'").collect(Collectors.toList());
    return StringUtils.join(collect, ",");
  }

  public QueryDataByColumnsRespDTO getDataByColumnIds(QueryDataByColumnsDTO req) {
    if (CollectionUtils.isEmpty(req.getColumnList())) {
      return null;
    }

    List<Integer> columnIds =
        req.getColumnList().stream()
            .map(QueryDataRuleDTO::getColumnId)
            .distinct()
            .collect(Collectors.toList());

    List<FileFieldPO> fileFieldPOS = fileFieldService.listByIds(columnIds);

    if (CollectionUtils.isEmpty(fileFieldPOS)) {
      return null;
    }

    Map<Integer, FileFieldPO> fieldPOMap =
        fileFieldPOS.stream()
            .collect(Collectors.toMap(FileFieldPO::getId, Function.identity(), (k1, k2) -> k1));

    Integer dataModelId = req.getColumnList().get(0).getDataModelId();

    DataModelFilePO active =
        dataModelFileService.getOne(
            new LambdaQueryWrapper<DataModelFilePO>()
                .eq(DataModelFilePO::getActive, FileActiveTypeEnum.ACTIVE.getValue())
                .eq(DataModelFilePO::getDataModelId, dataModelId)
                .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    if (Objects.isNull(active)) {
      return null;
    }

    DataModelFileMasterPO master =
        dataModelFileMasterService.getOne(
            new LambdaQueryWrapper<DataModelFileMasterPO>()
                .eq(DataModelFileMasterPO::getDataModelId, dataModelId)
                .eq(DataModelFileMasterPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    if (Objects.isNull(master)) {
      return null;
    }

    Integer dimension =
        dataModelCalService.judgeDimension(
            dataModelId, active.getFileId(), fileFieldPOS.get(0).getFileId());

    String fromSql = "";
    if (Objects.equals(dimension, DimensionEnum.CASE.getValue())) {
      fromSql = master.getCaseMergeSql();
    } else {
      fromSql = master.getMergeSql();
    }

    String query = buildColumnQuery(master.getDataModelId(), fileFieldPOS, false);

    StringBuilder sql = new StringBuilder("select " + query + " from " + fromSql);

    StringBuilder countSql = new StringBuilder(SQL_SELECT + fromSql);

    VariantTableDataDTO variantTableDataDTO = null;

    Integer count = 0;

    StringBuilder where = new StringBuilder(" where 1=1 ");

    for (QueryDataRuleDTO rule : req.getColumnList()) {
      if (Objects.equals(rule.getRule(), RuleEnum.GTEQ.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`"
                  + RuleEnum.GTEQ.getName()
                  + matchValue.get(0));
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.GT.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`"
                  + RuleEnum.GT.getName()
                  + matchValue.get(0));
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.LTEQ.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`"
                  + RuleEnum.LTEQ.getName()
                  + matchValue.get(0));
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.LT.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`"
                  + RuleEnum.LT.getName()
                  + matchValue.get(0));
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.EQ.getValue())
          || Objects.equals(rule.getRule(), RuleEnum.TIME_EQ.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "` in ("
                  + buildRefArr(matchValue)
                  + ")");
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.NQ.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "` not in ("
                  + buildRefArr(matchValue)
                  + ")");
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.TIME.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO)
            && CollectionUtils.isNotEmpty(matchValue)
            && matchValue.size() == 2) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`>='"
                  + matchValue.get(0)
                  + "' and "
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`<='"
                  + matchValue.get(1)
                  + "'");
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.TIME_START.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`>='"
                  + matchValue.get(0)
                  + "'");
        }
      }

      if (Objects.equals(rule.getRule(), RuleEnum.TIME_END.getValue())) {
        FileFieldPO fieldPO = fieldPOMap.get(rule.getColumnId());
        List<String> matchValue = rule.getMatchValue();
        if (Objects.nonNull(fieldPO) && CollectionUtils.isNotEmpty(matchValue)) {
          String tableName =
              dataHouseService.makeTableNameFromFileID(dataModelId, fieldPO.getFileId());
          where.append(
              AND_INFO
                  + tableName
                  + ".`"
                  + fieldPO.getFieldOrigin()
                  + "`<='"
                  + matchValue.get(0)
                  + "'");
        }
      }
    }

    sql.append(where);

    countSql.append(where);

    List<Map<String, String>> queryResp =
        cacheService.getCachePage(sql.toString(), req.getPageNum(), req.getPageSize());

    variantTableDataDTO = this.buildTableData4Exection(queryResp, fileFieldPOS);

    List<Map<String, String>> queryCountResp =
        datahouseUtil.searchCommandsJoin(countSql.toString());

    count =
        queryCountResp.stream().map(m -> Integer.valueOf(m.get(COUNT) + "")).findFirst().orElse(0);

    return QueryDataByColumnsRespDTO.builder()
        .count(count > 1000 ? 1000 : count)
        .data(variantTableDataDTO)
        .pageNum(req.getPageNum())
        .pageSize(req.getPageSize())
        .build();
  }

  private VariantTableDataDTO buildTableData4Exection(
      List<Map<String, String>> dataList, List<FileFieldPO> fieldList) {

    List<FileFieldPO> finalFieldList = fieldList;
    List<Map<String, String>> mapList =
        dataList.stream()
            .map(
                m -> {
                  Map<String, String> tableDataMap = new HashMap<>();
                  boolean allEmpty = true;
                  for (FileFieldPO field : finalFieldList) {
                    String value = m.get(field.getFieldOrigin());
                    if (StringUtils.isBlank(value)) {
                      value = Consts.EMPYT;
                    } else {
                      allEmpty = false;
                    }
                    tableDataMap.put(field.getFieldOrigin(), value);
                  }
                  if (allEmpty) {
                    // 当这一行数据都为空时，就不显示出来了
                    return null;
                  }
                  return tableDataMap;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    List<Lable> lables =
        fieldList.stream()
            .map(
                m ->
                    Lable.builder()
                        .columnId(m.getId())
                        .columnType(m.getFieldType())
                        .lable(m.getFieldOrigin())
                        .prop(m.getFieldOrigin())
                        .build())
            .collect(Collectors.toList());

    return VariantTableDataDTO.builder().headerData(lables).tableData(mapList).build();
  }

  public Integer judgeFileDimension(QueryFileDimensionDTO req) {
    DataModelFilePO active =
        dataModelFileService.getOne(
            new LambdaQueryWrapper<DataModelFilePO>()
                .eq(DataModelFilePO::getActive, FileActiveTypeEnum.ACTIVE.getValue())
                .eq(DataModelFilePO::getDataModelId, req.getDataModelId())
                .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    if (Objects.isNull(active)) {
      return 2;
    }

    return dataModelCalService.judgeDimension(
        req.getDataModelId(), active.getFileId(), req.getQueryFileId());
  }

  /**
   * 返回值 1：只有一种维度，2：包含两种维度
   *
   * @param req
   * @return
   */
  public Integer judgeColumnDimension(QueryColumnsDimensionDTO req) {

    DataModelFilePO active =
        dataModelFileService.getOne(
            new LambdaQueryWrapper<DataModelFilePO>()
                .eq(DataModelFilePO::getActive, FileActiveTypeEnum.ACTIVE.getValue())
                .eq(DataModelFilePO::getDataModelId, req.getDataModelId())
                .eq(DataModelFilePO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .last(LIMIT_1));

    if (Objects.isNull(active)) {
      return 2;
    }

    List<FileFieldPO> fileFieldPOS = fileFieldService.listByIds(req.getColumnIds());

    if (CollectionUtils.isEmpty(fileFieldPOS)) {
      return 2;
    }
    List<Integer> dimensionList = new ArrayList<>();

    for (FileFieldPO po : fileFieldPOS) {

      Integer dimension =
          dataModelCalService.judgeDimension(
              req.getDataModelId(), active.getFileId(), po.getFileId());
      dimensionList.add(dimension);
    }

    long count = dimensionList.stream().distinct().count();
    if (count == 0) {
      return 2;
    } else if (count == 1) {
      return 1;
    } else {
      return 2;
    }
  }

  public Integer getTableDataCount(QueryTableDataCountDTO request) {
    if (Objects.isNull(request.getFileId())) {
      return 0;
    }

    String sql = SQL_SELECT + dataHouseService.makeTableNameFromFileID(request.getFileId());
    List<Map<String, String>> queryResp = datahouseUtil.searchCommandsJoin(sql);

    return queryResp.stream()
        .map(
            m -> {
              String count = m.get(COUNT);
              if (StringUtils.isNotBlank(count)) {
                return Integer.valueOf(count);
              }
              return 0;
            })
        .findFirst()
        .orElse(0);
  }

  public String buildColumnQuery(
      int dataModelId, List<FileFieldPO> fileFieldPOS, boolean hasColumnId) {
    StringBuilder query = new StringBuilder();
    for (FileFieldPO po : fileFieldPOS) {
      if (StringUtils.isBlank(po.getFieldOrigin())) {
        if (hasColumnId) {
          query.append(
              dataHouseService.makeTableNameFromFileID(dataModelId, po.getFileId())
                  + ".`"
                  + po.getField()
                  + "` "
                  + po.getId()
                  + ",");
        } else {
          query.append(
              dataHouseService.makeTableNameFromFileID(dataModelId, po.getFileId())
                  + ".`"
                  + po.getField()
                  + "`,");
        }
      } else {
        if (hasColumnId) {
          query.append(
              dataHouseService.makeTableNameFromFileID(dataModelId, po.getFileId())
                  + ".`"
                  + po.getFieldOrigin()
                  + "` `"
                  + po.getId()
                  + "`,");
        } else {
          query.append(
              dataHouseService.makeTableNameFromFileID(dataModelId, po.getFileId())
                  + ".`"
                  + po.getFieldOrigin()
                  + "` `"
                  + po.getFieldOrigin()
                  + "`,");
        }
      }
    }
    if (query.length() > 0) {
      String substring = query.substring(0, query.length() - 1);
      return substring;
    }
    return query.toString();
  }
}
