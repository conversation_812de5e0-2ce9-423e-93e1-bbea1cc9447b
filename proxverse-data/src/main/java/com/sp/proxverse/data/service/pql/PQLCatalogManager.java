package com.sp.proxverse.data.service.pql;

import com.google.common.base.Preconditions;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.dto.TempPQLTableDto;
import com.sp.proxverse.common.model.dto.TopicFileDTO;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.dao.service.DataModelService;
import com.sp.proxverse.interfaces.service.data.pql.PQLParameterManager;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.catalyst.analysis.UnresolvedAttribute;
import org.apache.spark.sql.catalyst.expressions.Expression;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.pql.ModelCatalog;
import org.apache.spark.sql.pql.PQLEnv;
import org.apache.spark.sql.pql.analysis.PQLResolver;
import org.apache.spark.sql.pql.analysis.RawPQLTable;
import org.apache.spark.sql.pql.manager.CatalogManager;
import org.apache.spark.sql.pql.model.PQLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PQLCatalogManager implements CatalogManager {

  @Autowired ModelDescFactory modelDescFactory;
  @Autowired TopicFilterManagerImpl topicFilterManager;

  @Autowired private BusinessTopicDataService businessTopicDataService;

  @Autowired private PQLParameterManager pqlParameterManager;

  @Autowired private DataModelService dataModelService;

  @Autowired private TempPQLTableService tempPQLTableService;

  @Override
  public ModelCatalog createCatalog(int topicId) {
    TopicFileDTO fileByTopicId = businessTopicDataService.getTopicFileByTopicId(topicId);
    if (fileByTopicId == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_NOT_EXIST));
    }
    DataModelPO dataModelPO = dataModelService.getById(fileByTopicId.getDataModelId());
    if (dataModelPO == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_NOT_EXIST));
    }
    ModelCatalog modelCatalog =
        createCatalogByModelId(fileByTopicId.getDataModelId(), dataModelPO.getCurrentVersion());
    pqlParameterManager
        .getAllVariables(topicId, true)
        .forEach((k, v) -> modelCatalog.variables().put(k.toLowerCase(), v));

    // 指挥舱修改变量值不会更新数据库，需要从 topicParameter 获取变量值
    if (Objects.nonNull(PQLEnv.getLocalVariables())) {
      PQLEnv.getLocalVariables().forEach((k, v) -> modelCatalog.variables().put(k, v));
    }
    setupTempPQLTables(modelCatalog, topicId);

    return modelCatalog;
  }

  private void setupTempPQLTables(ModelCatalog modelCatalog, int topicId) {
    PQLContext before = PQLContext.context().get();
    try {
      PQLContext.context().set(new PQLContext(modelCatalog, null));
      for (TempPQLTableDto pqlTable : tempPQLTableService.getTempPQLTablesIsExist(topicId)) {
        setupTempPQLTable(modelCatalog, pqlTable);
      }
    } finally {
      if (before == null) {
        PQLContext.context().remove();
      } else {
        PQLContext.context().set(before);
      }
    }
  }

  private void setupTempPQLTable(ModelCatalog modelCatalog, TempPQLTableDto pqlTable) {
    modelCatalog.registerTempPQLTable(
        () ->
            PQLResolver.resolveRawPQLTable(
                pqlTable.getName(),
                new RawPQLTable(
                    pqlTable.getColumns().stream()
                        .map(TempPQLTableDto.NamedColumn::namedExpr)
                        .collect(Collectors.toList()),
                    pqlTable.getFilters(),
                    pqlTable.getLimit(),
                    pqlTable.getSorts().stream()
                        .map(TempPQLTableDto.Sort::getColumn)
                        .collect(Collectors.toList()),
                    pqlTable.getSorts().stream()
                        .map(TempPQLTableDto.Sort::getDirection)
                        .collect(Collectors.toList()))),
        pqlTable.getName());
    pqlTable
        .getJoins()
        .forEach(
            (leftKey, rightKey) -> {
              // from tablename to spark table name
              Expression resolved = functions.expr(leftKey).expr();
              Preconditions.checkArgument(
                  resolved instanceof UnresolvedAttribute
                      && ((UnresolvedAttribute) resolved).nameParts().size() == 3,
                  "Invalid Temp Table join key: " + leftKey);
              UnresolvedAttribute leftAttr = (UnresolvedAttribute) resolved;
              modelCatalog.addLeftJoinRelation(
                  String.format(
                      "%s.`%s`.`%s`",
                      leftAttr.nameParts().head(),
                      leftAttr.nameParts().apply(1),
                      leftAttr.nameParts().last()),
                  rightKey);
            });
  }

  @Override
  public ModelCatalog createCatalogByModelId(int modelId) {
    DataModelPO dataModelPO = dataModelService.getById(modelId);
    if (dataModelPO == null) {
      throw new BizException(
          ErrorCode.ERROR_SYSTEM.getCode(), I18nUtil.getMessage(I18nConst.DATA_NOT_EXIST));
    }
    return this.createCatalogByModelId(modelId, dataModelPO.getCurrentVersion());
  }

  @Override
  public ModelCatalog createCatalogByModelId(int modelId, int versionNumber) {
    return new ModelCatalog(
        modelDescFactory.getOrCreate(modelId, versionNumber), SparkSessionEnv.getSparkSession());
  }
}
