package com.sp.proxverse.data.util.bpmn.simulation.analyze

import com.sp.proxverse.data.util.bpmn.simulation.Simulator
import com.sp.proxverse.data.util.bpmn.simulation.analyze.SimulatorDataAnalyze.{caseSchema, eventSchema}
import org.apache.spark.sql.types.{IntegerType, StringType, StructField, StructType}
import org.apache.spark.sql.{Row, SparkSessionEnv}

import java.util
import scala.collection.JavaConverters.seqAsJavaListConverter

object SimulatorDataAnalyze {
  val eventSchema: StructType = StructType(
    Seq(
      StructField("status", StringType),
      StructField("case_id", StringType),
      StructField("event_name", StringType),
      StructField("time", IntegerType),
      Struct<PERSON>ield("resource_name", StringType),
      StructField("resource_count", IntegerType),
      StructField("fixed_cost", IntegerType),
      StructField("resource_cost", IntegerType),
    ))
  val caseSchema: StructType = StructType(
    Seq(
      StructField("status", StringType),
      StructField("case_id", StringType),
      StructField("time", IntegerType)))
}

class SimulatorDataAnalyze(simulator: Simulator) {

  val eventTableName = s"${simulator.simulationId.replace("-", "_")}_event"
  val caseTableName = s"${simulator.simulationId.replace("-", "_")}_case"
  private val GLOBAL_TEMP = "global_temp."

  def initTempView(): Unit = {
    val event_csv_path = simulator.simulationDataDir + s"/${simulator.simulationId}/event.csv"
    val case_csv_path = simulator.simulationDataDir + s"/${simulator.simulationId}/case.csv"
    val event_parquet_path = simulator.simulationDataDir + s"/${simulator.simulationId}/event.parquet"
    val case_parquet_path = simulator.simulationDataDir + s"/${simulator.simulationId}/case.parquet"

    val spark = SparkSessionEnv.getSparkSession

    // Create global temp view from CSV files
    val eventDF = spark.read
        .option("sep", "|")
        .schema(eventSchema)
        .csv(event_csv_path)
    eventDF.createOrReplaceGlobalTempView(GLOBAL_TEMP + eventTableName)

    val caseDF = spark.read
        .option("sep", "|")
        .schema(caseSchema)
        .csv(case_csv_path)
    caseDF.createOrReplaceGlobalTempView(GLOBAL_TEMP + caseTableName)

    // Write parquet files
    eventDF.write.parquet(event_parquet_path)
    caseDF.write.parquet(case_parquet_path)
  }

  def analyzeEvent(): Array[Row] = {

    SparkSessionEnv.getSparkSession.sql(s"select * from $GLOBAL_TEMP${eventTableName}").collect()
  }

  def analyzeCase(): util.Map[String, Any] = {
    val result = new util.HashMap[String, Any]()

    val finishCase = simulator.simulatorMetrics.finishCase.longValue()
    val failedCase = simulator.simulatorMetrics.submitCase - simulator.simulatorMetrics.finishCase.longValue()


    result.put("finishCase", finishCase);
    result.put("failedCase", failedCase);

    val case_id =
      s"""
         |select t1.case_id  from $GLOBAL_TEMP${caseTableName} t1 join $GLOBAL_TEMP${caseTableName} t2 on t1.case_id = t2.case_id
         |where t1.status = 'start' and t2.status = 'finish'
         |""".stripMargin

    val resource_pool_used = SparkSessionEnv.getSparkSession.sql(s"""
         | select sum(t1.resource_count), t1.resource_name from $GLOBAL_TEMP${eventTableName}  t1 join $GLOBAL_TEMP${eventTableName} t2 on t1.case_id = t2.case_id and  t1.event_name = t2.event_name
         | where t1.status = 'start' and t2.status = 'finish' and t1.resource_name is not null group by t1.resource_name
         |""".stripMargin).collect()

    val availability: util.HashMap[String, Double] = new util.HashMap[String, Double]
    // 资源利用率
    resource_pool_used.foreach { row =>
      val pool = simulator.resourcePoolsMapping.get(row.getString(1))
      availability.put(
        pool.getName,
        row.getLong(0).toDouble / (pool.getResourceRunTime * pool.resourceCount))
    }
    result.put("availability", availability);

    // 资源处理时间
    val resource_processing_time: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select sum(t2.time -t1.time), t1.resource_name from $GLOBAL_TEMP${eventTableName}  t1 join $GLOBAL_TEMP${eventTableName} t2 on  t1.case_id = t2.case_id  and t1.event_name = t2.event_name
         |  where t1.case_id in(${case_id}) and t1.status = 'start' and t2.status = 'finish' and t1.resource_name is not null group by t1.resource_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        resource_processing_time.put(row.getString(1), row.getLong(0))
      })

    result.put("resourceProcessingTime", resource_processing_time);

    // 资源总处理时间保护等待时间
    val total_resource_processing_time: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
             | select sum(t2.time -t1.time), t1.resource_name from $GLOBAL_TEMP${eventTableName}  t1 join $GLOBAL_TEMP${eventTableName} t2 on  t1.case_id = t2.case_id  and t1.event_name = t2.event_name
             |  where t1.case_id in(${case_id}) and t1.status = 'pending' and t2.status = 'finish' and t1.resource_name is not null group by t1.resource_name
             |""".stripMargin)
      .collect()
      .foreach(row => {
          total_resource_processing_time.put(row.getString(1), row.getLong(0))
      })

    result.put("totalResourceProcessingTime", total_resource_processing_time);

    // 完成的Case中资源池完成的案例数
    val resource_event_number: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         |select t1.resource_name,count(*) from $GLOBAL_TEMP${eventTableName} t1 join $GLOBAL_TEMP${eventTableName} t2 on t1.case_id = t2.case_id and t1.event_name = t2.event_name
         |where t1.case_id in(${case_id}) and t1.resource_name is not null and t1.status = 'start' and t2.status = 'finish' group by t1.resource_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        resource_event_number.put(row.getString(0), row.getLong(1))
      })
    result.put("resourceEventNumber", resource_event_number);

    // 平均吞吐时间 按照百分比
    val averageDurationChart = SparkSessionEnv.getSparkSession.sql(s"""
         |select t2.time - t1.time , (count(*)+0.0)/${finishCase} from $GLOBAL_TEMP${caseTableName} t1 join $GLOBAL_TEMP${caseTableName} t2 on t1.case_id = t2.case_id
         |where t1.status = 'pending' and t2.status = 'finish' group by t2.time - t1.time
         |""".stripMargin).collect()
    val averageDurationChartArray: util.List[util.ArrayList[Any]] = averageDurationChart
      .map(row => {
        val l = new util.ArrayList[Any]
        l.add(row.getInt(0));
        l.add(row.getDecimal(1));
        l
      })
      .toList
      .asJava;

    result.put("averageDurationChart", averageDurationChartArray);
    // 平均吞吐时间
    val avg_duration = SparkSessionEnv.getSparkSession.sql(s"""
         |select avg(t2.time - t1.time)  from $GLOBAL_TEMP${caseTableName} t1 join $GLOBAL_TEMP${caseTableName} t2 on t1.case_id = t2.case_id
         |where t1.status = 'pending' and t2.status = 'finish'
         |""".stripMargin).collect()
    val avg_duration_double: Double = avg_duration
      .map { row =>
        {
          var results = 0d;
          if (row.get(0) != null) {
            results = row.getDouble(0);
          }
          results
        }
      }
      .apply(0);

    result.put("avgDuration", avg_duration_double);

    // 平均开销
    val avg_cost = SparkSessionEnv.getSparkSession
      .sql(s"""
         | select avg(total_cost) from( select sum(fixed_cost + resource_cost) as total_cost from $GLOBAL_TEMP${eventTableName} where case_id in(${case_id}) group by case_id)
         |""".stripMargin)
      .collect()
      .map(row => {
        var results = 0d;
        if (row.get(0) != null) {
          results = row.getDouble(0);
        }
        results
      })
      .toList
      .asJava;

    result.put("avgCost", avg_cost);

    // 平均开销列表
    val avg_cost_list = SparkSessionEnv.getSparkSession
      .sql(s"""
         | select sum(fixed_cost + resource_cost) as total_cost from $GLOBAL_TEMP${eventTableName} where case_id in(${case_id}) group by case_id
         |""".stripMargin)
      .collect()
      .map(row => {
        row.getLong(0);
      })
      .toList
      .asJava;
    result.put("avgCostList", avg_cost_list);

    // 事件KPI
    // 执行次数
    val event_process_number: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select event_name ,count(*) from $GLOBAL_TEMP${eventTableName} where status = 'finish'  group by event_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        event_process_number.put(row.getString(0), row.getLong(1))
      });
    result.put("eventProcessNumber", event_process_number);

    // 执行次总数 包括未完成事件
    val event_process_sum_number: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select event_name ,count(*) from $GLOBAL_TEMP${eventTableName} where status = 'pending'  group by event_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        event_process_sum_number.put(row.getString(0), row.getLong(1))
      });
    result.put("eventProcessSumNumber", event_process_sum_number);

    // 不等待次数
    val event_not_wait_number: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select t1.event_name ,count(*) from $GLOBAL_TEMP${eventTableName} t1 join $GLOBAL_TEMP${eventTableName} t2 on t1.event_name = t2.event_name and t1.case_id = t2.case_id
         | where t1.status = 'pending' and t2.status = 'start' and t1.time == t2.time
         | group by t1.event_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        event_not_wait_number.put(row.getString(0), row.getLong(1))
      });
    result.put("eventNotWaitNumber", event_not_wait_number);

    // 事件吞吐时间
    val event_duration: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select t1.event_name ,sum(t2.time - t1.time) from $GLOBAL_TEMP${eventTableName} t1 join $GLOBAL_TEMP${eventTableName} t2 on t1.case_id = t2.case_id and t1.case_id in(${case_id}) and t1.event_name = t2.event_name
         | where  t1.status = 'pending' and t2.status = 'finish'
         | group by t1.event_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        event_duration.put(row.getString(0), row.getLong(1))
      });
    result.put("eventDuration", event_duration);

    // 事件顺序
    val event_name_order: util.ArrayList[String] = new util.ArrayList[String]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select distinct (event_name) from $GLOBAL_TEMP${eventTableName}
         |""".stripMargin)
      .collect()
      .foreach(row => {
        event_name_order.add(row.getString(0))
      });
    result.put("eventNameOrder", event_name_order);

    // 总处理时间

    val total_processing_time: util.HashMap[String, Long] = new util.HashMap[String, Long]
    SparkSessionEnv.getSparkSession
      .sql(s"""
         | select t1.event_name ,sum(t2.time - t1.time) from $GLOBAL_TEMP${eventTableName} t1 join $GLOBAL_TEMP${eventTableName} t2 on t1.case_id = t2.case_id
         | where t1.status = 'start' and t2.status = 'finish' and t1.event_name = t2.event_name
         | group by t1.event_name
         |""".stripMargin)
      .collect()
      .foreach(row => {
        total_processing_time.put(row.getString(0), row.getLong(1))
      });
    result.put("totalProcessingTime", total_processing_time);

    result
  }
}
