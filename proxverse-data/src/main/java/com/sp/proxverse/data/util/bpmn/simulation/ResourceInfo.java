package com.sp.proxverse.data.util.bpmn.simulation;

import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Semaphore;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-09-13 17:52
 */
@Data
public class ResourceInfo {

  public ResourceInfo() {
    // comment empty
  }

  public static ResourceInfo buildReResourceBo() {
    ResourceInfo resourceInfo = new ResourceInfo();

    Map<String, Resource> resourceMap = Maps.newHashMap();

    resourceInfo.setResourceMap(resourceMap);

    List<Resource> resources = new ArrayList<>();
    resourceInfo.setResources(resources);

    final Semaphore semaphore = new Semaphore(5);

    Resource resource = new Resource();
    resource.setResourceName("资源一(用户)");
    resource.setResourceNumber(5);
    resource.setConsumptionTime(20);
    resource.setSemaphore(semaphore);
    Set<String> eventNames = new HashSet<>();
    eventNames.add("接到订单");
    eventNames.add("用户使用产品");

    resource.setEventNames(eventNames);
    resourceMap.put(resource.getResourceName(), resource);

    Resource resource1 = new Resource();
    resource1.setResourceName("资源二（客服）");
    resource1.setResourceNumber(3);
    resource1.setConsumptionTime(10);
    final Semaphore semaphore1 = new Semaphore(3);
    resource1.setSemaphore(semaphore1);
    resourceMap.put(resource1.getResourceName(), resource1);

    Resource resource2 = new Resource();
    resource2.setResourceName("资源三（经理）");
    resource2.setResourceNumber(2);
    resource2.setConsumptionTime(10);
    final Semaphore semaphore2 = new Semaphore(2);
    resource2.setSemaphore(semaphore2);
    resourceMap.put(resource2.getResourceName(), resource2);

    resources.add(resource2);
    resources.add(resource1);
    resources.add(resource);

    return resourceInfo;
  }

  /** Key 资源ID，value 资源 */
  private Map<String, Resource> resourceMap;

  /** 资源信息 */
  private List<Resource> resources;
}
