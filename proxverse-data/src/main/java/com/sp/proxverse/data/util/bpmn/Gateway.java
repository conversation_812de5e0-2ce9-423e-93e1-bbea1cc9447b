package com.sp.proxverse.data.util.bpmn;

import java.util.Map;
import java.util.Set;

/**
 * 网关
 *
 * <AUTHOR>
 * @create 2022-08-30 9:55 上午
 */
public interface Gateway extends BaseElement, Event {

  /**
   * 增加事件概率
   *
   * @param eventFrequency
   */
  void setEventFrequency(Map<String, Double> eventFrequency);

  /**
   * 设置开始网关标识
   *
   * @param flag
   */
  void setStartGatewayFlag(boolean flag);

  /**
   * 获取是否是结束网关
   *
   * @return
   */
  boolean getEndGatewayFlag();

  /**
   * 设置能够继续运行的所需条件
   *
   * @param eventIdIds
   */
  void setPrepareEventId(Set<String> eventIdIds);

  /**
   * 获取能够继续运行的所需条件
   *
   * @return
   */
  Set<String> getPrepareEventId();
}
