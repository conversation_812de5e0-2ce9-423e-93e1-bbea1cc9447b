package com.sp.proxverse.data.service.pql;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.prx.service.model.ModelDescFactory;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.mapper.PQLTableMapper;
import com.sp.proxverse.common.model.dict.DataSourceTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.KpiSaveTypeEnum;
import com.sp.proxverse.common.model.dto.TempPQLTableDto;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.BusinessTopicDataPO;
import com.sp.proxverse.common.model.po.TempPQLTable;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.BusinessTopicDataService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.spark.sql.catalyst.analysis.UnresolvedAttribute;
import org.apache.spark.sql.catalyst.expressions.Expression;
import org.apache.spark.sql.pql.analysis.SubQueryEval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TempPQLTableService extends ServiceImpl<PQLTableMapper, TempPQLTable> {

  @Autowired private PQLService pqlService;

  @Autowired private BusinessTopicDataService businessTopicDataService;

  @Autowired private ModelDescFactory modelDescFactory;

  private LambdaQueryWrapper<TempPQLTable> newCriteria(int topicId) {
    return new QueryWrapper<TempPQLTable>()
        .lambda()
        .eq(TempPQLTable::getTopicId, topicId)
        .eq(TempPQLTable::isDeleted, false);
  }

  private ObjectMapper mapper = new ObjectMapper();

  private TempPQLTableDto toDto(TempPQLTable table) {
    try {
      TempPQLTableDto.TableDef tableDef =
          mapper.readValue(table.getTableDef(), TempPQLTableDto.TableDef.class);
      TempPQLTableDto dto = new TempPQLTableDto();
      dto.setId(table.getId());
      dto.setName(table.getName());
      dto.setColumns(tableDef.getColumns());
      dto.setFilters(tableDef.getFilters());
      dto.setSorts(tableDef.getSorts());
      dto.setLimit(tableDef.getLimit());
      dto.setJoins(tableDef.getJoins());
      return dto;
    } catch (JsonProcessingException e) {
      throw new IllegalStateException(e);
    }
  }

  private TempPQLTable fromDto(int topicId, TempPQLTableDto dto) {
    dupCheck(topicId, dto);
    resolveColsType(topicId, dto);
    try {
      TempPQLTableDto.TableDef tableDef = new TempPQLTableDto.TableDef();
      tableDef.setColumns(dto.getColumns());
      tableDef.setFilters(dto.getFilters());
      tableDef.setSorts(dto.getSorts());
      tableDef.setLimit(dto.getLimit());
      tableDef.setJoins(dto.getJoins());
      String tableDefJson = mapper.writeValueAsString(tableDef);
      TempPQLTable table = new TempPQLTable();
      table.setId(dto.getId());
      table.setName(dto.getName());
      table.setTableDef(tableDefJson);
      return table;
    } catch (JsonProcessingException e) {
      log.error("getPQLTable from Dto error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  private void resolveColsType(int topicId, TempPQLTableDto dto) {
    // resovle cols
    Result result =
        SubQueryEval.withEvalStack(
            dto.getName(),
            () ->
                pqlService.resolveResultTypes(
                    topicId,
                    dto.getColumns().stream()
                        .map(TempPQLTableDto.NamedColumn::namedExpr)
                        .collect(Collectors.toList()),
                    1,
                    dto.getFilters(),
                    dto.getSorts().stream()
                        .map(sort -> Pair.of(sort.getColumn(), sort.getDirection()))
                        .collect(Collectors.toList()),
                    false));
    for (int i = 0; i < dto.getColumns().size(); i++) {
      dto.getColumns().get(i).setType(result.getMetadata()[i].getColumnType());
    }

    // resolve joining key
    TempPQLTableDto.NamedColumn joinKey = null;
    for (TempPQLTableDto.NamedColumn column : dto.getColumns()) {
      if (column.isJoinKey()) {
        if (joinKey != null) {
          throw new BizException(5000, "More than one join key found");
        } else {
          joinKey = column;
        }
      }
    }
    if (joinKey != null && StringUtils.isNotBlank(joinKey.getExpr())) {
      Expression joinKeyExpr = pqlService.parsePQLExpr(topicId, joinKey.getExpr());
      if (joinKeyExpr instanceof UnresolvedAttribute
          && ((UnresolvedAttribute) joinKeyExpr).nameParts().size() == 3) {
        dto.getJoins()
            .put(
                joinKey.getExpr(),
                String.format("`%s`.`%s`", dto.getName(), joinKey.getName()).toLowerCase());
      } else {
        throw new BizException(
            5000,
            String.format(
                "Invalid join key %s. Join key need to be direct reference to column of model tables.",
                joinKey.getExpr()));
      }
    }
  }

  private void dupCheck(int topicId, TempPQLTableDto dto) {
    // check model tables
    if (modelDescFactory
        .getOrCreate(businessTopicDataService.getTopicFileByTopicId(topicId).getDataModelId())
        .tableName2SparkTableName()
        .containsKey(dto.getName().toLowerCase())) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.DUP_NAME));
    }
    // check existing temp tables
    List<TempPQLTable> existingTempTables = baseMapper.selectList(newCriteria(topicId));
    for (TempPQLTable existing : existingTempTables) {
      if (existing.getName().equalsIgnoreCase(dto.getName())
          && !Objects.equals(existing.getId(), dto.getId())) {
        throw new BizException(5000, I18nUtil.getMessage(I18nConst.DUP_NAME));
      }
    }
  }

  public List<TempPQLTableDto> getTempPQLTablesIsExist(int topicId) {
    List<TempPQLTableDto> tempPQLTableDtos =
        baseMapper.selectList(newCriteria(topicId)).stream()
            .map(
                po -> {
                  TempPQLTableDto dto = toDto(po);
                  dto.setKpiSaveTypeEnum(KpiSaveTypeEnum.CUSTOMIZE);
                  return dto;
                })
            .collect(Collectors.toList());

    BusinessTopicDataPO knownleadeModel =
        businessTopicDataService.getOne(
            new LambdaQueryWrapper<BusinessTopicDataPO>()
                .eq(BusinessTopicDataPO::getTopicId, topicId)
                .eq(
                    BusinessTopicDataPO::getDataSourceType,
                    DataSourceTypeEnum.KNOWNLEADE_MODEL.getValue())
                .eq(BusinessTopicDataPO::getDeleted, DeletedEnum.NO_DELETED.getValue()));
    if (knownleadeModel != null) {
      List<TempPQLTableDto> knownleadeModelTemp =
          baseMapper.selectList(newCriteria(knownleadeModel.getModelId())).stream()
              .map(
                  po -> {
                    TempPQLTableDto dto = toDto(po);
                    dto.setKpiSaveTypeEnum(KpiSaveTypeEnum.KNOWLEDGE_MODEL);
                    return dto;
                  })
              .collect(Collectors.toList());
      if (!knownleadeModelTemp.isEmpty()) {
        tempPQLTableDtos.addAll(0, knownleadeModelTemp);
      }
    }
    return tempPQLTableDtos;
  }

  public TempPQLTableDto createTempPQLTable(int topicId, TempPQLTableDto dto) {
    TempPQLTable table = fromDto(topicId, dto);
    table.setTopicId(topicId);
    baseMapper.insert(table);
    return toDto(table);
  }

  public TempPQLTableDto updateTempPQLTable(int topicId, TempPQLTableDto dto) {
    TempPQLTable table = fromDto(topicId, dto);
    table.setTopicId(topicId);
    table.setUpdatedAt(Timestamp.from(Instant.now()));
    if (baseMapper.updateById(table) != 0) {
      return toDto(table);
    } else {
      throw new BizException(5000, "Update temp view failed, check if table exists. ");
    }
  }

  public Boolean delete(Integer id) {
    return baseMapper.update(
            null, new UpdateWrapper<TempPQLTable>().eq("id", id).set("deleted", true))
        != 0;
  }

  public Boolean deleteByTopicId(Integer topicId) {
    return baseMapper.update(
            null, new UpdateWrapper<TempPQLTable>().eq("topic_id", topicId).set("deleted", true))
        != 0;
  }
}
