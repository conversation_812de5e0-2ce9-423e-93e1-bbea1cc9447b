package com.sp.proxverse.data.service.cache;

import com.alibaba.fastjson.JSON;
import com.sp.proxverse.common.util.cache.RedisKeyHelper;
import com.sp.proxverse.common.util.cache.RedisModuleEnum;
import com.sp.proxverse.data.util.DatahouseUtil;
import com.sp.proxverse.interfaces.dao.service.DataModelFileService;
import com.sp.proxverse.service.CacheUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CacheService {

  @Autowired private CacheUtil cacheUtil;

  @Autowired DatahouseUtil datahouseUtil;

  @Value("${redis.env:test}")
  private String env;

  @Value("${cache.limit:1000}")
  private Integer cacheLimit;

  @Autowired DataModelFileService dataModelFileService;

  public List<Map<String, String>> getCachePage(String sql, Integer pageNum, Integer pageSize) {
    String cacheKey = RedisKeyHelper.buildKey(env, RedisModuleEnum.CACHE_PAGE, sql);
    List<Map<String, String>> listMap = cacheUtil.getListMap(cacheKey);

    log.info("redis key={},query cache resp={}", sql, JSON.toJSONString(listMap));

    if (Objects.isNull(listMap)) {
      // 从cache中没有查到，需要重查，然后塞入cache
      String cacheSql = sql + " limit " + cacheLimit;
      List<Map<String, String>> mapList = datahouseUtil.searchCommandsJoin(cacheSql);
      cacheUtil.set(sql, mapList, 1800, TimeUnit.SECONDS);
      listMap = mapList;
    }

    return this.pageList(listMap, pageNum, pageSize);
  }

  private List pageList(List listMap, Integer pageNum, Integer pageSize) {
    // 这里对listMap进行分页查询
    int start = (pageNum - 1) * pageSize;

    int toIndex = pageNum * pageSize;
    if (toIndex > listMap.size()) {
      toIndex = listMap.size();
    }
    if (start > toIndex) {
      return new ArrayList<>();
    }
    return listMap.subList(start, toIndex);
  }
}
