package com.sp.proxverse.data.service.biz;

import com.sp.proxverse.common.model.dict.prehandler.PreHandlerVariableEnum;
import com.sp.proxverse.common.model.dto.job.QueryVariantIdByCaseIdReqDTO;
import com.sp.proxverse.data.service.datahouse.DataHouseService;
import com.sp.proxverse.data.util.DatahouseUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class JobBizService {

  @Value("${datahouse.label.set:}")
  private String eventSet;

  @Autowired private DatahouseUtil datahouseUtil;

  @Autowired DataHouseService dataHouseService;

  public List<Integer> queryVariantIdByCaseIdList(QueryVariantIdByCaseIdReqDTO req) {
    if (Objects.isNull(req.getFileId())) {
      return new ArrayList<>();
    }
    String sql =
        "select "
            + PreHandlerVariableEnum.VARIANT_ID.getValue()
            + " from "
            + dataHouseService.makeTableNameFromFileID(req.getFileId())
            + " where 1 = 1";

    if (CollectionUtils.isNotEmpty(req.getCaseIdList())) {
      List<String> collect =
          req.getCaseIdList().stream().map(m -> "'" + m + "'").collect(Collectors.toList());

      sql =
          sql
              + " and "
              + PreHandlerVariableEnum.CASE_ID.getValue()
              + " in ("
              + StringUtils.join(collect, ",")
              + ")";
    }

    List<Map<String, String>> queryResp = datahouseUtil.searchCommandsJoin(sql);
    List<Integer> variantIdList =
        queryResp.stream()
            .map(m -> m.get(PreHandlerVariableEnum.VARIANT_ID.getValue()))
            .filter(Objects::nonNull)
            .map(Integer::valueOf)
            .collect(Collectors.toList());
    return variantIdList;
  }
}
