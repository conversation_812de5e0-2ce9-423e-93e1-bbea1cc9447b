package com.sp.proxverse.data.service.pql;

import com.google.common.collect.Lists;
import com.sp.process.utils.PQLResult;
import com.sp.proxverse.common.common.LocalParameter;
import com.sp.proxverse.common.model.dict.EnableTypeEnum;
import com.sp.proxverse.common.model.dict.FilterConst;
import com.sp.proxverse.common.model.dict.OperationTypeEnum;
import com.sp.proxverse.common.model.dict.kpi.VariableKpiRuleEnum;
import com.sp.proxverse.common.model.dto.KpiParserParamDTO;
import com.sp.proxverse.common.model.dto.ParseDataModelKpiDTO;
import com.sp.proxverse.common.model.dto.result.Result;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import com.sp.proxverse.common.model.vo.request.FieldSearchRequest;
import com.sp.proxverse.common.model.vo.request.KpiVariableSortDTO;
import com.sp.proxverse.common.model.vo.request.ParseKpiDTO;
import com.sp.proxverse.common.util.HadoopUtil;
import com.sp.proxverse.interfaces.service.data.FilterService;
import com.sp.proxverse.interfaces.service.data.pql.PQLService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSessionEnv;
import org.apache.spark.sql.catalyst.expressions.Expression;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.pql.ModelCatalog;
import org.apache.spark.sql.pql.PQLBuilder;
import org.apache.spark.sql.pql.PQLEnv;
import org.apache.spark.sql.pql.analysis.PQLResolver;
import org.apache.spark.sql.pql.analysis.PQLTable;
import org.apache.spark.sql.pql.hook.PQLHook;
import org.apache.spark.sql.pql.model.PQLContext;
import org.apache.spark.sql.types.StructType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PQLServiceImpl implements PQLService {

  @Autowired PQLParameterManagerImpl parameterManager;

  @Autowired PQLCatalogManager pqlCatalogManager;

  @Autowired TopicFilterManagerImpl topicFilterManager;

  @Autowired private FilterService filterService;

  @Value("${prx.pql.olap.searchOnResultSet:true}")
  private boolean olapFilterOnResultSet;

  private static final String CALC_CROP_TO_NULL = " CALC_CROP_TO_NULL";
  private static final String NOT = " NOT( ISNULL (\n";

  private Row[] calc(
      Integer topicId, Integer sheetId, List<TopicFilterPO> filters, String expression, int limit) {
    try {
      ModelCatalog modelCatalog = pqlCatalogManager.createCatalog(topicId);
      PQLBuilder pqlBuilder = newPQLBuilder(topicId, sheetId);
      List<String> filterExpres =
          topicFilterManager.processTopicFilter(modelCatalog.modelDesc(), filters);
      pqlBuilder.addTopicFilters(filterExpres);
      pqlBuilder.addColumn(expression);
      if (limit > 0) {
        pqlBuilder.addLimit(limit);
      }
      Row[] collect = pqlBuilder.collect();
      return collect;
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  // TODO refactor this, should throw err....
  @Override
  public String validatePQL(int topicId, String rawStatement) {
    try {
      newPQLBuilder(topicId, null, Lists.newArrayList(), false).addColumn(rawStatement);
      return null;
    } catch (Exception e) {
      return e.getMessage();
    }
  }

  @Override
  public String evalPQL(int topicId, String rawStatement) {
    try {
      // search for query level conf
      int from = rawStatement.indexOf("spark.sql");
      while (from > -1) {
        int colIdx = rawStatement.indexOf(';', from);
        int lineSepIdx = rawStatement.indexOf(System.lineSeparator(), from);
        int to = Math.min(colIdx, lineSepIdx);
        if (to == -1) {
          to = colIdx < 0 ? lineSepIdx : colIdx;
        }
        if (to > -1) {
          String[] seps = new String[] {":", "="};
          for (String sep : seps) {
            String[] parts = rawStatement.substring(from, to).split(sep);
            if (parts.length == 2) {
              PQLEnv.addLocalConf(parts[0].trim(), parts[1].trim());
            }
          }
        }
        from = rawStatement.indexOf("spark.sql", from + 1);
      }

      PQLBuilder pqlBuilder = newPQLBuilder(topicId, null, Lists.newArrayList(), false);
      PQLTable pqlTable = parameterManager.parsePQLStatement(rawStatement);
      PQLResolver.resolvePQLTable(pqlBuilder, pqlTable);

      if (pqlTable.explain()) {
        return pqlBuilder.testCollectWithoutCache(Dataset::queryExecution).toString();
      } else {
        Row[] result =
            pqlBuilder.testCollectWithoutCache(
                df -> {
                  PQLHook.callPreCollectHooks(pqlBuilder);
                  Row[] ret = (Row[]) df.collect();
                  PQLHook.callPostCollectHooks(pqlBuilder);
                  return ret;
                });
        return Arrays.stream(result)
            .map(r -> r.mkString("|"))
            .collect(Collectors.joining(System.lineSeparator()));
      }
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  public String evalPQLWithModel(int modelId, String rawStatement) {

    try {
      PQLBuilder pqlBuilder = newPQLBuilder(modelId);
      PQLTable pqlTable = parameterManager.parsePQLStatement(rawStatement);
      PQLResolver.resolvePQLTable(pqlBuilder, pqlTable);

      if (pqlTable.explain()) {
        return pqlBuilder.resultDF().queryExecution().toString();
      } else {
        Row[] result = pqlBuilder.collect();
        return Arrays.stream(result)
            .map(r -> r.mkString("|"))
            .collect(Collectors.joining(System.lineSeparator()));
      }
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  public Expression parsePQLExpr(int topicId, String expression) {
    try {
      PQLBuilder pqlBuilder = newPQLBuilder(topicId, null, Lists.newArrayList(), false);
      pqlBuilder.addColumn(expression, "col1");
      return pqlBuilder.columns().head().expr().children().head();
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  @Trace(operationName = "calcExpression")
  public String calcExpression(int topicId, Integer sheetId, String expression) {
    Row[] rows = calc(topicId, sheetId, new ArrayList<>(), expression, 1);
    return getKPIResult(rows);
  }

  @Override
  public List<String> calcExpression(
      int topicId, Integer sheetId, String expression, List<TopicFilterPO> addFilter, int limit) {
    Row[] collect = calc(topicId, sheetId, addFilter, expression, limit);
    return getLimitResult(collect, limit);
  }

  @Override
  @Trace(operationName = "calcExpressionWithFilter")
  public List<String> calcExpressionWithFilter(
      int topicId,
      Integer sheetId,
      String expression,
      List<TopicFilterPO> filters,
      boolean skipFilter) {
    if (skipFilter) {
      try {
        ModelCatalog modelCatalog = pqlCatalogManager.createCatalog(topicId);
        PQLBuilder pqlBuilder = newPQLBuilder(topicId, sheetId, false);
        if (CollectionUtils.isNotEmpty(filters)) {
          List<String> filterExpres =
              topicFilterManager.processTopicFilter(modelCatalog.modelDesc(), filters);
          pqlBuilder.addTopicFilters(filterExpres);
        }
        pqlBuilder.addColumn(expression);
        Row[] collect = pqlBuilder.collect();
        return getLimitResult(collect, -1);
      } finally {
        PQLEnv.clearQueryEnv();
      }
    } else {
      String filter = null;
      if (CollectionUtils.isNotEmpty(filters)) {
        filter = filters.get(0).getExpression();
      }
      String s = calcExpressionWithFilter(topicId, sheetId, expression, filter);
      return Lists.newArrayList(s);
    }
  }

  @Override
  public String calcExpressionWithFilter(
      int topicId, Integer sheetId, String expression, String filter) {
    try {
      PQLBuilder pqlBuilder = newPQLBuilder(topicId, sheetId);
      if (StringUtils.isNotBlank(filter)) {
        ArrayList<String> filters = Lists.newArrayList(filter);
        pqlBuilder.addTopicFilters(filters);

        ModelCatalog modelCatalog = pqlCatalogManager.createCatalog(topicId);

        List<String> filterExpres =
            topicFilterManager.processTopicFilter(modelCatalog.modelDesc(), new ArrayList<>());
        pqlBuilder.addTopicFilters(filterExpres);
      }
      pqlBuilder.addColumn(expression);
      Row[] collect = pqlBuilder.collect();
      return getKPIResult(collect);
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  public String calcExpression(KpiParserParamDTO kpiParserParamDTO) {
    try {
      ModelCatalog modelCatalog = pqlCatalogManager.createCatalog(kpiParserParamDTO.getTopicId());
      PQLBuilder pqlBuilder =
          newPQLBuilder(kpiParserParamDTO.getTopicId(), kpiParserParamDTO.getSheetId());
      if (CollectionUtils.isNotEmpty(kpiParserParamDTO.getAddFilterList())) {
        List<String> filters =
            topicFilterManager.processTopicFilter(
                modelCatalog.modelDesc(), kpiParserParamDTO.getAddFilterList());
        pqlBuilder.addTopicFilters(filters);
      }
      pqlBuilder.addColumn(kpiParserParamDTO.getKpiPO().getExpression());
      Row[] collect = pqlBuilder.collect();
      return getKPIResult(collect);
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  public Result calcExpression(ParseKpiDTO parseKpiDTO) {
    if (parseKpiDTO.getAddTopicFilterList() == null) {
      parseKpiDTO.setAddTopicFilterList(new ArrayList<>());
    }
    try {
      String[] unit = new String[parseKpiDTO.getDimensionColumnList().size()];

      PQLBuilder pqlBuilder = this.buildPQLBuilder(parseKpiDTO, unit);
      pqlBuilder.setDistinct(parseKpiDTO.isDistinct());
      Row[] result = new Row[0];
      if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.SCROLL.getValue())) {
        pqlBuilder.addLimit((parseKpiDTO.getPageNum()) * parseKpiDTO.getPageSize());
        result = pqlBuilder.offset((parseKpiDTO.getPageNum() - 1) * parseKpiDTO.getPageSize());
      } else if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.LIMIT.getValue())) {
        if (parseKpiDTO.getLimit() != null) {
          pqlBuilder.addLimit(parseKpiDTO.getLimit());
        }
        result = pqlBuilder.collect();
      } else if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.HEAD_LIMIT.getValue())) {
        pqlBuilder.topK(parseKpiDTO.getLimit());
        result = pqlBuilder.collect();
      }

      if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.HEAD_LIMIT.getValue())) {
        Result ret =
            PQLResult.makeOtherResult(
                result, pqlBuilder.resultDF().schema(), parseKpiDTO.getType(), pqlBuilder);
        ret.setUnit(unit);
        return ret;
      } else {
        Result ret =
            PQLResult.makeResult(
                result, pqlBuilder.resultDF().schema(), parseKpiDTO.getType(), pqlBuilder);
        ret.setUnit(unit);
        return ret;
      }
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  public Result calcExpression(ParseDataModelKpiDTO parseDataModelKpiDTO) {
    try {
      PQLBuilder pqlBuilder = newPQLBuilder(parseDataModelKpiDTO.getDataModelId());
      if (CollectionUtils.isEmpty(parseDataModelKpiDTO.getDimensionColumnList())) {
        return null;
      }
      String[] unit = new String[parseDataModelKpiDTO.getDimensionColumnList().size()];
      int index = 0;
      for (KpiPO kpi : parseDataModelKpiDTO.getDimensionColumnList()) {
        pqlBuilder.addColumn(kpi.getExpression(), kpi.getName());
        unit[index] = StringUtils.isBlank(kpi.getUnit()) ? "" : kpi.getUnit();
        index++;
      }
      if (parseDataModelKpiDTO.getSortList() != null
          && !parseDataModelKpiDTO.getSortList().isEmpty()) {
        for (KpiVariableSortDTO sortDTO : parseDataModelKpiDTO.getSortList()) {
          pqlBuilder.addSort(sortDTO.getSortName(), sortDTO.getSortType());
        }
      }

      Row[] result = new Row[0];
      if (Objects.equals(parseDataModelKpiDTO.getRule(), VariableKpiRuleEnum.SCROLL.getValue())) {
        pqlBuilder.addLimit(
            (parseDataModelKpiDTO.getPageNum()) * parseDataModelKpiDTO.getPageSize());
        result =
            pqlBuilder.offset(
                (parseDataModelKpiDTO.getPageNum() - 1) * parseDataModelKpiDTO.getPageSize());
      } else if (Objects.equals(
          parseDataModelKpiDTO.getRule(), VariableKpiRuleEnum.LIMIT.getValue())) {
        if (parseDataModelKpiDTO.getLimit() != null) {
          pqlBuilder.addLimit(parseDataModelKpiDTO.getLimit());
        }
        result = pqlBuilder.collect();
      } else if (Objects.equals(
          parseDataModelKpiDTO.getRule(), VariableKpiRuleEnum.HEAD_LIMIT.getValue())) {
        pqlBuilder.topK(parseDataModelKpiDTO.getLimit());
        result = pqlBuilder.collect();
      } else {
        result = pqlBuilder.collect();
      }

      if (Objects.equals(
          parseDataModelKpiDTO.getRule(), VariableKpiRuleEnum.HEAD_LIMIT.getValue())) {
        Result ret =
            PQLResult.makeOtherResult(
                result, pqlBuilder.resultDF().schema(), parseDataModelKpiDTO.getType(), pqlBuilder);
        ret.setUnit(unit);
        return ret;
      } else {
        Result ret =
            PQLResult.makeResult(
                result, pqlBuilder.resultDF().schema(), parseDataModelKpiDTO.getType(), pqlBuilder);
        ret.setUnit(unit);
        return ret;
      }
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  @Override
  public Result resolveResultTypes(
      int topicId,
      List<String> columns,
      int limit,
      List<String> filters,
      List<Pair<String, String>> orders,
      boolean withFilter) {
    PQLBuilder pqlBuilder = newPQLBuilder(topicId, null, withFilter);
    pqlBuilder.addColumns(columns);
    if (limit >= 0) {
      pqlBuilder.addLimit(limit);
    }
    if (filters != null) {
      filters.forEach(pqlBuilder::addDirectFilter);
    }
    if (orders != null) {
      orders.forEach(p -> pqlBuilder.addSort(p.getLeft(), p.getRight()));
    }
    return PQLResult.makeResult(new Row[0], pqlBuilder.resultDF().schema(), 0, pqlBuilder);
  }

  private List<String> searchListToFilterExprs(
      List<FieldSearchRequest> searchList, Boolean likeDisabled) {
    List<String> filters = new ArrayList<>();
    if (org.apache.commons.collections.CollectionUtils.isEmpty(searchList)) {
      return filters;
    }
    for (FieldSearchRequest fieldSearchRequest : searchList) {
      OperationTypeEnum opType =
          OperationTypeEnum.fromIntValue(fieldSearchRequest.getOperationType());
      if (opType == OperationTypeEnum.TIME) {
        // time类型
        String[] split = fieldSearchRequest.getSearchValue().split("&");
        if (split.length == 2) {
          filters.add(
              FilterConst.timeFiltering(
                  fieldSearchRequest.getSearchValue(), fieldSearchRequest.getSearchKey()));
        }
      } else {
        if (opType == OperationTypeEnum.LIKE && likeDisabled) {
          opType = OperationTypeEnum.EQ;
        }
        if (opType.isLike()) {
          filters.add(
              "`"
                  + fieldSearchRequest.getSearchKey()
                  + "` "
                  + opType.getName()
                  + " '%"
                  + fieldSearchRequest.getSearchValue()
                  + "%'");
        } else {
          filters.add(
              "`"
                  + fieldSearchRequest.getSearchKey()
                  + "` "
                  + opType.getName()
                  + " '"
                  + fieldSearchRequest.getSearchValue()
                  + "'");
        }
      }
    }
    return filters;
  }

  @Override
  public PQLBuilder newPQLBuilder(Integer topicId, Integer sheetId, List<String> filters) {
    return newPQLBuilder(topicId, sheetId, filters, true);
  }

  @Override
  public PQLBuilder newPQLBuilder(Integer topicId, Integer sheetId, boolean withTopicFilters) {
    return newPQLBuilder(topicId, sheetId, Lists.newArrayList(), withTopicFilters);
  }

  @Override
  public PQLBuilder newPQLBuilder(Integer modelId) {
    PQLEnv.addEnvInfo("modelId", modelId);
    ModelCatalog modelCatalog = pqlCatalogManager.createCatalogByModelId(modelId);
    return new PQLBuilder(SparkSessionEnv.getSparkSession(), modelCatalog);
  }

  @Override
  public PQLBuilder newPQLBuilderWithTopic(Integer topicId) {
    ModelCatalog modelCatalog = pqlCatalogManager.createCatalog(topicId);
    PQLEnv.addEnvInfo("topicId", topicId);
    PQLEnv.addEnvInfo("modelId", modelCatalog.modelDesc().modelId());
    PQLBuilder pqlBuilder = new PQLBuilder(SparkSessionEnv.getSparkSession(), modelCatalog);
    PQLContext.setMandatoryFilters(
        filterService.getTopicInitFilter(topicId).stream()
            .map(TopicFilterPO::getExpression)
            .collect(Collectors.toList()));
    return pqlBuilder;
  }

  /**
   * create PQLBuilder and add topic filters
   *
   * @param topicId
   * @param withTopicFilters
   * @return
   */
  @Override
  public PQLBuilder newPQLBuilder(
      Integer topicId, Integer sheetId, List<String> customFilters, boolean withTopicFilters) {
    PQLEnv.addEnvInfo("sheetId", sheetId);
    PQLBuilder pqlBuilder = newPQLBuilderWithTopic(topicId);
    HashSet<String> filters = new HashSet<>(customFilters);
    Boolean disableFilter = false;
    if (Objects.nonNull(LocalParameter.disableFilterThreadLocal.get())) {
      disableFilter = LocalParameter.disableFilterThreadLocal.get();
    }
    // add user topic filter and topic filters
    if (withTopicFilters) {
      // 指挥舱根据disableFilter判断从哪里获取PQL
      if (!disableFilter) {
        filters.addAll(topicFilterManager.getAllTopicFilters(topicId));
      } else {
        List<String> componentFilterThreadLocal = LocalParameter.componentFilterThreadLocal.get();
        if (CollectionUtils.isNotEmpty(componentFilterThreadLocal)) {
          filters.addAll(componentFilterThreadLocal);
        }
      }
      filterService.getSheetFilter(sheetId).ifPresent(filters::add);
      filterService.getRestrictionFilterForCurrentUser(topicId).ifPresent(filters::add);
    }
    pqlBuilder.addTopicFilters(filters);
    return pqlBuilder;
  }

  /**
   * Create a PQLBuilder that can control whether to ignore the CalcFilter.
   *
   * @param topicId
   * @param sheetId
   * @param customFilters
   * @param withTopicCalcFilters
   * @return
   */
  @Override
  public PQLBuilder newPQLBuilderWithOptionalCalcFilter(
      Integer topicId,
      Integer sheetId,
      List<String> customFilters,
      boolean withTopicCalcFilters,
      boolean withCustomCalcFilters,
      boolean withUserCalcFilters) {
    PQLEnv.addEnvInfo("sheetId", sheetId);
    PQLBuilder pqlBuilder = newPQLBuilderWithTopic(topicId);

    HashSet<String> filters = new HashSet<>();

    if (withCustomCalcFilters) {
      filters.addAll(customFilters);
    } else {
      filters.addAll(
          customFilters.stream()
              .filter(t -> !t.contains("\n" + NOT + CALC_CROP_TO_NULL))
              .collect(Collectors.toSet()));
    }
    // add user topic filter and topic filters
    List<String> allTopicFilters = topicFilterManager.getAllTopicFilters(topicId);
    if (withTopicCalcFilters) {
      filters.addAll(allTopicFilters);
    } else {
      filters.addAll(
          allTopicFilters.stream()
              .filter(t -> !t.contains("\n" + NOT + CALC_CROP_TO_NULL))
              .collect(Collectors.toSet()));
    }

    filterService.getSheetFilter(sheetId).ifPresent(filters::add);
    filterService
        .getRestrictionFilterForCurrentUser(topicId)
        .ifPresent(
            filter -> {
              if (StringUtils.isNotBlank(filter)) {
                boolean contains = filter.contains("\n" + NOT + CALC_CROP_TO_NULL);
                if (withUserCalcFilters || !contains) {
                  filters.add(filter);
                }
              }
            });
    pqlBuilder.addTopicFilters(filters);
    return pqlBuilder;
  }

  @Override
  public PQLBuilder newPQLBuilderWithOutCalcFilter(
      Integer topicId, Integer sheetId, List<String> customFilters) {
    return newPQLBuilderWithOptionalCalcFilter(
        topicId, sheetId, customFilters, false, false, false);
  }

  @Override
  public PQLBuilder newPQLBuilderWithScriptFilterOnly(
      Integer topicId, Integer sheetId, List<String> customFilters) {
    PQLBuilder pqlBuilder = newPQLBuilderWithTopic(topicId);
    HashSet<String> filters = new HashSet<>();
    if (CollectionUtils.isNotEmpty(customFilters)) {
      filters.addAll(customFilters);
    }

    filterService.getSheetFilter(sheetId).ifPresent(filters::add);
    filterService.getRestrictionFilterForCurrentUser(topicId).ifPresent(filters::add);
    List<TopicFilterPO> scriptFilters = filterService.getTopicInitFilter(topicId);
    if (!scriptFilters.isEmpty()) {
      List<String> collect =
          scriptFilters.stream().map(TopicFilterPO::getExpression).collect(Collectors.toList());
      filters.addAll(collect);
    }
    pqlBuilder.addTopicFilters(filters);
    return pqlBuilder;
  }

  @Override
  public PQLBuilder newPQLBuilderExcludePrikey(
      Integer topicId, Integer sheetId, List<String> customFilters, Integer prikey) {
    PQLBuilder pqlBuilder = newPQLBuilderWithTopic(topicId);
    HashSet<String> filters = new HashSet<>(customFilters);
    if (prikey == null) {
      filters.addAll(topicFilterManager.getAllTopicFilters(topicId));
    } else {
      filters.addAll(topicFilterManager.getAllTopicFiltersExcludePrikey(topicId, prikey));
    }
    filterService.getSheetFilter(sheetId).ifPresent(filters::add);
    filterService.getRestrictionFilterForCurrentUser(topicId).ifPresent(filters::add);
    pqlBuilder.addTopicFilters(filters);
    return pqlBuilder;
  }

  private PQLBuilder buildPQLBuilder(ParseKpiDTO parseKpiDTO, String[] unit) {
    PQLBuilder pqlBuilder =
        newPQLBuilder(
            parseKpiDTO.getTopicId(),
            parseKpiDTO.getSheetId(),
            !parseKpiDTO.isTopicFilterDisabled());
    List<String> filters =
        topicFilterManager.processTopicFilter(
            pqlBuilder.modelDesc(), parseKpiDTO.getAddTopicFilterList());

    pqlBuilder.addTopicFilters(filters);

    if (CollectionUtils.isNotEmpty(parseKpiDTO.getAddFilterList())) {
      parseKpiDTO.getAddFilterList().forEach(pqlBuilder::addComponentFilter);
    }

    int index = 0;
    Map<String, String> dimensionAlias = new HashMap<>();
    for (KpiPO kpi : parseKpiDTO.getDimensionColumnList()) {
      dimensionAlias.put(kpi.getName(), kpi.getExpression());
      pqlBuilder.addColumn(kpi.getExpression(), kpi.getName());
      unit[index] = StringUtils.isBlank(kpi.getUnit()) ? "" : kpi.getUnit();
      index++;
    }
    if (olapFilterOnResultSet) {
      List<String> resultFilters =
          searchListToFilterExprs(
              parseKpiDTO.getSearchList(),
              Objects.equals(parseKpiDTO.getLikeDisabled(), EnableTypeEnum.TRUE.getValue()));
      resultFilters.forEach(pqlBuilder::addResultFilter);
    } else {
      // REMOVE THIS LATER
      Map<String, KpiPO> dimensionKpiMap =
          parseKpiDTO.getDimensionColumnList().stream()
              .collect(Collectors.toMap(KpiPO::getName, Function.identity(), (k1, k2) -> k1));
      if (parseKpiDTO.getSearchList() != null && !parseKpiDTO.getSearchList().isEmpty()) {
        for (FieldSearchRequest fieldSearchRequest : parseKpiDTO.getSearchList()) {
          KpiPO dimension = dimensionKpiMap.get(fieldSearchRequest.getSearchKey());
          if (Objects.nonNull(dimension)) {
            if (Objects.equals(
                OperationTypeEnum.TIME.getValue(), fieldSearchRequest.getOperationType())) {
              // time类型
              pqlBuilder.addComponentFilter(
                  FilterConst.timeFiltering(
                      fieldSearchRequest.getSearchValue(),
                      dimensionAlias.get(dimension.getName())));
            } else {
              String descByCode =
                  OperationTypeEnum.getDescByCode(fieldSearchRequest.getOperationType());
              if (StringUtils.isBlank(descByCode)
                  || Objects.equals(
                      fieldSearchRequest.getOperationType(), OperationTypeEnum.LIKE.getValue())) {
                if (Objects.equals(parseKpiDTO.getLikeDisabled(), EnableTypeEnum.TRUE.getValue())) {
                  descByCode = "=";
                } else {
                  descByCode = " ILIKE ";
                  fieldSearchRequest.setSearchValue(
                      "%" + fieldSearchRequest.getSearchValue() + "%");
                }
              }
              if (Objects.equals(
                  fieldSearchRequest.getOperationType(), OperationTypeEnum.NOT_LIKE.getValue())) {
                fieldSearchRequest.setSearchValue("%" + fieldSearchRequest.getSearchValue() + "%");
              }
              pqlBuilder.addComponentFilter(
                  dimensionAlias.get(dimension.getName())
                      + " "
                      + descByCode
                      + " '"
                      + fieldSearchRequest.getSearchValue()
                      + "' ");
            }
          }
        }
      }
    }
    if (parseKpiDTO.getSortList() != null && !parseKpiDTO.getSortList().isEmpty()) {
      for (KpiVariableSortDTO sortDTO : parseKpiDTO.getSortList()) {
        pqlBuilder.addSort(sortDTO.getSortName(), sortDTO.getSortType());
      }
    }

    return pqlBuilder;
  }

  @Override
  public StructType export(ParseKpiDTO parseKpiDTO, String filePath) {
    if (parseKpiDTO.getAddTopicFilterList() == null) {
      parseKpiDTO.setAddTopicFilterList(new ArrayList<>());
    }
    try {
      List<KpiPO> dimensionColumnList = parseKpiDTO.getDimensionColumnList();
      String[] unitArr = new String[dimensionColumnList.size()];

      PQLBuilder pqlBuilder = this.buildPQLBuilder(parseKpiDTO, unitArr);

      if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.SCROLL.getValue())) {
        pqlBuilder.addLimit((parseKpiDTO.getPageNum()) * parseKpiDTO.getPageSize());
      } else if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.LIMIT.getValue())) {
        if (parseKpiDTO.getLimit() != null) {
          pqlBuilder.addLimit(parseKpiDTO.getLimit());
        }
      } else if (Objects.equals(parseKpiDTO.getRule(), VariableKpiRuleEnum.HEAD_LIMIT.getValue())) {
        pqlBuilder.topK(parseKpiDTO.getLimit());
      }
      if (parseKpiDTO.isDistinct()) {
        pqlBuilder.setDistinct(true);
      }
      pqlBuilder.run(
          df -> {
            df.sparkSession().sessionState().catalogManager().setCurrentCatalog("spark_catalog");
            // 为每个 KPI 列添加单位
            for (KpiPO kpiPO : dimensionColumnList) {
              String name = kpiPO.getName();
              String unit = kpiPO.getUnit();
              df = df.withColumn(name, functions.concat(df.col(name), functions.lit(unit)));
            }

            HadoopUtil.deletePath(filePath);
            df.write()
                .format("csv")
                .option("header", "false")
                .option("delimiter", ",")
                .option("maxRecordsPerFile", 100000)
                .option("timestampFormat", "yyyy-MM-dd HH:mm:ss")
                .save(filePath);
            return null;
          });
      return pqlBuilder.resultDF().schema();
    } finally {
      PQLEnv.clearQueryEnv();
    }
  }

  private String getKPIResult(Row[] collect) {
    if (collect.length == 0) {
      return "0";
    }
    if (collect[0].isNullAt(0)) {
      return "0";
    } else {
      return collect[0].getAs(0).toString();
    }
  }

  private List<String> getLimitResult(Row[] collect, int limit) {
    if (collect.length == 0) {
      return new ArrayList<>();
    }
    int index = 0;
    List<String> list = new ArrayList<>();
    for (Row row : collect) {
      if (row.isNullAt(0)) {
        list.add("0");
      } else {
        list.add(row.getAs(0).toString());
      }

      index++;
      if (limit > 0 && index >= limit) {
        break;
      }
    }
    return list;
  }
}
