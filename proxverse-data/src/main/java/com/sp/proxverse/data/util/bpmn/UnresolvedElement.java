package com.sp.proxverse.data.util.bpmn;

/**
 * <AUTHOR>
 * @date 2023/7/19 13:44
 */
public class UnresolvedElement implements BaseElement {

  public UnresolvedElement(String id, String name) {
    this.id = id;
    this.name = name;
  }

  private String id;
  private String name;

  @Override
  public String getId() {
    return this.id;
  }

  @Override
  public void setId(String id) {
    this.id = id;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public void setName(String name) {
    this.name = name;
  }
}
