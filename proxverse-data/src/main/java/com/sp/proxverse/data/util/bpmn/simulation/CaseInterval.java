package com.sp.proxverse.data.util.bpmn.simulation;

import com.sp.proxverse.common.model.enums.Interval;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class CaseInterval {

  /** default to start event */
  String startEvent;

  Interval interval;

  int casePreTime;

  int genCaseIfNeed(long currentTime) {
    if (interval.isTimeArrived(currentTime)) {
      return casePreTime;
    }
    return 0;
  }
}
