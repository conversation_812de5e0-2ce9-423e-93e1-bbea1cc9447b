package com.sp.process.utils

import java.{math, util}
import java.math.RoundingMode

object StaticsTools {
  def getMin(list: util.List[math.BigDecimal]): math.BigDecimal = {
    var minValue = new math.BigDecimal(Double.MaxValue)
    for (i <- 0 until list.size()) {
      if (list.get(i).compareTo(minValue) < 0) minValue = list.get(i)
    }
    minValue
  }
  def getMax(list: util.List[math.BigDecimal]): math.BigDecimal = {
    var maxValue = new math.BigDecimal(Double.MinValue)
    for (i <- 0 until list.size()) {
      if (list.get(i).compareTo(maxValue) > 0) maxValue = list.get(i)
    }
    maxValue
  }
  def getSum(list: util.List[math.BigDecimal]): math.BigDecimal = {
    var sum = new math.BigDecimal(0)
    for (i <- 0 until list.size()) {
      sum = sum.add(list.get(i))
    }
    sum
  }
  def getAVG(list: util.List[math.BigDecimal]) =
    getSum(list).divide(new math.BigDecimal(list.size), RoundingMode.HALF_UP)
  def getSTD(list: util.List[math.BigDecimal]) = getSquareMean(list).subtract(getAVG(list).pow(2))

  def getSquareMean(list: util.List[math.BigDecimal]) = {
    var sum = new math.BigDecimal(0.0)
    for (i <- 0 until list.size()) {
      val num = list.get(i)
      sum = sum.add(num.pow(2))
    }
    sum.divide(new math.BigDecimal(list.size), RoundingMode.HALF_UP)
  }
}
