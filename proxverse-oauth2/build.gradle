/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
  id 'scala'
}
repositories {
  mavenLocal()
  maven { url 'http://121.37.155.119:8081/repository/maven-public/' }
  maven { url 'https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/' }
}

sourceSets {
  main {
    java {
      srcDirs = ['src/main/scala', 'src/main/java', 'src/saToken']
    }

  }
}
dependencies {
  implementation 'org.springframework.boot:spring-boot-starter-data-ldap:2.3.2.RELEASE'
  implementation 'org.springframework.security:spring-security-config:5.3.3.RELEASE'
  implementation project(':proxverse-web-config')
  implementation project(':proxverse-service-interface')
  implementation 'org.mapstruct:mapstruct:1.4.1.Final'
  implementation 'org.mapstruct:mapstruct-processor:1.4.1.Final'
  implementation 'org.slf4j:log4j-over-slf4j:1.7.30'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.apache.commons:commons-lang3:3.12.0'
  implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.7'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'mysql:mysql-connector-java:8.0.28'
  implementation 'com.alibaba:druid:1.1.21'
  implementation 'org.springframework.boot:spring-boot-starter-jdbc:2.3.2.RELEASE'
  implementation 'org.apache.commons:commons-csv:1.8'
  implementation 'net.sourceforge.htmlunit:htmlunit-android:2.67.0'
  implementation 'org.apache.commons:commons-collections4:4.4'
  implementation 'org.hibernate:hibernate-validator:6.2.0.Final'
  implementation 'net.logstash.logback:logstash-logback-encoder:5.2'
  implementation 'com.squareup.okhttp3:okhttp:3.14.9'
  implementation 'com.huaweicloud:esdk-obs-java-bundle:3.21.8'
  implementation 'org.python:jython-standalone:2.7.0'
  implementation project(':proxverse-common')
  implementation 'io.netty:netty-all:4.1.86.Final'
  implementation 'io.netty:netty-buffer:4.1.86.Final'
  implementation 'io.netty:netty-common:4.1.86.Final'
  implementation 'com.fasterxml.jackson.core:jackson-core:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4'
  implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4'
  implementation 'com.fasterxml.jackson.module:jackson-module-scala_2.12:2.13.0'
  implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.13.3'
  implementation 'commons-fileupload:commons-fileupload:1.3.3'
  implementation 'cn.dev33:sa-token-spring-boot-starter:1.37.0'
  testImplementation 'org.springframework.boot:spring-boot-starter-test:2.3.2.RELEASE'
  compileOnly 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'com.baomidou:mybatis-plus-core:3.5.7'
  implementation 'com.baomidou:mybatis-plus-generator:3.5.7'
  implementation 'com.sp:proxverse-pql:1.0.0'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:2.3.2.RELEASE'
  implementation 'com.sp:keymgr:1.0.0'
  implementation 'com.sp:keygen:1.0.0'
}

description = 'proxverse-oauth2'
