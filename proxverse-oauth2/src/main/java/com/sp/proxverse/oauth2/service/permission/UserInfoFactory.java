package com.sp.proxverse.oauth2.service.permission;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.mapper.AdminMapper;
import com.sp.proxverse.common.model.dto.dataauth.UserDataAuth;
import com.sp.proxverse.common.model.dto.domain.Admin;
import com.sp.proxverse.interfaces.dao.service.IUserGroupDetailService;
import com.sp.proxverse.oauth2.model.UserPermissionInfo;
import com.sp.proxverse.oauth2.service.biz.UserDataAuthBizService;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserInfoFactory implements InitializingBean {

  @Autowired private AdminMapper adminMapper;

  @Autowired private IUserGroupDetailService userGroupDetailService;

  @Autowired private UserDataAuthBizService userDataAuthBizService;

  @Autowired private UserInfoUtil userInfoUtil;

  @Value("${prx.authority.log.printPermissionInfo:true}")
  private Boolean printPermissionInfo;

  @Value("${prx.authority.log.slowLogThreshold:100}")
  private Long slowLogThreshold;

  public LoadingCache<Integer, UserPermissionInfo> userPermissionInfoLoadingCache;

  @Override
  public void afterPropertiesSet() {
    userPermissionInfoLoadingCache =
        CacheBuilder.newBuilder()
            .maximumSize(20000)
            .expireAfterAccess(10, TimeUnit.HOURS)
            .build(
                new CacheLoader<Integer, UserPermissionInfo>() {

                  @Override
                  public UserPermissionInfo load(Integer userId) {
                    long start = System.currentTimeMillis();
                    Admin admin = adminMapper.getAdminById(userId);
                    if (admin == null) {
                      return new UserPermissionInfo();
                    }
                    UserPermissionInfo userInfo =
                        UserPermissionInfo.builder()
                            .userId(userId)
                            .userName(admin.getUserName())
                            .level(admin.getLevel())
                            .groupId(admin.getTenantId())
                            .build();
                    UserDataAuth userDataAuth =
                        userDataAuthBizService.selectUserDataAuthority(userId);
                    long end = System.currentTimeMillis();
                    long takeUpTime = end - start;
                    if (takeUpTime > slowLogThreshold) {
                      log.info(
                          "userId={},UserPermissionInfo cache load time=>{}", userId, takeUpTime);
                    }
                    if (userDataAuth == null) {
                      if (printPermissionInfo) {
                        log.info("userId={},UserPermissionInfo is null", userId);
                      }
                      return userInfo;
                    }
                    userInfo.setTopicIdPermissionList(userDataAuth.getTopicIdAuthList());
                    userInfo.setTopicIdPermissionMap(userDataAuth.getTopicAuthMap());
                    userInfo.setDataPoolPermissionList(userDataAuth.getPoolIdAuthList());
                    userInfo.setDataPoolPermissionMap(userDataAuth.getPoolAuthMap());
                    if (printPermissionInfo) {
                      log.info("userId={},user permissionInfo={}", userId, userInfo.toString());
                    }
                    return userInfo;
                  }
                });
  }

  public UserPermissionInfo getOrCreate(int userId) {
    return userPermissionInfoLoadingCache.getUnchecked(userId);
  }

  public void invalidPermissionCache(int userId) {
    userPermissionInfoLoadingCache.invalidate(userId);
  }

  public void invalidPermissionCacheByUserIds(List<Integer> userIds) {
    for (Integer userId : userIds) {
      userPermissionInfoLoadingCache.invalidate(userId);
    }
  }
}
