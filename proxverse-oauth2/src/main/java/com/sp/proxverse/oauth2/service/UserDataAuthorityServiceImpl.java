package com.sp.proxverse.oauth2.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sp.proxverse.common.mapper.UserDataAuthorityMapper;
import com.sp.proxverse.common.model.dict.DataAuthValueEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.PermissionQueryTypeEnum;
import com.sp.proxverse.common.model.dict.UserTypeEnum;
import com.sp.proxverse.common.model.dto.dataauth.UserDataAuthDTO;
import com.sp.proxverse.common.model.dto.domain.DataAuthority;
import com.sp.proxverse.common.model.dto.domain.UserDataAuthority;
import com.sp.proxverse.common.model.dto.domain.UserGroupDetail;
import com.sp.proxverse.common.model.enums.UserStateEnum;
import com.sp.proxverse.interfaces.dao.service.IDataAuthorityService;
import com.sp.proxverse.interfaces.dao.service.IUserDataAuthorityService;
import com.sp.proxverse.interfaces.dao.service.IUserGroupDetailService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 用户数据权限 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Service
@DS("oauth2")
@Primary
public class UserDataAuthorityServiceImpl
    extends ServiceImpl<UserDataAuthorityMapper, UserDataAuthority>
    implements IUserDataAuthorityService {

  @Autowired IDataAuthorityService dataAuthorityService;

  @Autowired private IUserGroupDetailService userGroupDetailService;

  @Autowired private UserDataAuthorityMapper userDataAuthorityMapper;

  @Override
  public String getUserTopicFilter(Integer userId, Integer dataId) {
    String filter = this.baseMapper.getUserTopicFilter(userId, dataId);
    // 这里要考虑用户组的权限配置
    List<UserGroupDetail> groupDetailList =
        userGroupDetailService.list(
            new LambdaQueryWrapper<UserGroupDetail>()
                .eq(UserGroupDetail::getUserId, userId)
                .eq(UserGroupDetail::getDeleted, 0));
    if (CollectionUtils.isEmpty(groupDetailList)) {
      return filter;
    }
    DataAuthority dataAuthority = dataAuthorityService.getByDataId(dataId, 1);
    if (dataAuthority == null) {
      return filter;
    }
    List<Integer> groupIdList =
        groupDetailList.stream()
            .map(UserGroupDetail::getUserGroupId)
            .distinct()
            .collect(Collectors.toList());
    List<UserDataAuthority> userDataAuthorities =
        this.baseMapper.selectList(
            new LambdaQueryWrapper<UserDataAuthority>()
                .in(UserDataAuthority::getUserGroupId, groupIdList)
                .eq(UserDataAuthority::getDataAuthorityId, dataAuthority.getId())
                .eq(UserDataAuthority::getDeleted, 0));

    List<String> groupFilterList =
        userDataAuthorities.stream()
            .map(UserDataAuthority::getFilter)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
    groupFilterList.add(filter);
    return StringUtils.join(groupFilterList, " or ");
  }

  @Override
  public List<UserDataAuthority> getUserAuthListByAuthId(String dataAuthorityId) {
    return this.baseMapper.selectList(
        new LambdaQueryWrapper<UserDataAuthority>()
            .eq(UserDataAuthority::getDataAuthorityId, dataAuthorityId)
            .eq(UserDataAuthority::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public List<UserDataAuthDTO> selectUserAndGroupPage(
      Integer queryType,
      Integer authorityId,
      Integer id,
      String name,
      Integer pageNum,
      Integer pageSize) {
    if (pageNum == null || pageSize == null) {
      pageNum = 1;
      pageSize = 10;
    }
    Integer startNum = (pageNum - 1) * pageSize;
    if (Objects.equals(name, "")) {
      name = null;
    }
    if (PermissionQueryTypeEnum.queryGroup(queryType)) {
      return baseMapper.selectGroupPage(name, authorityId, startNum, pageSize);
    } else if (PermissionQueryTypeEnum.queryUser(queryType)) {
      return baseMapper.selectUserPage(
          id, name, UserStateEnum.getCommonUserStatus(), authorityId, startNum, pageSize);
    } else {
      return queryUserAndGroup(id, name, authorityId, startNum, pageSize);
    }
  }

  private List<UserDataAuthDTO> queryUserAndGroup(
      Integer id, String name, Integer authorityId, Integer startNum, Integer pageSize) {
    int groupCount =
        this.selectUserAndGroupCount(PermissionQueryTypeEnum.USER_GROUP.getValue(), null, name);
    long groupLeaveCount = groupCount - startNum;
    if (groupLeaveCount >= pageSize) {
      // all in group
      return baseMapper.selectGroupPage(name, authorityId, startNum, pageSize);
    } else if (groupLeaveCount <= 0) {
      // all in user
      int queryUserStartNum = startNum - groupCount;
      return baseMapper.selectUserPage(
          id, name, UserStateEnum.getCommonUserStatus(), authorityId, queryUserStartNum, pageSize);
    } else {
      // group and user
      List<UserDataAuthDTO> groupList =
          baseMapper.selectGroupPage(name, authorityId, startNum, pageSize);
      int queryUserPageSize = pageSize - groupList.size();
      List<UserDataAuthDTO> userList =
          baseMapper.selectUserPage(
              id, name, UserStateEnum.getCommonUserStatus(), authorityId, 0, queryUserPageSize);
      groupList.addAll(userList);
      return groupList;
    }
  }

  @Override
  public int selectUserAndGroupCount(Integer queryType, Integer id, String name) {
    if (id != null) {
      // cache input
      return 0;
    }
    if (StringUtils.isBlank(name)) {
      name = null;
    }
    if (PermissionQueryTypeEnum.queryUser(queryType)) {
      return userDataAuthorityMapper.selectUserCount(name);
    } else if (PermissionQueryTypeEnum.queryGroup(queryType)) {
      return userDataAuthorityMapper.selectGroupCount(name);
    } else {
      int userCount = userDataAuthorityMapper.selectUserCount(name);
      int userGroupCount = userDataAuthorityMapper.selectGroupCount(name);
      return userCount + userGroupCount;
    }
  }

  @Override
  public List<UserDataAuthority> getUserDataAuthorityListByUserId(Integer userId) {
    return this.list(
        new LambdaQueryWrapper<UserDataAuthority>()
            .eq(UserDataAuthority::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .eq(UserDataAuthority::getUserId, userId)
            .ge(UserDataAuthority::getAuthorityValue, DataAuthValueEnum.VIEW.getValue()));
  }

  @Override
  public List<UserDataAuthority> getUserDataAuthorityListByGroupIds(List<Integer> groupIds) {
    if (CollectionUtils.isEmpty(groupIds)) {
      return new ArrayList<>();
    }
    return this.list(
        new LambdaQueryWrapper<UserDataAuthority>()
            .eq(UserDataAuthority::getDeleted, DeletedEnum.NO_DELETED.getValue())
            .in(UserDataAuthority::getUserGroupId, groupIds)
            .ge(UserDataAuthority::getAuthorityValue, DataAuthValueEnum.VIEW.getValue()));
  }

  @Override
  public List<UserDataAuthority> listByDataAuthorityId(Integer dataAuthorityId) {
    return this.list(
        new LambdaQueryWrapper<UserDataAuthority>()
            .eq(UserDataAuthority::getDataAuthorityId, dataAuthorityId)
            .eq(UserDataAuthority::getDeleted, DeletedEnum.NO_DELETED.getValue()));
  }

  @Override
  public UserDataAuthority getOneByDataAuthIdAndUserType(
      Integer dataAuthId, Integer userType, Integer userId) {
    return this.getOne(
        new LambdaQueryWrapper<UserDataAuthority>()
            .eq(UserDataAuthority::getDataAuthorityId, dataAuthId)
            .eq(
                Objects.equals(userType, UserTypeEnum.USER.getValue()),
                UserDataAuthority::getUserId,
                userId)
            .eq(
                Objects.equals(userType, UserTypeEnum.USER_GROUP.getValue()),
                UserDataAuthority::getUserGroupId,
                userId)
            .eq(UserDataAuthority::getDeleted, 0));
  }
}
