package com.sp.proxverse.oauth2.controller;

import static global.namespace.fun.io.bios.BIOS.file;

import com.alibaba.fastjson.JSON;
import com.sp.proxverse.common.exception.ErrorCode;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.dto.domain.License;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.listener.service.LicensePublishService;
import com.sp.proxverse.common.model.vo.license.LicenseResp;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.service.ILicenseService;
import com.sp.proxverse.oauth2.service.DummyAdminService;
import global.namespace.fun.io.api.Source;
import global.namespace.truelicense.api.ConsumerLicenseManager;
import global.namespace.truelicense.api.LicenseManagementException;
import global.namespace.truelicense.api.LicenseValidationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import java.io.File;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 许可证相关接口
 *
 * <AUTHOR>
 * @since 2022-07-06
 */
@ApiIgnore
@Api(tags = "许可证相关接口")
@RestController
@RequestMapping("/license")
@Slf4j
public class LicenseController extends BaseController {

  @Value("${oauth2.license.path}")
  private String LICENSE_PATH;

  @Autowired @Lazy ILicenseService licenseService;

  @Autowired private LicensePublishService licensePublishService;

  @Autowired DummyAdminService dummyAdminService;

  @PostMapping(value = "/install")
  @ApiOperation(value = "用户端上传证书")
  public Response<Boolean> upload(@RequestParam MultipartFile file, HttpServletRequest request) {
    licenseService.install(file, request);
    return Response.success(true);
  }

  @PostMapping(value = "/activate")
  @ApiOperation(value = "激活证书")
  @ApiModelProperty(value = "sn")
  public Response<Object> activate(@RequestBody License bean) {
    License lic = licenseService.getLicense(bean.getSn());
    if (lic == null) {
      return Response.fail();
    }
    File licFile = new File(LICENSE_PATH, lic.getSn() + ".lic");

    ConsumerLicenseManager manager = com.proxverse.keymgr.LicenseManager.get();
    Source source = file(licFile);
    try {
      manager.uninstall();
      manager.install(source);
      global.namespace.truelicense.api.License load = manager.load();
      log.info("license info=>{}", JSON.toJSONString(load));

      manager.verify();
    } catch (LicenseValidationException ex) {
      log.error("LicenseValidationException ", ex);
      return Response.failOfMsg(
          ErrorCode.LICENSE_NOT_VALID, I18nUtil.getMessage(I18nConst.LICENSE_ILLEGAL));
    } catch (LicenseManagementException ex) {
      log.error("LicenseManagementException ", ex);
      return Response.failOfMsg(
          ErrorCode.LICENSE_NOT_FOUND, I18nUtil.getMessage(I18nConst.LICENSE_FLLE_NOT_FOUND));
    }

    licenseService.inactivateAll();
    licenseService.activate(lic);
    licenseService.validLicenseCacheInvalid();
    licensePublishService.publishLicenseActiveEvent();
    return Response.success(true);
  }

  @PostMapping(value = "/inactivate")
  @ApiOperation(value = "停用证书")
  @ApiModelProperty(value = "sn")
  public Response<Object> inactivate(@RequestBody License bean) {
    licenseService.inactivate(bean);
    licenseService.validLicenseCacheInvalid();
    licensePublishService.publishLicenseActiveEvent();
    return Response.success(true);
  }

  @PostMapping(value = "/delete")
  @ApiOperation(value = "删除证书")
  @ApiModelProperty(value = "sn")
  public Response<Object> delete(@RequestBody License bean) {
    licenseService.delete(bean);
    return Response.success(true);
  }

  @GetMapping(value = "/list")
  @ApiOperation(value = "获取用户已安装的许可证列表")
  public Response<List<License>> list() {
    return Response.success(licenseService.licenseList());
  }

  @GetMapping(value = "/getCurrentLicense")
  @ApiOperation(value = "获取用户当前使用的许可证信息")
  public Response<LicenseResp> getCurrentLicense() {
    License licenseInfo = licenseService.getLicenseInfo();
    if (licenseInfo == null) {
      // 这里如果给了null，前端的空间、人员数、有效期这块区域不再显示
      return Response.success(null);
    }
    LicenseResp resp =
        LicenseResp.builder()
            .sn(licenseInfo.getSn())
            .filename(licenseInfo.getFilename())
            .capacity(licenseInfo.getCapacity())
            .consumerAmount(licenseInfo.getConsumerAmount())
            .consumerType(licenseInfo.getConsumerType())
            .modelCountLimit(licenseInfo.getModelCountLimit())
            .notAfter(licenseInfo.getNotAfter())
            .notAfterZh(licenseInfo.getNotAfterZh())
            .notBefore(licenseInfo.getNotBefore())
            .status(licenseInfo.getStatus())
            .build();
    resp.setModelCount(dummyAdminService.queryModelCount(null));
    return Response.success(resp);
  }
}

/**
 * mvn org.apache.maven.plugins:maven-archetype-plugin:generate --batch-mode \
 * -DarchetypeGroupId='global.namespace.truelicense-maven-archetype' \
 * -DarchetypeArtifactId='truelicense-maven-archetype' \ -DarchetypeVersion='4.0.3' \
 * -DartifactId='licmgr' \ -Dcompany='Shanghai Shang Ping Tech' \ -DgroupId='com.proxverse' \
 * -Dpassword='sp2022pro1234_' \ -Dsubject='proxverse' \ -DtrueLicenseVersion='4.0.3' \
 * -Dversion='1.0-SNAPSHOT' cd licmgr chmod +x mvnw ./mvnw clean verify -Pintegration-test
 */
