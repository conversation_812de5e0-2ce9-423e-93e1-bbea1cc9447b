package com.sp.proxverse.oauth2.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.prx.service.BaseFileService;
import com.sp.proxverse.common.UserInfoUtil;
import com.sp.proxverse.common.exception.BizException;
import com.sp.proxverse.common.mapper.CompanyPropertyMapper;
import com.sp.proxverse.common.mapper.GroupMapper;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dto.domain.Group;
import com.sp.proxverse.common.model.enums.CompanyPropertyEnum;
import com.sp.proxverse.common.model.i18n.I18nConst;
import com.sp.proxverse.common.model.po.CompanyPropertyPo;
import com.sp.proxverse.common.util.DateTimeUtil;
import com.sp.proxverse.common.util.I18nUtil;
import com.sp.proxverse.interfaces.dao.impl.CompanyPropertyServiceImpl;
import com.sp.proxverse.interfaces.dao.service.IGroupService;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.CompanyConfigInfoRes;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.CompanyInfoVo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.GetGroupIconReq;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.GroupIconInfo;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.GroupIconInfoRes;
import com.sp.proxverse.interfaces.service.remote.model.oauth2.UpdateGroupPropertyReq;
import java.util.Date;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 用户数据权限和请求路径关联关系 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Service
@DS("oauth2")
@Primary
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Group> implements IGroupService {
  @Autowired UserInfoUtil userInfoUtil;

  @Autowired CompanyPropertyServiceImpl companyPropertyService;

  @Autowired CompanyPropertyMapper companyPropertyMapper;

  @Autowired private BaseFileService baseFileService;

  private static final Logger logger = LoggerFactory.getLogger(GroupServiceImpl.class);

  @Override
  public Group getGroup() {
    try {
      Integer groupId = userInfoUtil.getUserInfo().getGroupId();
      return baseMapper.selectById(groupId);
    } catch (Exception e) {
      log.error("userInfoUtil is null", e);
    }
    return new Group();
  }

  @Override
  public Group getByEmail(String email) {
    return baseMapper.selectOne(
        new LambdaQueryWrapper<Group>()
            .eq(Group::getEmail, email)
            .eq(Group::getStatus, 30)
            .last("LIMIT 1"));
  }

  @Override
  public String getUsernameByTenantId(Integer tenantId) {
    Group byId = baseMapper.selectById(tenantId);
    return Objects.isNull(byId) ? null : byId.getEmail();
  }

  @Override
  public CompanyInfoVo getCompanyInfo(String companyId) {
    CompanyInfoVo companyInfoVo = new CompanyInfoVo();

    Group one = this.getById(companyId);
    if (one == null) {
      return companyInfoVo;
    }
    companyInfoVo.setPhone(one.getPhone());
    companyInfoVo.setCompanyName(one.getCompany());
    companyInfoVo.setCompanyEmail(one.getEmail());
    companyInfoVo.setSource(
        one.getType() == 1
            ? I18nUtil.getMessage(I18nConst.USER_APPLY)
            : I18nUtil.getMessage(I18nConst.BACK_APPLY));

    String dateFull = DateTimeUtil.date2String(one.getExpiration());
    companyInfoVo.setExpiration(dateFull.split(" ")[0]);
    companyInfoVo.setExpiredFlag(DateTimeUtil.dateCompare(one.getExpiration(), new Date()));
    companyInfoVo.setSpaceSize(one.getSpaceSize());
    companyInfoVo.setModelCountLimit(one.getModelCountLimit());
    companyInfoVo.setEmployeesNumber(one.getEmployeesNumber());

    CompanyPropertyPo companyProperty =
        companyPropertyService.getCompanyProperty(one.getId(), CompanyPropertyEnum.THEME_COLOR);
    if (companyProperty != null) {
      companyInfoVo.setColorConfig(companyProperty.getPropertyValue());
    }
    return companyInfoVo;
  }

  @Override
  public String getGroupColor(String companyId) {
    CompanyPropertyPo companyProperty =
        companyPropertyService.getCompanyProperty(
            Integer.valueOf(companyId), CompanyPropertyEnum.THEME_COLOR);
    if (companyProperty == null) {
      return "";
    }
    return companyProperty.getPropertyValue();
  }

  @Override
  public Boolean removeCompanyProperty(UpdateGroupPropertyReq updateCompanyPropertyReq) {
    return companyPropertyMapper.removeCompanyProperty(
        updateCompanyPropertyReq.getCompanyId(),
        updateCompanyPropertyReq.getCompanyPropertyEnum().name());
  }

  @Override
  public CompanyConfigInfoRes getCompanyConfigInfo() {
    // 针对私有化部署，此时只有一个商户
    Group group = getGroupInfo(null);
    CompanyPropertyPo companyPropertyPo =
        companyPropertyService.getCompanyProperty(group.getId(), CompanyPropertyEnum.ICON);
    if (Objects.isNull(companyPropertyPo)) {
      return new CompanyConfigInfoRes();
    }

    CompanyConfigInfoRes groupIconInfoRes = new CompanyConfigInfoRes();

    GroupIconInfo groupIconInfo =
        JSON.parseObject(companyPropertyPo.getPropertyValue(), GroupIconInfo.class);
    if (Objects.isNull(groupIconInfo)) {
      return new CompanyConfigInfoRes();
    }

    BeanUtils.copyProperties(groupIconInfo, groupIconInfoRes);
    if (groupIconInfo.getLandingPageFileId() != null) {
      groupIconInfoRes.setLandingPageImage(
          baseFileService.getBase64EncodedImage(groupIconInfo.getLandingPageFileId()));
    }

    if (groupIconInfo.getLandingPageIconFileId() != null) {
      groupIconInfoRes.setLandingPageIcon(
          baseFileService.getBase64EncodedImage(groupIconInfo.getLandingPageIconFileId()));
    }
    if (groupIconInfo.getWebFileId() != null) {
      groupIconInfoRes.setWebIcon(
          baseFileService.getBase64EncodedImage(groupIconInfo.getWebFileId()));
    }

    return groupIconInfoRes;
  }

  @Override
  public Boolean updateGroupProperty(UpdateGroupPropertyReq updateGroupPropertyReq) {
    if (updateGroupPropertyReq.getCompanyPropertyEnum() == null) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON));
    }
    return companyPropertyService.updateCompanyProperty(
        updateGroupPropertyReq.getCompanyId(),
        updateGroupPropertyReq.getCompanyPropertyEnum(),
        updateGroupPropertyReq.getPropertyConfig());
  }

  @Override
  public GroupIconInfoRes getGroupIcon(GetGroupIconReq getGroupIconReq) {
    Group group = getGroupInfo(getGroupIconReq.getGroupId());

    CompanyPropertyPo companyPropertyPo =
        companyPropertyService.getCompanyProperty(group.getId(), CompanyPropertyEnum.ICON);
    GroupIconInfoRes groupIconInfoRes = new GroupIconInfoRes();
    groupIconInfoRes.setGroupId(group.getId());

    try {
      Class.forName("com.sp.proxverse.globalFilter.GlobalFilterImpl");
      groupIconInfoRes.setShowGlobalFilter("1");
    } catch (Exception e) {
      logger.error("com.sp.proxverse.globalFilter.GlobalFilterImpl class dont exist ");
      groupIconInfoRes.setShowGlobalFilter("1");
    }

    if (companyPropertyPo == null || StringUtils.isEmpty(companyPropertyPo.getPropertyValue())) {
      return groupIconInfoRes;
    }
    GroupIconInfo groupIconInfo =
        JSON.parseObject(companyPropertyPo.getPropertyValue(), GroupIconInfo.class);
    BeanUtils.copyProperties(groupIconInfo, groupIconInfoRes);

    if (groupIconInfo.getLandingPageFileId() != null) {
      groupIconInfoRes.setLandingPageImage(
          baseFileService.getBase64EncodedImage(groupIconInfo.getLandingPageFileId()));
    }

    if (groupIconInfo.getWebFileId() != null) {
      groupIconInfoRes.setWebIcon(
          baseFileService.getBase64EncodedImage(groupIconInfo.getWebFileId()));
    }

    if (groupIconInfo.getLandingPageIconFileId() != null) {
      groupIconInfoRes.setLandingPageIcon(
          baseFileService.getBase64EncodedImage(groupIconInfo.getLandingPageIconFileId()));
    }

    if (groupIconInfo.getLogoFileId() != null) {
      groupIconInfoRes.setLogoIcon(
          baseFileService.getBase64EncodedImage(groupIconInfo.getLogoFileId()));
    }
    return groupIconInfoRes;
  }

  private Group getGroupInfo(Integer groupId) {
    Group group;
    if (groupId != null) {
      group = this.getById(groupId);
    } else {
      group =
          this.getOne(
              new LambdaQueryWrapper<Group>()
                  .eq(Group::getDeleted, DeletedEnum.NO_DELETED.getValue())
                  .orderByDesc(Group::getId)
                  .last("LIMIT 1"));
    }
    if (group == null) {
      throw new IllegalArgumentException("User group does not exist");
    }
    return group;
  }
}
