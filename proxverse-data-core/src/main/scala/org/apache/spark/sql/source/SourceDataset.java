package org.apache.spark.sql.source;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/20 11:41
 */
public class SourceDataset {


    public static final Integer DEFAULT_LIMIT = 50;

    public Dataset<Row> source;

    String[] originalColumnNames;

    public SourceDataset(Dataset<Row> source) {
        this.source = source;
    }

    public String[] columns() {
        String[] columns = source.columns();
        return columns;
    }

    public List<Row> collect() {
        List<Row> rows = source.collectAsList();
        return rows;
    }

    public Row[] collectRow() {
        List<Row> rows = source.collectAsList();
        return rows.toArray(new Row[0]);
    }

    public List<Map<String, String>> collectToMap() {
        String[] names = source.columns();
        List<Map<String, String>> list = new ArrayList<>();
        List<Row> rows = source.collectAsList();
        for (Row r : rows) {
            Map<String, String> map = new HashMap<>();
            for (String s : names) {
                map.put(s, r.getAs(s) + "");
            }
            list.add(map);
        }
        return list;
    }


    public SourceDataset limit(Integer limit) {
        if (limit != null) {
            source = source.limit(limit);
        } else {
            return defaultLimit();
        }
        return this;
    }


    public SourceDataset defaultLimit() {
        source = source.limit(DEFAULT_LIMIT);
        return this;
    }

    public void setOriginalColumnNames(String[] originalColumnNames) {
        this.originalColumnNames = originalColumnNames;
    }

    public String[] getOriginalColumnNames() {
        return this.originalColumnNames;
    }


    public SourceDataset withColumn(String columnName, String expr) {
        source = source.withColumn(columnName, org.apache.spark.sql.functions.expr(expr));
        return this;
    }
}
