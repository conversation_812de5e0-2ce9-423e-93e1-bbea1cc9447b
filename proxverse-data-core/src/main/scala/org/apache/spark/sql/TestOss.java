package org.apache.spark.sql;

import java.io.IOException;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

public class TestOss {
  public static void main(String[] args) throws IOException {

    Configuration configuration = new Configuration();
    configuration.set("fs.AbstractFileSystem.oss.impl", "com.aliyun.jindodata.oss.OSS");
    configuration.set("fs.oss.impl", "com.aliyun.jindodata.oss.JindoOssFileSystem");
    configuration.set("fs.oss.accessKeyId", "LTAI5t6r5KrDZA3cUgorAWr6");
    configuration.set("fs.oss.accessKeySecret", "******************************");
    configuration.set("fs.oss.endpoint", "oss-cn-shanghai.aliyuncs.com");

    Path path = new Path("oss://pm-test-2/test");
    FileSystem fileSystem = FileSystem.get(configuration);

    try (FSDataOutputStream fsDataOutputStream = fileSystem.create(path);) {
        fsDataOutputStream.write(1);
    }


  }
}
