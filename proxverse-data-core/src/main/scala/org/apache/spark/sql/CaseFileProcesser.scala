package org.apache.spark.sql

import org.apache.spark.sql.datasource.DataSource
import org.apache.spark.sql.datasource.index.TableDesc
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataTypes, StringType}
object CaseFileProcesser {

  def process(
      sparkSession: SparkSession,
      caseIdName: String,
      timeStampName: String,
      eventName: String,
      startTimeStampName: String,
      tableName: String,
      caseTableName: String,
      caseTableJoinKey: String,
      vCaseTableName: String,
      variantTableName: String,
      bucketNum: Int,
      tableDesc: TableDesc,
      alignCaseTableData: Boolean): Unit = {
    SparkUtils.doWithJobDesc(
      s"build case table ${tableName}",
      SparkUtils.runWithConf(
        { conf =>
          conf.setLocalProperty("spark.sql.starry.enabled", "false")
        },
        { sparkSession =>
          val activityTable = sparkSession.table(tableName)
          val hasEncodingColumn =
            activityTable.schema.names.contains(PQLConstants.ENCODE_COLUMN_NAME)
          val isStringEventColumn =
            activityTable.schema
              .filter(_.name.equals(eventName))
              .head
              .dataType
              .sameType(StringType)

          val tsColumn =
            if (DataTypes.DateType.sameType(
                activityTable.select(timeStampName).schema.fields.head.dataType)) {
              col(timeStampName).cast(DataTypes.TimestampType).as(timeStampName)
            } else {
              col(timeStampName)
            }

          val startTimeStampColumn =
            if (DataTypes.DateType.sameType(
                activityTable.select(startTimeStampName).schema.fields.head.dataType)) {
              col(startTimeStampName).cast(DataTypes.TimestampType).as(startTimeStampName)
            } else {
              col(startTimeStampName)
            }

          val eventCol = col("event_list").getField(eventName)
          val timestampCol = col("event_list").getField(timeStampName)
          val startTimestampCol = col("event_list").getField("startTS")

          /**
           * case_id   case_list
           * 1,2,3,
           *
           * case_id event ts
           *
           * case_id (ts, event)
           *
           * case_id list<(ts, event)>
           * case_id sort(list<(ts, event)>)
           * case_id  list<event>      sort(list<(ts, event)>)
           */
          //SparkSessionEnv.getSparkSession.read.parquet(tmp_dir + "/column_data")
          val caseFrame = if (hasEncodingColumn) {
            activityTable
              .sortWithinPartitions(
                PQLConstants.ENCODE_COLUMN_NAME,
                tableDesc.sortColumn.map(e => s"`${e}`"): _*)
              .filter(col(timeStampName).isNotNull && col(eventName).isNotNull && col(
                caseIdName).isNotNull)
              .select(
                col(caseIdName),
                col(PQLConstants.ENCODE_COLUMN_NAME),
                struct(col(timeStampName), col(startTimeStampName).as("startTS"), col(eventName))
                  .as("event"))
              .groupBy(col(PQLConstants.ENCODE_COLUMN_NAME), col(caseIdName).as("case_id"))
              .agg(collect_list(col("event")).as("event_list"))
              .select(
                col("case_id"),
                col(PQLConstants.ENCODE_COLUMN_NAME),
                array_join(eventCol, ",").as("dag"),
                col("event_list").as("event_list"))
          } else {
            activityTable
              .sortWithinPartitions(tableDesc.sortColumn.head, tableDesc.sortColumn.drop(1): _*)
              .filter(col(timeStampName).isNotNull && col(eventName).isNotNull && col(
                caseIdName).isNotNull)
              .select(
                col(caseIdName),
                struct(tsColumn, startTimeStampColumn, col(eventName)).as("event"))
              .groupBy(col(caseIdName).as("case_id"))
              .agg(collect_list(col("event")).as("event_list"))
              .select(
                col("case_id"),
                array_join(eventCol, ",").as("dag"),
                col("event_list").as("event_list"))
          }

          val new_varant_id_dict = buildVariantDict(caseFrame, "dag")
          val broadcast_new_varant_id_dict =
            sparkSession.sparkContext.broadcast(new_varant_id_dict)
          val encode_varant_id_function = udf((value: String) => {
            if (value == null) {
              Option.empty[Int]
            } else {
              Option.apply(broadcast_new_varant_id_dict.value.get(value))
            }
          })
          var caseColumns = Seq(
            col("case_id").as("preCaseId"),
            encode_varant_id_function(col("dag")).as("variantId"),
            (unix_timestamp(element_at(timestampCol, size(timestampCol))) - unix_timestamp(
              element_at(startTimestampCol, 1))).as("caseDuration"),
            element_at(eventCol, size(eventCol)).as("endEvent"),
            element_at(timestampCol, size(timestampCol)).as("caseTime"),
            element_at(eventCol, lit(1)).as("startEvent"),
            when(eventCol.isNull, lit(0)).otherwise(size(eventCol)).as("eventNum"),
            (size(eventCol) - size(array_distinct(eventCol))).as("reworkEventNum"),
            element_at(timestampCol, lit(1)).as("startTime"),
            eventCol.as("sortedEventList"),
            timestampCol.as("sortedTSList"),
            col("dag").as("variant"))
          if (hasEncodingColumn) {
            caseColumns = caseColumns ++ Seq(col(PQLConstants.ENCODE_COLUMN_NAME))
          }
          if (isStringEventColumn) {
            val eventDict = buildVariantDict(activityTable, eventName)
            val broadcast_eventDict = sparkSession.sparkContext.broadcast(eventDict)
            val evt_start_code = broadcast_eventDict.value.size()
            val evt_end_code = evt_start_code + 1
            val eventEncoded = udf((value: Seq[String]) => {
              if (value == null) {
                null
              } else {
                Seq(evt_start_code) ++ value.map(v => broadcast_eventDict.value.get(v)) ++ Seq(
                  evt_end_code)
              }
            })
            caseColumns = caseColumns ++ Seq(
              eventEncoded(eventCol)
                .as(PQLConstants.ENCODE_EVENT_LIST_COLUMN_NAME_V1))
          }

          // caseTime,variantId,caseDuration,endEvent,startEvent,eventNum,preCaseId,reworkEventNum
          val receipt_case_df = if (caseTableName == null || !alignCaseTableData) {
            caseFrame.select(caseColumns: _*)
          } else {
            val caseTable = sparkSession.table(caseTableName).as("case_table")
            if (hasEncodingColumn) {
              val newCaseColumns = Seq(
                col(s"`case_table`.${caseTableJoinKey}").as("preCaseId"),
                col(s"`case_table`.${PQLConstants.ENCODE_COLUMN_NAME}")
                  .as(PQLConstants.ENCODE_COLUMN_NAME)) ++ caseColumns.filterNot(c =>
                c.named.name.equals("preCaseId") || c.named.name.equals("encoded_case_id"))
              caseTable
                .select(
                  s"`case_table`.${caseTableJoinKey}",
                  s"`case_table`.${PQLConstants.ENCODE_COLUMN_NAME}")
                .join(caseFrame.as("v_table"), Seq(PQLConstants.ENCODE_COLUMN_NAME), "left")
                .select(newCaseColumns: _*)
            } else {
              val newCaseColumns =
                Seq(col(s"case_table.${caseTableJoinKey}").as("preCaseId")) ++ caseColumns
                  .drop(1)
              caseTable
                .select(s"`case_table`.`$caseTableJoinKey`")
                .join(
                  caseFrame.as("v_table"),
                  expr(s"`case_table`.`$caseTableJoinKey`= v_table.case_id"),
                  "left")
                .select(newCaseColumns: _*)
            }
          }
          DataSource.deleteTable(vCaseTableName)
          receipt_case_df
            .repartition(bucketNum, col("preCaseId"))
            .write
            .sortBy("preCaseId")
            .bucketBy(bucketNum, "preCaseId")
            .format("parquet")
            .mode("overwrite")
            .saveAsTable(vCaseTableName)
          DataSource.analyzeTable(sparkSession, vCaseTableName)
          DataSource.deleteTable(variantTableName)

          sparkSession
            .table(vCaseTableName)
            .filter(col("variant").isNotNull)
            .groupBy(col("variant"), col("variantId").as("variant_id"))
            .agg(count("*").as("case_count"))
            .write
            .format("parquet")
            .mode("overwrite")
            .saveAsTable(variantTableName)
          DataSource.analyzeTable(sparkSession, variantTableName)
        }))
  }

  def buildDict(
      cleanedDF: DataFrame,
      column_name: String,
      old_dict: Map[String, Int]): java.util.LinkedHashMap[String, Int] = {
    val distinct_values = cleanedDF
      .select(col(column_name))
      .distinct()
      .sort(column_name)
      .collect()
      .map(_.getString(0))

    var map = new java.util.LinkedHashMap[String, Int]
    distinct_values
      .filterNot(old_dict.contains)
      .zipWithIndex
      .foreach(tp => map.put(tp._1, (tp._2 + old_dict.size)))
    map
  }

  def buildVariantDict(
      cleanedDF: DataFrame,
      column_name: String): java.util.LinkedHashMap[String, Int] = {
    val distinct_values =
      cleanedDF.select(column_name).distinct().orderBy(column_name).collect().map(_.getString(0))
    val map = new java.util.LinkedHashMap[String, Int]
    distinct_values.zipWithIndex
      .foreach(tp => map.put(tp._1, tp._2))
    map
  }
}
