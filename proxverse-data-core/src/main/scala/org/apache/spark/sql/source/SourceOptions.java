package org.apache.spark.sql.source;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/11 15:14
 */
public interface SourceOptions {
    Map<String, String> requireOptions();

    Map<String, String> extensionOptions();

    Map<String, String> defaultOptions();


    SourceOptions options(String key, String value);


    SourceOptions options(Map<String, String> options);


    List<String> getRequireOptionsKey();


    default boolean validate(Map<String, String> options) {
        for (String key : getRequireOptionsKey()) {
            if (!options.containsKey(key)) {
                return false;
            }
        }
        return true;
    }

    default boolean validate() {
        for (String key : getRequireOptionsKey()) {
            if (!requireOptions().containsKey(key)) {
                return false;
            }
        }
        return true;
    }


}
