/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql

import com.prx.starry.Starry
import com.sp.proxverse.common.util.FileUtils.readFileAsString
import io.delta.sql.DeltaSparkSessionExtension

import java.io.{File, IOException}
import java.sql._
import org.apache.spark.internal.Logging
import org.apache.spark.sql.SparkSession.{Builder, setActiveSession}
import org.apache.spark.sql.catalog.StarryCatalog
import org.apache.spark.sql.catalyst.optimizer.CollapseProject
import org.apache.spark.sql.connector.catalog.CatalogExtension
import org.apache.spark.sql.dialect.RegisterHiveSqlDialect
import org.apache.spark.sql.execution.columnar.cache.ColumnCacheExtension
import org.apache.spark.sql.internal.SharedState
import org.apache.spark.sql.pql.expressions.{CalcTime, CalcTimeFilter, CalcTimeWithWorkDay, EventDuration, EventDurationAVG, EventFilter, Process, Rework, SourceAny, SourceAnyWithStart, SourceFirst, SourceWithStart, TargetAny, TargetAnyWithEnd, TargetLast, TargetWithEnd}
import org.apache.spark.sql.pql.extension.PMExtension
import org.apache.spark.sql.pql.optimizer.{PushDownExprInAggregate, RewriteSingleCountTable}
import org.apache.spark.sql.pql.transform.CustomDictExprRewrite
import org.apache.spark.sql.utils.SQLUtils
import org.apache.spark.sql.execution.dict.{ExecutorDictManager, GlobalDictRegistry}
import org.apache.spark.sql.pql.dict.ColumnDictIndexCache
import org.apache.spark.ui.StarryUI
import org.apache.spark.util.event.{ServerEnv, SparkRestartEvent}

import scala.collection.JavaConverters._
import scala.reflect.runtime.currentMirror
import scala.reflect.runtime.universe._

object SparkSessionEnv extends Logging {
  @volatile
  private var spark: SparkSession = _
  @volatile
  private var initializingThread: Thread = null

  private val initFunctions =
    new java.util.concurrent.ConcurrentHashMap[String, SparkSession => Unit]()

  private val sparkConf = new java.util.concurrent.ConcurrentHashMap[String, String]()

  var sparkMaster = "local[16]"

  var columnarEnabled = "true"

  var metaCacheEnabled = "true"

  var healthTrackerEnabled = false

  def addSparkConf(key: String, value: String): Unit = {
    sparkConf.put(key, value)
  }

  def setSparkMaster(value: String): Unit = {
    sparkMaster = value
  }

  def setColumnarEnabled(value: String): Unit = {
    columnarEnabled = value
  }

  def setMetaCacheEnabled(value: String): Unit = {
    metaCacheEnabled = value
  }
  def setHealthTrackerEnabled(value: Boolean): Unit = {
    healthTrackerEnabled = value
  }

  def getSparkSession: SparkSession = withClassLoad {
    if (isEmptyResource()) {
      var count = 0
      while (isEmptyResource() && count < 3) {
        Thread.sleep(5 * 1000)
        logWarning("spark executor is empty, may be has some error , will be stop to restart.")
        count += 1
      }
      if (isEmptyResource()) {
        spark.stop()
      }
    }
    if (spark == null || spark.sparkContext.isStopped) {
      logInfo("Init spark.")
      initSpark()
    }
    setActiveSession(spark)
    spark
  }

  private def isEmptyResource(): Boolean = {
    healthTrackerEnabled && spark != null &&
    !spark.sparkContext.isStopped &&
    !spark.sparkContext.isLocal && spark.sparkContext.getExecutorIds().isEmpty
  }

  def setSparkSession(sparkSession: SparkSession): Unit = {
    spark = sparkSession
  }

  def init(): Unit = withClassLoad {
    getSparkSession
  }

  def withClassLoad[T](body: => T): T = {
    val t = body
    t
  }

  def initSpark(): Unit = withClassLoad {
    this.synchronized {
      if (initializingThread == null && (spark == null || spark.sparkContext.isStopped)) {
        initializingThread = new Thread(new Runnable {
          override def run(): Unit = {
            if (spark != null) {
              ServerEnv.postSynEvent(new SparkRestartEvent())
              ExecutorDictManager.dictCache.invalidateAll()
              ExecutorDictManager.executionDictCache.invalidateAll()
              ColumnDictIndexCache.invalidateAll()
              GlobalDictRegistry.invalidateAll()
            }
            try {
              if (metaCacheEnabled.toBoolean) {
                changeHiveCatalog()
              }
              if (System.getProperty("os.name").toLowerCase.startsWith("win")) {
                System.load(new File(".").getCanonicalPath + "/hadoop-3.3.1/bin/hadoop.dll")
              }
              val builder = SparkSession.builder
                .master(sparkMaster)
                .appName("sp_engine")
                .config("spark.driver.host", "0.0.0.0")
                .config(
                  "spark.hadoop.javax.jdo.option.ConnectionURL",
                  if (sparkConf.get("spark.hadoop.javax.jdo.option.ConnectionURL") == null) {
                    "**************************************************************************************************************"
                  } else { sparkConf.get("spark.hadoop.javax.jdo.option.ConnectionURL") })
                .config(
                  "spark.hadoop.javax.jdo.option.ConnectionPassword",
                  if (sparkConf.get("spark.hadoop.javax.jdo.option.ConnectionPassword") == null) {
                    "snfnqi123"
                  } else { sparkConf.get("spark.hadoop.javax.jdo.option.ConnectionPassword") })
                .config(
                  "spark.hadoop.javax.jdo.option.ConnectionUserName",
                  if (sparkConf.get("spark.hadoop.javax.jdo.option.ConnectionUserName") == null) {
                    "root"
                  } else { sparkConf.get("spark.hadoop.javax.jdo.option.ConnectionUserName") })
                .config("spark.hadoop.hive.metastore.schema.verification", "false")
                .config("spark.hadoop.datanucleus.schema.autoCreateTables", "true")
                .config("spark.sql.legacy.bucketedTableScan.outputOrdering", "true")
                .config("spark.sql.parquet.columnarReaderBatchSize", "10000")
                .config(
                  "spark.sql.catalog.spark_catalog",
                  "org.apache.spark.sql.delta.catalog.DeltaCatalog")
                .config("spark.sql.objectHashAggregate.sortBased.fallbackThreshold", "100000")
                .config("spark.sql.warehouse.dir", "/tmp/warehouse")
                .config("spark.sql.hive.metastore.sharedPrefixes", "com.h2database, org.h2")
                .config("spark.sql.execution.useObjectHashAggregateExec", "false")
                .config("spark.sql.join.preferSortMergeJoin", "true")
                .config("spark.sql.files.openCostInBytes", "64M")
                .config("spark.sql.adaptive.enabled", "false")
                .config("spark.sql.legacy.createHiveTableByDefault", "false")
                .config("spark.sql.pql.sourceTarget.encoding.enabled", "true")
                .config("spark.sql.parquet.enableNestedColumnVectorizedReader", "true")
                .config("spark.sql.parquet.outputTimestampType", "TIMESTAMP_MICROS")
                .config("spark.shuffle.checksum.enabled", "false")
                .config("spark.sql.legacy.timeParserPolicy", "LEGACY")
                .config("spark.sql.starry.enabled", "false")
                .config("spark.databricks.delta.retentionDurationCheck.enabled", "false")
                .config("spark.sql.sources.bucketing.autoBucketedScan.enabled", "false")
                .config("spark.sql.session.timeZone", "Asia/Shanghai")
                // calc_crop_to_null like "window" funcs does not support collapse
                .config(
                  "spark.sql.optimizer.excludedRules",
                  "org.apache.spark.sql.catalyst.optimizer.CollapseWindow")
                //                .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
                .withExtensions(new DeltaSparkSessionExtension)
                .withExtensions(new ColumnCacheExtension)
                .withExtensions(new PMExtension)
              if (columnarEnabled.equals("true")) {
                builder
                  .config("spark.plugins", "com.prx.starry.StarryPlugin")
                  .config("spark.sql.inMemoryColumnarStorage.partitionPruning", "false")
                  .config("spark.sql.inMemoryColumnarStorage.partitionPruning", "false")
                  .config("spark.sql.inMemoryColumnarStorage.enableVectorizedReader", "true")
                  .config(
                    "spark.sql.starry.expressions.NativeExpressionExtensionClass",
                    "org.apache.spark.sql.pql.transform.PQLTransform")
                  .config("spark.sql.codegen.maxFields", "1000")
                  .config("spark.sql.catalog.starry", classOf[StarryCatalog].getName)
                  .withExtensions(e => Starry.injectExtensions(e))
                CustomDictExprRewrite.registerRewriteFunctions()
              } else {
                builder
                  .config("spark.sql.pql.columnCacheEnabled", "false")
                  .config("spark.sql.pql.native.calc.enabled", "false")
                  .config("spark.sql.pql.encodedColDict.encodeWithModelBuild", "false")
                  .config("spark.sql.pql.sourceTarget.encoding.enabled", "false")
              }
              sparkConf.asScala.foreach(tp => builder.config(tp._1, tp._2))
              builder.enableHiveSupport()
              initClusterConfig(builder)
              spark = builder.getOrCreate()
              spark.sessionState.experimentalMethods.extraOptimizations = Seq(
                RewriteSingleCountTable,
                PushDownExprInAggregate,
                CollapseProject) ++ Starry.extraOptimizations
              registerUDF()
              RegisterHiveSqlDialect.register()
              PMFunctions.registerBuiltInFunc(spark)
              logInfo("Spark context started successfully with stack trace:")
              logInfo(Thread.currentThread().getStackTrace.mkString("\n"))
              logInfo(
                "Class loader: " + Thread
                  .currentThread()
                  .getContextClassLoader
                  .toString)
              if (spark.conf.get("spark.hadoop.javax.jdo.option.ConnectionURL").contains("h2")) {
                initInmemSparkMeta(spark.conf.get("spark.hadoop.javax.jdo.option.ConnectionURL"))
              }
              spark.sparkContext.ui.foreach(_.attachTab(new StarryUI(spark.sparkContext.ui.get)))
              if (columnarEnabled.toBoolean) {
                spark.sessionState.catalogManager
                  .catalog("starry")
                  .asInstanceOf[CatalogExtension]
                  .setDelegateCatalog(spark.sessionState.catalogManager.catalog("spark_catalog"))
              }
              SparkSession.setDefaultSession(spark)
            } catch {
              case throwable: Throwable =>
                logError("Error for initializing spark ", throwable)
            } finally {
              logInfo("Setting initializing Spark thread to null.")
              initializingThread = null
            }
          }
        })

        logInfo("Initializing Spark thread starting.")
        initializingThread.start()
      }

      if (initializingThread != null) {
        logInfo("Initializing Spark, waiting for done.")
        initializingThread.join()
      }
    }
  }

  def changeHiveCatalog(): Unit = {
    val myObjectMirror = currentMirror.reflect(SharedState)
    val fieldMirror = myObjectMirror.reflectField(
      typeOf[SharedState.type].declaration(TermName("HIVE_EXTERNAL_CATALOG_CLASS_NAME")).asTerm)
    fieldMirror.set("org.apache.spark.sql.hive.CachedHiveExternalCatalog")
  }

  def initInmemSparkMeta(jdbc: String): Unit = {
    try {
      val connection: Connection = DriverManager.getConnection(jdbc, "", "")
      initDB(connection, "db_init_sql/proxverse_meta_table.sql")
      connection.close()
    } catch {
      case e: SQLException =>
        throw new RuntimeException(e)
      case e: IOException =>
        throw new RuntimeException(e)
      case e: InterruptedException =>
        throw new RuntimeException(e)
    }
  }

  @throws[SQLException]
  @throws[IOException]
  private def initDB(connection: Connection, path: String): Unit = {
    val statement: Statement = connection.createStatement
    val resultSet: ResultSet = statement.executeQuery("show tables")
    if (resultSet.next) return
    var strings1 =
      SQLUtils.splitSemiColon(readFileAsString(Thread.currentThread.getContextClassLoader, path))
    for (query <- strings1.asScala) {
      var q = query
      q = q.replaceAll("CHARACTER SET utf8 COLLATE utf8_general_ci", "")
      q = q.replaceAll("IF NOT EXISTS", "")
      q = q.replaceAll("\\).*ENGINE.*(?=)", ");")
      q = q.replaceAll("USING BTREE*", "")
      try statement.executeUpdate(query.replaceAll("\n", " "))
      catch {
        case e: SQLException =>
          throw new RuntimeException(e)
      }
    }
    statement.close()
  }

  def initClusterConfig(build: Builder): Builder = {
    if (!sparkMaster.startsWith("local")) {
      val instances = sparkConf.get("spark.executor.instances").toInt
      val cores = sparkConf.get("spark.executor.cores").toInt
      val sparkCores = instances * cores
      build.config("spark.sql.shuffle.partitions", sparkCores.toString)
      build.config("spark.debug.maxToStringFields", "1000")
      build.config("spark.scheduler.mode", "FAIR")
    } else {
      build
    }

  }

  def registerUDF(): Unit = {
    spark.udf.register("calc_time_filter", CalcTimeFilter.function)
    spark.udf.register("calc_time", CalcTime.function)
    spark.udf.register("event_filter", EventFilter.function)
    spark.udf.register("rework_num", Rework.function)
    spark.udf.register("process_filter", Process.function)
    spark.udf.register("event_duration", EventDuration.function)
    spark.udf.register("event_duration_avg", EventDurationAVG.function)
    spark.udf.register("calc_time_with_work_day", CalcTimeWithWorkDay.function)
    spark.udf.register("source_any", SourceAny.function)
    spark.udf.register("source_any_with_start", SourceAnyWithStart.function)
    spark.udf.register("source__start", SourceWithStart.function)
    spark.udf.register("source_first", SourceFirst.function)
    spark.udf.register("target_any", TargetAny.function)
    spark.udf.register("target_any_with_end", TargetAnyWithEnd.function)
    spark.udf.register("target_end", TargetWithEnd.function)
    spark.udf.register("target_last", TargetLast.function)

  }
}
