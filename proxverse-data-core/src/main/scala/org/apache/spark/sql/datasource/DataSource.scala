package org.apache.spark.sql.datasource

import com.sp.proxverse.common.model.bo.Message
import com.sp.proxverse.common.model.dict.DataExtractorUpdateTypeEnum
import com.sp.proxverse.common.model.dto.result.{Result, StructFieldInfo}
import com.sp.proxverse.common.util.{EncryptDBUtil, HadoopUtil}
import io.delta.tables.DeltaTable
import org.apache.spark.common.model.DataTypeEnum
import org.apache.spark.internal.Logging
import org.apache.spark.sql.SparkUtils.runWithConf
import org.apache.spark.sql._
import org.apache.spark.sql.catalyst.TableIdentifier
import org.apache.spark.sql.catalyst.analysis.UnresolvedAttribute
import org.apache.spark.sql.common.util.ColumnHelper
import org.apache.spark.sql.datasource.DataSourceUtils.{makeFullTableName, toSQLColumn}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.source.{JDBCSource, SourceFactory}
import org.apache.spark.sql.types._

import java.util.{HashMap, Map, Properties, UUID}
import scala.collection.JavaConverters._

object DataSource extends Logging {

  private val validNameFormat = "([\\w_]+)".r

  def databaseExists(database: String): Boolean = {
    SparkSessionEnv.getSparkSession.catalog.databaseExists(database)
  }

  def findDuplicateValues(
      sparkTableName: String,
      columnName: String,
      limit: Integer): Array[Row] = {
    var value = SparkSessionEnv.getSparkSession
      .table(sparkTableName)
      .filter(s"`${columnName}` is not null")
      .groupBy(columnName)
      .agg(count("*").alias("count"))
      .filter(col("count") > 1)
    if (limit > 0) {
      value = value.limit(limit);
    }
    value.collect()
  }

  def tableExists(tableName: String, database: String): Boolean = {
    if (!SparkSessionEnv.getSparkSession.catalog.databaseExists(database)) {
      return false
    }
    SparkSessionEnv.getSparkSession.catalog.tableExists(s"${database}.${tableName}")
  }

  def tableExists(tableName: String): Boolean = {
    SparkSessionEnv.getSparkSession.catalog.tableExists(s"${tableName}")
  }

  def createDatabaseIfNotExists(database: String): Unit = {
    if (!SparkSessionEnv.getSparkSession.catalog.databaseExists(database)) {
      synchronized {
        if (!SparkSessionEnv.getSparkSession.catalog.databaseExists(database)) {
          SparkSessionEnv.getSparkSession.sql(s"create database ${database}")
        }
      }
    }
  }

  def createCSVTable(
      tableName: String,
      database: String,
      path: String,
      options: java.util.Map[String, String],
      fieldNames: Array[String]): Unit = {
    SparkUtils.doWithJobDesc(
      s"create csv table ${tableName}", {
        createDatabaseIfNotExists(database)
        SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${database}.${tableName}")
        SparkUtils.deleteTableWhenFailed(
          s"${database}.${tableName}", {
            val createTableDDL =
              s"""
                           |CREATE TABLE ${database}.${tableName}
                           |(${fieldNames
                .map(name => s"${DataSourceUtils.toSQLColumn(name)} string")
                .mkString(",")})
                           |USING CSV
                           |OPTIONS (${options.asScala
                .map(entry => s"${entry._1}=${entry._2}")
                .mkString(",")})
                           |LOCATION "${path}"
                           |""".stripMargin
            logInfo(s"Create table with DDL: ${createTableDDL}")
            SparkSessionEnv.getSparkSession.sql(createTableDDL)
          })
      })
  }

  def refreshSparkTable(tableName: String): Unit = {
    SparkSessionEnv.getSparkSession.sql(s"REFRESH TABLE  ${tableName}")
  }

  def queryJDBCTableWithDriver(
      poolId: Integer,
      tableName: String,
      jdbcTableName: String,
      schemaName: String,
      jdbc: String,
      filter: String,
      userName: String,
      password: String,
      driver: String,
      columnExpr: java.util.List[String]): Result = {

    SparkUtils.doWithJobDesc(
      s"create JDBC table ${tableName}", {
        SparkUtils.deleteTableWhenFailed(
          tableName, {

            val temTableName = DataSourceUtils.makeJdbcTempView(poolId, jdbcTableName)

            val schemaTableName = if (isEmpty(schemaName)) {
              jdbcTableName
            } else {
              schemaName + "." + jdbcTableName
            }
            val columns = ColumnHelper.makeCastColumnsByStr(columnExpr)
            val decryptedUserName = EncryptDBUtil.encrypt(userName)
            val decryptedPassword = EncryptDBUtil.encrypt(password)
            val jdbcSource = SourceFactory
              .jdbcSource()
              .poolId(poolId)
              .jdbcTableName(schemaTableName)
              .option(JDBCSource.TEMPTABLE_NAME, temTableName)
              .option(JDBCSource.WHERE_RULE, filter)
              .option(JDBCSource.SOURCE_COLUMN, columns)
              .option(JDBCSource.FULL_UPDATE, true)
              .option(JDBCSource.JDBC_URL, jdbc)
              .option(JDBCSource.JDBC_USER, decryptedUserName)
              .option(JDBCSource.JDBC_PASSWORD, decryptedPassword)
              .option(JDBCSource.JDBC_DRIVER, driver)
              .option(JDBCSource.LIMIT_ROW, 50)

            val dataset = jdbcSource.source()
            val array = dataset
              .collectRow()
              .map(_.toSeq.map(c => if (c == null) "" else c.toString).toArray)
            Result.builder().data(array).build()
          })
      })
  }

  def queryJDBCTableFieldType(
      jdbcTableName: String,
      jdbc: String,
      userName: String,
      password: String,
      driver: String): Result = {

    SparkUtils.doWithJobDesc(
      s"get ${jdbcTableName} table column type", {
        val connectionProperties = new Properties()
        if (userName != null) {
          connectionProperties.put("user", userName)
        }
        if (password != null) {
          connectionProperties.put("password", password)
        }
        connectionProperties.put("driver", driver)
        connectionProperties.put("useSSL", "false")

        val mataData = SparkSessionEnv.getSparkSession.read
          .jdbc(jdbc, jdbcTableName, connectionProperties)
          .schema
          .map(field => {
            StructFieldInfo
              .builder()
              .columnName(field.name)
              .columnType(
                DataTypeEnum.transformationDataTypeEnumByName(field.dataType.typeName).getValue)
              .build()
          })
          .toArray

        Result.builder().metadata(mataData).build()
      })
  }

  def isExistTable(tableName: String): Boolean = {
    val rows = SparkSessionEnv.getSparkSession.catalog.tableExists(tableName)
    rows
  }

  def isEmpty(buf: String): Boolean = {
    buf == null || buf.length == 0
  }

  /**
   * 创建JDBC表
   *
   * @param jdbcTableName 需要创建的source表
   * @param deltaTable    需要创建的 parquet表
   * @param dbTableName   拉去数据库的库名和表名
   * @param jdbc
   * @param userName
   * @param password
   * @param driver
   * @param updateType
   * @param etlColumns
   */
  def createJDBCTable(
      jdbcTableName: String,
      deltaTable: String,
      dbTableName: String,
      jdbc: String,
      userName: String,
      password: String,
      driver: String,
      updateType: Int,
      etlColumns: Array[Column]): Unit = {
    val decryptedUserName = EncryptDBUtil.encrypt(userName)
    val decryptedPassword = EncryptDBUtil.encrypt(password)
    SparkUtils.doWithJobDesc(
      s"create JDBC table ${jdbcTableName}", {
        createDatabaseIfNotExists(jdbcTableName.split("\\.").apply(0))
        SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${jdbcTableName}")

        SparkSessionEnv.getSparkSession.sql(s"""
                       |CREATE TABLE IF NOT EXISTS ${jdbcTableName}
                       |USING org.apache.spark.sql.jdbc
                       |OPTIONS (
                       |url "${jdbc}",
                       |dbtable "${dbTableName}",
                       |user '${decryptedUserName}',
                       |password '${decryptedPassword}',
                       |driver  "${driver}",
                       |useCursorFetch "true",
                       |fetchsize "1024"
                       |)
                       |""".stripMargin)
        val schema =
          SparkSessionEnv.getSparkSession.table(jdbcTableName).select(etlColumns: _*).schema

        if (updateType == DataExtractorUpdateTypeEnum.FULL.getValue) {
          SparkSessionEnv.getSparkSession
            .createDataFrame(SparkSessionEnv.getSparkSession.sparkContext.emptyRDD[Row], schema)
            .write
            .saveAsTable(deltaTable)
        } else {
          DeltaTable
            .create(SparkSessionEnv.getSparkSession)
            .tableName(deltaTable)
            .addColumns(schema)
            .execute()
        }
      })
  }

  def createDeltaTable(
      deltaTable: String,
      columns: Array[StructField],
      databaseName: String,
      tableName: String): Unit = {
    SparkUtils.doWithJobDesc(
      s"create delta table ${deltaTable}",
      runWithConf(
        { conf =>
          conf
            .setLocalProperty("spark.sql.starry.enabled", "false")
          conf.setLocalProperty(
            "spark.sql.cache.serializer",
            "org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer")
        },
        { _ =>
          val temTableName = "`" + tableName + "_tem`";
          val temFullTableName = makeFullTableName(temTableName, databaseName)
          SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS $temFullTableName")
          createTable(temFullTableName, columns, "parquet")

          SparkSessionEnv.getSparkSession
            .table(temFullTableName)
            .write
            .mode("overwrite")
            .format("delta")
            .saveAsTable(deltaTable)
          SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS $temFullTableName")
        }))
  }

  def createTable(tableName: String, columns: Array[StructField], usingType: String): Unit = {
    SparkUtils.doWithJobDesc(
      s"create delta table ${tableName}", {
        SparkUtils.deleteTableWhenFailed(
          s"$tableName", {
            val createTableDDL =
              s"""
               |CREATE TABLE $tableName
               |(${columns
                .map(name =>
                  s"${DataSourceUtils.toSQLColumn(name.name)} ${name.dataType.typeName}")
                .mkString(",")})
               |USING $usingType
               |""".stripMargin
            logInfo(s"Create table with DDL: ${createTableDDL}")
            SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS $tableName")
            SparkSessionEnv.getSparkSession.sql(createTableDDL)
          })
      })
  }

  def builderSourceOptions(
      tableName: String,
      groupName: String,
      maxName: String,
      partitionValue: Integer): Map[Integer, Long] = {
    val result = new HashMap[Integer, Long]()
    val frame = SparkSessionEnv.getSparkSession
      .table(tableName)
      .groupBy(groupName)
      .agg(max(maxName))
    if (frame.count() > 0) {
      val javaRdd = frame.rdd.toJavaRDD()
      val scalaRdd = javaRdd.rdd
      val scalaMap = scalaRdd.map(row => (row.getInt(0), row.getLong(1))).collectAsMap()
      for (i <- 0 until partitionValue) {
        var offsets = 0L;
        if (scalaMap.contains(i)) {
          offsets = scalaMap.get(i).get;
        }
        result.put(i, offsets + 1)
      }
    }
    result
  }

  def computeMaxOfColumn(tableName: String, updateFieldName: String): String = {
    val maxValueDF =
      SparkSessionEnv.getSparkSession.table(tableName).agg(max(updateFieldName))

    val maxValueRow = maxValueDF.head()
    if (maxValueRow.isNullAt(0)) {
      ""
    } else {
      maxValueRow(0).toString
    }
  }

  def appendToParquetTable(source: Dataset[Row], deltaTableName: String): Unit = {
    source.write
      .format("parquet")
      .mode(SaveMode.Append)
      .saveAsTable(deltaTableName)
  }

  def appendToDeltaTable(tempFullTableName: String, deltaTableName: String): Unit = {

    val newDataRowsCount = SparkSessionEnv.getSparkSession.table(tempFullTableName).count()
    if (newDataRowsCount == 0) {
      return
    }

    DeltaTable
      .forName(SparkSessionEnv.getSparkSession, deltaTableName)
      .as("old_data")
      .merge(SparkSessionEnv.getSparkSession.table(tempFullTableName).as("new_data"), "false")
      .whenNotMatched()
      .insertAll()
      .execute()
    refreshSparkTable(tempFullTableName)
  }

  def createDeltaTable(
      sourceTable: String,
      deltaTable: String,
      tempTable: String,
      etlColumns: Array[Column],
      whereCause: String): Message = {
    SparkUtils.doWithJobDesc(
      s"create delta table ${deltaTable}", {
        try {
          val message = new Message()
          var mysql = SparkSessionEnv.getSparkSession.read
            .table(sourceTable)
            .select(etlColumns: _*)
          if (whereCause.nonEmpty) {
            mysql = mysql.filter(whereCause)
          }
          mysql.write
            .mode("overwrite")
            .format("parquet")
            .saveAsTable(tempTable)
          val rowCount = SparkSessionEnv.getSparkSession.table(tempTable).count()
          message.setRowsCount(rowCount)
          SparkSessionEnv.getSparkSession
            .table(tempTable)
            .write
            .mode("overwrite")
            .format("delta")
            .saveAsTable(deltaTable)
          message
        } catch {
          case e: Exception =>
            SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${tempTable}")
            SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${sourceTable}")
            SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${deltaTable}")
            throw e
        }
      })
  }

  def createViewTable(database: String, sourceTable: String, createTable: String): Boolean = {
    SparkUtils.doWithJobDesc(
      s"CREATE VIEW ${createTable}", {
        createDatabaseIfNotExists(database)
        SparkSessionEnv.getSparkSession.sql(s"DROP VIEW IF EXISTS ${database}.${createTable}")
        SparkUtils.deleteTableWhenFailed(
          s"${database}.${createTable}", {
            val createTableDDL =
              s"""
                       |CREATE VIEW ${database}.${createTable} AS
                       | SELECT * FROM ${database}.${sourceTable}
                       |""".stripMargin
            logInfo(s"Create view with DDL: ${createTableDDL}")
            SparkSessionEnv.getSparkSession.sql(createTableDDL)
          })
      })
    false
  }

  def refreshTable(
      sourceTable: String,
      targetTable: String,
      saveTempTable: String,
      etlColumns: Array[Column],
      whereCause: String): Message = {

    SparkUtils.doWithJobDesc(
      s" refresh table ", {
        val result = new Message()

        var mysql = SparkSessionEnv.getSparkSession.read
          .table(sourceTable)
          .select(etlColumns: _*)
        if (whereCause.nonEmpty) {
          mysql = mysql.filter(whereCause)
        }
        mysql.write
          .mode("overwrite")
          .format("parquet")
          .saveAsTable(saveTempTable)

        SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${targetTable}")

        val rowCount = SparkSessionEnv.getSparkSession.table(saveTempTable).count()
        result.setRowsCount(rowCount);
        SparkSessionEnv.getSparkSession
          .table(saveTempTable)
          .write
          .mode("overwrite")
          .format("parquet")
          .saveAsTable(targetTable)

        SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${saveTempTable}")
        refreshSparkTable(targetTable)
        result
      })
  }

  def appendDeltaTable(
      sourceTable: String,
      deltaTable: String,
      tempTable: String,
      etlColumns: Array[Column],
      whereCause: String,
      pkNameList: String,
      updateFieldNameList: Array[String]): Message = {
    SparkUtils.doWithJobDesc(
      s"merge delta table ${deltaTable}",
      runWithConf(
        { conf =>
          conf
            .setLocalProperty("spark.sql.starry.enabled", "false")
          conf
            .setLocalProperty(
              "spark.sql.sources.parallelPartitionDiscovery.parallelism",
              (SparkSessionEnv.getSparkSession.leafNodeDefaultParallelism * 2).toString)
          conf.setLocalProperty(
            "spark.sql.cache.serializer",
            "org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer")
        },
        { _ =>
          val message = new Message();
          var filter: Column = null
          for (updateFieldName <- updateFieldNameList) {
            val maxValueDF =
              SparkSessionEnv.getSparkSession.table(deltaTable).agg(max(updateFieldName))
            val maxValue = maxValueDF.collect()
            message.getMaxTimeMap.put(updateFieldName, maxValue.mkString)
            if (filter == null) {
              filter = col(updateFieldName) >= lit(maxValue.head.getAs(0))
                .cast(maxValueDF.schema.fields.head.dataType)
            } else {
              filter = filter.or(
                col(updateFieldName) >= lit(maxValue.head.getAs(0))
                  .cast(maxValueDF.schema.fields.head.dataType))
            }
          }

          val mergeCondition = if (pkNameList.isEmpty) {
            "false"
          } else {
            pkNameList
          }
          var mysql = SparkSessionEnv.getSparkSession.read
            .table(sourceTable)
            .select(etlColumns: _*)
          if (whereCause.nonEmpty) {
            mysql = mysql.filter(whereCause)
          }

          if (SparkSessionEnv.getSparkSession.table(deltaTable).count() != 0) {
            mysql = mysql
              .filter(filter)
          }
          mysql.write
            .mode("overwrite")
            .format("parquet")
            .saveAsTable(tempTable)

          val newDataRowsCount = SparkSessionEnv.getSparkSession.table(tempTable).count()
          message.setDeltaRowsCount(newDataRowsCount)

          if (newDataRowsCount == 0) {
            message.setRowsCount(0L)
            return message
          }
          val dataRowsCountBefore = SparkSessionEnv.getSparkSession.table(deltaTable).count()
          DeltaTable
            .forName(SparkSessionEnv.getSparkSession, deltaTable)
            .as("old_data")
            .merge(
              SparkSessionEnv.getSparkSession.table(tempTable).as("new_data"),
              mergeCondition)
            .whenMatched()
            .updateAll()
            .whenNotMatched()
            .insertAll()
            .execute()
          DeltaTable
            .forName(SparkSessionEnv.getSparkSession, deltaTable)
            .optimize()
            .executeCompaction()

          DeltaTable
            .forName(SparkSessionEnv.getSparkSession, deltaTable)
            .vacuum(
              SparkSessionEnv.getSparkSession.conf
                .get(PQLConf.DELTA_LOG_RETENTION_HOURS.key)
                .toDouble)

          refreshSparkTable(deltaTable)
          val dataRowsCountAfter = SparkSessionEnv.getSparkSession.table(deltaTable).count()
          message.setRowsCount(dataRowsCountAfter - dataRowsCountBefore)
          message
        }))
  }

  def appendDeltaTable(
      sourceTable: DataFrame,
      deltaTable: String,
      pks: Array[String],
      updateFieldNameList: Array[String]): Message = {
    SparkUtils.doWithJobDesc(
      s"merge delta table ${deltaTable}",
      runWithConf(
        { conf =>
          conf
            .setLocalProperty("spark.sql.starry.enabled", "false")
          conf
            .setLocalProperty(
              "spark.sql.sources.parallelPartitionDiscovery.parallelism",
              (SparkSessionEnv.getSparkSession.leafNodeDefaultParallelism * 2).toString)
          conf
            .setLocalProperty("spark.sql.autoBroadcastJoinThreshold", "-1")
          conf.setLocalProperty(
            "spark.sql.cache.serializer",
            "org.apache.spark.sql.execution.columnar.DefaultCachedBatchSerializer")
        },
        { _ =>
          val message = new Message()
          val mergeCondition = if (pks.isEmpty) {
            "false"
          } else {
            pks
              .map(k => s"old_data.`${k}` = new_data.`${k}`")
              .reduce((tp1, tp2) => s"${tp1} and ${tp2}")
          }
          val df_deduplicated = if (pks.nonEmpty) {
            val windowSpec =
              if (updateFieldNameList.nonEmpty) {
                Window
                  .partitionBy(pks.map(col): _*)
                  .orderBy(updateFieldNameList.map(col(_).desc): _*)
              } else {
                Window.partitionBy(pks.map(col): _*).orderBy(col(pks.head))
              }
            val df_with_row_number =
              sourceTable.withColumn("row_number", row_number().over(windowSpec))
            // # 只保留每组数据中row_number为1的行，即时间戳最大的行

            df_with_row_number
              .filter(col("row_number") === 1)
              .drop("row_number")
              .as("new_data")
          } else {
            sourceTable
          }
          val dataRowsCountBefore = SparkSessionEnv.getSparkSession.table(deltaTable).count()
          DeltaTable
            .forName(SparkSessionEnv.getSparkSession, deltaTable)
            .as("old_data")
            .merge(df_deduplicated, mergeCondition)
            .whenMatched()
            .updateAll()
            .whenNotMatched()
            .insertAll()
            .execute()
          DeltaTable
            .forName(SparkSessionEnv.getSparkSession, deltaTable)
            .optimize()
            .executeCompaction()
          DeltaTable
            .forName(SparkSessionEnv.getSparkSession, deltaTable)
            .vacuum(
              SparkSessionEnv.getSparkSession.conf
                .get(PQLConf.DELTA_LOG_RETENTION_HOURS.key)
                .toDouble)
          refreshSparkTable(deltaTable)
          val dataRowsCountAfter = SparkSessionEnv.getSparkSession.table(deltaTable).count()
          message.setRowsCount(dataRowsCountAfter - dataRowsCountBefore)
          message
        }))
  }

  def isDeltaTable(tableName: String): Boolean = {
    try {
      DeltaTable.forName(tableName)
      true
    } catch {
      case e: Throwable => false
    }
  }

  def createNonPartitionParquetTableFromDataSet(
      sourceTable: String,
      dataSet: Dataset[Row],
      saveTableName: String,
      database: String,
      etlColumns: Array[Column]): Unit = {
    SparkUtils.doWithJobDesc(
      s"load ${sourceTable} to ${saveTableName}", {
        dataSet
          .select(etlColumns: _*)
          .write
          .mode("overwrite")
          .format("parquet")
          .saveAsTable(s"${database}.${saveTableName}")
      })
  }

  def createNonPartitionParquetTableFromSourceTable(
      sourceTable: String,
      parquetTable: String,
      database: String,
      etlColumns: Array[Column]): Unit = {
    SparkUtils.doWithJobDesc(
      s"load ${sourceTable} to ${parquetTable}", {
        SparkSessionEnv.getSparkSession
          .table(s"${database}.${sourceTable}")
          .select(etlColumns: _*)
          .write
          .mode("overwrite")
          .format("parquet")
          .saveAsTable(s"${database}.${parquetTable}")
      })
  }

  def decideTableBucketNum(tableName: String): Int = {
    SparkUtils.doWithJobDesc(
      s"decideTableBucketNum ${tableName}", {
        ((SparkSessionEnv.getSparkSession
          .table(s"${tableName}")
          .count() / PQLConf.preBukcetNumberRows) + 1).toInt
      })
  }

  def decideTableBucketNum(tableName: String, database: String): Int = {
    decideTableBucketNum(s"${database}.${tableName}");
  }

  def deleteTablesByPrefix(dataBase: String, tableName: String): Unit = {
    val tableNameLower = tableName.toLowerCase()
    val rows = SparkSessionEnv.getSparkSession.catalog
      .listTables(dataBase)
      .filter(_.name.toLowerCase().startsWith(s"$tableNameLower"))
      .rdd
      .map(_.name)
      .collect()
    for (elem <- rows) {
      deleteTable(DataSourceUtils.makeFullTableName(toSQLColumn(elem), dataBase))
    }
  }

  def deleteTable(tableName: String): Unit = {
    SparkUtils.doWithJobDesc(
      s"delete table ${tableName}", {
        SparkSessionEnv.getSparkSession.sql(s"DROP TABLE IF EXISTS ${tableName}")
        val name = UnresolvedAttribute.parseAttributeName(tableName)
        if (databaseExists(name.head)) {
          val path = SparkSessionEnv.getSparkSession.sessionState.catalog
            .defaultTablePath(TableIdentifier(name.last, Option.apply(name.head)))
          HadoopUtil.deletePath(path)
        }
      })
  }

  def deleteView(tableName: String): Unit = {
    SparkUtils.doWithJobDesc(
      s"delete view ${tableName}", {
        SparkSessionEnv.getSparkSession.sql(s"DROP VIEW IF EXISTS ${tableName}")
      })
  }

  def getTableSize(tableName: String): Long = {
    SparkUtils.doWithJobDesc(
      s"get table size", {
        val frame = SparkSessionEnv.getSparkSession
          .table(tableName)
        if (frame.inputFiles.isEmpty) {
          0L
        } else {
          frame.queryExecution.analyzed.stats.sizeInBytes.toLong
        }
      })
  }

  def getBucketNumber(tableName: String): Int = {
    val rows = SparkSessionEnv.getSparkSession
      .sql(s"DESC FORMATTED ${tableName}")
      .filter(col("col_name").contains("Bucket"))
      .take(1)
    if (rows.isEmpty) {
      throw new IllegalArgumentException(s"Error for found bucket number with table ${tableName}")
    }
    rows.head.getString(0).toInt
  }

  def analyzeTable(sparkSession: SparkSession, tableName: String): Unit = {
    if (PQLConf.autoAnalyzeEnabled) {
      val columns = DataSourceUtils.toAnalyzeColumns(sparkSession.table(tableName).schema)
      sparkSession.sql(s"ANALYZE TABLE ${tableName} COMPUTE STATISTICS for columns ${columns}")
    }
  }
}

object DataSourceUtils {

  def randomTableName(prefix: String): String = {
    s"`${prefix}_${UUID.randomUUID.toString.replace("-", "")}`"
  }

  def makeDatabaseName(poolId: Int): String = {
    s"pool_${poolId}"
  }

  def makeTableName(modelID: Int, fileName: String, id: String): String = {
    s"`t_${modelID}_${fileName}_${id}`"
  }

  def makeTableName(modelID: Int, fileName: String): String = {
    s"`t_${modelID}_${fileName}`"
  }

  def makeFileSourceTableName(fileName: String, id: String): String = {
    s"`t_${fileName}_${id}_source`"
  }

  def sourceTableNameToTableName(fileName: String): String = {
    fileName.replace("_source", "")
  }

  def makeFileTableName(fileName: String, id: String): String = {
    s"`t_${fileName}_${id}`"
  }

  def makeFileTableName(fileName: String, id: String, fileType: Int): String = {
    if (fileType == 6) {
      fileName
    } else {
      makeFileTableName(fileName, id)
    }
  }

  def makeBucketTableName(tableName: String): String = {
    s"`${tableName.replace("`", "")}_bucket`"
  }

  def toSQLColumn(columnName: String): String = {
    s"`${columnName}`"
  }

  def makeFullTableName(tableName: String, database: String): String = {
    s"${database}.${tableName}"
  }

  def makeJdbcTempView(poolId: Int, tableName: String): String = {
    s"tem_jdbc_pool_${poolId}_${tableName}"
  }

  def toAnalyzeColumns(schema: StructType): String = {
    schema
      .filter(st => supportsAnalyzeType(st.dataType))
      .map(_.name)
      .map(name => s"`${name}`")
      .mkString(",")
  }

  def supportsAnalyzeType(dataType: DataType): Boolean = dataType match {
    case _: IntegralType => true
    case _: DecimalType => true
    case DoubleType | FloatType => true
    case BooleanType => true
    case DateType => true
    case TimestampType => true
    case BinaryType | StringType => true
    case _ => false
  }

}
