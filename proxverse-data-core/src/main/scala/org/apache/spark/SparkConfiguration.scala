package org.apache.spark

import com.alibaba.fastjson.parser.Feature
import com.alibaba.fastjson.{JSON, JSONObject}
import com.sp.proxverse.common.exception.BizException
import com.sp.proxverse.common.model.i18n.I18nConst
import com.sp.proxverse.common.util.{ClassUtil, EncryptDBUtil, I18nUtil}
import org.apache.commons.io.FileUtils
import org.apache.spark.internal.Logging
import org.apache.spark.internal.config.{ConfigReader, EnvProvider, SystemProvider}
import org.apache.spark.sql.SparkSessionEnv
import org.yaml.snakeyaml.Yaml

import java.io.{Closeable, File, FileInputStream}
import java.util.Objects

object SparkConfiguration extends Logging {

  def tryWith[T <: Closeable, R](resource: T)(func: T => R): R = {
    try {
      func(resource)
    } finally {
      try {
        if (resource != null) {
          resource.close()
        }
      } catch {
        case err: Throwable =>
          logWarning(s"Error closing $resource", err)
      }
    }
  }

  def initSparkWithArgs(args: Array[String]): Unit = {
    // e.g. --spring.profiles.active=private --spring.config.location=/data/conf/bootstrap-private.yaml
    val argValue = (key: String) => {
      args.map(_.trim).filter(_.contains(key)).map(_.split("=").last.trim).headOption
    }
    lazy val confLoc = argValue("spring.config.location")
    lazy val profile = argValue("spring.profiles.active").orElse(
      Option(System.getProperty("spring.profiles.active")))

    if (confLoc.isDefined) {
      withConfFile(confLoc.get, profile).initSparkSession()
    } else {
      withProfile(profile.get).initSparkSession()
    }
  }

  def withProfile(activeProfile: String): SparkConfiguration = {
    val fileName = s"bootstrap-$activeProfile.yaml"
    logInfo(s"Reading conf from $fileName")
    tryWith(this.getClass.getClassLoader.getResourceAsStream(fileName)) { stream =>
      try {
        new SparkConfiguration(new Yaml().load(stream), Some(activeProfile))
      } catch {
        case err: Throwable =>
          throw new RuntimeException(s"Error open config file $fileName", err)
      }
    }
  }

  def withConfFile(
      confFileLocation: String,
      activeProfile: Option[String]): SparkConfiguration = {
    logInfo(s"Reading conf from $confFileLocation")
    tryWith(new FileInputStream(new File(confFileLocation))) { stream =>
      try {
        new SparkConfiguration(new Yaml().load(stream), activeProfile)
      } catch {
        case err: Throwable =>
          throw new RuntimeException(s"Error open config file $confFileLocation", err)
      }
    }
  }

}

case class SparkConfiguration(
    confMap: java.util.Map[String, Object],
    activeProfile: Option[String])
    extends Logging {

  private val sysPropReader = new ConfigReader(new SystemProvider())
  private val envReader = new ConfigReader(new EnvProvider())

  private def substituteEnvOrProps(value: String): String =
    envReader.substitute(sysPropReader.substitute(value))

  private def strConf(key: String, defaultValue: String = null): Option[String] = {
    val confValue = if (confMap.get(key) == null) {
      Option(defaultValue)
    } else {
      Some(confMap.get(key).toString)
    }
    confValue.map(substituteEnvOrProps)

  }

  private def conf(keyValue: String): Option[String] = {
    val idx = keyValue.indexOf(":")
    if (idx == -1) {
      strConf(keyValue)
    } else {
      strConf(keyValue.substring(0, idx), keyValue.substring(idx + 1))
    }
  }

  private val sparkStorePath = conf("spark.store.path:")
  private val columnarEnabled = conf("spark.sql.columnar.enabled:true")
  private val metaCacheEnabled = conf("spark.sql.metacache.enabled:true")
  private val sparkJDBCURL = conf("spark.metastore.jdbc.url")
  private val instances = conf("spark.executor.instances:1")
  private val cores = conf("spark.executor.cores:1")
  private val memory = conf("spark.executor.memory:1G")
  private val sparkJDBCUser = conf("spark.metastore.jdbc.user")
  private val sparkJDBCPassword = conf("spark.metastore.jdbc.password")
  private val sparkMaster = conf("spark.master")
  private val sparkDriverHost = conf("spark.driver.host:0.0.0.0")
  private val sparkConf = conf("spark.conf:{}")
  private val healthTrackerEnabled = conf("spark.healthTrackerEnabled:false")
  private val akmType = conf("akm.service.type:remote")
  private val akmEnv = conf("akm.env.name:UAT")
  private val akmAddress = conf("akm.remote.address.list:jcld.sdc.bocomm.com:8093")
  private val akmToken = conf("akm.app.token:BMK0000010881-3e2fd01cc367479b9782bf27975da85b")

  private def setSparkConf(key: String, value: Option[String]): Unit = {
    value.foreach(SparkSessionEnv.addSparkConf(key, _))
  }

  def initSparkSession(): Unit = {
    setSparkConf("akm.service.type", akmType)
    setSparkConf("akm.env.name", akmEnv)
    setSparkConf("akm.remote.address.list", akmAddress)
    setSparkConf("akm.app.token", akmToken)
    setSparkConf("spark.sql.warehouse.dir", sparkStorePath)
    setSparkConf("spark.hadoop.javax.jdo.option.ConnectionURL", sparkJDBCURL)
    if (sparkJDBCUser != None && sparkJDBCPassword != None) {
      val userName = getUsername(sparkJDBCUser, akmType, akmEnv, akmAddress, akmToken)
      val password = getPassword(sparkJDBCPassword, akmType, akmEnv, akmAddress, akmToken)
      setSparkConf("spark.hadoop.javax.jdo.option.ConnectionUserName", Option.apply(userName))
      setSparkConf("spark.hadoop.javax.jdo.option.ConnectionPassword", Option.apply(password))
    }
    setSparkConf("spark.driver.host", sparkDriverHost)
    sparkMaster.foreach(SparkSessionEnv.setSparkMaster)
    columnarEnabled.foreach(SparkSessionEnv.setColumnarEnabled)
    metaCacheEnabled.foreach(SparkSessionEnv.setMetaCacheEnabled)
    healthTrackerEnabled.foreach(confStr =>
      SparkSessionEnv.setHealthTrackerEnabled(confStr.toBoolean))
    val parse = JSON.parse(sparkConf.get, Feature.CustomMapDeserializer).asInstanceOf[JSONObject]
    parse.entrySet.forEach { entry =>
      SparkSessionEnv.addSparkConf(entry.getKey, entry.getValue.toString)
    }

    confMap.forEach { case (key, value) =>
      if (key.startsWith("spark.sql")) {
        SparkSessionEnv.addSparkConf(key, substituteEnvOrProps(value.toString))
      }
    }

    sparkMaster.filter(!_.startsWith("local")).foreach { _ =>
      ClassUtil.addClasspath("/data/conf")
      setSparkConf("spark.executor.cores", cores)
      setSparkConf("spark.executor.instances", instances)
      setSparkConf("spark.executor.memory", memory)
    }

    activeProfile.filter(_ == "test-inmem").foreach { _ =>
      sparkStorePath.foreach { path =>
        val file = new File(path)
        if (file.exists) {
          FileUtils.deleteDirectory(file)
          file.deleteOnExit()
        }
      }
    }

    logInfo("Init Spark...")
    val thread = new Thread(
      new Runnable() {
        override def run(): Unit = {
          SparkSessionEnv.getSparkSession.sql("create database if not exists test")
          SparkSessionEnv.getSparkSession.catalog
            .listTables("test")
            .collect()
          SparkSessionEnv.getSparkSession.sparkContext.range(0, 10, 2).count()
        }
      },
      "spark-init")
    thread.setDaemon(true)
    thread.start()
  }

  def getUsername(
      userName: Option[String],
      akmType: Option[String],
      akmEnv: Option[String],
      akmAddress: Option[String],
      akmToken: Option[String]): String = {
    if (Objects.isNull(userName)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON))
    }
    val result =
      EncryptDBUtil.encrypt(userName.get, akmType.get, akmEnv.get, akmAddress.get, akmToken.get)
    result
  }

  def getPassword(
      password: Option[String],
      akmType: Option[String],
      akmEnv: Option[String],
      akmAddress: Option[String],
      akmToken: Option[String]): String = {
    if (Objects.isNull(password)) {
      throw new BizException(5000, I18nUtil.getMessage(I18nConst.ERROR_PARAM_COMMON))
    }
    val result =
      EncryptDBUtil.encrypt(password.get, akmType.get, akmEnv.get, akmAddress.get, akmToken.get)
    result
  }
}
