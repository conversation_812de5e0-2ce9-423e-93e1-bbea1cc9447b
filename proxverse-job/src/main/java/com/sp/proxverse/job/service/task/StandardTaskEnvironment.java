package com.sp.proxverse.job.service.task;

/**
 * <AUTHOR>
 * @create 2023-01-05 14:26
 */
public class StandardTaskEnvironment extends AbstractTaskEnvironment {

  private static final Integer DEFAULT_THREAD_POOL_NUMBER = 1;

  private static final Integer DEFAULT_QUEUE_NUMBER = 16;

  @Override
  public Integer getThreadPoolNumber() {
    if (threadPoolNumber == null) {
      return DEFAULT_THREAD_POOL_NUMBER;
    }
    return threadPoolNumber;
  }

  @Override
  public Integer getQueueNumber() {
    if (queuePoolNumber == null) {
      return DEFAULT_QUEUE_NUMBER;
    }
    return queuePoolNumber;
  }
}
