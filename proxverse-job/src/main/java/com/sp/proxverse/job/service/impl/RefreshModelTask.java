package com.sp.proxverse.job.service.impl;

import com.sp.proxverse.common.model.dict.DataModelRunStatusEnum;
import com.sp.proxverse.common.model.job.JobRunStatusEnum;
import com.sp.proxverse.common.model.job.create.RefreshJobMetrics;
import com.sp.proxverse.common.model.po.DataModelPO;
import com.sp.proxverse.common.model.po.TaskExecutorPo;
import com.sp.proxverse.job.param.JobContext;
import com.sp.proxverse.job.param.RefreshModelContext;
import com.sp.proxverse.job.param.create.PushTask;
import com.sp.proxverse.job.param.create.RefreshModelPushTask;
import com.sp.proxverse.job.service.Task;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.exception.ExceptionUtils;

/**
 * <AUTHOR>
 * @create 2023-01-09 17:42
 */
@Slf4j
public class RefreshModelTask implements Task {

  private JobContext jobContext;

  private RefreshModelContext refreshModelContext;

  public RefreshModelTask(PushTask pushTask) {
    jobContext = pushTask.getJobContext();
    RefreshModelPushTask refreshModelPushTask = (RefreshModelPushTask) pushTask;
    RefreshJobMetrics jobMetrics = (RefreshJobMetrics) refreshModelPushTask.getJobMetrics();
    jobMetrics.setCurrentTaskExecutorId(refreshModelPushTask.getCurrentTaskExecutorId());
    refreshModelContext = new RefreshModelContext();
    refreshModelContext.setRefreshModelPushTask(refreshModelPushTask);
    refreshModelContext.jobMetrics = jobMetrics;
    refreshModelContext.jobMetrics.setParentTaskExecutorId(
        refreshModelPushTask.getParentTaskExecutorId());
    this.refreshModelContext.jobMetrics.setDataModelId(refreshModelPushTask.getDataModelId());
  }

  @Override
  public void init() {
    this.refreshModelContext.jobMetrics.updateRunStatus(JobRunStatusEnum.INIT);
  }

  @Override
  public void run() {
    this.refreshModelContext.jobMetrics.updateRunStatus(JobRunStatusEnum.RUN);
    try {
      jobContext
          .getModelBuilderService()
          .buildModel(refreshModelContext.getRefreshModelPushTask().getDataModelId());
    } catch (Exception e) {
      refreshModelContext.jobMetrics.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
      log.error("Error for exec TransformationTask", e);
    }
  }

  @Override
  public String log() {
    this.refreshModelContext.jobMetrics.updateRunStatus(JobRunStatusEnum.LOG);
    Integer dataModelId = this.refreshModelContext.jobMetrics.getDataModelId();
    if (dataModelId != null) {
      DataModelPO dataModelPO = jobContext.getDataModelService().getById(dataModelId);
      if (dataModelPO != null) {
        this.refreshModelContext.jobMetrics.setDataModelName(dataModelPO.getName());
        dataModelPO.setUpdateTime(new Date());
        jobContext.getDataModelService().updateById(dataModelPO);
        if (ObjectUtils.equals(
            dataModelPO.getStatus(), DataModelRunStatusEnum.LOAD_FAILED.getValue())) {
          this.refreshModelContext.jobMetrics.setErrorMessage("Data model load failed");
        }
      } else {
        this.refreshModelContext.jobMetrics.setErrorMessage("Data model does not exist");
      }

    } else {
      this.refreshModelContext.jobMetrics.setErrorMessage("Data model does not exist");
    }
    this.refreshModelContext.jobMetrics.updateRunStatus(JobRunStatusEnum.END);

    StringBuilder taskSummary =
        JobBeanUtilsService.jobMetricsToString(this.refreshModelContext.jobMetrics);
    int queueNumber = 0;
    if (this.refreshModelContext.jobMetrics.getImmediatelyFlag()) {
      queueNumber = jobContext.getJobSchedule().immediatelyThreadPoolExecutor.getQueue().size();
      taskSummary.append("Execute the remaining tasks of the thread pool immediately : ");
    } else {
      queueNumber = jobContext.getJobSchedule().threadPoolExecutor.getQueue().size();
      taskSummary.append("Remaining tasks in thread pool : ");
    }

    taskSummary.append(queueNumber);
    taskSummary.append(" \n");

    taskSummary.append("Data Model ID : ");
    taskSummary.append(this.refreshModelContext.jobMetrics.getDataModelId());
    taskSummary.append(" \n");

    taskSummary.append("Data Model Name : ");
    taskSummary.append(this.refreshModelContext.jobMetrics.getDataModelName());
    taskSummary.append(" \n");

    taskSummary.append("Error message : ");
    taskSummary.append(this.refreshModelContext.jobMetrics.getErrorMessage());
    taskSummary.append(" \n");

    return taskSummary.toString();
  }

  @Override
  public String error() {
    return this.refreshModelContext.jobMetrics.getErrorMessage();
  }

  @Override
  public void finish() {
    this.refreshModelContext.jobMetrics.updateRunStatus(JobRunStatusEnum.FINISH);
    // 这里刷新完成之后，要给quartz job发个bus通知，推送动作流程
    Integer dataModelId = this.refreshModelContext.jobMetrics.getDataModelId();
    //        jobContext.getLoadModelPublishService().publishRefreshModelEvent(dataModelId);
    log.info("modelId={}，推送优化信号", dataModelId);
    // userId=0则认为是自动执行
    jobContext.getQuartzJobPushActionFlowService().pushExecutionAction(dataModelId, 0);
  }

  @Override
  public TaskExecutorPo getTaskExecutor() {
    return refreshModelContext.getRefreshModelPushTask().getTaskExecutorPo();
  }

  @Override
  public Boolean isFileSizeUpdated() {
    return false;
  }
}
