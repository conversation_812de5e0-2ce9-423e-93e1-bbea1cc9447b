package com.sp.proxverse.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.model.dict.DataConnectorTypeEnum;
import com.sp.proxverse.common.model.dict.DeletedEnum;
import com.sp.proxverse.common.model.dict.TaskRateTypeEnum;
import com.sp.proxverse.common.model.job.create.JdbcTaskGeneratorInfo;
import com.sp.proxverse.common.model.job.create.KafkaTaskGeneratorInfo;
import com.sp.proxverse.common.model.po.DataExtractorPO;
import com.sp.proxverse.common.model.po.DataTaskChildPo;
import com.sp.proxverse.common.model.po.DataTaskPO;
import com.sp.proxverse.data.service.TransformationService;
import com.sp.proxverse.interfaces.dao.impl.DataTaskChildServiceImpl;
import com.sp.proxverse.interfaces.dao.impl.TransformationSqlServiceImpl;
import com.sp.proxverse.interfaces.dao.service.DataExtractorService;
import com.sp.proxverse.interfaces.dao.service.DataTaskService;
import com.sp.proxverse.job.service.impl.IncrementalFileTaskGenerator;
import com.sp.proxverse.job.service.impl.JDBCTaskGenerator;
import com.sp.proxverse.job.service.impl.KafkaTaskGenerator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ScheduleTaskService {

  @Autowired private DataTaskService dataTaskService;

  @Autowired TransformationService transformationService;

  @Autowired TransformationSqlServiceImpl transformationSqlService;

  @Autowired JDBCTaskGenerator jdbcJobCreate;

  @Autowired KafkaTaskGenerator kafkaTaskGenerator;
  @Autowired IncrementalFileTaskGenerator incrementalFileTaskGenerator;

  @Autowired private DataTaskChildServiceImpl dataTaskChildService;

  @Autowired private DataExtractorService dataExtractorService;

  /** 定时器每五分钟跑一次 */
  public void roundDataTaskExtractor() {
    List<DataTaskPO> dataTaskPOS =
        dataTaskService.list(
            new LambdaQueryWrapper<DataTaskPO>()
                .eq(DataTaskPO::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .ne(DataTaskPO::getRateType, TaskRateTypeEnum.HANDLE.getValue())
                .ne(DataTaskPO::getRateType, TaskRateTypeEnum.CRON.getValue())
                .lt(DataTaskPO::getNextExecutionTime, System.currentTimeMillis())
                .orderByAsc(DataTaskPO::getId));

    if (CollectionUtils.isEmpty(dataTaskPOS)) {
      return;
    }
    dataTaskService.updateNextExecutionTime(dataTaskPOS);

    List<Integer> dataTaskIds =
        dataTaskPOS.stream().map(DataTaskPO::getId).collect(Collectors.toList());

    Set<Integer> existDataTaskIds =
        dataTaskChildService
            .list(
                new LambdaQueryWrapper<DataTaskChildPo>()
                    .in(DataTaskChildPo::getDataTaskId, dataTaskIds)
                    .eq(DataTaskChildPo::getTaskType, 0)
                    .eq(DataTaskChildPo::getState, 0))
            .stream()
            .map(DataTaskChildPo::getDataTaskId)
            .collect(Collectors.toSet());

    dataTaskPOS =
        dataTaskPOS.stream()
            .filter(po -> existDataTaskIds.contains(po.getId()))
            .collect(Collectors.toList());

    for (DataTaskPO po : dataTaskPOS) {
      this.syncSendTaskExtractor(po);
    }
  }

  public void syncSendTaskExtractor(DataTaskPO dataTaskPO) {
    Integer dataTaskId = dataTaskPO.getId();

    List<DataTaskChildPo> dataTaskChildPos =
        dataTaskChildService.list(
            new LambdaQueryWrapper<DataTaskChildPo>()
                .eq(DataTaskChildPo::getDataTaskId, dataTaskId)
                .eq(DataTaskChildPo::getTaskType, 0)
                .eq(DataTaskChildPo::getState, 0)
                .eq(DataTaskChildPo::getDeleted, DeletedEnum.NO_DELETED.getValue())
                .orderByAsc(DataTaskChildPo::getSort));
    if (dataTaskChildPos.isEmpty()) {
      return;
    }
    List<Integer> taskExtractorIds =
        dataTaskChildPos.stream().map(DataTaskChildPo::getBindingId).collect(Collectors.toList());
    Map<Integer, DataTaskChildPo> dataTaskChildPoMap =
        dataTaskChildPos.stream()
            .collect(
                Collectors.toMap(
                    DataTaskChildPo::getBindingId,
                    DataTaskChildPo -> DataTaskChildPo,
                    (po1, po2) -> po1));
    List<DataExtractorPO> dataExtractorPOS = dataExtractorService.listByIds(taskExtractorIds);

    for (DataExtractorPO dataExtractorPO : dataExtractorPOS) {
      DataTaskChildPo dataTaskChildPo = dataTaskChildPoMap.get(dataExtractorPO.getId());

      if (Objects.equals(DataConnectorTypeEnum.JDBC.getValue(), dataExtractorPO.getType())) {
        JdbcTaskGeneratorInfo jdbcCreateJob = new JdbcTaskGeneratorInfo();
        jdbcCreateJob.setDataTaskId(dataTaskPO.getId());
        jdbcCreateJob.setPoolId(dataTaskPO.getPoolId());
        jdbcCreateJob.setTenantId(dataTaskPO.getTenantId());
        jdbcCreateJob.setDataExtractorPO(dataExtractorPO);
        jdbcCreateJob.setDataTaskChildPo(dataTaskChildPo);
        jdbcJobCreate.genTask(jdbcCreateJob);
      }

      if (Objects.equals(DataConnectorTypeEnum.KAFKA.getValue(), dataExtractorPO.getType())) {
        KafkaTaskGeneratorInfo kafkaTaskGeneratorInfo = new KafkaTaskGeneratorInfo();
        kafkaTaskGeneratorInfo.setPoolId(dataTaskPO.getPoolId());
        kafkaTaskGeneratorInfo.setDataTaskId(dataTaskPO.getId());
        kafkaTaskGeneratorInfo.setTenantId(dataTaskPO.getTenantId());
        kafkaTaskGeneratorInfo.setDataTaskChildPo(dataTaskChildPo);
        kafkaTaskGeneratorInfo.setDataExtractorPO(dataExtractorPO);
        kafkaTaskGenerator.genTask(kafkaTaskGeneratorInfo);
      }

      if (Objects.equals(
          DataConnectorTypeEnum.INCREMENTAL_FILE.getValue(), dataExtractorPO.getType())) {
        KafkaTaskGeneratorInfo kafkaTaskGeneratorInfo = new KafkaTaskGeneratorInfo();
        kafkaTaskGeneratorInfo.setPoolId(dataTaskPO.getPoolId());
        kafkaTaskGeneratorInfo.setDataTaskId(dataTaskPO.getId());
        kafkaTaskGeneratorInfo.setTenantId(dataTaskPO.getTenantId());
        kafkaTaskGeneratorInfo.setDataTaskChildPo(dataTaskChildPo);
        kafkaTaskGeneratorInfo.setDataExtractorPO(dataExtractorPO);
        incrementalFileTaskGenerator.genTask(kafkaTaskGeneratorInfo);
      }
    }
  }
}
