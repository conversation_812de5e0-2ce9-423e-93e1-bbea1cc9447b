package com.sp.proxverse.job.service.task;

/**
 * <AUTHOR>
 * @create 2023-01-05 14:23
 */
public abstract class AbstractTaskEnvironment implements TaskEnvironment {

  protected Integer threadPoolNumber;

  protected Integer queuePoolNumber;

  @Override
  public void setThreadPoolNumber(Integer number) {
    this.threadPoolNumber = number;
  }

  @Override
  public void setQueueNumber(Integer number) {
    this.queuePoolNumber = number;
  }
}
