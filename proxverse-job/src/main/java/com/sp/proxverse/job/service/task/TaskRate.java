package com.sp.proxverse.job.service.task;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-12 14:05
 */
@Data
public class TaskRate {

  @ApiModelProperty("上一次执行时间")
  private Date execTime;

  @ApiModelProperty("频率（秒数）")
  private Integer rateType;

  @ApiModelProperty("分钟")
  private Integer minute;

  @ApiModelProperty("小时")
  private Integer hour;

  @ApiModelProperty("周几")
  private Integer week;
}
