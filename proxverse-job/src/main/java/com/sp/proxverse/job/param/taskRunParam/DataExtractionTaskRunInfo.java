package com.sp.proxverse.job.param.taskRunParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-13 11:18
 */
@Data
public class DataExtractionTaskRunInfo extends TaskRunInfo {

  @ApiModelProperty("File id")
  private int fileId;

  @ApiModelProperty("连接器名称")
  private String name;

  @ApiModelProperty("jdbcURL")
  private String url;

  @ApiModelProperty("用户名")
  private String user;

  @ApiModelProperty("密码")
  private String password;

  @ApiModelProperty("whereRule")
  private String whereRule;

  @ApiModelProperty("数据集")
  private String eventset;

  @ApiModelProperty("业务ID")
  private Integer source;

  private Integer incr;

  @ApiModelProperty("TEMP TableName")
  private String tempTableName;

  @ApiModelProperty("JDBC TableName")
  private String jdbcTableName;

  @ApiModelProperty("TableName")
  private String tableName;

  @ApiModelProperty("DataPoolID")
  private int poolId;

  @ApiModelProperty("JDBC Driver")
  private String jdbcDriver;

  @ApiModelProperty(value = "数据连接Id")
  private Integer connectorId;

  @ApiModelProperty(value = "数据提取Id")
  private Integer dataExtractorId;

  @ApiModelProperty(
      value = "jdbc类型（1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala），当bizType=1时此字段不能为空")
  private Integer jdbcType;
}
