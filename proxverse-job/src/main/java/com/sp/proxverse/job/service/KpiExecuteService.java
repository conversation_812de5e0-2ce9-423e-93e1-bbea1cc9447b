package com.sp.proxverse.job.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.model.base.Response;
import com.sp.proxverse.common.model.base.ResponseUtil;
import com.sp.proxverse.common.model.po.KpiRelationPO;
import com.sp.proxverse.common.model.po.KpiReportFormsKpisPO;
import com.sp.proxverse.common.model.po.KpiReportFormsPO;
import com.sp.proxverse.common.model.po.KpiValuePO;
import com.sp.proxverse.interfaces.dao.service.KpiRelationService;
import com.sp.proxverse.interfaces.service.data.DataAPIService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiReportFormsKpisService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiReportFormsService;
import com.sp.proxverse.interfaces.service.data.kpiexecute.KpiValueService;
import com.sp.proxverse.interfaces.service.remote.model.QueryKpiDTO;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class KpiExecuteService {

  @Autowired KpiReportFormsService kpiReportFormsService;

  @Autowired KpiReportFormsKpisService kpiReportFormsKpisService;

  @Autowired KpiRelationService kpiRelationService;

  @Autowired KpiValueService kpiValueService;

  @Autowired DataAPIService dataAPIService;

  /** 定时器每1小时跑一次 */
  public void kpiReportFormsTask(boolean test) {
    try {
      // 获取运行中的报表数据
      List<KpiReportFormsPO> kpiReportFormsPOS =
          kpiReportFormsService.list(
              new LambdaQueryWrapper<KpiReportFormsPO>()
                  .eq(KpiReportFormsPO::getStatus, 1)
                  .eq(KpiReportFormsPO::getDeleted, 0));
      if (CollectionUtils.isNotEmpty(kpiReportFormsPOS)) {
        for (KpiReportFormsPO kpiReportFormsPO : kpiReportFormsPOS) {
          Integer id = kpiReportFormsPO.getId();
          // （0：每日；1：每周；2：每月；3：工作日）
          Integer dateType = kpiReportFormsPO.getDateType();
          // 日期（周/月）周几或者几号
          Integer date = kpiReportFormsPO.getDate();
          // 报表时间:HH:mm:ss
          String time = kpiReportFormsPO.getTime();
          Date dataTime = new SimpleDateFormat("HH:mm").parse(time);
          if (test) {
            saveKpiValue(id, dateType, dataTime);
          }
          // 判断每天的时间是否符合接口调用时间
          else if (Objects.equals(dateType, 0) && isEffectiveDate(dataTime)) {
            saveKpiValue(id, dateType, dataTime);
          } else if (Objects.equals(dateType, 1)) {
            // 判断当前日期是否符合报表日期周几
            if (getWeek(date) && isEffectiveDate(dataTime)) {
              saveKpiValue(id, dateType, dataTime);
            }
          } else if (Objects.equals(dateType, 2)) {
            // 判断当前日期是否符合报表每月几号
            if (getDay(date) && isEffectiveDate(dataTime)) {
              saveKpiValue(id, dateType, dataTime);
            }
          } else if (Objects.equals(dateType, 3)) {
            // 判断当前日期是否符合周一至周五
            if (getWeekdays() && isEffectiveDate(dataTime)) {
              saveKpiValue(id, dateType, dataTime);
            }
          }
        }
      }
    } catch (Exception e) {
      log.error(e.getMessage());
    }
  }

  /**
   * 保存数据
   *
   * @param id 报表id
   * @param type （0：每日；1：每周；2：每月；3：工作日）
   * @return
   */
  public int saveKpiValue(Integer id, Integer type, Date dataTime) {
    Date date = setDate(dataTime);

    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    List<KpiReportFormsKpisPO> kpiReportFormsKpisPOS =
        kpiReportFormsKpisService.list(
            new LambdaQueryWrapper<KpiReportFormsKpisPO>()
                .eq(KpiReportFormsKpisPO::getDeleted, 0)
                .eq(KpiReportFormsKpisPO::getKpiReportFormsId, id));
    if (CollectionUtils.isNotEmpty(kpiReportFormsKpisPOS)) {
      List<Integer> kpiIds =
          kpiReportFormsKpisPOS.stream()
              .map(KpiReportFormsKpisPO::getKpiId)
              .collect(Collectors.toList());
      List<KpiRelationPO> kpiRelationPOS =
          kpiRelationService.list(
              new LambdaQueryWrapper<KpiRelationPO>()
                  .eq(KpiRelationPO::getDeleted, 0)
                  .in(KpiRelationPO::getKpiId, kpiIds));
      if (CollectionUtils.isNotEmpty(kpiRelationPOS)) {
        for (KpiRelationPO kpiRelationPO : kpiRelationPOS) {
          QueryKpiDTO queryKpiValueDTO = new QueryKpiDTO();
          queryKpiValueDTO.setKpiId(kpiRelationPO.getKpiId());
          queryKpiValueDTO.setDataModelId(kpiRelationPO.getDataModelId());
          log.info("========开始远程调用，入参：" + JSON.toJSON(queryKpiValueDTO));
          try {
            Response<BigDecimal> bigDecimalResponse =
                dataAPIService.queryKpiValueById(queryKpiValueDTO);
            if (ResponseUtil.isSucc(bigDecimalResponse)) {
              log.info("============开始封装kpivalue实体类");
              KpiValuePO kpiValuePO = new KpiValuePO();
              kpiValuePO.setKpiReportFormsId(id);
              kpiValuePO.setKpiRelationId(kpiRelationPO.getId());
              kpiValuePO.setKpiValue(String.valueOf(bigDecimalResponse.getData()));
              kpiValuePO.setKpiTime(df.format(date));
              kpiValuePO.setKpiId(kpiRelationPO.getKpiId());
              kpiValuePO.setDeleted(0);
              kpiValuePO.setCreateTime(date);
              kpiValuePO.setUpdateTime(date);
              kpiValuePO.setType(type);
              boolean save = kpiValueService.save(kpiValuePO);
              log.info("===========保存结果：" + save);
            } else {
              log.error("==========调用失败了");
            }
            log.info("============结果：" + bigDecimalResponse.getData());
          } catch (Exception e) {
            log.error("==========远程调用失败了,错误信息是：" + e.getMessage());
          }
        }
      }
    }
    return 0;
  }

  private Date setDate(Date dataTime) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(dataTime); // 放入Date类型数据
    int hour = calendar.get(Calendar.HOUR_OF_DAY); // 时（24小时制）
    int minute = calendar.get(Calendar.MINUTE); // 分
    Calendar instance = Calendar.getInstance();
    instance.setTime(new Date());
    // 将时分秒,毫秒域清零
    instance.set(Calendar.HOUR_OF_DAY, hour);
    instance.set(Calendar.MINUTE, minute);
    return instance.getTime();
  }

  /**
   * 判断当前时间是否是工作日（周一至周五）
   *
   * @return
   * @throws Exception
   */
  public static boolean getWeekdays() {
    Calendar calendar = Calendar.getInstance();
    int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
    /*        weekday == 1 – 周日
    weekday == 2 – 周一
    weekday == 3 – 周二
    weekday == 4 – 周三
    weekday == 5 – 周四
    weekday == 6 – 周五
    weekday == 7 – 周六*/
    if (dayOfWeek > 1 || dayOfWeek < 7) {
      return true;
    }
    return false;
  }

  /**
   * 判断当前时间是否符合当月几号
   *
   * @param day 几号
   * @return
   * @throws Exception
   */
  public static boolean getDay(int day) {
    Calendar cale = Calendar.getInstance();
    int monthDay = cale.get(Calendar.DATE);
    return Objects.equals(day, monthDay);
  }

  /**
   * 判断当前时间是否符合周几
   *
   * @param day 周几
   * @return
   * @throws Exception
   */
  public static boolean getWeek(int day) throws ParseException {
    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    int num;
    Calendar calendar = Calendar.getInstance();
    int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - (day - 1);
    if (dayOfWeek == 1) {
      num = -6;
    } else {
      num = 2 - dayOfWeek;
    }
    calendar.add(Calendar.DATE, num);
    Date monday = calendar.getTime();
    String format = df.format(monday);
    Date parse = df.parse(format);
    return DateUtils.isSameDay(parse, new Date());
  }

  /**
   * 判断当前时间是否在[当前时间前一小时, 当前时间后一小时]区间，注意时间格式要一致
   *
   * @param nowTime 当前时间
   * @return
   */
  public static boolean isEffectiveDate(Date nowTime) throws ParseException {
    SimpleDateFormat df = new SimpleDateFormat("HH:mm");
    Calendar lastHourCalendar = Calendar.getInstance();
    lastHourCalendar.set(Calendar.HOUR_OF_DAY, lastHourCalendar.get(Calendar.HOUR_OF_DAY) - 1);
    String lastHourStr = df.format(lastHourCalendar.getTime());
    Date startTime = df.parse(lastHourStr);

    Calendar nextHourCalendar = Calendar.getInstance();
    nextHourCalendar.set(Calendar.HOUR_OF_DAY, nextHourCalendar.get(Calendar.HOUR_OF_DAY) + 1);
    String nextHour = df.format(nextHourCalendar.getTime());
    Date endTime = df.parse(nextHour);

    if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
      return true;
    }
    Calendar date = Calendar.getInstance();
    date.setTime(nowTime);

    Calendar begin = Calendar.getInstance();
    begin.setTime(startTime);

    Calendar end = Calendar.getInstance();
    end.setTime(endTime);

    if (date.after(begin) && date.before(end)) {
      return true;
    } else {
      return false;
    }
  }
}
