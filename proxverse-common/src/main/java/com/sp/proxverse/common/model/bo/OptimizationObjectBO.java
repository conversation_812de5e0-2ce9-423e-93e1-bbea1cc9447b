package com.sp.proxverse.common.model.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * 优化对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-09 19:07:25
 */
@Builder
@Data
@TableName("t_optimization_object")
public class OptimizationObjectBO {

  @Tolerate
  public OptimizationObjectBO() {
    // comment empty
  }

  /** 编号 */
  @TableId private Integer id;
  /** 名称 */
  private String name;

  private Integer status;

  private OptimizationSignalBO signal;

  private OptimizationExecutionRulesBO rules;
}
