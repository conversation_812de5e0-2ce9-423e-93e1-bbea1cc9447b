package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程仿真方案
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
@Getter
@Setter
@TableName("t_simulation_programme")
public class SimulationProgrammePo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 仿真Id */
  private Integer simulationId;

  /** 方案名称 */
  private String name;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  /** 租户ID */
  private Integer tenantId;
}
