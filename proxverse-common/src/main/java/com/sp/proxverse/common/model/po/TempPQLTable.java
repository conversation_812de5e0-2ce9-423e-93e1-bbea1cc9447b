package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sp.proxverse.common.model.CopyEntityService;
import java.sql.Timestamp;
import lombok.Data;

@Data
@TableName("t_temp_pql_table")
public class TempPQLTable implements CopyEntityService<TempPQLTable> {
  @Override
  public TempPQLTable copy() {
    TempPQLTable tempPQLTable = new TempPQLTable();
    tempPQLTable.setName(this.name);
    tempPQLTable.setTableDef(this.tableDef);
    return tempPQLTable;
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private Integer topicId;

  private String name;

  private String tableDef;

  private boolean deleted;

  private Timestamp updatedAt;

  private Timestamp createdAt;

  public TempPQLTable() {
    // comment empty
  }
}
