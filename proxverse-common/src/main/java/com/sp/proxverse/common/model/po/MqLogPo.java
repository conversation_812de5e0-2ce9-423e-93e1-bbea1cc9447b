package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 消息队列日志
 *
 * <AUTHOR>
 * @since 2022-06-18
 */
@Getter
@Setter
@TableName("t_mq_log")
public class MqLogPo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 消息类型 */
  private Integer type;

  /** 消息入参 */
  private String param;

  /** 运行状态：0：开始执行，1：执行成功，2：执行失败 */
  private Integer runState;

  /** 错误信息 */
  private String errorMessage;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  /** 租户ID */
  private Integer tenantId;
}
