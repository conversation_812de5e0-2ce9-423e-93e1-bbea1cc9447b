package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 执行动作修改日志
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Getter
@Setter
@TableName("t_action_modify_log")
public class ActionModifyLogPo implements Serializable {

  /** 日志操作类型 创建 */
  public static final Integer MODIFY_TYPE_CREAT = 1;

  /** 日志操作类型 修改 */
  public static final Integer MODIFY_TYPE_MODIFY = 2;

  /** 日志操作类型 删除 */
  public static final Integer MODIFY_TYPE_DELETE = 3;

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 动作流Id */
  private Integer actionFlowId;

  /** 类型，1：创建，2：修改，3：删除 */
  private Integer modifyType;

  private String info;

  /** 修改人 */
  private Integer createUser;

  private LocalDateTime createTime;
}
