package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("数据池update对象")
public class DataPoolUpdateRequest {

  @ApiModelProperty(value = "数据池ID，编辑时必传，创建时为空")
  @NotNull(message = "{400006}")
  private Integer poolId;

  @ApiModelProperty(value = "名称")
  @NotBlank(message = "{400007}")
  private String name;
}
