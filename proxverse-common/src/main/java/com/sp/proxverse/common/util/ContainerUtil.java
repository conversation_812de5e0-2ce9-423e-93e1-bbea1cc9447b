package com.sp.proxverse.common.util;

import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 容器工具类
 *
 * <AUTHOR>
 * @create 2023-02-06 14:12
 */
public class ContainerUtil {

  /**
   * 取map最后一个元素 LinkedHashMap
   *
   * @param map
   * @param <K>
   * @param <V>
   * @return
   */
  public static <K, V> Entry<K, V> getTail(Map<K, V> map) {
    Iterator<Entry<K, V>> iterator = map.entrySet().iterator();
    Entry<K, V> tail = null;
    while (iterator.hasNext()) {
      tail = iterator.next();
    }
    return tail;
  }
}
