package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_topic_sheet")
public class TopicSheetPO {

  @Tolerate
  public TopicSheetPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("主题ID，这里主要是业务分析图谱")
  private Integer topicId;

  @ApiModelProperty("sheet类型（1：流程AI，2：流程变体，3：案例视图，4：业务视图，5：流程视图）")
  private Integer type;

  @ApiModelProperty("排序字段")
  @TableField(value = "`order`")
  private Integer order;

  @ApiModelProperty("快照父ID")
  private Integer snapshotParentId;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @ApiModelProperty("map json 格式的配置")
  private String configMap;
}
