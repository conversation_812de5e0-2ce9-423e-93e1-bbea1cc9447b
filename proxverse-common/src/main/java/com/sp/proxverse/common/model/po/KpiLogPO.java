package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * kpi日志 kpi日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 11:03:15
 */
@Builder
@Data
@TableName("t_kpi_log")
public class KpiLogPO {

  @Tolerate
  public KpiLogPO() {
    // comment empty
  }

  /** 主键Id */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  /** KPI执行id */
  private Integer kpiRelationId;
  /** 类型（0任务分配/1关闭任务/2:完成任务/3重启任务/4编辑任务/5删除任务/6创建任务） */
  private Integer type;
  /** 操作人的用户id */
  private Integer distributionUserId;
  /** 被分配的用户id */
  private Integer assignedUserId;
  /** 创建人Id */
  private Integer createId;
  /** 创建时间 */
  private Date createTime;
  /** 更新时间 */
  private Date updateTime;
  /** 是否逻辑删除 */
  private Integer deleted;
  /** 排序号 */
  private Integer orderNo;
}
