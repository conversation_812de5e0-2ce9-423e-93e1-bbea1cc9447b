package com.sp.proxverse.common.model.vo.action;

import com.alibaba.fastjson.JSON;
import com.sp.proxverse.common.model.enums.ActionConnectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-06-09 2:38 下午
 */
@Getter
@Setter
@ApiModel("执行动作连接信息")
public class ActionConnectInfoResVo {

  @ApiModelProperty("连接类型")
  @NotNull(message = "{400008}")
  private ActionConnectTypeEnum actionConnectTypeEnum;

  @ApiModelProperty("连接名称")
  @NotNull(message = "{400007}")
  private String name;

  @ApiModelProperty("信息")
  private Object connectInfo;

  public String getConnectInfoString() {
    if (this.connectInfo instanceof String) {
      return this.connectInfo.toString();
    }
    return JSON.toJSONString(this.connectInfo);
  }
}
