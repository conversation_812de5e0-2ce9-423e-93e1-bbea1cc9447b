package com.sp.proxverse.common.model.vo.action;

import com.sp.proxverse.common.model.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-05-27 10:38 上午
 */
@Getter
@Setter
@ApiModel("获取执行动作流列表")
public class GetActionFlowLogsReqVo extends PageRequest {

  @ApiModelProperty("topicId")
  @NotNull(message = "{400006}")
  private Integer topicId;
}
