package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_data_task_extractor")
public class DataTaskExtractorPO {

  @Tolerate
  public DataTaskExtractorPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("任务ID")
  private Integer taskId;

  @ApiModelProperty("事实上就是fileId")
  private Integer fileId;

  @ApiModelProperty("事实上就是fileId")
  private Integer deleted;

  @ApiModelProperty("该数据任务选择的提取器ID")
  private Integer extractorId;

  @ApiModelProperty("就是在数仓创建的链接器名称，用来向数仓查询状态用的")
  private String taskName;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
