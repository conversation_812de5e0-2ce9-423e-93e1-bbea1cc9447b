package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_data_model_file_master")
public class DataModelFileMasterPO {

  @Tolerate
  public DataModelFileMasterPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("所属数据模型ID")
  private Integer dataModelId;

  @ApiModelProperty("所属文件ID")
  private Integer originFileId;

  @ApiModelProperty("所属字段ID")
  private Integer mergeFileId;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("0：未加载成功，1：加载中，2：加载成功")
  private Integer status;

  @ApiModelProperty("merge后的文件UUID名称")
  private String mergeFileUniqname;

  @ApiModelProperty("merge后的拼接sql")
  private String mergeSql;

  private String caseMergeSql;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
