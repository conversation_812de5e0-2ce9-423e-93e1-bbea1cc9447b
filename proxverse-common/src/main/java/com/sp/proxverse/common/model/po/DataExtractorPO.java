package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_data_extractor")
public class DataExtractorPO {

  @Tolerate
  public DataExtractorPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("链接器ID")
  private Integer connectorId;

  /** @see DataExtractorTypeEnum */
  @ApiModelProperty(value = "类型（1：jdbc，2：kafka）")
  private Integer type;

  @ApiModelProperty("1：增量更新（默认），2：全量更新")
  private Integer updateType;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("事实上就是fileId")
  private Integer fileId;

  @ApiModelProperty("所选表名称")
  private String tableName;

  @ApiModelProperty("where规则")
  private String whereRule;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("0：不可用（只创建了提取器，没有选择字段），1：可用（提交了字段）")
  private Integer status;

  @ApiModelProperty("0：暂停，1：开始")
  private Integer start;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @ApiModelProperty("data pool id")
  private Integer poolId;

  @ApiModelProperty("spark 中 jdbc 表的表名")
  private String sparkTableName;
}
