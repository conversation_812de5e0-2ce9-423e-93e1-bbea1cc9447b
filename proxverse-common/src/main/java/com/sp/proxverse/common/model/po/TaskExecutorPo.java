package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 任务执行信息
 *
 * <AUTHOR>
 * @since 2023-01-06
 */
@Getter
@Setter
@TableName("t_task_executor")
public class TaskExecutorPo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /**
   * 状态: 0，暂停，1，开启，2，已经执行,3 正在执行 ，4 加载
   *
   * @see JobStatusEnum
   */
  private Integer status;

  private String taskName;

  /** 任务来源: 0 用户创建，1 任务生成， */
  private Integer source;

  /** 是否立即执行 */
  private Boolean immediatelyFlag;

  /** 父任务Id */
  private Integer parentId;

  private Integer dataTaskId;

  private Integer dataTaskChildId;

  /** 任务枚举 */
  private String taskEnum;

  /** 任务信息 */
  private String taskInfo;

  /** 任务信息标志: 用了确认这个任务是否一样 */
  private String taskInfoFlag;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  /** 租户ID */
  private Integer tenantId;

  @ApiModelProperty("关联ID，taskEnum是仿真时代表simulationId")
  private Integer releationId;

  private Integer poolId;
}
