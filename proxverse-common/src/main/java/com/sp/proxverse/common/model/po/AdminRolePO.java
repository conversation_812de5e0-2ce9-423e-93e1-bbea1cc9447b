package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_admin_role")
public class AdminRolePO {

  @Tolerate
  public AdminRolePO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private Integer userId;

  private Integer roleId;

  private Integer createUser;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
