package com.sp.proxverse.common.model.vo.kpi.response;

import com.sp.proxverse.common.model.vo.KpiNameVO;
import com.sp.proxverse.common.model.vo.UserNameVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("kpi报表详情响应对象")
public class KpiReportFormsInfoResponse {

  @ApiModelProperty(value = "kpi报表主键")
  private Integer id;

  @ApiModelProperty(value = "报表名称")
  private String reportFormsName;

  @ApiModelProperty(value = "报表时间类型（0：每日；1：每周；2：每月）")
  private Integer dateType;

  @ApiModelProperty(value = "是否是工作日")
  private Boolean workDay;

  @ApiModelProperty(value = "日期（周/月）")
  private Integer date;

  @ApiModelProperty(value = "报表时间")
  private String time;

  @ApiModelProperty(value = "发送的用户")
  private List<UserNameVO> user;

  @ApiModelProperty(value = "kpis")
  private List<KpiNameVO> kpis;
}
