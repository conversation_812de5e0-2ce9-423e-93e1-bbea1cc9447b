package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_topic_config_global")
public class TopicConfigGlobalPO {

  @Tolerate
  public TopicConfigGlobalPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private Integer topicId;

  private Integer tenantId;

  private Integer deleted;

  private String description;

  private String eventColumn;

  private String format;

  private String formatting;

  private String timeParam;
}
