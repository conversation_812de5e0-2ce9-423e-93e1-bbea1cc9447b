package com.sp.proxverse.common.model.vo.request.topic;

import io.swagger.annotations.ApiModel;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("PQL validation req")
public class PQLValidationRequest {

  @Tolerate
  public PQLValidationRequest() {
    // comment empty
  }

  private Integer topicId;

  @NotNull private String statement;
}
