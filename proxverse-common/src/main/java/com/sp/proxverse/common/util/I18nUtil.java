package com.sp.proxverse.common.util;

import com.sp.proxverse.common.config.MyLocaleResolver;
import java.util.Locale;
import java.util.Objects;
import javax.annotation.PostConstruct;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

@Component
public class I18nUtil {

  private static MessageSource messageSource;

  private MyLocaleResolver resolver;

  private static MyLocaleResolver myLocaleResolver;

  public I18nUtil(MyLocaleResolver myLocaleResolver) {
    this.resolver = myLocaleResolver;
  }

  private static void setMyLocaleResolver(MyLocaleResolver resolver) {
    I18nUtil.myLocaleResolver = resolver;
  }

  @PostConstruct
  public void init() {
    setMyLocaleResolver(resolver);
  }

  public static void setMessageSource(MessageSource messageSource) {
    I18nUtil.messageSource = messageSource;
  }

  public static String getMessage(String msgKey) {
    Locale locale;
    if (myLocaleResolver == null) {
      locale = new Locale("en", "");
    } else {
      locale = myLocaleResolver.getLocale();
    }
    try {
      return messageSource.getMessage(msgKey, null, locale);
    } catch (Exception e) {
      return msgKey;
    }
  }

  public static boolean isZh() {
    Locale locale = myLocaleResolver.getLocale();
    return Objects.equals("zh", locale.getLanguage());
  }
}
