package com.sp.proxverse.common.model.vo.request.topic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("保存bookmark请求对象")
public class SaveBookMarkRequest {

  @Tolerate
  public SaveBookMarkRequest() {
    // comment empty
  }

  @NotNull(message = "{400006}")
  private Integer topicId;

  @NotBlank(message = "{400007}")
  private String name;

  private Integer markId;

  @ApiModelProperty("0：工作台添加（发布时需要删除覆盖），1：指挥舱添加（不可删除覆盖）")
  private Integer type;
}
