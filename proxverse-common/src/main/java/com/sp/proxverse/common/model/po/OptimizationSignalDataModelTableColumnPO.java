package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * 优化信号数据模型表列
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-09 19:07:25
 */
@Builder
@Data
@TableName("t_optimization_signal_data_model_table_column")
public class OptimizationSignalDataModelTableColumnPO {

  @Tolerate
  public OptimizationSignalDataModelTableColumnPO() {
    // comment empty
  }

  /** 编号 */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  /** 数据模型id */
  private Integer dataModelId;
  /** 文件id（表） */
  private Integer fileId;
  /** 列id */
  private Integer columnId;
  /** 优化信号id */
  private Integer optimizationSignalId;
  /** 创建人id */
  private Integer createrId;
  /** */
  private Date createTime;
  /** */
  private Date updateTime;
  /** 是否删除 */
  private Long deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
