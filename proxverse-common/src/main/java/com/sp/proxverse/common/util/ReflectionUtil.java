package com.sp.proxverse.common.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Map;
import org.apache.commons.collections.map.UnmodifiableMap;

/**
 * <AUTHOR>
 * @create 2023-03-17 16:19
 */
public class ReflectionUtil {

  /** * 获取私有成员变量的值 */
  public static Object getValue(Object instance, String fieldName)
      throws IllegalAccessException, NoSuchFieldException {

    Field field = instance.getClass().getDeclaredField(fieldName);
    // 参数值为true，禁止访问控制检查
    field.setAccessible(true);

    return field.get(instance);
  }

  /** * 设置私有成员变量的值 */
  public static void setValue(Object instance, String fileName, Object value)
      throws NoSuchFieldException, SecurityException, IllegalArgumentException,
          IllegalAccessException {
    Field field = instance.getClass().getDeclaredField(fileName);
    field.setAccessible(true);
    field.setInt(field, field.getModifiers() & ~Modifier.FINAL);
    field.set(instance, value);
  }

  /** * 设置私有成员变量的值 */
  public static Map getPublicValueMap(Object instance, String fileName)
      throws NoSuchFieldException, SecurityException, IllegalArgumentException,
          IllegalAccessException {
    Field field = instance.getClass().getDeclaredField(fileName);
    field.setAccessible(true);

    UnmodifiableMap unmodifiableMap = (UnmodifiableMap) field.get(instance);

    return unmodifiableMap;
  }

  /** * 访问私有方法 */
  public static Object callMethod(
      Object instance, String methodName, Class[] classes, Object[] objects)
      throws NoSuchMethodException, SecurityException, IllegalAccessException,
          IllegalArgumentException, InvocationTargetException {

    Method method = instance.getClass().getDeclaredMethod(methodName, classes);
    method.setAccessible(true);
    return method.invoke(instance, objects);
  }
}
