package com.sp.proxverse.common.util;

import com.sp.proxverse.common.exception.BizException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IOUtils;

@Slf4j
public class HadoopUtil {

  public static final Configuration conf = new Configuration();

  public static FileSystem getDefaultFileSystem(Path path) {
    return getDefaultFileSystem(path, conf);
  }

  public static FileSystem getDefaultFileSystem(Path path, Configuration conf) {
    try {
      return path.getFileSystem(conf);
    } catch (IOException e) {
      log.info("getDefaultFileSystem error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  public static FileSystem getDefaultFileSystem() {
    Configuration config = new Configuration();
    FileSystem fileSystem;
    try {
      fileSystem = FileSystem.get(config);
    } catch (IOException e) {
      log.info("error for getFile:", e);
      throw new BizException(5000, e.getMessage());
    }
    return fileSystem;
  }

  public static void copyToPath(InputStream inputStream, String path) {
    Path distPath = new Path(path);
    FileSystem defaultFileSystem = getFileSystemByPath(distPath);

    try (FSDataOutputStream fsDataOutputStream = defaultFileSystem.create(distPath)) {
      IOUtils.copyBytes(inputStream, fsDataOutputStream, new Configuration());
    } catch (IOException e) {
      log.info("copyToPath error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  public static void deletePath(String path) {
    Path hadoopPath = new Path(path);
    deleteInternal(hadoopPath);
  }

  public static void rename(String path, String newPath) {
    Path hadoopPath = new Path(path);
    Path newHadoopPath = new Path(newPath);
    try {
      FileSystem defaultFileSystem = getFileSystemByPath(hadoopPath);
      defaultFileSystem.rename(hadoopPath, newHadoopPath);
    } catch (IOException e) {
      log.info("rename error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  public static void deletePath(URI path) {
    Path hadoopPath = new Path(path);
    deleteInternal(hadoopPath);
  }

  private static void deleteInternal(Path hadoopPath) {
    try {
      FileSystem defaultFileSystem = getFileSystemByPath(hadoopPath);
      if (defaultFileSystem.exists(hadoopPath)) {
        defaultFileSystem.delete(hadoopPath, true);
      }
    } catch (IOException e) {
      log.info("deleteInternal error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  public static boolean exists(String path) {
    Path hadoopPath = new Path(path);
    try {
      FileSystem defaultFileSystem = getFileSystemByPath(hadoopPath);
      return defaultFileSystem.exists(hadoopPath);
    } catch (IOException e) {
      log.info("path exists error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  public static void createDir(String path) {
    Path hadoopPath = new Path(path);
    try {
      FileSystem defaultFileSystem = getFileSystemByPath(hadoopPath);

      if (!defaultFileSystem.exists(hadoopPath)) {
        defaultFileSystem.mkdirs(hadoopPath);
      }
    } catch (IOException e) {
      log.info("createDir error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  public static void createDir(Path path) {
    try {
      FileSystem defaultFileSystem = getFileSystemByPath(path);
      if (!defaultFileSystem.exists(path)) {
        defaultFileSystem.mkdirs(path);
      }
    } catch (IOException e) {
      log.info("create dir by path error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  /**
   * @param path
   * @return 字节 b
   */
  public static Long getFileSize(String path) {
    Path hadoopPath = new Path(path);

    try {
      FileSystem defaultFileSystem = getFileSystemByPath(hadoopPath);
      if (defaultFileSystem.exists(hadoopPath)) {
        return defaultFileSystem.getFileStatus(hadoopPath).getLen();
      } else {
        return 0L;
      }
    } catch (IOException e) {
      log.info("getFileSize error:", e);
      throw new BizException(5000, e.getMessage());
    }
  }

  /**
   * 根据路径获取适当的FileSystem 如果路径的scheme是oss，则使用该路径的FileSystem 否则使用默认的FileSystem
   *
   * @param path 文件路径
   * @return 适用于该路径的FileSystem
   */
  public static FileSystem getFileSystemByPath(Path path) {
    if (path.toUri().getScheme() != null && path.toUri().getScheme().equals("oss")) {
      return getDefaultFileSystem(path);
    } else {
      return getDefaultFileSystem();
    }
  }
}
