package com.sp.proxverse.common.model.vo.api;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-10-20 15:07
 */
@Data
@ApiModel("运行数据转换请求")
public class RunTransformationReqVo {

  private Integer poolId;

  private String sqlContent;

  private Integer transformationId;

  private Integer dataTaskId;

  private Boolean asyncFlag;

  public RunTransformationReqVo() {
    // comment empty
  }
}
