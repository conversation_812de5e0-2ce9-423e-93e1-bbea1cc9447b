package com.sp.proxverse.common.model.vo.request.topic;

import com.sp.proxverse.common.model.page.PageRequest;
import com.sp.proxverse.common.model.po.KpiPO;
import com.sp.proxverse.common.model.vo.request.FieldSearchRequest;
import com.sp.proxverse.common.model.vo.request.KpiVariableSortDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("参数dimension kpi数据请求对象")
public class ParameterDimensionKpiDataRequest extends PageRequest {

  @Tolerate
  public ParameterDimensionKpiDataRequest() {
    // comment empty
  }

  private Integer topicId;

  @ApiModelProperty(value = "当前主题表格ID", required = true)
  private Integer topicSheetId;

  private Integer componentId;

  private List<KpiPO> pqlList;

  private Integer dataModelId;

  @ApiModelProperty(value = "排序（现在暂时只有一个）")
  private List<KpiVariableSortDTO> sortList;

  @ApiModelProperty(value = "对字段搜索")
  private List<FieldSearchRequest> searchList;

  private String[] expressions;

  @ApiModelProperty(value = "忽略topic过滤")
  public Boolean ignoreTopicFilter;

  @ApiModelProperty(value = "忽略通用设置导出数据验证->下载工作时间表")
  public Boolean ignoreExportVerify;

  public boolean getIgnoreTopicFilter() {
    if (ignoreTopicFilter == null) {
      return false;
    }
    return ignoreTopicFilter;
  }

  @ApiModelProperty(value = "行数规则（1：滚动，2：限制，3：前N行），注：OLAP表单可传1和2，柱状图和饼图可传2、3")
  private Integer rule;

  @ApiModelProperty(value = "限制行数，当rule=2、3时，此字段必填")
  private Integer limit;

  @ApiModelProperty(value = "取行还是取列，为0或者空，默认取行，为1：取列（目前echarts用到）")
  private Integer type;

  @ApiModelProperty(value = "是否对查询结果去重，1：是，其他：否")
  private Integer distinct;

  @ApiModelProperty(value = "是否关闭模糊，1：是，0：否")
  private Integer likeDisabled;
}
