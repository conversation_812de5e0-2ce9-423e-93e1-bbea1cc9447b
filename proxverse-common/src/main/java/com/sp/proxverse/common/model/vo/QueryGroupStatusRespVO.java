package com.sp.proxverse.common.model.vo;

import com.sp.proxverse.common.model.enums.VersionsEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("查询租户状态返回DTO")
public class QueryGroupStatusRespVO {

  @Tolerate
  public QueryGroupStatusRespVO() {
    // comment empty
  }

  @ApiModelProperty(value = "企业名称")
  private String company;

  @ApiModelProperty(value = "租户ID")
  private Integer groupId;

  @ApiModelProperty(value = "")
  private VersionsEnum version;

  @ApiModelProperty(value = "有效期（为空则表示”即将上线“）")
  private String expiration;

  @ApiModelProperty(value = "员工人数")
  private Integer number;

  @ApiModelProperty(value = "总员工人数")
  private Integer sumNumber;

  @ApiModelProperty("空间大小MB")
  private Long spaceSize;

  @ApiModelProperty("已用空间大小MB")
  private Long usedSpaceSize;

  private Long modelCountLimit;

  private Long modelCount;

  private Boolean aiEnabled;
}
