package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@TableName(value = "t_file_variant")
@Data
@Builder
public class FileVariantPO {

  @Tolerate
  public FileVariantPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty(value = "文件ID")
  private Integer fileId;

  @ApiModelProperty(value = "变体值")
  private String variant;

  @ApiModelProperty(value = "当前变体数量")
  private Integer count;

  @ApiModelProperty(value = "持续时间")
  private BigDecimal caseDuration;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "是否删除（0：未，1：已删除）")
  private Integer deleted;

  @ApiModelProperty(value = "0:file相关的最大变体，1：topic相关的可变变体（过滤项）")
  private Integer type;

  @ApiModelProperty(value = "变体ID")
  private Integer variantId;

  private Integer dataModelId;

  @ApiModelProperty(value = "topicId")
  private Integer topicId;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  private Integer skip;
}
