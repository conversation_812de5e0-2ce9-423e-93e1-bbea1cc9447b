package com.sp.proxverse.common.model.vo.action;

import com.sp.proxverse.common.model.enums.ActionRunStartEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-24 10:59 上午
 */
@Data
@ApiModel("执行日志")
public class ActionLogResVo {

  @ApiModelProperty("动作类型")
  private String actionType;

  @ApiModelProperty("动作编码")
  private Integer actionCode;

  @ApiModelProperty("运行时间")
  private String runTime;

  @ApiModelProperty("运行状态")
  private ActionRunStartEnum actionRunStartEnum;

  @ApiModelProperty("运行人")
  private String runUser;

  @ApiModelProperty("耗时所用时间")
  private String consumingTime;

  @ApiModelProperty("执行动作名称")
  private String actionName;
}
