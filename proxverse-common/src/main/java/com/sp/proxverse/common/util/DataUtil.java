package com.sp.proxverse.common.util;

import java.util.Objects;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class DataUtil {

  private String[] parseDate = {"yyyy-MM-dd", "yyyy年MM月dd日", "yyyy/MM/dd"};

  private String[] parseTime = {"HH:mm:ss", "HH:mm"};

  private String[] parseDateTime = {
    "yyyy年MM月dd HH时mm分ss",
    "yyyy-MM-dd HH:mm:ss",
    "yyyy-MM-dd HH:mm",
    "yyyy/MM/dd HH:mm:ss",
    "yyyy/MM/dd HH:mm"
  };

  private String timeSuffix = " 00:00:00";

  private String datePrefix = "2000-01-01 ";

  public String formatDate(String str) {
    if (this.isDate(str)) {
      str = str.replace("/", "-");
      str = str.replace("年", "-");
      str = str.replace("月", "-");
      str = str.replace("日", "");
      String[] split = str.split("-");
      if (split[1].length() != 2) {
        split[1] = "0" + split[1];
      }
      if (split[2].length() != 2) {
        split[2] = "0" + split[2];
      }
      str = StringUtils.join(split, "-");
      return str + timeSuffix;
    }
    if (this.isTime(str)) {
      String[] split = str.split(":");
      for (int i = 0; i < split.length; i++) {
        if (split[i].length() != 2) {
          split[i] = "0" + split[i];
        }
      }
      str = StringUtils.join(split, ":");
      if (str.length() <= 5) {
        return datePrefix + str + ":00";
      }
      return datePrefix + str;
    }
    if (this.isDateTime(str)) {
      if (str.contains("/")) {
        str = str.replace("/", "-");
      }
      if (str.contains("年")) {
        str = str.replace("年", "-");
      }
      if (str.contains("月")) {
        str = str.replace("月", "-");
      }
      if (str.contains("日")) {
        str = str.replace("日", "");
      }
      if (str.contains("时")) {
        str = str.replace("时", ":");
      }
      if (str.contains("分")) {
        str = str.replace("分", ":");
      }
      if (str.contains("秒")) {
        str = str.replace("秒", "");
      }

      String[] s = str.split(" ");
      String[] data = s[0].split("-");
      for (int i = 1; i < data.length; i++) {
        if (data[i].length() != 2) {
          data[i] = "0" + data[i];
        }
      }
      String dataStr = StringUtils.join(data, "-");

      String[] time = s[1].split(":");
      for (int i = 0; i < time.length; i++) {
        if (time[i].length() != 2) {
          time[i] = "0" + time[i];
        }
      }
      String timeStr = StringUtils.join(time, ":");

      str = dataStr + " " + timeStr;
      if (str.length() <= 16) {
        str = str + ":00";
      }
      return str;
    }
    return str;
  }

  public boolean isDate(String str) {
    if (StringUtils.isBlank(str)) {
      return false;
    }

    if (str.contains("yyyyy") || str.contains("MMM")) {
      return false;
    }
    if (str.contains("yyyy") && str.contains("M") && !str.contains("H")) {
      return true;
    }

    return false;
  }

  public String turnDataFormat(String format) {
    String replace = format.replace("MM", "M");
    String replace1 = replace.replace("dd", "d");
    String replace2 = replace1.replace("HH", "H");
    String replace3 = replace2.replace("mm", "m");
    String replace4 = replace3.replace("ss", "s");
    return replace4;
  }

  public boolean isTime(String str) {
    if (StringUtils.isBlank(str)) {
      return false;
    }
    if (str.contains("HHH") || str.contains("mmm")) {
      return false;
    }
    if (!str.contains("yyyy") && str.contains("H") && str.contains("m")) {
      return true;
    }

    return false;
  }

  public boolean isDateTime(String str) {
    if (StringUtils.isBlank(str)) {
      return false;
    }
    if (str.contains("yyyyy") || str.contains("MMM") || str.contains("HHH")) {
      return false;
    }
    if (str.contains("yyyy") && str.contains("M") && str.contains("H")) {
      return true;
    }

    return false;
  }

  public static boolean isInteger(String str) {
    if (null == str || "".equals(str) || str.length() >= 20) {
      return false;
    }
    Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
    return pattern.matcher(str).matches();
  }

  public static boolean isDouble(String str) {
    if (null == str || "".equals(str)) {
      return false;
    }
    Pattern pattern = Pattern.compile("^[-\\+]?\\d*[.]\\d+$");
    return pattern.matcher(str).matches();
  }

  public boolean isBool(String str) {
    if (Objects.equals(str, "true")
        || Objects.equals(str, "TRUE")
        || Objects.equals(str, "false")
        || Objects.equals(str, "FALSE")) {
      return true;
    }
    return false;
  }

  public static boolean isNumeric(String str) {
    if (StringUtils.isBlank(str)) {
      return false;
    }
    Pattern pattern = Pattern.compile("[0-9]*");
    if (str.indexOf('.') >= 1) {
      if (str.indexOf('.') == str.lastIndexOf('.') && str.split("\\.").length == 2) {
        return pattern.matcher(str.replace(".", "")).matches();
      } else {
        return false;
      }
    } else {
      return pattern.matcher(str).matches();
    }
  }
}
