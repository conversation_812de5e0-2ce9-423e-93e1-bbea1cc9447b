package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("Kpi编辑对象")
public class KpiEditRequest {

  @ApiModelProperty(value = "kpiId（如果是知识模型导入再编辑时，此字段代表原kpiId）", required = true)
  private Integer kpiId;

  @ApiModelProperty(value = "修改后kpiId（如果是知识模型导入再编辑时，此字段代表修改后的kpiId）")
  private Integer editKpiId;

  @ApiModelProperty(value = "名称，saveType=2时必传")
  private String name;

  @ApiModelProperty(value = "单位，saveType=2时必传")
  private String unit;

  @ApiModelProperty(value = "类型（1:越大越优，2：越小越优），saveType=2时必传")
  private Integer kpiType;

  @ApiModelProperty(value = "基线，saveType=2时必传")
  private String baseLine;

  @ApiModelProperty(value = "公式，saveType=2时必传")
  private String expression;

  @ApiModelProperty(
      value = "在业务知识模型内编辑时时，传业务知识模型ID（topicId），在主题表格sheet内创建时传topicSheetId",
      required = true)
  @NotNull(message = "{400006}")
  private Integer businessDataId;

  @ApiModelProperty(value = "创建kpi时的业务类型，在主题表格内创建时传1，在业务知识模型内创建时传2", required = true)
  @NotNull(message = "{400008}")
  private Integer businessType;

  @ApiModelProperty(value = "创建kpi的方式（1：从知识模型中导入，2：自定义）", required = true)
  @NotNull(message = "{400004}")
  private Integer saveType;

  private String formatting;

  private String format;

  private String columnType;
}
