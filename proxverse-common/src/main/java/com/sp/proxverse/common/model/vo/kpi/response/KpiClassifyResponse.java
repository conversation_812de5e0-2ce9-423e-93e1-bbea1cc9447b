package com.sp.proxverse.common.model.vo.kpi.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("kpi分类响应对象")
public class KpiClassifyResponse {

  @ApiModelProperty(value = "平均值", required = true)
  public String avgValue;

  @ApiModelProperty(value = "字段值", required = true)
  public String column;

  @ApiModelProperty(value = "当前值", required = true)
  public String value;

  @ApiModelProperty(value = "范围-大", required = true)
  public String maxValue;

  @ApiModelProperty(value = "范围-小", required = true)
  public String minValue;

  @ApiModelProperty(value = "单位", required = true)
  public String unit;
}
