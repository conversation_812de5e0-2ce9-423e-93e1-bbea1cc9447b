package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.vo.datamodel.request.SetFieldTypeRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.unsafe.types.UTF8String;

@Data
@ApiModel("查询文件数据请求对象")
public class QueryFileDataRequest {

  public static final String DEFAULT_SEPARATOR_MARK = ",";

  @ApiModelProperty(value = "文件ID")
  @NotNull(message = "{400006}")
  private Integer fileId;

  @ApiModelProperty(value = "文件分割符，有值则按此符号分割，为空则默认分割")
  private String separatorMark;

  private String encoding;

  private List<SetFieldTypeRequest> fieldList;

  /** 是否包含表头 */
  private Boolean includeHeader;

  public Boolean getIncludeHeader() {
    if (includeHeader == null) {
      return true;
    }
    return includeHeader;
  }

  /** 属性列重命名 */
  private Map<String, String> columnRename;

  public List<SetFieldTypeRequest> getFieldList(String escapedMark) {
    if (CollectionUtils.isEmpty(fieldList)) {
      return fieldList;
    }
    List<SetFieldTypeRequest> fieldListRet = new ArrayList<>();

    for (SetFieldTypeRequest setFieldTypeRequest : fieldList) {
      String field = setFieldTypeRequest.getField();
      UTF8String[] split =
          UTF8String.fromString(field).split(UTF8String.fromString(escapedMark), -1);
      if (split.length == 1) {
        fieldListRet.add(setFieldTypeRequest);
      } else {
        for (UTF8String part : split) {
          UTF8String current = part;
          while (true) {
            UTF8String[] nestedSplit = current.split(UTF8String.fromString(escapedMark), -1);
            if (nestedSplit.length == 1) {
              SetFieldTypeRequest setFieldTypeRequestNew = new SetFieldTypeRequest();
              setFieldTypeRequestNew.setField(current.toString());
              setFieldTypeRequestNew.setFieldType(setFieldTypeRequest.getFieldType());
              fieldListRet.add(setFieldTypeRequestNew);
              break;
            } else {
              current = nestedSplit[0];
              for (int i = 1; i < nestedSplit.length; i++) {
                SetFieldTypeRequest setFieldTypeRequestNew = new SetFieldTypeRequest();
                setFieldTypeRequestNew.setField(nestedSplit[i].toString());
                setFieldTypeRequestNew.setFieldType(setFieldTypeRequest.getFieldType());
                fieldListRet.add(setFieldTypeRequestNew);
              }
            }
          }
        }
      }
    }
    return fieldListRet;
  }

  public String getSeparatorMark() {
    return separatorMark;
  }
}
