package com.sp.proxverse.common.model.vo.request.processAi;

import com.sp.proxverse.common.model.enums.EditLockEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-03-03 10:46
 */
@Data
@ApiModel("更新编辑锁")
public class TryLockEditLockReq {

  private String lockComponentId;

  private String unlockComponentId;

  private EditLockEnum editLockEnum;

  public EditLockEnum getEditLockEnum() {
    if (editLockEnum == null) {
      return EditLockEnum.SHEET;
    }
    return this.editLockEnum;
  }
}
