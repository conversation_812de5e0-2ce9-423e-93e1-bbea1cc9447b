package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.dto.NewsheetTemplateInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("给业务主题添加一个表格sheet请求对象")
public class AddTopicSheetRequest {

  @Tolerate
  public AddTopicSheetRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "当前所在主题ID", required = true)
  @NotNull(message = "{400002}")
  private Integer topicId;

  /**
   * 1: 'ai', 3: 'case', 4: 'business-view', 5: 'route-explorer', 6: 'consis-explor', 7: 'rework',
   * 8: 'new-sheet', 9: 'duration-time', 10: 'process-compare', 11: 'process-explorer', 12:
   * 'ai-sheet', 13: 'process-overview', 14: 'ROOT_CAUSE_ANALYSIS',
   */
  @ApiModelProperty(
      value =
          "表格类型（1：流程AI，2：流程变体，3：案例视图，4：业务视图，5：流程视图，6：一致性探索,7: newSheet,9:吞吐时间，10：流程对比,11: 路径探索,14: 根因分析-new）",
      required = true)
  @NotNull(message = "{400008}")
  private Integer type;

  private String sheetName;

  private String newsheetTemplateName;

  @ApiModelProperty(value = "导入文件方式创建topic")
  private NewsheetTemplateInfo newsheetTemplateInfo;
}
