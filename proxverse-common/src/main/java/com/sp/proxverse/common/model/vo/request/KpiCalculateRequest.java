package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("KPI点击计算结果请求对象")
public class KpiCalculateRequest {

  @ApiModelProperty(value = "当前所在主题ID，如果是主题表格sheet内创建kpi，也要传主题ID（而不是sheetId）", required = true)
  @NotNull(message = "{400002}")
  private Integer topicId;

  private Integer sheetId;

  @ApiModelProperty(value = "kpi公式", required = true)
  @NotBlank(message = "{400001}")
  private String expression;

  @ApiModelProperty(value = "组件ID，如果是从newsheet组件过来的，此字段要传值")
  private Integer componentId;

  private String unit;

  @ApiModelProperty(value = "1:从topic过滤规则过来的，2：从sheet过滤规则过来的，3：自定义过滤来的")
  private Integer source;

  private Integer prikey;
}
