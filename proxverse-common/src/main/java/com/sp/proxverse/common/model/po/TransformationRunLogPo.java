package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 运行日志
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
@Getter
@Setter
@TableName("t_transformation_run_log")
public class TransformationRunLogPo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 数据池Id */
  private Integer dataPoolId;

  /** 运行Sql */
  private String sqlContent;

  private String transformationName;

  private String errorMessage;

  private Integer transformationId;

  private Boolean asyncFlag;

  private Long timeScope;

  /** @TableLogStatusEnum 1：运行中，2：完成，3：失败 */
  private Integer successFlag;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  private LocalDateTime createTime;

  private Integer tenantId;
}
