package com.sp.proxverse.common.model.vo.kpi.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Getter
@Setter
@Builder
@ApiModel("查询kpi信息返回DTO")
public class KpiInfoResponse {

  @Tolerate
  public KpiInfoResponse() {
    // comment empty
  }

  @ApiModelProperty(value = "数据模型id")
  public Integer dataModelId;

  @ApiModelProperty(value = "数据模型名称")
  public String dataModelName;

  @ApiModelProperty(value = "数据池名称")
  public String dataPoolName;

  @ApiModelProperty(value = "kpiId")
  private Integer kpiId;

  @ApiModelProperty(value = "kpi名称")
  private String name;

  @ApiModelProperty(value = "单位")
  private String unit;

  @ApiModelProperty(value = "1:越大越优，2：越小越优")
  private Integer type;

  @ApiModelProperty(value = "目标值")
  private BigDecimal target;

  @ApiModelProperty(value = "公式")
  private String expression;

  @ApiModelProperty(value = "更新时间")
  private String updateTime;

  @ApiModelProperty(value = "当前值")
  private String value;

  @ApiModelProperty(value = "选择的时间列ID")
  private Integer timeColumnId;

  @ApiModelProperty(value = "选择的时间列名称")
  private String timeColumnName;

  @ApiModelProperty(value = "选择的表ID")
  private Integer fileId;
}
