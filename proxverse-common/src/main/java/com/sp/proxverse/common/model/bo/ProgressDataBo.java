package com.sp.proxverse.common.model.bo;

import com.sp.proxverse.common.util.CalculationUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-04-13 1:47 下午
 */
@Getter
@Setter
public class ProgressDataBo {

  public ProgressDataBo() {
    this.bytesRead = 0L;
    this.totalBytes = 0L;
  }

  @ApiModelProperty(value = "已经加载")
  private Long bytesRead;

  @ApiModelProperty(value = "文件大小")
  private Long totalBytes;

  @ApiModelProperty(value = "完成百分比")
  private double completePercentage;

  public double getCompletePercentage() {
    if (this.bytesRead == null || this.totalBytes == null) {
      return 0;
    }
    return CalculationUtil.calculateOfPercentage(this.bytesRead, this.totalBytes, 2);
  }

  public void processCompletePercentage() {
    this.completePercentage =
        CalculationUtil.calculateOfPercentage(this.bytesRead, this.totalBytes, 2);
  }
}
