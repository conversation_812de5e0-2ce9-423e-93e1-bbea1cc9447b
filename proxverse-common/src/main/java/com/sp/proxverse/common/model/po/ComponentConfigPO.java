package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sp.proxverse.common.model.CopyEntityService;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_component_config")
public class ComponentConfigPO implements CopyEntityService<ComponentConfigPO> {

  @Tolerate
  public ComponentConfigPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private Integer componentId;

  private String componentValue;

  private String componentConfig;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @Override
  public ComponentConfigPO copy() {
    ComponentConfigPO componentConfigPO = new ComponentConfigPO();
    componentConfigPO.setComponentValue(this.componentValue);
    componentConfigPO.setComponentConfig(this.componentConfig);
    componentConfigPO.setDeleted(this.deleted);
    componentConfigPO.setTenantId(this.tenantId);
    return componentConfigPO;
  }
}
