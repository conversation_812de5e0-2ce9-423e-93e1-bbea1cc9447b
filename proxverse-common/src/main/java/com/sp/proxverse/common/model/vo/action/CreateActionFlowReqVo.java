package com.sp.proxverse.common.model.vo.action;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-05-13 5:24 下午
 */
@Getter
@Setter
@ApiModel("创建动作流")
public class CreateActionFlowReqVo {

  @ApiModelProperty(value = "父Id")
  @NotNull(message = "{400002}")
  public Integer parentTopicId;

  @ApiModelProperty(value = "名称")
  @NotNull(message = "{400002}")
  public String topicName;
}
