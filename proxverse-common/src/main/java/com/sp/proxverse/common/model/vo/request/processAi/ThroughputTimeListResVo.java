package com.sp.proxverse.common.model.vo.request.processAi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-07-25 9:54 上午
 */
@Data
@ApiModel
public class ThroughputTimeListResVo {

  @ApiModelProperty(value = "案例ID")
  private String caseId;

  @ApiModelProperty(value = "事件数")
  private String eventNum;

  @ApiModelProperty(value = "返工事件数")
  private String reworkEventNum;

  @ApiModelProperty(value = "是否返工（0：否，1：是）")
  private Long hasRework;

  @ApiModelProperty(value = "吞吐时间 单位：秒")
  private String caseDuration;
}
