package com.sp.proxverse.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("流程树过滤条件显示VO对象")
public class TopicFilterOutputVO {
  @Tolerate
  public TopicFilterOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "主键ID，删除用这个")
  private Integer id;

  private Integer componentId;

  @ApiModelProperty(value = "过滤项类型描述")
  private String type;

  @ApiModelProperty(value = "过滤项")
  private String desc;

  @ApiModelProperty(value = "过滤项类型，为500则不允许编辑,为100则跳到事件过滤页面，700：变量过滤，800：流程过滤，900：吞吐时间过滤，1000：返工过滤")
  private Integer filterType;

  private String info;

  @ApiModelProperty(value = "1:允许跳转，2：不允许跳转")
  private Integer enableJump;

  private String formatting;

  private String format;

  private List<String> pql;

  private String extension;

  private String param2;

  private String value2;
}
