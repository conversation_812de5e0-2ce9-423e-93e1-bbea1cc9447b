package com.sp.proxverse.common.model.vo.request.processAi;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-03-03 10:56
 */
@Data
@ApiModel("编辑锁状态响应")
public class TryLockEditLockRes {

  private Boolean lockFlag;

  private Boolean isMyselfLocked;

  private String holdName;

  private Integer holdId;

  private Long remainingTime;

  public TryLockEditLockRes() {
    this.isMyselfLocked = false;
    this.lockFlag = false;
  }
}
