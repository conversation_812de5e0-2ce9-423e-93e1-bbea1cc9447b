package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * new sheet信息
 *
 * <AUTHOR>
 * @since 2022-03-07
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "TopicSheetNew对象", description = "new sheet信息")
@TableName("t_topic_sheet_new")
public class TopicSheetNewPo {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty(value = "当业务主题为图谱时，则可能会关联kpi")
  private Integer topicSheetId;

  @ApiModelProperty(value = "组件值")
  private String componentsValue;

  @ApiModelProperty(value = "组件信息")
  private String componentsInfo;

  @ApiModelProperty(value = "0：未删除，1：已删除")
  private Integer deleted;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
