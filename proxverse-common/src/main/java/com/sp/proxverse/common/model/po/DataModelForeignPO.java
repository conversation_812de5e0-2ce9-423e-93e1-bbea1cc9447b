package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_data_model_foreign")
public class DataModelForeignPO {

  @Tolerate
  public DataModelForeignPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("所属数据模型ID")
  private Integer dataModelId;

  @ApiModelProperty("所属文件ID")
  private Integer fileId;

  @ApiModelProperty("所属字段ID")
  private Integer fieldId;

  @ApiModelProperty("所属字段ID")
  private String field;

  @ApiModelProperty("所属文件ID")
  private Integer pointFileId;

  @ApiModelProperty("所属字段ID")
  private Integer pointFieldId;

  @ApiModelProperty("所属字段ID")
  private String pointField;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("0：未merge，1：已merge")
  private Integer hasMerge;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
