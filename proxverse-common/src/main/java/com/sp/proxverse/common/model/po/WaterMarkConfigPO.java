package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sp.proxverse.common.model.dict.WaterMarkEnabledEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_water_mark_config")
public class WaterMarkConfigPO {

  @Tolerate
  public WaterMarkConfigPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("是否开启水印，0：否，1：是")
  private Integer waterMarkEnabled;

  /** @see WaterMarkEnabledEnum */
  @ApiModelProperty("字体")
  private Integer word;

  @ApiModelProperty("字号大小")
  private Integer fontSize;

  @ApiModelProperty("透明度")
  private String transparency;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
