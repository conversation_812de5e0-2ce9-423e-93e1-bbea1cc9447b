package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据模型位置信息
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Getter
@Setter
@TableName("t_data_model_site")
public class DataModelSitePo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 文件Id */
  private Integer fileId;

  /** 数据模型Id */
  private Integer dataModelId;

  private String topRatio;

  private String leftRatio;

  private String topValue;

  private String leftValue;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
