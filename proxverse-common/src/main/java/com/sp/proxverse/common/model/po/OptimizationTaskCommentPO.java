package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * 优化任务评论
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-09 19:07:26
 */
@Builder
@Data
@TableName("t_optimization_task_comment")
public class OptimizationTaskCommentPO {

  @Tolerate
  public OptimizationTaskCommentPO() {
    // comment empty
  }

  /** 编号 */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  /** 评论内容 */
  private String commentContent;
  /** 评论的用户id */
  private Integer commentUserId;
  /** 优化任务id */
  private Integer optimizationTaskId;
  /** 创建人id */
  private Integer createrId;
  /** */
  private Date updateTime;
  /** */
  private Date createTime;
  /** 是否删除 */
  private Long deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
