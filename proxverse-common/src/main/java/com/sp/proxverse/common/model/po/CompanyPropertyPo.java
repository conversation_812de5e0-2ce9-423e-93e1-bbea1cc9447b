package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 公司配置
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Getter
@Setter
@TableName("t_company_property")
public class CompanyPropertyPo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  private Integer tenantId;

  /** CompanyPropertyEnum */
  private String propertyKey;

  private String propertyValue;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  @TableField(fill = FieldFill.INSERT)
  private LocalDateTime createTime;

  private LocalDateTime updateTime;
}
