package com.sp.proxverse.common.model.vo.request.processAi;

import com.sp.proxverse.common.model.enums.TimeUnitEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-07-25 11:49 上午
 */
@Data
@ApiModel
public class UpdateContrastFilterReqVo {
  @ApiModelProperty("sheetId")
  @NotNull(message = "{400006}")
  private Integer sheetId;

  @ApiModelProperty("参数值")
  private String paramValue;

  @ApiModelProperty(value = "表ID(事件过滤此字段不填，type=700时此字段必填)")
  private Integer fileId;

  @ApiModelProperty(value = "变量ID(事件过滤此字段不填，type=700时此字段必填)")
  private Integer fieldId;

  @ApiModelProperty(value = "左右标识：0左，1右")
  @NotNull(message = "{400009}")
  private Integer leftAndRightFlag;

  @ApiModelProperty(
      value =
          "运算符号类型（10：>=，11：>，20：<=，21：<，30：=，40：time勾选，41：time的范围）(事件过滤此字段不填)，type=700时此字段可填可不填")
  private Integer operationType;

  @ApiModelProperty(value = "时间单位，当是吞吐时间时传此参数 ")
  private TimeUnitEnum timeUnitEnum;
}
