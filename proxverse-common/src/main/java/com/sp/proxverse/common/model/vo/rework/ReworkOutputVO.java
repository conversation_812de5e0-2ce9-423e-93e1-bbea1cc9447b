package com.sp.proxverse.common.model.vo.rework;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("获取返工探索列表VO对象v1.1")
public class ReworkOutputVO {
  @Tolerate
  public ReworkOutputVO() {
    // comment empty
  }

  @ApiModelProperty(value = "案例ID")
  private String caseId;

  @ApiModelProperty(value = "事件数")
  private String eventNum;

  @ApiModelProperty(value = "返工事件数")
  private String reworkEventNum;

  @ApiModelProperty(value = "是否返工（0：否，1：是）")
  private Integer hasRework;
}
