package com.sp.proxverse.common.model.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("保存联系我们请求DTO")
public class SaveContactRequestVO {

  @Tolerate
  public SaveContactRequestVO() {
    // comment empty
  }

  @ApiModelProperty(value = "租户ID", required = true)
  @NotNull(message = "租户ID不能为空")
  private Integer groupId;

  @ApiModelProperty(value = "数据来源，：1，延长有效期，2，升级版本", required = true)
  @NotNull(message = "数据来源")
  private Integer source;
}
