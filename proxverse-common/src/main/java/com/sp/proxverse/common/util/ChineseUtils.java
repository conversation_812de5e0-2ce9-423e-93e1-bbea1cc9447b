package com.sp.proxverse.common.util;

import com.sp.proxverse.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

@Slf4j
public class ChineseUtils {
  public static HanyuPinyinOutputFormat formart = new HanyuPinyinOutputFormat();

  static {
    formart.setCaseType(HanyuPinyinCaseType.LOWERCASE);
    formart.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
    formart.setVCharType(HanyuPinyinVCharType.WITH_V);
  }

  public static String transformChineseToPinyinIfNeed(String china) {
    char[] arrays = china.trim().toCharArray();
    StringBuilder result = new StringBuilder();
    for (int i = 0; i < arrays.length; i++) {
      char ti = arrays[i];
      if (Character.toString(ti).matches("[\\u4e00-\\u9fa5]")) { // 匹配是否是中文
        try {
          result.append(PinyinHelper.toHanyuPinyinStringArray(ti, formart)[0]);
        } catch (BadHanyuPinyinOutputFormatCombination e) {
          log.error("Error for transform chinese to pinyin {}", ti);
          log.error("transformChineseToPinyinIfNeed {}", e);
          throw new BizException(5000, e.getMessage());
        }
      } else {
        result.append(ti);
      }
    }
    return result.toString();
  }
}
