package com.sp.proxverse.common.model.vo.action;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-24 12:47 下午
 */
@Data
@ApiModel("执行动作修改日志")
public class ActionModifyLogResVo {

  @ApiModelProperty("动作流Id")
  private Integer actionFlowId;

  @ApiModelProperty("动作流名称")
  private String actionFlowName;

  @ApiModelProperty("类型，1：创建，2：修改，3：删除")
  private Integer modifyType;

  @ApiModelProperty("修改时间")
  private String modifyTime;

  @ApiModelProperty("编辑人名称")
  private String userName;

  @ApiModelProperty("编辑人名称-别名")
  private String name;
}
