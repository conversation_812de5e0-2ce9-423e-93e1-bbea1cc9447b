package com.sp.proxverse.common.model.vo.request;

import com.sp.proxverse.common.model.dto.CalcTimeFilterDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("相关系数请求对象")
public class RelatedRequest {

  @ApiModelProperty(value = "相关变量集合", required = true)
  private List<VariableSaveSubRequest> variableList;

  @ApiModelProperty(value = "事件名称，v1.1修改，event和variant在各自的场景中不能为空")
  private String event;

  @ApiModelProperty(value = "业务主题表格ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicSheetId;

  private Integer topicId;

  @ApiModelProperty(value = "事件吞吐对象")
  private CalcTimeFilterDTO calcDto;

  private String reason;

  private Integer reasonType;

  private String variant;

  private String rework;

  private String startTime;

  private String endTime;
}
