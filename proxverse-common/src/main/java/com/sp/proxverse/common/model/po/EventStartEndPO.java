package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Builder;
import lombok.Data;

@TableName("t_event_start_end")
@Data
@Builder
public class EventStartEndPO {
  @ApiModelProperty(value = "")
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty(value = "")
  private Integer topicId;

  @ApiModelProperty(value = "")
  private Integer fileId;

  @ApiModelProperty(value = "开始事件")
  private String start;

  @ApiModelProperty(value = "结束事件")
  private String end;

  @ApiModelProperty(value = "")
  private Date createTime;

  @ApiModelProperty(value = "")
  private Byte deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
