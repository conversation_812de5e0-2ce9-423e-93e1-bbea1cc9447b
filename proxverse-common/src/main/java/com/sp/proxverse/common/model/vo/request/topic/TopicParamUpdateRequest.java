package com.sp.proxverse.common.model.vo.request.topic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@ApiModel("topic 参数修改对象")
public class TopicParamUpdateRequest {

  @Tolerate
  public TopicParamUpdateRequest() {
    // comment empty
  }

  @ApiModelProperty(value = "topicId")
  @NotNull(message = "{400002}")
  private Integer topicId;

  private Integer paramId;

  private String name;

  @ApiModelProperty(value = "表达式")
  private String value;

  private Integer maxExport;

  private Integer allowExport;

  @ApiModelProperty("是否开启工作时间表")
  private Boolean workdayEnable;

  @ApiModelProperty("工作时间表名")
  private String workdayTableName;
}
