package com.sp.proxverse.common.model.vo.action;

import com.sp.proxverse.common.model.enums.ActionRunStartEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-19 5:49 下午
 */
@Data
@ApiModel("动作流运行结果")
public class RunActionFlowStatusResVo {

  @ApiModelProperty("动作流运行状态")
  private ActionRunStartEnum runStatus;

  @ApiModelProperty("每个动作状态")
  private List<ActionRunStartVo> actionStatus;
}
