package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 血缘关系
 *
 * <AUTHOR>
 * @since 2023-01-10
 */
@Getter
@Setter
@TableName("t_lineage_relation")
public class LineageRelationPo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 数据来源表Id */
  private String fromTable;

  /** 生成表Id */
  private String toTable;

  /** 关系Id */
  private Integer relationshipId;

  /** 类型 0：etl */
  private Integer relationType;

  private LocalDateTime createTime;

  private LocalDateTime updateTime;

  /** 0：未删除，1：已删除 */
  private Integer deleted;

  /** 租户ID */
  private Integer tenantId;

  public enum RelationType {
    ETL(1);
    final Integer code;

    RelationType(Integer code) {
      this.code = code;
    }

    public Integer getCode() {
      return code;
    }
  }
}
