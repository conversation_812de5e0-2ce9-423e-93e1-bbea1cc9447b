package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_data_connector_jdbc")
public class DataConnectorJdbcPO {

  @Tolerate
  public DataConnectorJdbcPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("连接器ID")
  private Integer connectorId;

  @ApiModelProperty("IP")
  private String ip;

  @ApiModelProperty("端口")
  private Integer port;

  @ApiModelProperty("库名称")
  private String dbname;

  @ApiModelProperty("所选表名称")
  private String schemaName;

  @ApiModelProperty(value = "实例号（HANA DB）")
  private String instanceNumber;

  @ApiModelProperty("用户名")
  private String username;

  @ApiModelProperty("密码")
  private String password;

  @ApiModelProperty("1:好,0:否")
  private Integer connected;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @ApiModelProperty("data pool id")
  private Integer poolId;
}
