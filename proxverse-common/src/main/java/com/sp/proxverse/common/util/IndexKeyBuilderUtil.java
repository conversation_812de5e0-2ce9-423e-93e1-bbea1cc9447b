package com.sp.proxverse.common.util;

/**
 * <AUTHOR>
 * @date 2024/1/30 18:10
 */
public class IndexKeyBuilderUtil {

  public static String makeIndexCacheKey(int modeId, int versionNumber) {
    return modeId + "_" + versionNumber;
  }

  public static Integer extractModeIdByIndexCacheKey(String IndexCache) {
    int index = IndexCache.indexOf('_');
    if (index < 0) {
      return 0;
    } else {
      return Integer.valueOf(IndexCache.substring(0, index));
    }
  }

  public static Integer extractVersionByIndexCacheKey(String indexCache) {
    int index = indexCache.indexOf('_');
    if (index < 0) {
      return 0;
    } else {
      return Integer.valueOf(indexCache.substring(index + 1, indexCache.length()));
    }
  }
}
