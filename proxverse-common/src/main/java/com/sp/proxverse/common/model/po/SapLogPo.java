package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * SAP LOAD LOG
 *
 * <AUTHOR>
 * @since 2023-11-23
 */
@Getter
@Setter
@TableName("t_sap_log")
public class SapLogPo implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 信息 */
  private String info;

  /** 加载类型 */
  private String loadType;

  private String runSql;

  private Date createTime;

  /** 租户ID */
  private Integer tenantId;
}
