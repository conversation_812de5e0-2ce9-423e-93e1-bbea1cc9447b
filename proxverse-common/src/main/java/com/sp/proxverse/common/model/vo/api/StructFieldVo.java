package com.sp.proxverse.common.model.vo.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-12-09 16:52
 */
@Data
@ApiModel("数据类型")
public class StructFieldVo {

  private String columnName;

  /** @see DataTypeEnum */
  @ApiModelProperty(
      " INT(1, \"int\"),\n"
          + "    FLOAT(2, \"float\"),\n"
          + "    BOOL(3, \"boolean\"),\n"
          + "    STR(4, \"string\"),\n"
          + "    DATE(5, \"date\"),\n"
          + "    TIME(6, \"time\"),\n"
          + "    DATE_TIME(7, \"datetime\"),")
  private String columnType;

  // private Integer columnType;

  private String expressionLhs;
}
