package com.sp.proxverse.common.model.bo.actionLog;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-05-19 10:30 上午
 */
@Data
@Builder
public class CreatActionLogBo {

  public static final Integer ACTIVE_TYPE_FLOW = -1;

  private Integer actionLogId;

  /** 运行编码 */
  private Integer runCoed;

  /** 动作类型 */
  private Integer activeType;

  /** 动作Id */
  private Integer actionId;

  /** 动作Code */
  private Integer actionCode;

  /** 动作流Id */
  private Integer actionFlowId;

  /** 动作参数 */
  private String actionParam;

  /** 动作结果 */
  private String actionResult;

  /** 成功标识：-1：未知，0：成功，1：失败 */
  private Integer successFlag;

  /** 日志是否为动作流日志：0：是，1：不是 */
  private Integer actionFlowFlag;

  /** 单节点运行标识 0否 1是 */
  private Boolean singleNodeFlag;

  /** 运行人 */
  private Integer userId;

  /** 节点名称 */
  private String actionName;

  /** 数据来源：1 ：正常生成，2：snapshot生成 */
  private Integer sourceType;

  /** 前端图信息 */
  private String nodeInfo;

  public Integer getSingleNodeFlagInt() {
    if (this.singleNodeFlag) {
      return 1;
    }
    return 0;
  }
}
