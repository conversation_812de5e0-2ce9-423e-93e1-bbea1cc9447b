package com.sp.proxverse.common.model.vo.action;

import com.sp.proxverse.common.action.bo.FilterParamBo;
import com.sp.proxverse.common.action.enums.ActionTypeEnum;
import io.swagger.annotations.ApiModel;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022-05-18 1:10 下午
 */
@Getter
@Setter
@ApiModel("动作流列表信息")
public class ActionFlowListResVo {

  /** 执行动作名称 */
  private String name;

  /** 动作编号 */
  private Integer actionCode;

  /** 动作流Id */
  private Integer actionFlowId;

  /** 动作类型 */
  private ActionTypeEnum activeType;

  /** 动作参数 */
  private String actionParam;

  /** 动作结果 */
  private String actionResult;

  /** 该节点是否需要过滤： 0：需要，1：不需要 */
  private Boolean filterFlag;

  /** 过滤文本 */
  private List<FilterParamBo> filterParams;

  /** 起始标识 */
  private Boolean startFlag;
}
