package com.sp.proxverse.common.model.po;

import static org.apache.spark.common.model.BucketGroupDesc.fromJsonArray;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.apache.spark.common.model.BucketGroupDesc;
import scala.Serializable;

@Data
@Builder
@TableName("t_data_model")
public class DataModelPO implements Serializable {

  @Tolerate
  public DataModelPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("所属数据池ID")
  private Integer poolId;

  @ApiModelProperty("数据模型名称")
  private String name;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("创建时间")
  public Date createTime;

  @ApiModelProperty("修改时间")
  public Date updateTime;

  @ApiModelProperty("0：未加载，1：加载中，2：加载成功，3：加载失败")
  private Integer status;

  @ApiModelProperty(value = "所设caseId列")
  private Integer caseid;

  @ApiModelProperty(value = "所设event列")
  private Integer event;

  @ApiModelProperty(value = "结束时间列")
  private Integer time;

  @ApiModelProperty(value = "开始时间列")
  private Integer startTime;

  @ApiModelProperty("租户ID")
  private Integer tenantId;

  @ApiModelProperty("bucket 数量")
  private Integer numBuckets;

  private Integer currentVersion;

  private Integer maxVersion;

  private String bucketGroups;

  public BucketGroupDesc[] toBucketGroups() {
    if (bucketGroups == null || bucketGroups.isEmpty()) {
      return null;
    }
    return fromJsonArray(bucketGroups);
  }
}
