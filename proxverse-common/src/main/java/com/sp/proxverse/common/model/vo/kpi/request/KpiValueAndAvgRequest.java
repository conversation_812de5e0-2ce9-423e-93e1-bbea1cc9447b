package com.sp.proxverse.common.model.vo.kpi.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("kpi值（当前值，平均值，目标值）请求对象")
public class KpiValueAndAvgRequest {

  @ApiModelProperty(value = "kpi执行Id", required = true)
  private Integer kpiRelationId;

  @ApiModelProperty(value = "时间类型：10:日/20:月", required = true)
  private Integer type;
}
