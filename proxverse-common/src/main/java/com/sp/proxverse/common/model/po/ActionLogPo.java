package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * 执行动作日志
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Getter
@Setter
@TableName("t_action_log")
@Builder
public class ActionLogPo implements Serializable {

  @Tolerate
  public ActionLogPo() {
    // comment empty
  }

  /** 是否是动作流日志 */
  public static final Integer ACTION_FLOW_TRUE = 0;

  public static final Integer ACTION_FLOW_FALSE = 1;

  /** 是否是单节点日志标识 */
  public static final Integer SINGLE_NODE_FLAG = 0;

  public static final Integer SINGLE_NODE_TRUE = 1;

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /** 动作Id */
  private Integer actionId;

  /** 动作编码 */
  private Integer actionCode;

  /** 运行编码: 运行码为-1时为但节点运行 */
  private Integer runCode;

  /** 动作类型 */
  private Integer activeType;

  /** 动作流Id */
  private Integer actionFlowId;

  /** 节点名称 */
  private String actionName;

  /** 动作参数 */
  private String actionParam;

  /** 动作结果 */
  private String actionResult;

  /** 日志是否为动作流日志：0：是，1：不是 */
  private Integer actionFlowFlag;

  /** 单节点运行标识： 0：否 1：是 */
  private Integer singleNodeFlag;

  /** 成功标识：-1：未知，0：成功，1：失败 */
  private Integer successFlag;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  /** 创建人 */
  private Integer createUser;

  /** 本次执行状态 */
  private String state;

  /** 前端节点信息 */
  private String nodeInfo;

  /** 数据来源：1 ：正常生成，2：snapshot生成 */
  private Integer sourceType;

  private Date createTime;

  private Date updateTime;

  private Long taskId;

  private Integer userId;

  // 2:手动执行，3：自动执行
  private Integer type;

  private Integer tenantId;

  public Boolean getSingleNodeFlagBoolean() {
    if (this.singleNodeFlag == 1) {
      return true;
    }
    return false;
  }
}
