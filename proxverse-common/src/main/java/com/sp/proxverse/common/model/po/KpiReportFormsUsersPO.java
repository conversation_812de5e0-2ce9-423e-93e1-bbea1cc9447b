package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * kpi报表关联的用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 11:03:14
 */
@Builder
@Data
@TableName("t_kpi_report_forms_users")
public class KpiReportFormsUsersPO {

  @Tolerate
  public KpiReportFormsUsersPO() {
    // comment empty
  }

  /** 主键Id */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  /** kpi报表id */
  private Integer kpiReportFormsId;
  /** 发送人的用户id */
  private Integer userId;
  /** 创建人Id */
  private Integer createId;
  /** 创建时间 */
  private Date createTime;
  /** 更新时间 */
  private Date updateTime;
  /** 是否逻辑删除 */
  private Integer deleted;
  /** 排序号 */
  private Integer orderNo;
}
