package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
@TableName("t_cause_kpi")
public class CauseKpiPO {

  @Tolerate
  public CauseKpiPO() {
    // comment empty
  }

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @ApiModelProperty("根因ID")
  private Integer causeId;

  @ApiModelProperty("层级，目前只有2级")
  private Integer kpiId;

  @ApiModelProperty("0：未删除，1：已删除")
  private Integer deleted;

  @ApiModelProperty("租户ID")
  private Integer tenantId;
}
