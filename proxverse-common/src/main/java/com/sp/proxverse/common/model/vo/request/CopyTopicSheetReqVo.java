package com.sp.proxverse.common.model.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-06-30 5:30 下午
 */
@Data
@ApiModel("拷贝")
public class CopyTopicSheetReqVo {

  @ApiModelProperty(value = "业务主题表格ID", required = true)
  @NotNull(message = "{40000}")
  private Integer topicSheetId;

  @ApiModelProperty(value = "业务主题ID", required = true)
  @NotNull(message = "{400006}")
  private Integer topicId;

  @ApiModelProperty(value = "业务主题名称")
  private String sheetName;
}
