package com.sp.proxverse.common.model.vo.sheet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("定义工作时间请求对象")
public class SaveCalcWorkTimeRequest {

  @ApiModelProperty(value = "组件ID")
  @NotNull(message = "{400006}")
  private Integer componentId;

  @ApiModelProperty(value = "具体工作时间")
  private List<WorkTimeRequest> workTimeList;
}
