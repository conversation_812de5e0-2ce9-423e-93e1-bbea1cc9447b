package com.sp.proxverse.common.util;

import com.sp.proxverse.common.exception.BizException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.shaded.org.apache.commons.net.telnet.TelnetClient;

@Slf4j
public class TelnetUtil {

  private static final Pattern JDBC_PATTERN = Pattern.compile("(?<host>[^:@/]+):(?<port>\\d+).*");
  public static final String PHOENIX_PREFIX = "jdbc:phoenix";
  private static final Pattern PHOENIX_PATTERN =
      Pattern.compile("jdbc:phoenix:(?<host>\\S+):(?<port>\\d+).*");
  private static final String HOST_KEY = "host";
  private static final String PORT_KEY = "port";
  private static final String SPLIT_KEY = ",";

  public static void telnet(String ip, int port) {
    TelnetClient client = null;
    try {
      client = new TelnetClient();
      client.setConnectTimeout(3000);
      client.connect(ip, port);
    } catch (Exception e) {
      log.info("connect error:", e);
      throw new BizException(5000, "Unable connect to : " + ip + ":" + port);
    }
  }

  public static void telnet(String url) {
    if (url == null || url.trim().length() == 0) {
      throw new IllegalArgumentException("url can not be null");
    }

    String host = null;
    int port = 0;
    Matcher matcher = null;
    if (StringUtils.startsWith(url, PHOENIX_PREFIX)) {
      matcher = PHOENIX_PATTERN.matcher(url);
    } else {
      matcher = JDBC_PATTERN.matcher(url);
    }
    if (matcher.find()) {
      host = matcher.group(HOST_KEY);
      port = Integer.parseInt(matcher.group(PORT_KEY));
    }

    if (host == null || port == 0) {
      // oracle高可用jdbc url此处获取不到IP端口，直接return。
      return;
    }

    if (host.contains(SPLIT_KEY)) {
      String[] hosts = host.split(SPLIT_KEY);
      for (String s : hosts) {
        if (StringUtils.isNotBlank(s)) {
          telnet(s, port);
        }
      }
    } else {
      telnet(host, port);
    }
  }
}
