package com.sp.proxverse.common.model.bo;

import com.sp.proxverse.common.model.enums.ConformanceTypeEnum;
import com.sp.proxverse.common.model.po.TopicFilterPO;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-08-10 1:41 下午
 */
@Data
public class GetVariantInfoListBo {

  /** */
  private Integer topicSheetId;

  private String variant;

  /** 类型 */
  private List<ConformanceTypeEnum> typeEnums;

  /** 过滤条件标识： 1，通过sheetId分析，2，采用参数的，3，不使用过滤条件 */
  private Integer filterFlag;

  @ApiModelProperty(value = "当前流程（不一致流程）的变体ID")
  private Integer variantId;

  @ApiModelProperty(value = "当前流程（不一致流程）的变体ID")
  private Integer limit;

  /** 过滤条件 */
  private List<TopicFilterPO> addFilterList;

  public GetVariantInfoListBo() {
    this.typeEnums = new ArrayList<>();
  }

  public void addTypeEnums(ConformanceTypeEnum conformanceTypeEnum) {
    this.typeEnums.add(conformanceTypeEnum);
  }
}
