package com.sp.proxverse.common.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * kpi任务 kpi任务表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 11:03:15
 */
@Builder
@Data
@TableName("t_kpi_task")
public class KpiTaskPO {

  @Tolerate
  public KpiTaskPO() {
    // comment empty
  }

  /** 主键Id */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;
  /** KPI执行id */
  private Integer kpiRelationId;
  /** 责任人id */
  private Integer userId;
  /** 任务名称 */
  private String taskName;
  /** 状态（0未完成；1已完成；2重启任务） */
  private Integer status;
  /** 创建人Id */
  private Integer createId;
  /** 创建时间 */
  private Date createTime;
  /** 更新时间 */
  private Date updateTime;
  /** 是否逻辑删除 */
  private Integer deleted;
  /** 排序号 */
  private Integer orderNo;
}
