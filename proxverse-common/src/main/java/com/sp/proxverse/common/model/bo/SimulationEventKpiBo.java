package com.sp.proxverse.common.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-10-10 17:33
 */
@Data
@ApiModel("事件KPI响应")
public class SimulationEventKpiBo {
  @ApiModelProperty("事件名称")
  private String eventName;

  @ApiModelProperty("事件执行次数")
  private Long eventProcessNumber;

  @ApiModelProperty("事件吞吐时间")
  private Long eventDuration;

  @ApiModelProperty("等待比例")
  private Double waitingRatio;

  @ApiModelProperty("总处理时间")
  private Long totalProcessingTime;
}
