/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    id 'com.sp.java-conventions'
}

dependencies {
    implementation project(':proxverse-common')
    implementation project(':proxverse-service-interface')
    implementation project(':proxverse-web-config')
    implementation project(':proxverse-data')
    implementation project(':proxverse-oauth2')
    implementation 'org.apache.httpcomponents:httpclient:4.5.13'
    implementation 'io.springfox:springfox-swagger2:2.9.2'
    implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
    implementation 'io.springfox:springfox-swagger-ui:2.9.2'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'org.hibernate:hibernate-validator:6.2.0.Final'
    implementation 'net.logstash.logback:logstash-logback-encoder:5.2'
    implementation 'mysql:mysql-connector-java:8.0.28'
    implementation 'com.alibaba:druid:1.1.21'
    implementation 'org.mapstruct:mapstruct:1.4.1.Final'
    implementation 'org.mapstruct:mapstruct-processor:1.4.1.Final'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.13.4'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4'
    implementation 'org.apache.spark:spark-sql_2.12:3.3.1-prx-0.0.2'
  implementation 'org.projectlombok:lombok:1.18.20'
  annotationProcessor 'org.projectlombok:lombok:1.18.20'
  implementation 'io.springfox:springfox-swagger2:2.9.2'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.5.1'
  implementation 'com.baomidou:mybatis-plus-core:3.5.7'
  implementation 'com.baomidou:mybatis-plus-generator:3.5.7'
  implementation 'com.baomidou:mybatis-plus-extension:3.5.7'

  implementation 'com.sp:proxverse-pql:1.0.0'
  implementation 'io.springfox:springfox-swagger-ui:2.9.2'
  implementation 'com.github.xiaoymin:swagger-bootstrap-ui:1.9.6'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'org.springframework.cloud:spring-cloud-starter-oauth2:2.2.0.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:2.3.2.RELEASE'
}

description = 'proxverse-execution'
