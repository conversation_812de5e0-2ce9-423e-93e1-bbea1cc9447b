package com.sp.proxverse.execution.service.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sp.proxverse.common.model.dict.signal.SignalRuleStatusEnum;
import com.sp.proxverse.common.model.po.ExecutionActionPO;
import com.sp.proxverse.common.model.po.SignalRulePO;
import com.sp.proxverse.interfaces.dao.service.ExecutionActionService;
import com.sp.proxverse.interfaces.dao.service.SignalRuleService;
import com.sp.proxverse.interfaces.service.execution.ExecutionApiService;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ExecutionApiServiceImpl implements ExecutionApiService {

  @Autowired private SignalRuleService signalRuleService;

  @Autowired private ExecutionActionService executionActionService;

  @Override
  public Boolean removeActionBind(Integer topicId) {
    return Boolean.TRUE;
  }

  @Override
  public Boolean getActionFlowBinding(Integer topicId) {
    long count =
        executionActionService.count(
            new LambdaQueryWrapper<ExecutionActionPO>()
                .eq(ExecutionActionPO::getActionId, topicId)
                .eq(ExecutionActionPO::getDeleted, 0));
    return count > 0;
  }

  @Override
  public void updateSignalRule(List<Integer> modelIds) {
    List<SignalRulePO> signalRuleList = signalRuleService.getListByModelIds(modelIds, null);
    if (CollectionUtils.isEmpty(signalRuleList)) {
      return;
    }
    signalRuleList.forEach(f -> f.setStatus(SignalRuleStatusEnum.CLOSE.getValue()));
    signalRuleService.updateBatchById(signalRuleList);
  }
}
