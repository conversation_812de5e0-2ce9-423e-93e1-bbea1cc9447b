package com.sp.proxverse.execution.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Setter
@Getter
@Builder
public class SignalRuleInfoResponse {
  @Tolerate
  public SignalRuleInfoResponse() {
    // comment empty
  }

  private Integer signalRuleId;

  private String name;

  /** @see com.sp.proxverse.common.model.dict.DataSourceTypeEnum */
  private Integer dataType;

  private Integer modelId;

  private String modelName;

  private Integer poolId;

  private String poolName;

  private Integer createUserId;

  private String createUser;

  private String createName;

  private Integer status;

  private Integer optimizationCount;

  private Integer executionActionCount;

  private String executionTime;
}
