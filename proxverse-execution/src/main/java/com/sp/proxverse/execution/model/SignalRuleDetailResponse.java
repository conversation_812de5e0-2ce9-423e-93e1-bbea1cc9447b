package com.sp.proxverse.execution.model;

import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

@Setter
@Getter
@Builder
public class SignalRuleDetailResponse {
  @Tolerate
  public SignalRuleDetailResponse() {
    // comment empty
  }

  private Integer signalRuleId;

  private Integer dataType;

  private Integer modelId;

  private Integer topicId;

  private String name;

  // 信号规则的状态，0：关闭（被删除了数据模型），1：运行中，2：暂停
  private Integer status;

  private List<Optimization> optimizationList;

  private ExecutionAction executionAction;

  private Integer signalLimit;
}
